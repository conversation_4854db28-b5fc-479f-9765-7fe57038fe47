# curl 'https://oa.9ji.com/cloudapi_nc/afterservice/api/smallpro/saveOrUpdateSmallpro' -H 'authority: oa.9ji.com' -H 'pragma: no-cache' -H 'cache-control: no-cache' -H 'accept: application/json, text/plain, */*' -H 'sec-fetch-dest: empty' -H 'authorization: 007931F669114B25A19DDF25240CF7B4' -H 'xservicename: oa-afterservice' -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.163 Safari/537.36' -H 'content-type: application/json' -H 'origin: https://oa.9ji.com' -H 'sec-fetch-site: same-origin' -H 'sec-fetch-mode: cors' -H 'referer: https://oa.9ji.com/staticpc/' -H 'accept-language: zh-CN,zh;q=0.9' -H 'cookie: sslKey_45265=0A0C308844E9638824E9004; shop=22_0; rusername=9372; uuid=2f0e2cb5-ad7b-4af1-8a3e-3e00a728841e; temp_city=%e4%ba%91%e5%8d%97%e6%98%86%e6%98%8e%20%e4%ba%94%e5%8d%8e%20%e7%94%b5%e4%bf%a1; areaid=22; _jzqx=1.1583133070.1583133070.1.jzqsr=9ji%2Ecom|jzqct=/.-; city=53_%E4%BA%91%E5%8D%97%E7%9C%81-5329_%E5%A4%A7%E7%90%86%E5%B7%9E-532901_%E5%A4%A7%E7%90%86%E5%B8%82-s_1; cityid=532901; _jzqa=1.3122257393842849300.1578555971.1586237847.1586255790.4; Hm_lvt_928cdc01cf5e200d345bd802227d3a9c=1585195270,1585575730,1586237847,1586399293; ch999MemberID=2386200; ch999MemberUserName=test9; ukey=2f0e2cb5-ad7b-4af1-8a3e-3e00a728841e; sslid=48846; Authorization=6b9a871022bf740c4cc0c6d3d669538a; signTicket=6b9a871022bf740c4cc0c6d3d669538a; ASP.NET_SessionId=jhc14vtmhg40y4g5zlzopttv; sslKey_48846=BEE80F8D76A5755A43B5165; userid_co=9372; pwd_co=5B41D19E4A7C7E6D5C75954DD3C9DC2B; area_co=HQ; isstar=1; userid%5Fco=9372; pwd%5Fco=5B41D19E4A7C7E6D5C75954DD3C9DC2B; area%5Fco=HQ; autoOut=1; pcOaToken=007931F669114B25A19DDF25240CF7B4' --data-binary '{"imei":"","kind":2,"isBaoxiu":true,"situationKind":2,"problem":"","outwardFlag":1,"outward":"","config":"","dataRelease":1,"groupId":1,"comment":"","warrantyStatus":true,"area":"HQ","areaId":22,"id":0,"mobile":"18669161024","userName":"测试","userId":5288749,"name":"测试商品-6","sub_id":15697337,"buydate":"2020-04-09 20:08:35","smallProKinds":[],"stats":0,"smallproBillList":[{"basketId":34880140,"count":1,"ppriceid":75443}]}' --compressed
POST {{after_host}}/api/smallpro/saveOrUpdateSmallpro
authority: oa.9ji.com
pragma: no-cache
cache-control: no-cache
accept: application/json, text/plain, */*
sec-fetch-dest: empty
authorization: 007931F669114B25A19DDF25240CF7B4
xservicename: oa-afterservice
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.163 Safari/537.36
origin: https://oa.9ji.com
sec-fetch-site: same-origin
sec-fetch-mode: cors
referer: https://oa.9ji.com/staticpc/
accept-language: zh-CN,zh;q=0.9
cookie: sslKey_45265=0A0C308844E9638824E9004; shop=22_0; rusername=9372; uuid=2f0e2cb5-ad7b-4af1-8a3e-3e00a728841e; temp_city=%e4%ba%91%e5%8d%97%e6%98%86%e6%98%8e%20%e4%ba%94%e5%8d%8e%20%e7%94%b5%e4%bf%a1; areaid=22; _jzqx=1.1583133070.1583133070.1.jzqsr=9ji%2Ecom|jzqct=/.-; city=53_%E4%BA%91%E5%8D%97%E7%9C%81-5329_%E5%A4%A7%E7%90%86%E5%B7%9E-532901_%E5%A4%A7%E7%90%86%E5%B8%82-s_1; cityid=532901; _jzqa=1.3122257393842849300.1578555971.1586237847.1586255790.4; Hm_lvt_928cdc01cf5e200d345bd802227d3a9c=1585195270,1585575730,1586237847,1586399293; ch999MemberID=2386200; ch999MemberUserName=test9; ukey=2f0e2cb5-ad7b-4af1-8a3e-3e00a728841e; sslid=48846; Authorization=6b9a871022bf740c4cc0c6d3d669538a; signTicket=6b9a871022bf740c4cc0c6d3d669538a; ASP.NET_SessionId=jhc14vtmhg40y4g5zlzopttv; sslKey_48846=BEE80F8D76A5755A43B5165; userid_co=9372; pwd_co=5B41D19E4A7C7E6D5C75954DD3C9DC2B; area_co=HQ; isstar=1; userid%5Fco=9372; pwd%5Fco=5B41D19E4A7C7E6D5C75954DD3C9DC2B; area%5Fco=HQ; autoOut=1; pcOaToken=007931F669114B25A19DDF25240CF7B4
Content-Type: application/json

{
  "imei": "",
  "kind": 2,
  "isBaoxiu": true,
  "situationKind": 2,
  "problem": "",
  "outwardFlag": 1,
  "outward": "",
  "config": "",
  "dataRelease": 1,
  "groupId": 1,
  "comment": "",
  "warrantyStatus": true,
  "area": "HQ",
  "areaId": 22,
  "id": 0,
  "mobile": "18669161024",
  "userName": "测试",
  "userId": 5288749,
  "name": "测试商品-6",
  "sub_id": 15697337,
  "buydate": "2020-04-09 20:08:35",
  "smallProKinds": [],
  "stats": 0,
  "smallproBillList": [
    {
      "basketId": 34880140,
      "count": 1,
      "ppriceid": 75443
    }
  ]
}

###

