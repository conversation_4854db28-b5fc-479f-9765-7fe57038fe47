package com.jiuji.oa.afterservice.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.sys.po.SqlServerColumnPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2023/5/17 10:49
 */
@Mapper
public interface SqlServerColumnMapper extends BaseMapper<SqlServerColumnPo> {
    Long selectMax(@Param("mTableInfoEnum") MTableInfoEnum mTableInfoEnum, @Param("types") Collection types);

    Object selectId(@Param("mTableInfoEnum") MTableInfoEnum mTableInfoEnum, @Param("tableId") Object tableId, @Param("types") Collection types);
}
