/**
 * machine_test_server_cfg
 * 服务主表 服务主表
 *
 * <AUTHOR> 2021/8/10 10:13:17
 * @version 1.0
 * Generated by javascript
 * <p>
 * machine_test_server_cfg(服务主表)
 * //服务主表
 * ---------------------------------------------------
 * id                                PKInteger   //<<类名:int,自增长>>
 * server_name(服务名称)             String(256) //<<类名:nvarchar>>服务名称
 * server_regulations(服务条例)      String(1024) //<<类名:nvarchar>>服务条例
 * business_type(业务类型)           Integer     //<<类名:int>>业务类型
 * sound_record(是否录音)            Bool        //<<类名:bit>>是否录音
 * create_time                       Date        //<<类名:datetime>>
 * create_user                       String(20)  //<<类名:nvarchar>>
 * update_time                       Date        //<<类名:datetime>>
 * is_del                            Bool        //<<类名:bit>>
 * machine_test_server_cfg_rv        Date        //<<类名:timestamp,非空>>
 */

package com.jiuji.oa.afterservice.machine.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务主表 服务主表
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("machine_test_server_cfg")
@ApiModel("验机服务PO")
public class MachineTestServerCfgPO extends Model<MachineTestServerCfgPO> {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("id")
    private Integer id;
    /**
     * 服务名称 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serverName;
    /**
     * 服务条例 服务条例
     */
    @ApiModelProperty("服务条例")
    private String serverRegulations;
    /**
     * 业务类型 业务类型
     */
    @ApiModelProperty("业务类型")
    private Integer businessType;
    /**
     * 条例地址
     */
    @ApiModelProperty("条例地址")
    private String regulationUrl;
    /**
     * 是否录音 是否录音
     */
    @ApiModelProperty("是否录音")
    private Boolean soundRecord;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean isEnable;

    /**
     * 删除
     */
    @TableField("is_del")
    @TableLogic(value = "0",delval = "1")
    @ApiModelProperty("删除")
    private Boolean isDel;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}