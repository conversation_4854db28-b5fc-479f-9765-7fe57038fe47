package com.jiuji.oa.afterservice.bigpro.m.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.bigpro.bo.HuishouInfoBo;
import com.jiuji.oa.afterservice.other.bo.ShouhouTuiHuanInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel
public class ServiceInfoAndSubInfo {

    @ApiModelProperty(value = "会员信息")
    private MemberBasicInfo memberBasicInfo;

    @ApiModelProperty(value = "订单信息")
    private OrderInfo orderInfo;

    @ApiModelProperty(value = "购买服务信息")
    private List<AfterServiceInfo> serviceInfoList;

    @ApiModelProperty(value = "服务结构项")
    private UnGracefulAfterServiceStructure unGracefulServiceStructure;

    @Data
    @Accessors(chain = true)
    @ApiModel
    public static class RecoverInfo {

        @ApiModelProperty(value = "商品ID")
        private Integer productId;

        @ApiModelProperty(value = "PPID")
        private Integer ppid;

        @ApiModelProperty(value = "销售类别")
        private Integer saleType;

        @ApiModelProperty(value = "大件库存ID")
        private Integer mkcId;

        @ApiModelProperty(value = "商品名称")
        private String productName;

        @ApiModelProperty(value = "规格")
        private String productColor;

        @ApiModelProperty(value = "购买时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeDate;

        @ApiModelProperty(value = "串号")
        private String imei;

        @ApiModelProperty(value = "购买地区")
        private String area;

        @ApiModelProperty(value = "购买地区ID")
        private Integer areaId;

        @ApiModelProperty(value = "下单手机号")
        private String subMobile;

        @ApiModelProperty(value = "会员ID")
        private Integer userId;

        @ApiModelProperty(value = "会员等级")
        private Integer userClass;

        @ApiModelProperty(value = "会员等级描述")
        private String userClassName;

        @ApiModelProperty(value = "会员姓名")
        private String userName;

        @ApiModelProperty(value = "黑名单标识")
        private Boolean blackList;

        @ApiModelProperty(value = "品牌ID")
        private Integer brandId;

        @ApiModelProperty(value = "分类CID")
        private Integer cid;

        @ApiModelProperty(value = "商品价格")
        private BigDecimal price;

        @ApiModelProperty(value = "订单号")
        private Integer subId;

        @ApiModelProperty(value = "basketId 订单项ID")
        private Integer basketId;

        @ApiModelProperty(value = "订单类型")
        private Integer type;

        @ApiModelProperty(value = "是否在保")
        private Boolean isGuarantee;

        @ApiModelProperty(value = "质保结束时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime guaranteeEndDate;

        public static RecoverInfo from(HuishouInfoBo info){
            ServiceInfoAndSubInfo.RecoverInfo recoverInfo = new ServiceInfoAndSubInfo.RecoverInfo();
            recoverInfo.setProductId(info.getProductid())
                    .setPpid(info.getPpriceid())
                    .setMkcId(info.getMkc_id())
                    .setProductName(info.getProduct_name())
                    .setProductColor(info.getProduct_color())
                    .setTradeDate(info.getTradedate())
                    .setImei(info.getImei())
                    .setArea(info.getArea())
                    .setAreaId(info.getAreaid())
                    .setSubMobile(info.getSub_mobile())
                    .setBrandId(info.getBrandid())
                    .setCid(info.getCid())
                    .setPrice(info.getPrice())
                    .setSubId(info.getSub_id())
                    .setBasketId(info.getBasket_id())
                    .setType(info.getType())
                    .setIsGuarantee(info.getIsbaoxiu())
                    .setGuaranteeEndDate(info.getBaoxiuEndDate())
                    .setUserClass(info.getUserclass())
                    .setUserClassName(info.getUserclassname())
                    .setUserId(info.getUserid())
                    .setUserName(info.getUsername());
            return recoverInfo;
        }
    }

    @Data
    @Accessors(chain = true)
    @ApiModel
    public static class MemberBasicInfo{

        @ApiModelProperty(value = "会员ID")
        private Integer userId;

        @ApiModelProperty(value = "会员等级")
        private Integer userClass;

        @ApiModelProperty(value = "等级中文描述")
        private String userClassName;

        @ApiModelProperty(value = "会员昵称")
        private String userName;

        @ApiModelProperty(value = "会员姓名")
        private String realName;

        @ApiModelProperty(value = "黑名单标识")
        private Boolean blackList;

        @ApiModelProperty(value = "是否绑定微信")
        private Boolean isBindWechat;

        @ApiModelProperty(value = "微信绑定url")
        private String wechatBindUrl;

        @ApiModelProperty(value = "折价退换次数")
        private Integer afterServicesDiscount;

        @ApiModelProperty(value = "退换列表")
        private List<ShouhouTuiHuanInfo> tuiHuanList;

        @ApiModelProperty(value = "手机号码")
        private String mobile;

    }

    @Data
    @Accessors(chain = true)
    @ApiModel
    public static class AfterServiceInfo{

        @ApiModelProperty(value = "服务购买时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime serviceTradeDate;

        @ApiModelProperty(value = "服务开始生效时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime serviceStartDate;

        @ApiModelProperty(value = "服务失效时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime serviceEndDate;

        @ApiModelProperty(value = "服务名")
        private String serviceName;

        @ApiModelProperty(value = "服务ppid")
        private Integer servicePpid;

        @ApiModelProperty(value = "是否过期")
        private Boolean expired;

        @ApiModelProperty(value = "是否使用")
        private Boolean isInUse;

        @ApiModelProperty(value = "质保时间 （单位：年）")
        private Double insureYear;

        @ApiModelProperty(value = "售卖价格")
        private BigDecimal price;

        @ApiModelProperty(value = "费用")
        private BigDecimal feiYong;

        @ApiModelProperty(value = "是否购买过")
        private Boolean exists;

        @ApiModelProperty(value = "售后维修单号")
        private Integer afterServiceShouHouId;
        /**停止服务订单类型
         * @see com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum
         */
        @ApiModelProperty("枚举类: BusinessTypeEnum")
        private Integer stopSubType;

    }

    @Data
    @Accessors(chain = true)
    @ApiModel
    public static class OrderInfo{
        @ApiModelProperty(value = "商品ID")
        private Integer productId;

        @ApiModelProperty(value = "PPID")
        private Integer ppid;

        @ApiModelProperty(value = "大件库存ID")
        private Integer mkcId;

        @ApiModelProperty(value = "商品名称")
        private String productName;

        @ApiModelProperty(value = "商品规格")
        private String productColor;

        @ApiModelProperty(value = "购买时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeDate;

        @ApiModelProperty(value = "串号")
        private String imei;

        @ApiModelProperty(value = "下单门店")
        private String area;

        @ApiModelProperty(value = "下单门店ID")
        private Integer areaId;

        @ApiModelProperty(value = "下单手机号码")
        private String subMobile;

        @ApiModelProperty(value = "品牌ID")
        private Integer brandId;

        @ApiModelProperty(value = "分类CID")
        private Integer cid;

        @ApiModelProperty(value = "商品价格")
        private BigDecimal price;

        @ApiModelProperty(value = "订单号")
        private Integer subId;

        @ApiModelProperty(value = "basketId 订单项ID")
        private Integer basketId;

        @ApiModelProperty(value = "订单类型")
        private Integer type;

        @ApiModelProperty(value = "下单人")
        private String subTo;

        @ApiModelProperty(value = "")
        private Boolean boughtCarePlusFlag;

        @ApiModelProperty(value = "是否在保修期内")
        private Boolean isGuarantee;

        @ApiModelProperty(value = "质保结束时间")
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime guaranteeEndDate;

        @ApiModelProperty(value = "是否是瑕疵机")
        private Boolean isXcMkc;

        @ApiModelProperty(value = "瑕疵机信息")
        private String xcMkcInfo;

        @ApiModelProperty(value = "是否是回收机")
        private Boolean isRecover;

        @ApiModelProperty(value = "串号记录")
        private List<String> imeiList;

        @ApiModelProperty(value = "")
        private Boolean lastRecoverFlag;

        @ApiModelProperty(value = "回收信息")
        private List<RecoverInfo> recoverInfo;

        @ApiModelProperty(value = "意外保版本")
        private String accidentProtectionVersion;

        @ApiModelProperty(value = "是否意外")
        private Boolean isAccident;

        @ApiModelProperty(value = "是否开具发票")
        private Boolean invoiceTicket;

        //会员信息
        @ApiModelProperty(value = "会员ID")
        private Integer userId;

        @ApiModelProperty(value = "黑名单标识")
        private Boolean blackList;

        @ApiModelProperty("是否绑定微信")
        private Boolean isBindWx;

        @ApiModelProperty("绑定微信URL")
        private String wxBindUrl;

        @ApiModelProperty(value = "用户近一年退换次数")
        private Integer afterServicesDiscount;

        @ApiModelProperty("会员名称")
        private String userName;

        @ApiModelProperty("手机号码")
        private String mobile;

        /**
         * 是否保修
         */
        @ApiModelProperty("是否保修")
        private Boolean baoXiu;

        /**
         * 订单状态
         */
        @ApiModelProperty("订单状态")
        private Integer subCheck;

        @ApiModelProperty("手机号码")
        private String realName;

        @ApiModelProperty("会员等级")
        private Integer userClass;

        @ApiModelProperty("会员等级名称")
        private String userClassName;

        @ApiModelProperty(value = "退换列表")
        private List<ShouhouTuiHuanInfo> tuiHuanList;

    }

    @Data
    @Accessors(chain = true)
    @ApiModel
    public static class UnGracefulAfterServiceStructure{

        @ApiModelProperty(value = "碎屏保信息")
        private AfterServiceInfo screenServiceInfo;

        @ApiModelProperty(value = "售后碎屏保信息")
        private AfterServiceInfo afterScreenServiceInfo;

        @ApiModelProperty(value = "电池保信息")
        private AfterServiceInfo batteryServiceInfo;

        @ApiModelProperty(value = "后盖信息")
        private AfterServiceInfo backCoverServiceInfo;

    }

}
