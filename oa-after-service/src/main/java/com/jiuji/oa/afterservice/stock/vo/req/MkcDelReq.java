package com.jiuji.oa.afterservice.stock.vo.req;

import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class MkcDelReq {
    private Integer id;

    @NotNull(message = "kinds不能为空")
    private String kinds;

    private Integer mkcId;

    private String comment;

    private BigDecimal price1;

    private BigDecimal price2;

    private Integer newPpid;

    private Double youHuiPrice;

    private Integer lpToAreaId;

    @ApiModelProperty(value = "瑕疵机责任门店ID")
    private String frAreaId;

    private List<FileReq> files;

}
