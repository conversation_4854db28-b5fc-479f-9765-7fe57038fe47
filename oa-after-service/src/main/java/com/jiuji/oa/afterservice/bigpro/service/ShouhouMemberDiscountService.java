package com.jiuji.oa.afterservice.bigpro.service;

import com.jiuji.oa.afterservice.bigpro.bo.discount.DiscountInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.discount.UseDiscountBo;
import com.jiuji.oa.afterservice.bigpro.enums.discount.DiscountEnum;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.tc.common.vo.R;

/**
 * 售后会员折扣服务类
 * <AUTHOR>
 * @since 2021/12/22 16:14
 */
public interface ShouhouMemberDiscountService {
    /**
     * 通过会员id获取会员折扣
     * @param memberId
     * @return
     */
    DiscountEnum getMemberDiscount(Integer memberId);

    /**
     * 售后单是否存在使用折扣的情况
     * @param shouhouId
     * @return
     */
    boolean existDiscount(Integer shouhouId);

    /**
     * 是否存在使用折扣的情况
     * @param wxkcoutput
     * @return
     */
    boolean existDiscount(Wxkcoutput wxkcoutput);

    /**
     * 尝试使用折扣
     * @return
     * @param useDiscount
     */
    R<DiscountInfoBo> tryUseDiscount(UseDiscountBo useDiscount);

    /**
     * 撤销使用折扣
     * @param shouhouId
     * @param wxkcId
     */
    void cancelDiscount(Integer shouhouId,Integer wxkcId);
}
