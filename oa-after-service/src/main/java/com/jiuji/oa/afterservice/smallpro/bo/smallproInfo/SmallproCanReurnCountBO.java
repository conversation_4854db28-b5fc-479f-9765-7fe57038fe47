package com.jiuji.oa.afterservice.smallpro.bo.smallproInfo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * description: <Class introduction>
 * translation: <Method translation introduction>
 *
 * <AUTHOR>
 * @date 2020/1/19
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproCanReurnCountBO implements Serializable {

    private static final long serialVersionUID = -7160812173301152220L;
    private Integer ppid;

    private Integer count;

    private Integer basketid;
}
