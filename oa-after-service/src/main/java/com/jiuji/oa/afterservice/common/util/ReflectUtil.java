package com.jiuji.oa.afterservice.common.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.tc.utils.common.FieldModified;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;


/**
 * @Auther: qiweiqing
 * @Date: 2019/12/09/16:58
 * @Description:
 */
@Slf4j
public class ReflectUtil {

    public static void setProperty(Object obj, LocalDateTime updateTime, Integer updateBy) {
        //获取字节码对象
        try {
            Class clazz = obj.getClass();
            Field updateByFiled = clazz.getDeclaredField("updateBy");
            Field updateTimeFiled = clazz.getDeclaredField("updateTime");
            updateByFiled.setAccessible(true);
            updateTimeFiled.setAccessible(true);
            updateByFiled.set(obj, updateBy);
            updateTimeFiled.set(obj, updateTime);
        } catch (Exception e) {
            log.error("反射异常{}:", e.getMessage());
//            e.printStackTrace();
        }
    }

    /**
     * 获取对象的更新字段
     * 比较两个对象之间每个字段对应的值，如果值不相同，就是变更字段
     *
     * @param cls
     * @param beforeObj     更新前对象
     * @param afterObj      更新后对象
     * @param excludeFields 需要排除比较的字段列表
     * @return 变更的字段名称列表
     */
    public static List<String> getModifiedFields(Class<?> cls, Object beforeObj, Object afterObj,
                                                 Set<String> excludeFields) {
        Objects.requireNonNull(cls);
        if (beforeObj == null || afterObj == null) {
            return Collections.emptyList();
        }
        Field[] fields = cls.getDeclaredFields();
        List<String> modifiedField = new ArrayList<>();
        for (Field field : fields) {
            field.setAccessible(true);
            Object valBefore = null;
            Object valAfter = null;
            try {
                valBefore = field.get(beforeObj);
                valAfter = field.get(afterObj);
            } catch (IllegalAccessException e) {
                log.error("getModifiedFields failed", e);
            }
            if (!Objects.equals(valBefore, valAfter) && !excludeFields.contains(field.getName())) {
                String fieldName = field.getName();
                modifiedField.add(fieldName);
            }
        }
        return modifiedField;
    }

    /**
     * 获取对象单个字段上特定类型的注解
     *
     * @param clz           目标对象类型
     * @param fieldName     字段名称
     * @param annotationCls 注解类型
     * @param <T>           特定注解
     * @return
     */
    public static <T extends Annotation> T getFieldAnnotation(Class<?> clz, String fieldName, Class<T> annotationCls) {
        Objects.requireNonNull(clz);
        Objects.requireNonNull(fieldName);
        Objects.requireNonNull(annotationCls);
        Field field;
        try {
            field = clz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            log.error("getFieldAnnotation failed", e);
            return null;
        }
        field.setAccessible(true);
        T annotation = field.getDeclaredAnnotation(annotationCls);
        return annotation;
    }

    /**
     * 获取对象上指定字段名称的值
     *
     * @param clz       对象类型
     * @param fieldName 字段名称
     * @param targetObj 目标对象
     * @param <T>       字段的值
     * @return
     */
    public static <T> T getFieldValue(Class<?> clz, String fieldName, Object targetObj) {
        Objects.requireNonNull(clz);
        Field field;
        try {
            field = clz.getDeclaredField(fieldName);
            field.setAccessible(true);
        } catch (NoSuchFieldException e) {
            log.error("getFieldValue failed", e);
            return null;
        }
        Object val = null;
        try {
            val = field.get(targetObj);
        } catch (IllegalAccessException e) {
            log.error("getFieldValue failed", e);
        }
        if (val == null) {
            return null;
        }
        return (T)val;
    }

    /**
     * 字段修改日志记录
     * @param savedPO
     * @param updatedPO
     * @return
     */
    public static <T> List<String> getFieldModifiedLog(Class<T> clas, T savedPO,T updatedPO){
        // 获取变更的字段名称列表
        List<String> modifiedFields = ReflectUtil.getModifiedFields(clas, savedPO, updatedPO,
                new HashSet<>());
        if (CollectionUtils.isEmpty(modifiedFields)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        for (String fieldName : modifiedFields) {
            Object beforeValue = ReflectUtil.getFieldValue(clas, fieldName, savedPO);
            Object afterValue = ReflectUtil.getFieldValue(clas, fieldName, updatedPO);
            // 获取字段显示名称
            FieldModified fieldModified = ReflectUtil.getFieldAnnotation(clas, fieldName,
                    FieldModified.class);
            if (fieldModified == null) {
                continue;
            }
            // 获取枚举code值对应的message
            Class<? extends CodeMessageEnumInterface> enumCls = fieldModified.enumClass();
            if (enumCls != CodeMessageEnumInterface.class) {
                beforeValue = EnumUtil.getMessageByCode(enumCls, beforeValue);
                afterValue = EnumUtil.getMessageByCode(enumCls, afterValue);
                if (beforeValue == null && afterValue == null) {
                    log.error("获取枚举类型变更字段失败，字段名称: [{}]", fieldName);
                }
            }
            // 设置字段的显示名称
            String fieldDisplayName = fieldName;
            if (StringUtils.isNotBlank(fieldModified.displayName())) {
                fieldDisplayName = fieldModified.displayName();
            }
            // 记录字段变化日志
            if(beforeValue == null||StringUtils.isBlank(String.valueOf(beforeValue))){
                beforeValue = "空";
            }
            if(afterValue == null||StringUtils.isBlank(String.valueOf(afterValue))){
                afterValue = "空";
            }
            if (beforeValue.toString().equals(afterValue.toString())){
                continue;
            }
            String msg = fieldDisplayName+"由【"+beforeValue+"】修改为【"+afterValue+"】";
            result.add(msg);
        }
        return result;
    }

    public static void main(String[] args) {

    }

}
