package com.jiuji.oa.afterservice.smallpro.bo.smallproInfo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * description: <小件接件详情订单信息BO>
 * translation: <Smallpro Details Order Information BO>
 *
 * <AUTHOR>
 * @date 2019/11/28
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproInfoOrderBO implements Serializable {
    private static final long serialVersionUID = -7133595466517051021L;
    /**
     * 订单条目Id
     */
    private Integer basketId;

    /**
     * 商品加单时间
     */
    private LocalDateTime basketDate;
    /**
     * 订单条目价格
     */
    private BigDecimal price;
    /**
     * 商品原价
     */
    private BigDecimal memberPrice;
    /**
     * 订单条目所在地区
     */
    private Integer areaId;

    /**
     * 退款金额
     */
    private BigDecimal priceReturn;
}
