package com.jiuji.oa.afterservice.lossReview.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/22 10:08
 */
@Data
@Accessors(chain = true)
public class LossReviewAuditPageReq {
    @NotNull(message = "页数不能空")
    @ApiModelProperty(value = "当前页数", required = true)
    private Integer current = 1;

    @NotNull(message = "每页条目不能空")
    @ApiModelProperty(value = "每页条目", required = true)
    private Integer size = 10;

    private Integer exemptReviewFlag;

    /**
     * 门店id
     **/
    private List<Integer> areaIdList;

    /**
     * 审核状态 1, "待审核",2, "待一审",3, "待二审",4 "审核完成"
     **/
    private List<Integer> auditStatus;

    /**
     * 异常类型
     **/
    private List<Integer> errorType;

    /**
     *状态 0处理中，1已修好，3修不好
     */
    private List<Integer> baoxiu;

    /**
     * 九机服务方式
     */
    private List<Integer> serviceType;

    private Integer searchType;

    /**
     * 关键字
     **/
    private String searchValue;

    /**
     * 特殊质保
     */
    private List<Integer> specialQualityAssurance;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

}
