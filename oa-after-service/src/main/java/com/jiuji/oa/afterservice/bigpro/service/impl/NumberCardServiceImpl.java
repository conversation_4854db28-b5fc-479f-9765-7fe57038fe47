package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo;
import com.jiuji.oa.afterservice.bigpro.bo.RedeemContext;
import com.jiuji.oa.afterservice.bigpro.dao.NumberCardMapper;
import com.jiuji.oa.afterservice.bigpro.enums.CouponStatusEnum;
import com.jiuji.oa.afterservice.bigpro.enums.EUserSpecialTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.NumberCardLimitTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.YuYueSTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.NumberCardReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.CouponRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.NumberCardRes;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
@Slf4j
@Service
public class NumberCardServiceImpl extends ServiceImpl<NumberCardMapper, NumberCard> implements NumberCardService {

    @Autowired
    private RedissonClient redisson;

    @Lazy
    @Autowired
    private CardLogsService cardLogsService;

    @Autowired
    private YuyueLogsService yuyueLogsService;
    @Autowired
    private MemberClient memberClient;
    @Autowired
    private ShouhouConstants shouhouConstants;
    @Resource
    private ShouhouQudaoService shouhouQudaoService;
    @Resource
    private WxkcoutputService wxkcoutputService;


    @Override
    public Boolean cancelNumerCard(Integer id) {
        UpdateWrapper<NumberCard> updateWrapper = new UpdateWrapper();
        updateWrapper.lambda().set(NumberCard::getUseCount, 0).set(NumberCard::getState, 0).eq(NumberCard::getId, id);
        return this.update(updateWrapper);
    }

    @Override
    @Transactional
    public R<String> useYuyueCoupon(String coupon, Integer yyid, String inuser, Integer stype, Integer areaId) {
        if (stype == null || stype == 0) {
            stype = 4;
        }
        RLock fairLock = redisson.getFairLock("javaoa:useYuyueCoupon:" + coupon);
        try {
            boolean flag = fairLock.tryLock(5, 10, TimeUnit.SECONDS);
            if (flag) {
                //判断优惠码是否使用过
                List<NumberCard> list = this.getUnUsedCoupon(coupon);
                if (CollectionUtils.isEmpty(list)) {
                    return R.error("优惠码不存在");
                }
                NumberCard numberCard = list.get(0);
                List<String> limitIdsList = new LinkedList<>();
                if (StringUtils.isNotEmpty(numberCard.getLimitids())) {
                    limitIdsList = Arrays.stream(numberCard.getLimitids().split(",")).collect(Collectors.toList());
                }
                Boolean b = numberCard.getLimitType() == 3 && CollectionUtils.isNotEmpty(limitIdsList) && (limitIdsList.contains("30108") || limitIdsList.contains("39308"));
                if (YuYueSTypeEnum.SMKX.getCode().equals(stype) && !b) {
                    return R.error("优惠码类型不正确");
                }
                //判断该维修单是否使用过优惠码
                Boolean checkCouponFlag = CollectionUtils.isEmpty(limitIdsList) || (!limitIdsList.contains("23") || 1 != numberCard.getLimitType() || -25 != numberCard.getCh999Id());
                if (checkCouponFlag) {
                    return R.error("优惠码类型不正确");
                }
                if (!DateUtil.checkCurrentTimeInArea(numberCard.getStartTime(), numberCard.getEndTime())) {
                    return R.error("该优惠码未在有效期内");
                }
                // limit 2 不限制使用次数
                if (numberCard.getState() && ObjectUtil.notEqual(numberCard.getLimit(),2)) {
                    return R.error("该优惠码已使用！");
                }
                //使用优惠码
                this.useCoupon(numberCard, yyid, areaId, inuser);
                //预约单增加日志
                yuyueLogsService.yuyueLogsAdd(yyid, "使用优惠码", inuser, 0);
                return R.success("", "优惠码使用成功！");
            }
        } catch (Exception e) {
            log.error("优惠码使用异常：{}", e.getMessage(),e);
        } finally {
            try {
                if (fairLock.isHeldByCurrentThread()) {
                    //解锁
                    fairLock.unlock();
                }
            } catch (RedisException e) {
                log.error("useYuyueCoupon->fairLock.unlock error", e);
            }
        }
        return R.error("优惠码使用失败");
    }

    @Override
    public Boolean checkCouponIsUsed(String coupon) {
        QueryWrapper<NumberCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NumberCard::getIsdel, 0).eq(NumberCard::getCardID, coupon);
        Integer flag = baseMapper.selectCount(queryWrapper);
        if (flag > 0) {
            return false;
        }
        return true;
    }

    @Override
    public List<NumberCard> getUnUsedCoupon(String coupon) {
        QueryWrapper<NumberCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NumberCard::getIsdel, 0).eq(NumberCard::getCardID, coupon);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 使用优惠码
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    @Override
    public void useCoupon(NumberCard numberCard, Integer subId, Integer areaId, String useName) {
        try {
            numberCard.setUseCount(numberCard.getUseCount() + 1);
            numberCard.setState(true);
            baseMapper.updateById(numberCard);
            //写优惠码使用日志
            CardLogs cardLogs = new CardLogs();
            cardLogs.setCardid(numberCard.getId());
            cardLogs.setSubId(subId);
            cardLogs.setPushTime(LocalDateTime.now());
            cardLogs.setAreaid(areaId);
            cardLogs.setUseren(useName);
            cardLogs.setUserid(numberCard.getUserid());
            cardLogs.setUseType(1);
            cardLogs.setPrices(numberCard.getTotal());
            cardLogs.setLiangpin(4);
            cardLogsService.save(cardLogs);
        } catch (Exception e) {
            log.error("优惠码使用异常：{}", e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Override
    public List<CouponRes> getUserBindCoupon(Integer userId) {
        List<CouponRes> list = baseMapper.getUserBindCoupon(userId);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        for (CouponRes couponRes : list) {
            Integer status = 0;
            String startTimeStr = couponRes.getStartTime().format(DateUtil.format_day) + " 00:00:00";
            String endTimeStr = couponRes.getEndTime().format(DateUtil.format_day) + " 23:59:59";
            if (DateUtil.checkCurrentTimeInArea(couponRes.getStartTime(), couponRes.getEndTime())) {
                status = CouponStatusEnum.ZC.getCode();
            } else if (DateUtil.stringToLocalDateTime(startTimeStr).isAfter(LocalDateTime.now())) {
                status = CouponStatusEnum.WKS.getCode();
            } else if (DateUtil.stringToLocalDateTime(endTimeStr).isBefore(LocalDateTime.now())) {
                status = CouponStatusEnum.YGQ.getCode();
            }
            couponRes.setStatus(status);
            couponRes.setStatusName(EnumUtil.getMessageByCode(CouponStatusEnum.class, status));
            if (couponRes.getLimit2()) {
                couponRes.setLimit1Name(couponRes.getLimit1() + "可累加");
            } else {
                couponRes.setLimit1Name(couponRes.getLimit1() + "不可累加");
            }
        }
        return list;
    }

    @Transactional
    @Override
    public R<List<Integer>> addNumberCard(List<NumberCardReq> numberCards) {
        if (CollectionUtils.isEmpty(numberCards)) {
            return R.error("优惠码信息不能为空");
        }

        List<Integer> cardList = new LinkedList<>();
        try {
            for (NumberCardReq numberCard : numberCards) {
                R<MemberBasicRes> memberBasicResR = memberClient.getMemberBasicInfo(numberCard.getUserid());
                if (ResultCode.SUCCESS != memberBasicResR.getCode() || memberBasicResR.getData() == null) {
                    return R.error("获取用户信息失败");
                }
                MemberBasicRes memberInfo = memberBasicResR.getData();
                if (EUserSpecialTypeEnum.HUANGNIU.getCode().equals(memberInfo.getSpecialType())) {
                    return R.error("黄牛用户，生成优惠码失败！");
                }
                Boolean flag = numberCard.getCount() == null || numberCard.getCount() == 0 || StringUtils.isEmpty(numberCard.getGName())
                        || numberCard.getStartTime() == null || numberCard.getEndTime() == null || numberCard.getLimit1() == null || numberCard.getLimit1() == 0;
                if (flag) {
                    return R.error("参数不足");
                }
                if (numberCard.getLimit2() == null) {
                    numberCard.setLimit2(false);
                }
                if (numberCard.getLimitprice() == null) {
                    numberCard.setLimitprice(BigDecimal.ZERO);
                }

                if (numberCard.getLimitType() == null) {
                    numberCard.setLimitids("");
                }

                if (StringUtils.isNotEmpty(numberCard.getLimitids())) {
                    if (numberCard.getLimitids().contains("，")) {
                        return R.error("限制类别中包含中文逗号，请使用英文逗号分隔！");
                    }
                    if (numberCard.getLimitType() == null) {
                        return R.error("请选择限制类别");
                    }

                    String limitIdArr[] = numberCard.getLimitids().split(",");
                    List<Integer> oldIds = Arrays.stream(limitIdArr).map(e -> Integer.parseInt(e)).collect(Collectors.toList());
                    List<Integer> newIds = baseMapper.getOldLimitIds(numberCard.getLimitType(), oldIds);
                    List<Integer> other = oldIds.stream().filter(e -> !newIds.contains(e)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(other)) {
                        return R.error(EnumUtil.getMessageByCode(NumberCardLimitTypeEnum.class, numberCard.getLimitType()) + "对应ID无效");
                    }
                }
                if (StringUtils.isNotEmpty(numberCard.getAreas()) && numberCard.getAreas().contains("，")) {
                    return R.error("地区中包含中文逗号，请使用英文逗号分隔！");
                }
                numberCard.setState(false);
                numberCard.setIsdel(false);
                numberCard.setAddTime(LocalDate.now());
                numberCard.setBinduid(numberCard.getBinduid() == null ? false : numberCard.getBinduid());
                numberCard.setIsNoProfit(numberCard.getIsNoProfit() == null ? false : numberCard.getIsNoProfit());
                if (numberCard.getAuthorizeid() != null) {
                    numberCard.setAuthorizeid(numberCard.getAuthorizeid() == 0 ? 1 : numberCard.getAuthorizeid());
                }
                baseMapper.insert(numberCard);
                cardList.add(numberCard.getId());
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("添加优惠码异常：{}", e.getMessage());
            return R.error("添加优惠码异常：" + e.getMessage());
        }
        return R.success("添加优惠码成功", cardList);
    }

    @Override
    public Integer updateNumberCardUseCount(String cardId) {
        return baseMapper.updateNumberCardUseCount(cardId);
    }

    @Override
    public R<List<NumberCardRes>> getKxYouhuimas(Long userId, Boolean isSoft,Integer shouhouId) {
        //自修且都是手工费
        boolean isZiXiuManualByAll = false;
        boolean isJiujiXtenant = false;
        //获取当前维修单所有配件信息
        List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouhouId);
        if (XtenantEnum.isJiujiXtenant()){
            isJiujiXtenant = true;
            boolean isManualByAll = false;
            //是否存在渠道
            Integer shQuDaoCount = shouhouQudaoService.count(new LambdaQueryWrapper<ShouhouQudao>().eq(ShouhouQudao::getShouhouid, shouhouId));
            //是否外送 true是外送售后，false是自修
            boolean isWaiSong = Boolean.TRUE.equals(CommenUtil.isNotNullZero(shQuDaoCount));

            if (CollUtil.isNotEmpty(hexiaoBoList)){
                //为true的话 都是手工费
                isManualByAll = hexiaoBoList.stream().allMatch(hexiaoBo -> CommenUtil.isNullOrZero(hexiaoBo.getPpid()));
            }
            //判断 自修且都是手工费的话
            if (Boolean.FALSE.equals(isWaiSong) && isManualByAll){
                isZiXiuManualByAll = true;
            }
            //外修 且全部都是手工费
            if (isWaiSong && isManualByAll){
                return R.success("查无相关记录");
            }
        }
        List<NumberCardRes> numberCardList = baseMapper.getKxYouhuimas(userId, isSoft,shouhouId,isZiXiuManualByAll,isJiujiXtenant);
        //如果存积分兑换券 需要进行临时表判断
        if(XtenantEnum.isJiujiXtenant() && CollUtil.isNotEmpty(RedeemContext.getTempNumberCardList())){
            Map<String, Object> params =new HashMap<>();
            params.put("userId",userId);
            params.put("isSoft",isSoft);
            params.put("shouhouId",shouhouId);
            params.put("isZiXiuManualByAll",isZiXiuManualByAll);
            params.put("isJiujiXtenant",isJiujiXtenant);
            List<TempNumberCard> tempCards = RedeemContext.getTempNumberCardList();
            params.put("tempCards",tempCards);
            RedeemContext.clear();
            List<NumberCardRes> shouhouCardData = baseMapper.getShouhouCardData(params);
            numberCardList.addAll(shouhouCardData);
        }
        if (CollectionUtils.isEmpty(numberCardList)) {
            return R.success("查无相关记录");
        }
        LocalDateTime weixiuCodeUpdateTime = shouhouConstants.getWeixiuCodeUpdateTime();
        List<NumberCardRes> resList = new LinkedList<>();
        ShouhouExService shouhouExService = SpringUtil.getBean(ShouhouExService.class);
        List<Wxkcoutput> idPpidList = wxkcoutputService.lambdaQuery().select(Wxkcoutput::getId, Wxkcoutput::getPpriceid)
                .in(Wxkcoutput::getId, hexiaoBoList.stream().map(HexiaoBo::getId).collect(Collectors.toSet()))
                .isNotNull(Wxkcoutput::getPpriceid).list();
        for (NumberCardRes m : numberCardList) {

            List<Integer> limitIds = Collections.emptyList();
            if (StringUtils.isNotEmpty(m.getLimitids())) {
                limitIds = Arrays.asList(m.getLimitids().split(",")).stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList());
            }

            LocalDate codeAddTime = m.getAddTime();
            NumberCardRes resInfo = new NumberCardRes();
            BeanUtils.copyProperties(m, resInfo);

            if (codeAddTime.isBefore(LocalDate.parse(DateUtil.localDateTime2LocalDate(weixiuCodeUpdateTime), DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {
                if (!limitIds.contains(23)) {
                    continue;
                }
            }
            Set<Integer> wxkcIds = hexiaoBoList.stream().map(HexiaoBo::getId).collect(Collectors.toSet());
            boolean isExcludePppid = shouhouExService.removeExcludePpidWxkcId(m.getExcludePpIds(), wxkcIds,idPpidList);
            if(isExcludePppid && wxkcIds.isEmpty()){
                //优惠码被排除的ppid排除了
                continue;
            }
            if (m.getStartTime().isBefore(LocalDate.now()) && m.getEndTime().isAfter(LocalDate.now())) {
                resInfo.setStats(1);
                resInfo.setMsg("正常");
            } else if (m.getStartTime().isAfter(LocalDate.now())) {
                resInfo.setStats(0);
                resInfo.setMsg("还未开始");
            } else if (m.getEndTime().isBefore(LocalDate.now())) {
                resInfo.setStats(0);
                resInfo.setMsg("已过期");
            }
            if(StrUtil.isNotEmpty(m.getCardID())){
                resInfo.setCardIDShow(m.getCardID().substring(0, 2) + "**" + m.getCardID().substring(m.getCardID().length() - 2, m.getCardID().length()));
            }


            resInfo.setLimit2(m.getLimit2() == null ? false : m.getLimit2());
            if (resInfo.getLimit2()) {
                resInfo.setLimit1name(getlimit1name(m.getLimit1().toString()) + " 可累加 ");
            } else {
                resInfo.setLimit1name(getlimit1name(m.getLimit1().toString()) + " 不可累加 ");
            }

            resList.add(resInfo);
        }

        return R.success(resList);
    }

    @Override
    public List<NumberCard> getNumberCardByCardId(String cardId) {
        return baseMapper.getNumberCardByCardId(cardId);
    }

    @Override
    public List<Integer> validateYouHuiMaSatisfyUseCondition(List<Integer> limitIds, Integer shouhouId) {
        return baseMapper.validateYouHuiMaSatisfyUseCondition(limitIds, shouhouId);
    }

    @Override
    public List<String> getYxNumberCardByCardIdList(List<String> cardIds) {
        return CommonUtils.bigDataInQuery(cardIds, baseMapper::getYxNumberCardByCardIdList);
    }

    /**
     * 优惠码，获取限制1的名称
     *
     * @param limit1
     * @return
     */
    private String getlimit1name(String limit1) {
        String limit1name = ""; //默认是空字符串
        switch (limit1) {
            case "1":
                limit1name = "仅大件";
                break;
            case "2":
                limit1name = "仅小件";
                break;
            case "3":
                limit1name = "不限制";
                break;
            case "4":
                limit1name = "大件+小件";
                break;
            default:
                limit1name = "";
        }
        return limit1name;
    }
}
