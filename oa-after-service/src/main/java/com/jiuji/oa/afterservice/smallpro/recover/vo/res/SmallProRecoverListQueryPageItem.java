package com.jiuji.oa.afterservice.smallpro.recover.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class SmallProRecoverListQueryPageItem {

    @ApiModelProperty(value = "回收旧件编号")
    private Integer id;

    @ApiModelProperty(value = "回收时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inDate;

    @ApiModelProperty(value = "分区")
    private String smallArea;

    private Integer areaId;
    private Integer toAreaId;
    private String area;
    private String toArea;

    @ApiModelProperty(value = "地区（展示）")
    private String areaText;

    @ApiModelProperty(value = "售后单号")
    private Integer shouhouId;

    @ApiModelProperty(value = "PPID")
    private Integer ppid;

    @ApiModelProperty(value = "旧件名称")
    private String name;

    @ApiModelProperty(value = "旧件类别",hidden = true)
    private Integer kind;

    @ApiModelProperty(value = "旧件类别")
    private String kindText;

    private Integer channelCode;

    @ApiModelProperty(value = "渠道类别")
    private String channelType;

    @ApiModelProperty(value = "附件")
    private String topFid;

    @ApiModelProperty(value = "返还/回收")
    private String returnOrRecover;

    private Boolean isFan = Boolean.FALSE;

    private Boolean isHuan = Boolean.FALSE;

    @ApiModelProperty(value = "保修状态")
    private Integer baoXiu;

    @ApiModelProperty(value = "保修状态")
    private String baoXiuText;

    @ApiModelProperty(value = "旧件价格")
    private BigDecimal price;

    @ApiModelProperty(value = "换回旧件价值")
    private BigDecimal huanHuiPrice;

    @ApiModelProperty(value = "登记人")
    private String inUser;

    @ApiModelProperty(value = "回收公司")
    private String companyName;

    @ApiModelProperty(value = "物流单号")
    private Integer wuLiuId;

    @ApiModelProperty(value = "标签")
    private String pLabel;

    @ApiModelProperty(value = "配件类别")
    private String pjKinds;

    @ApiModelProperty(value = "问题描述")
    private String problem;

    @ApiModelProperty(value = "出售价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "出售时间")
    private LocalDateTime saleDate;

    private LocalDateTime sDTime;

    @ApiModelProperty(value = "经手人")
    private String saleUser;

    @ApiModelProperty(value = "备注")
    private String remark;

    private String huanHuoUser;

    private LocalDateTime huanHuoTime;

    private Integer yaPing;

    private BigDecimal inPrice = BigDecimal.ZERO;

    private Boolean isSale;

    private Boolean fanCheck;

    private String checkUser;

    private LocalDateTime checkDate;

    private Boolean isTaArea;

    private Integer returnBasketId;

    private Boolean isSaleCheck;

    private Integer bindToolCount;

    private BigDecimal bindToolPrice;

    private Long baoFeiPq;

    private Long baoFeiPz;

    private Long salePiQianId;

    private Long salePzId;

    private String cidFamily;

}
