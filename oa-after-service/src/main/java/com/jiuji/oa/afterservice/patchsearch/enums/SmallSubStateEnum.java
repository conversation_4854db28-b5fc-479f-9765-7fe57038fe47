package com.jiuji.oa.afterservice.patchsearch.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Auther: qiweiqing
 * @Date: 2020/03/05
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum SmallSubStateEnum implements CodeMessageEnumInterface {

    SMALL_SUB_STATE_0(0, "处理中"),
    SMALL_SUB_STATE_1(1, "处理完成"),
    SMALL_SUB_STATE_2(2, "已删除"),
    SMALL_SUB_STATE_3(3, "送修中"),
    SMALL_SUB_STATE_4(4, "已返厂"),
    SMALL_SUB_STATE_5(5, "已修好");


    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;


}
