package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.DateTimeUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.cloud.extend.param.WeChatTemplateCloudParam;
import com.jiuji.cloud.extend.service.WebExtendsWeixinCloud;
import com.jiuji.cloud.extend.vo.WeChatSendCloudVO;
import com.jiuji.cloud.extend.vo.WxTemplateAndKeyCloudVO;
import com.jiuji.oa.afterservice.apollo.ShouhouRiskNotificationConfig;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogNoticeBo;
import com.jiuji.oa.afterservice.bigpro.bo.SmsTokenBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRiskNotificationMapper;
import com.jiuji.oa.afterservice.bigpro.enums.ShouhouRiskNotificationStatusEnum;
import com.jiuji.oa.afterservice.bigpro.enums.ShouhouRiskNotificationTypeEnum;
import com.jiuji.oa.afterservice.bigpro.machinehero.config.MqttAfterConfig;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouRiskNotification;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouLogsService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRiskNotificationService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.ShouHouRiskNotificationVO;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouReceiveReq;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.web.DocumentVO;
import com.jiuji.oa.afterservice.cloud.vo.web.ElectronicTypeDocumentVO;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.electronicTicket.vo.AfterSalesPropellingMovement;
import com.jiuji.oa.afterservice.electronicTicket.vo.MsgBaseInfo;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.shouhou.vo.req.ShouhouRiskNotificationReq;
import com.jiuji.oa.afterservice.shouhou.vo.req.SignShouhouRiskNotificationReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationDataRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationInfoRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationUrlRes;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproSendReviewUserWechatInfoBO;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class ShouhouRiskNotificationServiceImpl extends ServiceImpl<ShouhouRiskNotificationMapper, ShouhouRiskNotification> implements ShouhouRiskNotificationService {
    private static final String SMS_TOKEN_KEY = "sms:tokeninfo:{0}";
    @Resource
    private AreainfoService areainfoService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SmallproMapper smallproMapper;
    @Resource
    private WebExtendsWeixinCloud webExtendsWeixinCloud;
    @Resource
    private WebCloud webCloud;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ShouhouRiskNotificationConfig shouhouRiskNotificationConfig;
    @Resource
    private SmsService smsService;
    @Resource
    private ShouhouLogsService logsService;
    @Resource
    private MqttAfterConfig mqttAfterConfig;


    private static final String RISKNOTIFICATION_TOPIC = "RiskNotification/shouHouId/%s";
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delRiskNotification(Long id) {
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录人失效"));
        if(ObjectUtil.isNull(id)){
            throw new CustomizeException("删除id不能为空");
        }
        ShouhouRiskNotification riskNotification = Optional.ofNullable(this.getById(id)).orElseThrow(() -> new CustomizeException("查询风险确认书为空"));
        if(!ShouhouRiskNotificationStatusEnum.NOT_AGREE.getCode().equals(riskNotification.getStatus())){
            throw new CustomizeException("只有"+ShouhouRiskNotificationStatusEnum.NOT_AGREE.getMessage()+"才能进行删除");
        }
        boolean removeById = this.removeById(id);
        if(!removeById){
            throw new CustomizeException("删除失败");
        }
        //维修单日志记录
        ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
        shouhouLogNoticeBo.setNeedNotice(false);
        String notificationTypeValue = ShouhouRiskNotificationTypeEnum.getEnumByCode(riskNotification.getNotificationType()).getMessage();
        logsService.addShouhouLog(userBO.getUserName(), riskNotification.getShouhouId(), ShouHouLogTypeEnum.CLXX.getCode(),
                "删除"+notificationTypeValue, shouhouLogNoticeBo, false, null);
    }

    /**
     * 获取风险确认书列表
     * @param shouHouId
     * @return
     */
    @Override
    public List<ShouHouRiskNotificationVO> selectRiskNotificationList(Integer shouHouId) {
        List<ShouHouRiskNotificationVO> list = new ArrayList<>();
        try {
            List<ShouhouRiskNotification> notifications = this.lambdaQuery().eq(ShouhouRiskNotification::getShouhouId, shouHouId)
                    .orderByDesc(ShouhouRiskNotification::getCreateTime)
                    .list();
            if(CollectionUtils.isEmpty(notifications)){
                return list;
            }
            //信息封装
            list = notifications.stream().map(item->{
                ShouHouRiskNotificationVO shouHouRiskNotificationVO = new ShouHouRiskNotificationVO();
                BeanUtil.copyProperties(item, shouHouRiskNotificationVO);
                shouHouRiskNotificationVO.setNotificationTypeValue(ShouhouRiskNotificationTypeEnum.getEnumByCode(item.getNotificationType()).getMessage());
                shouHouRiskNotificationVO.setStatusValue(ShouhouRiskNotificationStatusEnum.getEnumByCode(item.getStatus()).getMessage());
                //签署内容封装
                ShouhouRiskNotificationReq req = new ShouhouRiskNotificationReq();
                req.setShouhouId(item.getShouhouId());
                req.setRiskNotificationType(item.getNotificationType());
                req.setShouhouRiskNotificationId(Optional.ofNullable(item.getId()).orElse(0L).intValue());
                String riskNotificationUrl = getRiskNotificationUrl(req, 3);
                shouHouRiskNotificationVO.setRiskNotificationUrl(riskNotificationUrl);
                Optional.ofNullable(item.getSignatureTime()).ifPresent(obj->{
                    shouHouRiskNotificationVO.setSignatureTimeStr(obj.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                });
                return shouHouRiskNotificationVO;
            }).collect(Collectors.toList());
        }catch (Exception e){
            RRExceptionHandler.logError("获取风险确认书列表异常", shouHouId, e, smsService::sendOaMsgTo9JiMan);
        }
        return list;
    }

    /**
     * 推送风险确认书
     *
     * @return
     */
    @Override
    public void pushRiskNotification(ShouhouReceiveReq shouhou) {
        try {
            if (Boolean.FALSE.equals(shouhouRiskNotificationConfig.getSwitchRiskNotification())) {
                return;
            }
            if (!XtenantEnum.isJiujiXtenant()) {
                return;
            }
            List<Integer> riskNotificationTypeList = Optional.ofNullable(shouhou.getRiskNotificationTypeList()).orElse(new ArrayList<>());
            Optional.ofNullable(shouhou.getRiskNotificationType()).ifPresent(riskNotificationTypeList::add);
            if(CollectionUtils.isNotEmpty(riskNotificationTypeList)){
                //数据去重
                riskNotificationTypeList = riskNotificationTypeList.stream().distinct().collect(Collectors.toList());
                for (Integer riskNotificationType : riskNotificationTypeList){
                    ShouhouRiskNotificationReq req = new ShouhouRiskNotificationReq();
                    req.setShouhouId(shouhou.getId());
                    req.setRiskNotificationType(riskNotificationType);
                    pushRiskNotification(req);
                }
            }

        } catch (Exception e) {
            RRExceptionHandler.logError("推送售后风险确认书异常", shouhou, e, smsService::sendOaMsgTo9JiMan);
        }
    }

    /**
     * 推送风险确认书
     *
     * @return
     */
    @Override
    public R<String> pushRiskNotification(ShouhouRiskNotificationReq req) {
        if (Boolean.FALSE.equals(shouhouRiskNotificationConfig.getSwitchRiskNotification())) {
            return R.success("操作成功");
        }
        OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();
        ShouhouRiskNotificationTypeEnum enumByCode = ShouhouRiskNotificationTypeEnum.getEnumByCode(req.getRiskNotificationType());
        if (enumByCode == null) {
            throw new CustomizeException("风险告知书类型不存在");
        }
        if (Arrays.asList(ShouhouRiskNotificationTypeEnum.NOT_SEND.getCode(), ShouhouRiskNotificationTypeEnum.CANNOT_SIGNED.getCode()).contains(Optional.ofNullable(req.getRiskNotificationType()).orElse(0))) {
            return R.success("操作成功");
        }
        Shouhou shouhou = getShouhou(req.getShouhouId());
        Integer userId = shouhou.getUserid().intValue();
        String openId = getOpenIdByUserId(userId);
        ShouhouRiskNotification shouhouRiskNotification = this.baseMapper.getShouhouRiskNotificationByShouhouIdAndType(shouhou.getId(), req.getRiskNotificationType());
        if (shouhouRiskNotification == null) {
            DocumentVO document = getDocumentByType(req.getRiskNotificationType());
            shouhouRiskNotification = LambdaBuild.create(ShouhouRiskNotification.class)
                    .set(ShouhouRiskNotification::setShouhouId, shouhou.getId())
                    .set(ShouhouRiskNotification::setNotificationType, req.getRiskNotificationType())
                    .set(ShouhouRiskNotification::setDocumentId, document.getId())
                    .set(ShouhouRiskNotification::setContent, document.getContent())
                    .set(ShouhouRiskNotification::setSourceType, 1)
                    .set(ShouhouRiskNotification::setInuser, currentStaffId.getUserName())
                    .build();
            this.save(shouhouRiskNotification);
        }
        req.setShouhouRiskNotificationId(shouhouRiskNotification.getId().intValue());
        req.setUserId(userId);
        R<String> stringR= new R<>();
        try {
            if(StringUtils.isNotEmpty(openId)){
                String riskNotificationUrl = getRiskNotificationUrl(req, 1);
                AfterSalesPropellingMovement afterSalesPropellingMovement = new AfterSalesPropellingMovement()
                        .setOpenId(openId)
                        .setUrl(riskNotificationUrl)
                        .setFirst(LambdaBuild.create(MsgBaseInfo.class).set(MsgBaseInfo::setValue, shouhouRiskNotificationConfig.getRiskNotificationFirst()).build())
                        .setHandleType(LambdaBuild.create(MsgBaseInfo.class).set(MsgBaseInfo::setValue, shouhouRiskNotificationConfig.getRiskNotificationHandleType()).build())
                        .setStatus(LambdaBuild.create(MsgBaseInfo.class).set(MsgBaseInfo::setValue, shouhouRiskNotificationConfig.getRiskNotificationHandleStatus()).build())
                        .setRowCreateDate(LambdaBuild.create(MsgBaseInfo.class).set(MsgBaseInfo::setValue, LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateTimeUtils.DATETIME_FORMAT))).build())
                        .setLogType(LambdaBuild.create(MsgBaseInfo.class).set(MsgBaseInfo::setValue, shouhouRiskNotificationConfig.getRiskNotificationLogType()).build());
                stringR = pushMsgByWebService(afterSalesPropellingMovement);
            }
        } catch (Exception e) {
            stringR = R.error("推送告知书异常：" + e.getMessage());
        }
        if (stringR.getCode() == 0) {
            String riskNotificationUrlNoToken = getRiskNotificationUrl(req, 3);
            String logMsg = String.format(shouhouRiskNotificationConfig.getRiskNotificationLogMsg(), riskNotificationUrlNoToken, enumByCode.getNotificationName());
            SpringUtil.getApplicationContext().getBean(ShouhouService.class).saveShouhouLog(shouhou.getId(), logMsg, currentStaffId.getUserName(), 0, true);
        }
//       else {
//            //删除告知书推送记录
//            this.lambdaUpdate()
//                    .set(ShouhouRiskNotification::getDelFlag, true)
//                    .eq(ShouhouRiskNotification::getId, shouhouRiskNotification.getId())
//                    .eq(ShouhouRiskNotification::getShouhouId, shouhou.getId())
//                    .update();
//        }
        return stringR;
    }

    @Override
    public ShouhouRiskNotificationInfoRes getShouhouRiskNotificationInfo(ShouhouRiskNotificationReq req) {
        Shouhou shouhou = getShouhou(req.getShouhouId());
        Integer areaId = shouhou.getToareaid() == null ? shouhou.getAreaid() : shouhou.getToareaid();
        String printName = Optional.ofNullable(areainfoService.getById(areaId)).map(Areainfo::getPrintName).orElse("");
        ShouhouRiskNotificationInfoRes shouhouRiskNotificationRes = this.baseMapper.getShouhouRiskNotificationInfo(req);
        if (Objects.nonNull(shouhouRiskNotificationRes)) {
            if (StringUtils.isBlank(shouhouRiskNotificationRes.getImagePath())) {
                shouhouRiskNotificationRes.setImagePath(shouhouRiskNotificationConfig.getRiskNotificationProductImagePath());
            } else {
                shouhouRiskNotificationRes.setImagePath(getProductImgUrl(shouhouRiskNotificationRes.getImagePath()));
            }
            shouhouRiskNotificationRes.setShortTitle(printName);
            /**
             * 判断该条记录是否为已经删除的状态
             */
            if(NumberConstant.ONE.equals(shouhouRiskNotificationRes.getIsDelete())){
                shouhouRiskNotificationRes.setStatus(-NumberConstant.ONE);
            }
        }
        return shouhouRiskNotificationRes;
    }

    /**
     * 获取风险确认书签署url
     *
     * @param req
     * @return
     */
    @Override
    public ShouhouRiskNotificationUrlRes getRiskNotificationSignUrl(ShouhouRiskNotificationReq req) {
        ShouhouRiskNotificationTypeEnum enumByCode = ShouhouRiskNotificationTypeEnum.getEnumByCode(req.getRiskNotificationType());
        if (enumByCode == null) {
            throw new CustomizeException("风险告知书类型不存在");
        }
        Shouhou shouhou = getShouhou(req.getShouhouId());
        req.setUserId(shouhou.getUserid().intValue());
        ShouhouRiskNotification shouhouRiskNotification = this.baseMapper.getShouhouRiskNotificationByShouhouIdAndType(req.getShouhouId(), req.getRiskNotificationType());
        if (shouhouRiskNotification == null) {
            DocumentVO document = getDocumentByType(req.getRiskNotificationType());
            shouhouRiskNotification = LambdaBuild.create(ShouhouRiskNotification.class)
                    .set(ShouhouRiskNotification::setShouhouId, req.getShouhouId())
                    .set(ShouhouRiskNotification::setNotificationType, req.getRiskNotificationType())
                    .set(ShouhouRiskNotification::setDocumentId, document.getId())
                    .set(ShouhouRiskNotification::setContent, document.getContent())
                    .set(ShouhouRiskNotification::setSourceType, NumberConstant.TWO)
                    .set(ShouhouRiskNotification::setInuser, currentRequestComponent.getCurrentStaffId().getUserName())
                    .build();
            this.save(shouhouRiskNotification);
        }

        req.setShouhouRiskNotificationId(shouhouRiskNotification.getId().intValue());
        String riskNotificationUrl = getRiskNotificationUrl(req, 2);
        ShouhouRiskNotificationUrlRes res = new ShouhouRiskNotificationUrlRes();
        res.setRiskNotificationUrl(riskNotificationUrl);
        return res;
    }

    @Override
    public ShouhouRiskNotificationDataRes getRiskNotificationList(Integer shouhouId) {
        Shouhou shouhou = getShouhou(shouhouId);
        List<ShouhouRiskNotificationTypeEnum> enumList = ShouhouRiskNotificationTypeEnum.getEnumByCodes(shouhouRiskNotificationConfig.getRiskNotificationTypes());
        List<ShouhouRiskNotificationDataRes.ShouhouRiskNotificationData> shouhouRiskNotificationDataList = enumList.stream().map(v -> LambdaBuild.create(ShouhouRiskNotificationDataRes.ShouhouRiskNotificationData.class)
                .set(ShouhouRiskNotificationDataRes.ShouhouRiskNotificationData::setShouhouId, shouhou.getId())
                .set(ShouhouRiskNotificationDataRes.ShouhouRiskNotificationData::setRiskNotificationType, v.getCode())
                .set(ShouhouRiskNotificationDataRes.ShouhouRiskNotificationData::setRiskNotificationName, v.getNotificationName())
                .build()).collect(Collectors.toList());

        for (ShouhouRiskNotificationDataRes.ShouhouRiskNotificationData shouhouRiskNotificationData : shouhouRiskNotificationDataList) {
            ShouhouRiskNotification shouhouRiskNotification = this.lambdaQuery()
                    .eq(ShouhouRiskNotification::getNotificationType, shouhouRiskNotificationData.getRiskNotificationType())
                    .eq(ShouhouRiskNotification::getShouhouId, shouhouRiskNotificationData.getShouhouId())
                    .one();
            if (Objects.nonNull(shouhouRiskNotification )) {
                shouhouRiskNotificationData.setRiskNotificationId(shouhouRiskNotification.getId());
                shouhouRiskNotificationData.setStatus(shouhouRiskNotification.getStatus());
            }
        }
        ShouhouRiskNotificationDataRes res = new ShouhouRiskNotificationDataRes();
        res.setRiskNotificationDataList(shouhouRiskNotificationDataList);
        return res;
    }

    @Override
    public boolean signShouhouRiskNotification(SignShouhouRiskNotificationReq req) {
        ShouhouRiskNotification shouhouRiskNotification = this.lambdaQuery()
                .eq(ShouhouRiskNotification::getId, req.getShouhouRiskNotificationId())
                .eq(ShouhouRiskNotification::getShouhouId, req.getShouhouId())
                .one();
        if (shouhouRiskNotification == null) {
            throw new CustomizeException("风险告知书不存在");
        }
        if (Objects.equals(1, shouhouRiskNotification.getStatus())) {
            throw new CustomizeException("已经签署或同意告知书");
        }
        boolean flag = this.lambdaUpdate()
                .set(ShouhouRiskNotification::getSignature, req.getSignature())
                .set(ShouhouRiskNotification::getStatus, 1)
                .set(ShouhouRiskNotification::getSignatureImage, req.getSignatureImage())
                .set(ShouhouRiskNotification::getSignatureTime, LocalDateTime.now())
                .eq(ShouhouRiskNotification::getId, req.getShouhouRiskNotificationId())
                .in(ShouhouRiskNotification::getStatus, Arrays.asList(0,2))
                .eq(ShouhouRiskNotification::getShouhouId, req.getShouhouId())
                .update();
        if (flag) {
            ShouhouService shouhouService = SpringUtil.getApplicationContext().getBean(ShouhouService.class);
            ShouhouRiskNotificationReq shouhouRiskNotificationReq = new ShouhouRiskNotificationReq();
            shouhouRiskNotificationReq.setShouhouRiskNotificationId(shouhouRiskNotification.getId().intValue());
            shouhouRiskNotificationReq.setShouhouId(shouhouRiskNotification.getShouhouId());
            shouhouRiskNotificationReq.setRiskNotificationType(shouhouRiskNotification.getNotificationType());
            String riskNotificationUrlNoToken = getRiskNotificationUrl(shouhouRiskNotificationReq, 3);
            String notificationName = Optional.ofNullable(ShouhouRiskNotificationTypeEnum.getEnumByCode(shouhouRiskNotification.getNotificationType()))
                    .map(ShouhouRiskNotificationTypeEnum::getNotificationName).orElse("");
            String logMsg = String.format(shouhouRiskNotificationConfig.getRiskNotificationSignMsg(), riskNotificationUrlNoToken, notificationName);
            shouhouService.saveShouhouLog(shouhouRiskNotification.getShouhouId(), logMsg, "用户", 0, true);
            if (StringUtils.isNotBlank(req.getSignatureImage())) {
                String msg = String.format(shouhouRiskNotificationConfig.getRiskNotificationSignImageMsg(), req.getSignatureImage());
                shouhouService.saveShouhouLog(shouhouRiskNotification.getShouhouId(), msg, "用户", 0, false);
            }
            //发送mqtt相关风险告知书数据
            AtomicReference<String> msg = new AtomicReference<>();
            AtomicReference<String> topIc = new AtomicReference<>();
            try {
                Integer shouHouId = shouhouRiskNotification.getShouhouId();
                topIc.getAndSet(String.format(RISKNOTIFICATION_TOPIC, shouHouId));
                List<ShouHouRiskNotificationVO> shouHouRiskNotificationVOS = selectRiskNotificationList(shouHouId);
                if(CollectionUtils.isNotEmpty(shouHouRiskNotificationVOS)){
                    Map<Long, ShouHouRiskNotificationVO> notificationVOMap = shouHouRiskNotificationVOS.stream().collect(Collectors.toMap(ShouHouRiskNotificationVO::getId, Function.identity(), (n1, n2) -> n2));
                    Optional.ofNullable(notificationVOMap.get(Optional.ofNullable(shouhouRiskNotification.getId()).orElse(0L).longValue())).ifPresent(item->{
                        Optional.ofNullable(item.getSignatureTime()).ifPresent(obj->{
                            item.setSignatureTimeStr(obj.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        });
                        msg.getAndSet(JSONUtil.toJsonStr(item));
                        mqttAfterConfig.publish(topIc.get(),msg.get());
                        log.warn("签署风险告知书topIc：{}，推送内容：{}", topIc.get(), msg.get());
                    });
                }
            } catch (Exception e){
                RRExceptionHandler.logError("签署风险告知书推送mqtt异常" , String.format("签署风险告知书topIc：%s，推送内容：%s", topIc.get(), msg.get()), e, smsService::sendOaMsgTo9JiMan);
            }
        }
        return flag;
    }

    /**
     * 查询是否签署确认书
     *
     * @param shouhouId
     */
    @Override

    public boolean isSignShouhouRiskNotification(Integer shouhouId) {
        Integer hisRiskNotificationId = shouhouRiskNotificationConfig.getHisRiskNotificationId();
        return Optional.ofNullable(this.lambdaQuery()
                .gt(ShouhouRiskNotification::getId, hisRiskNotificationId)
                .eq(ShouhouRiskNotification::getShouhouId, shouhouId)
                .eq(ShouhouRiskNotification::getSourceType, 1)
                .eq(ShouhouRiskNotification::getStatus, 0)
                .count()).orElse(0) > 0;
    }

    @Override
    public Boolean flunkWeiXiu(ShouhouRiskNotificationReq req) {
        OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();
        ShouhouRiskNotification shouhouRiskNotification = this.lambdaQuery()
                .eq(ShouhouRiskNotification::getNotificationType, req.getRiskNotificationType())
                .eq(ShouhouRiskNotification::getShouhouId, req.getShouhouId())
                .one();
        if (shouhouRiskNotification == null) {
            throw new CustomizeException("风险告知书不存在");
        }
        if (Objects.equals(1, shouhouRiskNotification.getStatus())) {
            throw new CustomizeException("已经签署或同意告知书");
        }
        if (Objects.equals(2, shouhouRiskNotification.getStatus())) {
            throw new CustomizeException("已经放弃维修");
        }
        //放弃维修
        boolean flag = this.lambdaUpdate()
                .set(ShouhouRiskNotification::getStatus, 2)
                .eq(ShouhouRiskNotification::getId, shouhouRiskNotification.getId())
                .eq(ShouhouRiskNotification::getStatus, 0)
                .eq(ShouhouRiskNotification::getShouhouId, req.getShouhouId())
                .update();
        if (flag) {
            ShouhouService shouhouService = SpringUtil.getApplicationContext().getBean(ShouhouService.class);
            shouhouService.saveShouhouLog(shouhouRiskNotification.getShouhouId(),
                    "用户不同意风险，放弃维修", currentStaffId.getUserName(), 0, true);
        }
        return flag;
    }

    public R<String> pushMsgByWebService(AfterSalesPropellingMovement afterSalesPropellingMovement) {
        log.warn("售后风险确认书推送微信公众初始数据：{}", JSONUtil.toJsonStr(afterSalesPropellingMovement));
        //通过主站的接口获取微信模板 templateId
        long xtenant = XtenantEnum.getXtenant().longValue();
        Result<WxTemplateAndKeyCloudVO> wxTemplateKey = webExtendsWeixinCloud.getWxTemplateKey(shouhouRiskNotificationConfig.getWxTemplateType(), 0, xtenant);
        log.warn("售后风险确认书获取微信templateId获取结果：wxTemplateKey:{}", JSONUtil.toJsonStr(wxTemplateKey));
        if (ResultCode.SUCCESS != wxTemplateKey.getCode()) {
            return R.error(Optional.ofNullable(wxTemplateKey.getUserMsg()).orElse(wxTemplateKey.getMsg()));
        }
        WxTemplateAndKeyCloudVO data = Optional.ofNullable(wxTemplateKey.getData()).orElse(new WxTemplateAndKeyCloudVO());
        String templateId = data.getTemplateId();
        if (StringUtils.isEmpty(templateId)) {
            return R.error("获取微信templateId不能为空");
        }
        //开始构建微信推送所需要的数据
        WeChatTemplateCloudParam weChatCommonTemplateParam = new WeChatTemplateCloudParam();
        weChatCommonTemplateParam.setTemplateId(templateId);
        weChatCommonTemplateParam.setOpenId(afterSalesPropellingMovement.getOpenId());
        weChatCommonTemplateParam.setData(createMap(afterSalesPropellingMovement));
        weChatCommonTemplateParam.setUrl(afterSalesPropellingMovement.getUrl());
        log.warn("售后风险确认书推送通知传入参数：weChatCommonTemplateParam:{},xtenant:{}", JSONUtil.toJsonStr(weChatCommonTemplateParam), xtenant);
        Result<WeChatSendCloudVO> weChatSendCloudResult = webExtendsWeixinCloud.sendWeChatTemplateMsg(weChatCommonTemplateParam, xtenant);
        log.warn("售后风险确认书推送通知返回结果：{}", JSONUtil.toJsonStr(weChatSendCloudResult));
        if (ResultCode.SUCCESS != weChatSendCloudResult.getCode()) {
            String sysErr = Optional.ofNullable(weChatSendCloudResult.getUserMsg()).orElse(weChatSendCloudResult.getMsg());
            if (StringUtils.isNotEmpty(sysErr)) {
                return R.error("web-extend服务异常" + sysErr);
            }
            String errmsg = weChatSendCloudResult.getData().getErrmsg();
            if (StringUtils.isNotEmpty(errmsg)) {
                return R.error(errmsg);
            }
            return R.error("推送异常");
        }
        return R.success("推送成功");
    }

    /**
     * 查询售后单
     *
     * @param shouhouId
     * @return
     */
    private Shouhou getShouhou(Integer shouhouId) {
        ShouhouService shouhouService = SpringUtil.getApplicationContext().getBean(ShouhouService.class);
        Shouhou shouhou = shouhouService.getById(shouhouId);
        if (shouhou == null) {
            throw new CustomizeException("维修单号错误");
        }
        Assert.isFalse(shouhou.getUserid() == null, "售后单会员信息错误");
        return shouhou;
    }

    private String getRiskNotificationUrl(ShouhouRiskNotificationReq req, Integer sourceType) {
        String riskNotificationUrl = String.format(shouhouRiskNotificationConfig.getRiskNotificationUrl(), req.getShouhouId(), req.getShouhouRiskNotificationId(), req.getRiskNotificationType());
        R<String> valueR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, XtenantEnum.getXtenant());
        if (ResultCode.SUCCESS != valueR.getCode()) {
            throw new CustomizeException("获取域名接口异常" + Optional.ofNullable(valueR.getUserMsg()).orElse(valueR.getMsg()));
        }
        riskNotificationUrl = valueR.getData() + riskNotificationUrl;
        String token = UUID.randomUUID().toString();
        if (sourceType == 1 || sourceType == 2) {
            String key = MessageFormat.format(SMS_TOKEN_KEY, token);
            SmsTokenBo data = new SmsTokenBo();
            data.setUserId(req.getUserId())
                    .setUrl(riskNotificationUrl);
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(data), 5, TimeUnit.MINUTES);
        }
        riskNotificationUrl += "&smsToken=" + token;
        riskNotificationUrl += "&sourceType=" + sourceType;
        return riskNotificationUrl;
    }

    /**
     * 创建主站所需要的map用来微信推送
     *
     * @param afterSalesPropellingMovement
     * @return
     */
    private Map<String, Object> createMap(AfterSalesPropellingMovement afterSalesPropellingMovement) {
        Map<String, Object> map = new HashMap<>(NumberConstant.FOUR);
        map.put("first", afterSalesPropellingMovement.getFirst());
        map.put("HandleType", afterSalesPropellingMovement.getHandleType());
        map.put("Status", afterSalesPropellingMovement.getStatus());
        map.put("RowCreateDate", afterSalesPropellingMovement.getRowCreateDate());
        map.put("LogType", afterSalesPropellingMovement.getLogType());
        map.put("remark", afterSalesPropellingMovement.getRemark());
        return map;
    }

    /**
     * 通过userId获取微信的OpenId
     *
     * @param userId
     * @return
     */
    public String getOpenIdByUserId(Integer userId) {
        SmallproSendReviewUserWechatInfoBO wxxinUserByUserId = Optional.ofNullable(smallproMapper.getUserWechatInfo(userId)).orElse(new SmallproSendReviewUserWechatInfoBO());
//        if (wxxinUserByUserId.getOpenId() == null) {
//            throw new CustomizeException("获取该用户的微信信息为空");
//        }
        return wxxinUserByUserId.getOpenId();
    }

    private String getProductImgUrl(String picUrl) {
        if (StringUtils.isEmpty(picUrl)) {
            return null;
        }
        picUrl = StringUtils.stripEnd(picUrl, "/");
        String pa = "^\\d{1,9}/.*";
        Pattern pattern = Pattern.compile(pa);
        String url = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.IMG_URL, XtenantEnum.getXtenant()).getData();
        if (pattern.matcher(picUrl).matches()) {
            url += "/newstatic/" + picUrl;
        } else {
            url += "/pic/product/440x440/" + picUrl;
        }
        return url;
    }

    /**
     * 查询风险确认书文档
     *
     * @param type
     * @return
     */
    private DocumentVO getDocumentByType(Integer type) {
        Integer typeId = shouhouRiskNotificationConfig.getDocumentTypeIdByNotificationType(type);
        Integer documentId = Optional.ofNullable(webCloud.getDocumentIdByTypeIdAndXtenant(typeId, XtenantEnum.getXtenant()))
                .map(Result::getData).map(ElectronicTypeDocumentVO::getDocumentId).orElseThrow(() -> new CustomizeException("未查询到风险确认书文档配置"));
        return Optional.ofNullable(webCloud.getDocumentDetail(documentId)).filter(v -> Objects.equals(0, v.getCode())).map(Result::getData).orElseThrow(() -> new CustomizeException("查询风险告知书文档失败，请检查是否正确配置风险告知书文档"));
    }
}




