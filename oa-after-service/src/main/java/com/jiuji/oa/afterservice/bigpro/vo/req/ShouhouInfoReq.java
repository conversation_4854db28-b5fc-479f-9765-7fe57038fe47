package com.jiuji.oa.afterservice.bigpro.vo.req;

import com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouHouImportantBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouHouZengpinBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouServiceOutBo;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.vo.WeixiuzuKindVo;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouFuwuDrRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTroubleListRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.WxConfigRes;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 售后维修表单提交信息
 * @author: Li Quan
 * @date: 2020/5/20
 */
@Data
public class ShouhouInfoReq extends Shouhou {


    /**
     * 原因
     */
    private List<String> reasonList;
    @ApiModelProperty(value = "附件")
    private List<FileReq> files;

    @ApiModelProperty(value = "维修配件")
    private List<HexiaoBo> hexiaoTabs;
    @ApiModelProperty(value = "会员等级")
    private Integer userClass;
    @ApiModelProperty(value = "会员等级名称")
    private String userClassName;
    @ApiModelProperty(value = "内部员工姓名")
    private String ch999User;
    @ApiModelProperty(value = "当前所在地")
    private String nowarea;
    @ApiModelProperty(value = "维修绑定url")
    private String wxBindUrl;
    @ApiModelProperty(value = "是否新机单")
    private String isxinjidan;

    private Integer areaZh;
    private String areaZhName;
    private Integer toareaZh;
    private String toareaZhName;
    @ApiModelProperty(value = "退换类别")
    private Integer tuihuanKind;
    @ApiModelProperty(value = "退换类别描述")
    private String tuihuanKindText;

    @ApiModelProperty(value = "重大办")
    private ShouHouImportantBo zdb;

    @ApiModelProperty(value = "已(未)关注")
    private Boolean isSubCollect;

    @ApiModelProperty(value = "是否提醒")
    private Boolean isTx;

    @ApiModelProperty(value = "售后提醒")
    private ShouhouTixing shouhouTx;

    @ApiModelProperty(value = "订单是否存在接待记录")
    private Boolean isSubReception;

    @ApiModelProperty(value = "容量升级物流信息")
    private ShouhouRomUpgrade expressInfo;

    @ApiModelProperty(value = "区域对象")
    private AreaInfo areaSubject;

    @ApiModelProperty(value = "区域对象(可以为空)")
    private AreaInfo areaInfoOne;

    @ApiModelProperty(value = "中邮送修人客户信息")
    private ShouhouBusinessinfo bItem;

    @ApiModelProperty(value = "维修单赠品")
    private ShouHouZengpinBo zengPin;

    @ApiModelProperty(value = "维修单赠品1")
    private ShouHouZengpinBo zengPin1;

    @ApiModelProperty(value = "取机审核信息")
    private String checkInfo;

    @ApiModelProperty(value = "判断取机审核")
    private String qjCheck;

    @ApiModelProperty(value = "库存ppid")
    private List<Integer>kcOutPpids;

    @ApiModelProperty(value = "服务出险记录")
    private List<ShouhouServiceOutBo> serviceOutInfo;

    @ApiModelProperty(value = "维修组列表")
    private List<WeixiuzuKindVo> wxzKind;

    @ApiModelProperty(value = "最后一条测试数据信息")
    private ShouhoutestInfo testInfo;

    @ApiModelProperty(value = "所有故障类型")
    private List<ShouhouTroubleListRes> troubleList;

    @ApiModelProperty(value = "files2")
    List<Attachments> attachments;

    @ApiModelProperty(value = "服务展示类型")
    private Integer fuwuShowType;

    @ApiModelProperty(value = "fuwuDR")
    private ShouhouFuwuDrRes fuwuDR;

    @ApiModelProperty(value = "维修机型配置")
    private WxConfigRes wxconfig;
    /***
     * @see WaiGuanStatusEnum
     */
    @ApiModelProperty(value = "iswaiguan")
    private Integer isWaiguan;

    @ApiModelProperty(value = "可修改串号")
    private String imei1;

    @ApiModelProperty(value = "配件id")
    private List<Integer>kcOutId;

    @ApiModelProperty(value = "配件价格")
    private List<BigDecimal>kcPrice;

    @ApiModelProperty(value = "工时费")
    private List<BigDecimal>kcPriceGs;

    @ApiModelProperty(value = "配件成本")
    private List<BigDecimal>kcInprice;

    private Boolean isWeb;

    @ApiModelProperty("预约地址相关")
    private ShouhouAddressInfo addressInfo;

    @ApiModelProperty(value = "内部附件")
    private List<FileReq> files2;

    @ApiModelProperty(value = "故障类型")
    private List<Integer>troubleIds;

    @ApiModelProperty(value = "售后渠道信息")
    private ShouhouInfoRes.ShouhouQudao qudaoInfo;

    @ApiModelProperty(value = "九机服务出险标识")
    private Boolean isJiujiServiceFlag;

}
