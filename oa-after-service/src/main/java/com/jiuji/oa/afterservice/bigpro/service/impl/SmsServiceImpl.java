package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.afterservice.bigpro.bo.DiamondMemberMsgPushReq;
import com.jiuji.oa.afterservice.bigpro.bo.MemberScanBindOaPush;
import com.jiuji.oa.afterservice.bigpro.bo.SmsTokenBo;
import com.jiuji.oa.afterservice.bigpro.bo.ZnSendConnBo;
import com.jiuji.oa.afterservice.bigpro.bo.sms.SmsPlatFormBo;
import com.jiuji.oa.afterservice.bigpro.dao.SmsContentMapper;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.WeChatTemplateTypeEnum;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.SmsProperties;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.ConfigConsts;
import com.jiuji.oa.afterservice.common.constant.NumberConsts;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.UrlConstant;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.source.WwwUrlSource;
import com.jiuji.oa.afterservice.common.template.SendSaveMoneyWeixinTemplateItem;
import com.jiuji.oa.afterservice.common.template.WeChatMsgTemplate;
import com.jiuji.oa.afterservice.common.template.WechatTemplateLog;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.HttpClientUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.SmsChannelBo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.shouhou.vo.sms.AppInfoVo;
import com.jiuji.oa.afterservice.sys.bo.AuthModel;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 基础服务
 * @author: gengjiaping
 * @date: 2020/1/17
 */

@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    @Resource
    private WwwUrlSource wwwUrlSource;
    @Resource
    private InwcfUrlSource inwcfUrlSource;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    @Lazy
    private WeixinUserService weixinUserService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    @Lazy
    private SysConfigService sysConfigService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private MemberClient memberClient;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    @Lazy
    private AuthConfigService authConfigService;
    @Resource
    @Lazy
    private AreainfoService areainfoService;
    @Autowired
    private SmsContentMapper smsContentMapper;

    private static final String smsTokenKey = "sms:tokeninfo:{0}";

    private static final String urlParaAppend = "?";
    @Autowired
    private SmsProperties smsProperties;

    /**
     * {
     *   "phones": "18000559081",
     *   "content": "换手机，找九机，换新补贴，优惠多多",
     *   "appId": "wtjxinXW",
     *   "msgType":1
     * }
     * @param phone
     * @param content
     * @param sendTime
     * @param sender
     * @param sendChannel
     * @return
     */
    @Override
    public R<Boolean> sendSmsNew(String phone, String content, String sendTime, String sender, Integer sendChannel, String sign) {
        try {
            Assert.isFalse(StrUtil.isBlank(smsProperties.getPlatformUrl()),"短信平台地址不能为空");
            Assert.isFalse(StrUtil.length(StrUtil.trim(sendTime))>20,"短信发送时间格式错误");
            Assert.isFalse(StrUtil.isBlank(phone),"短信发送号码不能为空");
            Assert.isFalse(StrUtil.isBlank(content),"发送的短信内容不能为空");
            //获取当前的用户名密码
            AppInfoVo appInfo = smsContentMapper.getAppInfo(Namespaces.get());
            Assert.isFalse(ObjectUtil.isNull(appInfo),"短信平台用户信息不能为空");
            Assert.isFalse(StrUtil.isBlank(appInfo.getSecretKey()),"短信平台用户密钥不能为空");
            String json = new SmsPlatFormBo().setAppId(appInfo.getAppId()).setChannelId(sendChannel).setPhones(phone)
                    .setSign(sign)
                    .setContent(content).toJson();
            log.warn("问题排查短信发送内容：{}",json);
            HttpResponse httpResponse = HttpUtil.createPost(StrUtil.format("{}/cloudapi_nc/api/sendmsg?xservicename=sms", smsProperties.getPlatformUrl()))
                    .header("token", DigestUtil.md5Hex(StrUtil.format("sms_{}{}", appInfo.getSecretKey()
                            , LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))))
                    .body(json).execute();
            Assert.isTrue(httpResponse.isOk(), StrUtil.format("短信平台接口{}异常", httpResponse.getStatus()));
            Assert.isFalse(StrUtil.isBlank(httpResponse.body()),"短信平台返回结果为空");
            log.warn("问题排查短信返回结果：{}", JSONUtil.toJsonStr(httpResponse.body()));
           return Optional.ofNullable(JSON.parseObject(httpResponse.body(), R.class))
                   .map(r->{
                      R<Boolean> rr = new R<>(r.getCode(),r.getUserMsg());
                      if(rr.isSuccess()){
                          rr.setData(Boolean.TRUE);
                          rr.setUserMsg("短信发送成功");
                      }else{
                          //优化提示信息
                          rr.setUserMsg(StrUtil.format("短信发送失败,原因: {}",rr.getUserMsg()));
                      }
                      if(r.getData() != null){
                          rr.addBusinessLog(String.valueOf(r.getData()));
                      }
                      return rr;
                   })
                   .orElseGet(()->R.error("短信平台发送短信未知错误"));
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
    }

    @Override
    public R<Boolean> sendSms(String phone, String content, String sendTime, String sender, Integer sendChannelParam) {
        return sendSms(phone, content, sendTime, sender, sendChannelParam, "");
    }

    @Override
    public R<Boolean> sendSms(String phone, String content, String sendTime, String sender, Integer sendChannelParam, String sign) {
        //号码存入变量
        SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(RequestAttrKeys.SEND_SMS_PHONE_NUMBER, phone));
        Integer sendChannel = Optional.ofNullable(sendChannelParam).filter(CommenUtil::isNotNullZero)
        .orElseGet(()->Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId())
                .map(oaUser->sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.SMS_SERVER, oaUser.getXTenant()))
                    .map(R::getData).filter(NumberUtil::isNumber).map(NumberUtil::parseInt).orElse(0));

        //先调用新接口发送,如果发送失败,调用旧接口
        R<Boolean> r = sendSmsNew(phone, content, sendTime, sender, sendChannel, sign).addBusinessLog("新接口发送短信");
        return Optional.ofNullable(r).filter(R::isSuccess)
                .orElseGet(() -> {
                    //通知关注人
                    boolean isNeedErrorNotice = !Optional.ofNullable((String) redisTemplate.opsForValue().get("send_sms_notice"))
                            .map(nen->StrUtil.contains(nen,StrUtil.format("{}_{}",XtenantEnum.getTenantName(),r.getUserMsg())))
                            .filter(Boolean::booleanValue).isPresent();
                    RRExceptionHandler.logError("新平台短信发送失败", Dict.create().set("phone",phone).set("content",content)
                                    .set("sign",sign)
                                    .set("sendTime",sendTime).set("sender",sender).set("sendChannel",sendChannel),
                            new CustomizeException(r.getUserMsg()),
                            sms -> {
                                if(SpringContextUtil.isProduce() && isNeedErrorNotice){
                                    sendOaMsgTo9JiMan(sms);
                                }
                            });
                    return r;
                    /*return sendSmsOld(phone, content, sendTime, sender, sendChannel)
                            .addAllBusinessLog(r.businessLogs()).addBusinessLog(r.getUserMsg())
                            .addBusinessLog("老接口发送短信");*/
                });
    }

    @Override
    public R<Boolean> sendSmsOld(String phone, String content, String sendTime, String sender, Integer sendChannel) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String sendChannelStr = String.valueOf(sendChannel);
        if (CommenUtil.isNullOrZero(sendChannel)) {
            sendChannelStr = Optional.ofNullable(sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.SMS_SERVER, oaUserBO.getXTenant())).orElseGet(() -> "0");
        }
        if (StringUtils.isEmpty(sender)) {
            sender = "系统";
        }
        Map<String, String> param = new HashMap<>();
        param.put("phones", phone);
        param.put("content", content);
        param.put("sendtime", sendTime);
        param.put("server", sendChannelStr);
        param.put("sender", sender);
        R<String> valueByCode = sysConfigClient.getValueByCode(SysConfigConstant.SMS_URL);
        String url = valueByCode.getData() + "/?test=";
        String smsRet = HttpClientUtil.post(url, param);
        if (!StringUtils.isBlank(smsRet) && smsRet.contains("True")) {
            return R.success("发送成功", true);
        }

        return R.error(ResultCode.RETURN_ERROR, "短信发送失败");
    }

    @Override
    public R<Boolean> sendZyWeixinMsg(Integer fuwuId, Integer userId, String title, String wxName, String remark,
                                      String url, Integer cityId) {
        String json = HttpClientUtil.get("http://fix.999buy.com/api/user/getOpenId/v1?userid=" + userId);
        if (StringUtils.isEmpty(json)) {
            return R.error("获取openId失败");
        }
        JSONObject jsonObject = JSON.parseObject(json);
        if (jsonObject == null || Integer.parseInt(jsonObject.get("code").toString()) != 0) {
            return R.error("获取openId失败");
        }
        String openId = jsonObject.get("data").toString();
        String zytel = "0871-63150626";
        if (cityId != 0) {
            int provinceId = cityId / (int) 1e4;
            if (provinceId == 51) {
                zytel = "028-65170271";
            } else if (provinceId == 52) {
                zytel = "0851-5820005";
            } else if (provinceId == 54) {
                zytel = "0891-6831969";
            }
        }
        String weixinToken = HttpClientUtil.get("http://price.ch999img.com/DataAPI.ashx?act=g-zhongyou-token");
        String token_url = String.format("https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s", weixinToken);
        Map<String, Object> templateMsg = new HashMap<>();
        templateMsg.put("touser", openId);
        templateMsg.put("template_id", "TWwW9Tmj-x2lCjAnbBgZgrp2Ch5c6fcBKi8dpN07QyM");
        templateMsg.put("url", url);
        templateMsg.put("topcolor", "#0048a3");
        Map<String, Map<String, String>> data = new HashMap<>();
        Map<String, String> first = new HashMap<>();
        first.put("value", title);
        first.put("color", "#173177");
        data.put("first", first);
        Map<String, String> track_number = new HashMap<>();
        first.put("value", fuwuId.toString());
        first.put("color", "#173177");
        data.put("track_number", track_number);
        Map<String, String> asp_name = new HashMap<>();
        first.put("value", "中邮高讯");
        first.put("color", "#173177");
        data.put("asp_name", asp_name);
        Map<String, String> asp_tel = new HashMap<>();
        first.put("value", zytel);
        first.put("color", "#595959");
        data.put("asp_tel", asp_tel);
        Map<String, String> remarkm = new HashMap<>();
        first.put("value", remark);
        first.put("color", "#173177");
        data.put("remark", remarkm);
        templateMsg.put("data", data);
        String jsonStr = JSON.toJSONString(templateMsg);
        //HttpClientUtil.
        return null;
    }

    @Override
    public R<Boolean> sendZnMsg(ZnSendConnBo con) {
        if (con == null) {
            return R.error("发送消息内容不能为空");
        }
        String url = wwwUrlSource.getZnMsg();
        try {
            Map<String, String> param = new HashMap<>();
            param.put("title", con.getTitle());
            //kind: 1=>活动，4=》其他
            param.put("kind", String.valueOf(con.getKind()));
            param.put("PlatForm", con.getPlatForm());
            if (StringUtils.isNotEmpty(con.getLink())) {
                param.put("link", con.getLink());
            }
            if (StringUtils.isNotEmpty(con.getAppLink())) {
                param.put("AppLink", con.getAppLink());
            }
            if (StringUtils.isNotEmpty(con.getHdimg())) {
                param.put("img", con.getHdimg());
            }
            if (StringUtils.isNotEmpty(con.getEndTime())) {
                param.put("endtime", con.getEndTime());
            }
            param.put("content", con.getContent());
            param.put("ExtraData", con.getExtraData());
            if (8 == con.getKind()) {
                param.put("userid", String.valueOf(0));
                String json = HttpClientUtil.post(url, param);
                if (StringUtils.isNotEmpty(json) && Objects.equals(json, NumberConsts.ONE)) {
                    return R.success("发送成功", true);
                }
            } else {
                int nowCount = 0;
                String sendUserIds = "";
                if (StringUtils.isNotEmpty(con.getSmsnumber())) {
                    String[] smsArr = con.getSmsnumber().split(",");
                    for (int i = 0; i < smsArr.length; i++) {
                        sendUserIds += smsArr[i].replace("\r", "").replace("\n", "");
                        if (i < smsArr.length - 1) {
                            sendUserIds += ",";
                        }
                        nowCount++;
                        if (nowCount % 2000 == 0 || nowCount == smsArr.length) {
                            param.put("userid", sendUserIds);
                            String json = HttpClientUtil.post(url, param);
                            if (StringUtils.isNotEmpty(json) && Objects.equals(json, "1")) {
                                return R.success("发送成功", true);
                            } else {
                                return R.error("发送失败");
                            }

                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("推送站内消息失败：{}", e.getMessage());
        }
        return R.error("发送失败");
    }

    @Override
    @Async("pushMessageExecutor")
    public R<Boolean> asyncSendZnMsg(ZnSendConnBo con) {
        return this.sendZnMsg(con);
    }


    @Override
    public void sendOaMsg(String content, String link, String ch999ids, String msgType) {
        String sendHanderUrl = inwcfUrlSource.getOaMsg(content, link,
                ch999ids, msgType);
        String host = SpringUtil.getBean(SysConfigClient.class).getValueByCode(SysConfigConstant.IN_WCF_HOST).getData();
        Map<String, String> params = new HashMap<>();
        params.put("content", content);
        params.put("link", link);
        params.put("ch999ids", ch999ids);
        params.put("msgType", msgType);
        params.put("act","oaMessagePush");

        if (StringUtils.isNotEmpty(host)) {
            sendHanderUrl = host + "/ajax.ashx";
        }
        if (log.isDebugEnabled()){
            log.debug("oa推送地址: {}, 参数: {}", sendHanderUrl, JSON.toJSONString(params, SerializerFeature.PrettyFormat));
        }
        if (SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.NOT_NOTICE_USER))
                .map(Convert::toBool).orElse(Boolean.FALSE)){
            //开启不通知开关,不发送通知
            log.debug("不通知开关开启,不进行推送oa消息");
            return;
        }
        HttpClientUtil.post(sendHanderUrl, params);
    }

    @Override
    public void sendOaMsg(String content, String link, String ch999ids, OaMesTypeEnum msgType) {
        sendOaMsg(content, link, ch999ids, msgType.getCode().toString());
    }

    @Override
    public void oaRabbitMQWorkQueue(String message, String queue) {
        if (StringUtils.isEmpty(message)) {
            return;
        }
        if (StringUtils.isEmpty(queue)) {
            queue = RabbitMqConfig.QUEUE_TOPIC_OAASYNC;
        }
        try {
            rabbitTemplate.convertAndSend(queue, message);
        } catch (Exception e) {
            log.error("oaRabbitMQWorkQueue消息推送异常：{}", e.getMessage());
        }

    }

    @Override
    public String getShortUrl(Long xtenant, String fullUrl, String des) {

        //todo 获取短链后续需要替换成 getShortUrlV2 方法
        Map<String, String> params = new HashMap<>();
        params.put("xtenant", String.valueOf(xtenant));
        params.put("description", des);
        params.put("fullUrl", fullUrl);
        R<String> domainR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.WEB_URL, Math.toIntExact(xtenant));
        String domain = "";
        if (domainR.getCode() == ResultCode.SUCCESS && domainR.getData() != null) {
            domain = domainR.getData();
        }
        String url = domain + UrlConstant.SHORT_URL;
        String resultJson = HttpClientUtil.post(url, params);
        if (StringUtils.isEmpty(resultJson)) {
            return null;
        }
        R<String> shortUrlRet = JSON.parseObject(resultJson, new TypeReference<R<String>>() {
        });
        if (shortUrlRet.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(shortUrlRet.getData())) {
            fullUrl = shortUrlRet.getData();
        }
        return fullUrl;
    }

    @Override
    public String getShortLinkWithToken(Integer userId, String fullUrl, Integer xTenant,Integer minutes) {
        fullUrl = getLinkWithToken(userId, fullUrl, minutes);
        return this.getShortUrlV2(fullUrl);
    }

    /**
     * 给链接增加免密登录的token
     * @param userId
     * @param fullUrl
     * @param minutes
     * @return
     */
    @Override
    @NonNull
    public String getLinkWithToken(Integer userId, String fullUrl, Integer minutes) {
        String token = UUID.randomUUID().toString();
        String key = MessageFormat.format(smsTokenKey, token);
        SmsTokenBo data = new SmsTokenBo();
        data.setUserId(userId)
                .setUrl(fullUrl);
        redisTemplate.opsForValue().set(key, JSON.toJSONString(data), minutes, TimeUnit.MINUTES);
        log.warn("短链生成token：{}, userId: {}, minutes: {}",key, userId, minutes);
        if (fullUrl.contains(urlParaAppend)) {
            fullUrl += "&smsToken=" + token;
        } else {
            fullUrl += "?smsToken=" + token;
        }
        return fullUrl;
    }

    private String getShortUrlV2(String fullUrl) {

        try {
            fullUrl = URLEncoder.encode(fullUrl, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return "";
        }
        R<String> domainR = sysConfigClient.getValueByCode(SysConfigConstant.WEB_URL);
        String domain = "";
        if (domainR.getCode() == ResultCode.SUCCESS && domainR.getData() != null) {
            domain = domainR.getData();
        }
        String url = String.format(domain + UrlConstant.SHORT_URL_V2, fullUrl);
        String resultJson = HttpClientUtil.get(url);
        if (StringUtils.isEmpty(resultJson)) {
            return null;
        }
        R<String> shortUrlRet = JSON.parseObject(resultJson, new TypeReference<R<String>>() {
        });
        if (shortUrlRet.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(shortUrlRet.getData())) {
            fullUrl = shortUrlRet.getData();
        }
        return fullUrl;
    }


    @Override
    public R<String> sendSaveMoneyWeixin(String openId, String title, String keyword1, String keyword3,
                                         LocalDateTime keyword4, String remark, String url, Integer wxId) {

        String templateId = weixinUserService.getTemplateID(WeChatTemplateTypeEnum.SaveMoney.getCode(), 1);
        SendSaveMoneyWeixinTemplateItem msgData = new SendSaveMoneyWeixinTemplateItem();
        SendSaveMoneyWeixinTemplateItem first = new SendSaveMoneyWeixinTemplateItem();
        first.setValue(title);
        first.setColor("#ff0000");
        msgData.setFirst(first);

        //支出金额
        msgData.setKeyword1(new SendSaveMoneyWeixinTemplateItem().setValue(keyword1).setColor("#595959"));
        //账户余额
        msgData.setKeyword2(new SendSaveMoneyWeixinTemplateItem().setValue("余额支付").setColor("#595959"));
        //支出项目
        msgData.setKeyword3(new SendSaveMoneyWeixinTemplateItem().setValue(keyword3).setColor("#173177"));
        //支出时间
        msgData.setKeyword4(new SendSaveMoneyWeixinTemplateItem().setValue(DateUtil.localDateTimeToString(keyword4)).setColor("#173177"));
        //备注信息
        msgData.setRemark(new SendSaveMoneyWeixinTemplateItem().setValue(remark).setColor("#173177"));

        WeChatMsgTemplate<SendSaveMoneyWeixinTemplateItem> weChatMsgTemplate = new WeChatMsgTemplate<SendSaveMoneyWeixinTemplateItem>();
        weChatMsgTemplate.setData(msgData);

        String result = weixinUserService.sendTemplateMsg(openId, templateId, url, weChatMsgTemplate, "#0048a3", null);

        //记录日志
        weixinUserService.logWechatTemplateMessage(new WechatTemplateLog().setUrl(url).setMessage(msgData.toString()).setOpenId(openId).setType(WeChatTemplateTypeEnum.BusinessMsg.getCode().toString()), result);

        return R.success(result);
    }

    @Override
    public String sendEmail(String sendTo, String title, String msg) {
        String result;
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("operation", "sendEmail");
            params.put("RECEIVE_USER", sendTo);
            params.put("EMAIL_TITLE", title);
            params.put("EMAIL_CONTENT", msg);
            result = HttpUtil.post("http://office/Handler/api.ashx", params);
        } catch (Exception e) {
            result = "推送站内Emailshibai";
        }
        return result;
    }

    @Override
    public String getSmsChannelByTenant(Long xTenant) {
        // 都是验证码通道
        R<String> channelR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_CHANNEL, Math.toIntExact(xTenant));
        return CommonUtils.isRequestSuccess(channelR) ? channelR.getData() : "";
    }

    @Override
    public void sendDiamondMemberMsgWhenBusiness(Integer userId, Integer areaId, Integer businessType, Integer businessNo) {

        OaUserBO currentStaff = abstractCurrentRequestComponent.getCurrentStaffId();
        if (currentStaff.getXTenant() >= 1000) {
            return;
        }
        R<MemberBasicRes> memberBasicInfoR = memberClient.getMemberBasicInfo(userId);
        if (memberBasicInfoR.getCode() == ResultCode.SUCCESS && memberBasicInfoR.getData() != null) {
            MemberBasicRes memberBasicInfo = memberBasicInfoR.getData();
            if (CommenUtil.isNotNullZero(memberBasicInfo.getUserClass()) && memberBasicInfo.getUserClass() >= 5) {
                String host = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST);
                if (StringUtils.isNotEmpty(host)) {
                    //todo 钻石会员消息推送
                    String url = host + UrlConstant.DIAMOND_MEMBER_PUSH_URL;
                    DiamondMemberMsgPushReq diamondMember = new DiamondMemberMsgPushReq();
                    DiamondMemberMsgPushReq.Input input = new DiamondMemberMsgPushReq.Input();
                    input.setAreaId(areaId)
                            .setUserId(userId)
                            .setSub_type(businessType)
                            .setSub_id(businessNo.toString());
                    diamondMember.setInput(input);
                    String jsonStr = JSONObject.toJSONString(diamondMember);
                    String res = HttpUtil.post(url, jsonStr);
                    System.out.println(res);
                }
            }
        }
    }

    @Override
    public Integer getSmsChannelByTenant(Integer areaId, ESmsChannelTypeEnum channelType) {
        //获取短信通道 九机根据授权获取，输出根据优先获取门店信息表，如果没有则根据授权获取
        AreaInfo areaInfo = Optional.ofNullable(areaInfoClient.getAreaInfoById(areaId).getData()).orElseGet(AreaInfo::new);
        if (areaInfo == null) {
            return null;
        }
        Integer xTenant = areaInfo.getXtenant();
        Integer tenantThreshold = ConfigConsts.TENANT_THRESHOLD;
        Integer channel = 0;
        SmsChannelBo smsChannelInfo = new SmsChannelBo();
        if (xTenant >= tenantThreshold) {
            smsChannelInfo = areainfoService.getSmsChannelById(areaId);
        }
        AuthModel authModel = authConfigService.getAuthConfig().stream().filter(a -> Objects.equals(areaInfo.getAuthorizeId(), a.getId())).findFirst().orElseGet(AuthModel::new);
        switch (channelType) {
            case YXTD:
                channel = getMarketingChannel(xTenant, tenantThreshold, channel, smsChannelInfo, authModel);
                return channel;
            case YZMTD:
                // 之前对于通道的使用不是那么的规范很多 营销类的短信也还是会走到验证码YZMTD通道中
                // 因为之前已经盘点过验证码短信只走验证码通道了（VERIFICATION_CODE）所以现在YZMTD以这种方式进来的数据就是非验证码那就去不去走营销通道
                channel = getMarketingChannel(xTenant, tenantThreshold, channel, smsChannelInfo, authModel);
               //channel = getVCodeChannel(xTenant, tenantThreshold, channel, smsChannelInfo, authModel);
                return channel;
            case VERIFICATION_CODE:
                try {
                    /**
                     * 因为只是再SysConfig 配置了 xTenant =0 的数据所以 输出以及九机的子租户还是走的原来的逻辑
                     */
                    channel = Optional.ofNullable(sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_ID, xTenant))
                            .filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(NumberConstant.ZERO);
                    log.warn("获取短信通道传入参数：{},xTenant:{}，返回结果：{}", SysConfigConstant.VERIFICATION_CODE_ID, xTenant,channel);
                } catch (Exception e){
                    channel = getVCodeChannel(xTenant, tenantThreshold, channel, smsChannelInfo, authModel);
                    RRExceptionHandler.logError("获取短信通道异常", Dict.create().set("VERIFICATION_CODE_ID",SysConfigConstant.VERIFICATION_CODE_ID)
                            .set("xTenant",xTenant), e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                }
                if(ObjectUtil.isNull(channel) || NumberConstant.ZERO.equals(channel)){
                    channel = getVCodeChannel(xTenant, tenantThreshold, channel, smsChannelInfo, authModel);
                }
                return channel;
            default:
                break;
        }

        return channel;
    }

    /**
     * 获取营销通道
     * @param xTenant
     * @param tenantThreshold
     * @param channel
     * @param smsChannelInfo
     * @param authModel
     * @return
     */
    private Integer getMarketingChannel(Integer xTenant, Integer tenantThreshold, Integer channel, SmsChannelBo smsChannelInfo, AuthModel authModel) {
        if (xTenant >= tenantThreshold) {
            channel = smsChannelInfo.getMarketChannel();
            //先从门店获取，门店获取失败从授权获取
        } else {
            channel = Optional.ofNullable(sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.MARKETING_CHANNEL, xTenant))
                    .filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
        }
        if (CommenUtil.isNullOrZero(channel)) {
            channel = Optional.ofNullable(authModel.getMarketChannel()).filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
        }
        return channel;
    }

    /**
     * 获取验证码通道
     * @param xTenant 租户ID
     * @param tenantThreshold 租户阈值
     * @param channel 通道
     * @param smsChannelInfo 短信通道信息
     * @param authModel 授权模型
     * @return 验证码通道
     */
    private Integer getVCodeChannel(Integer xTenant, Integer tenantThreshold, Integer channel, SmsChannelBo smsChannelInfo, AuthModel authModel) {
        if (xTenant >= tenantThreshold) {
            channel = smsChannelInfo.getVCodeChannel();
            //先从门店获取，门店获取失败从授权获取
        } else {
            channel = Optional.ofNullable(sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_CHANNEL, xTenant))
                    .filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
        }
        if (CommenUtil.isNullOrZero(channel)) {
            channel = Optional.ofNullable(authModel.getVCodeChannel()).filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
        }
        return channel;
    }

    /**
     * say something
     * @param userId
     * @param ch999Id
     * @param subType
     * @param subId
     * @param businessNode
     */
    @Override
    public void pushMemberScanBind(Integer userId, Integer ch999Id, Integer subType, Integer subId, Integer businessNode) {
        MemberScanBindOaPush bindOaPush = new MemberScanBindOaPush();
        MemberScanBindOaPush.MemberScanBindOaPushData data = new MemberScanBindOaPush.MemberScanBindOaPushData();
        data.setBusinessNode(businessNode)
                .setCh999Id(ch999Id)
                .setUserId(userId)
                .setSubType(subType)
                .setSubId(subId);
        bindOaPush.setAct(RabbitMqConfig.MEMBER_SCAN_BIND_OA_PUSH);
        bindOaPush.setData(data);
        rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, JSON.toJSONString(bindOaPush));
    }

    @Override
    @RepeatSubmitCheck(argIndexs = {0})
    public R saveOrUpdateAppInfo(AppInfoVo appInfoVo) {
        long xtenant = Namespaces.get();
        return Optional.of(smsContentMapper.updateAppInfo(appInfoVo, xtenant)).filter(n->n<1)
                .map(n->smsContentMapper.insertAppInfo(appInfoVo,xtenant)).filter(n->n<1)
                .map(n->R.error("短信账号信息推送失败")).orElseGet(()->R.success(appInfoVo.getId()));
    }

    /**
     * 指定推送到九机oa系统
     *
     * @param content content
     * @param ch999ids ch999ids
     * @param msgType msgType
     */
    @Override
    public void sendOaMsgTo9Ji(String content, String ch999ids, String msgType) {
        //获取日志的连接地址
        sendOaMsgTo9Ji(content,"",ch999ids,msgType);
    }

    /**
     * 指定推送到九机oa系统
     *
     * @param content content
     * @param ch999ids ch999ids
     * @param msgType msgType
     */
    @Override
    public void sendOaMsgTo9Ji(String content, String link, String ch999ids, String msgType) {
        //获取日志的连接地址
        sendOaMsgWithHtml(content, link, ch999ids, msgType, "https://moa.9ji.com");
    }

    /**
     * 指定推送到九机oa系统,可以包含html,内部使用,不对外
     *
     * @param content content
     * @param ch999ids ch999ids
     * @param msgType msgType
     */
    @Override
    public void sendOaMsgWithHtml(String content, String link, String ch999ids, String msgType) {
        //获取日志的连接地址
        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        if (StrUtil.isBlank(host)){
            log.warn("地址为空,不进行oa推送: content: {} link: {} ch999ids: {} msgType: {}",content,link,ch999ids,msgType);
            return;
        }
        sendOaMsgWithHtml(content, link, ch999ids, msgType, host);
    }

    private void sendOaMsgWithHtml(String content, String link, String ch999ids, String msgType, String host) {
        String sendHarderUrl = StrUtil.format("{}/cloudapi_nc/org_service/api/myMessage/myMessage/oaMessagePush?xservicename=oa-org",host);
        HashMap<String, Object> param = new HashMap<>();
        param.put("act", "oaMessagePush");
        param.put("content", content);
        param.put("link", link);
        param.put("ch999Ids", StrUtil.splitTrim(ch999ids,StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList()));
        param.put("msgType", msgType);
        String paramStr = JSON.toJSONString(param);
        log.debug("推送oa的地址: {},参数: {}", sendHarderUrl,paramStr);
        log.debug("推送oa结果: {}",HttpUtil.post(sendHarderUrl, paramStr));
    }

    @Override
    public void sendOaMsgTo9JiMan(String template, Object... params) {
        String originContent = StrUtil.format(template,params);
        if (StrUtil.isBlank(originContent)){
            return;
        }
        String content = originContent;
        if(StrUtil.isNotBlank(XtenantEnum.getTenantName()) && !content.contains(XtenantEnum.getTenantName())){
            content = StrUtil.format("{}【{}{}】{}",Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId()).map(OaUserBO::getAreaId)
                    .map(areaId -> SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(areaId)).filter(R::isSuccess).map(R::getData)
                    .map(AreaInfo::getPrintName).orElse(""), XtenantEnum.getTenantName(), SpringContextUtil.isProduce() ? "" : "测试", content);
        }
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        if(StrUtil.isNotBlank(traceId)){
            String logUrl = RRExceptionHandler.getLogUrl("oajava-after", traceId);
            content = StrUtil.format("{}  <a target='_blank' href='{}'>跳转日志</a>",content,logUrl);
        }
        //
        sendOaMsgTo9Ji(content, "13685,13495,13682",OaMesTypeEnum.YCTZ.getCode().toString());
    }

}
