package com.jiuji.oa.afterservice.common.config.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.lang.Nullable;

import java.nio.charset.StandardCharsets;

/**
 * 从org.springframework.data.redis.serializer.StringRedisSerializer拷贝的，增加多租户支持
 */
@Slf4j
public class MyStringSerializer extends StringRedisSerializer {


    @Override
    public byte[] serialize(@Nullable String string) {
        if (string == null) {
            return null;
        }
        return string.getBytes(StandardCharsets.UTF_8);
    }
}

