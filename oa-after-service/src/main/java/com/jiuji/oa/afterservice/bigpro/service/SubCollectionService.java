package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.SubCollection;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
public interface SubCollectionService extends IService<SubCollection> {
    /**
     * 获取订单的ch99id
     * @param subId
     * @return
     */
    List<Integer> getSubCollectionUserIds(Integer subId);
}
