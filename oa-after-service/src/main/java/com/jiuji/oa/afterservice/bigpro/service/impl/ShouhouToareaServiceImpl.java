package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouToareaMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouToarea;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouToareaService;
import org.springframework.stereotype.Service;


@Service("shouhouToareaService")
public class ShouhouToareaServiceImpl extends ServiceImpl<ShouhouToareaMapper, ShouhouToarea> implements ShouhouToareaService {

    @Override
    public String getShiBieMa(Integer toAreaId) {
        return baseMapper.getShiBieMa(toAreaId);
    }

    @Override
    public Integer getCount(Integer id) {
        return baseMapper.getCount(id);
    }
}