package com.jiuji.oa.afterservice.bigpro.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 取机请求实体类
 * <AUTHOR>
 * @since 2022/2/8 14:49
 */
@Data
@ApiModel("售后取机Req")
public class QujiReq {
    @ApiModelProperty("售后单id")
    @NotNull(message = "售后id不能为空")
    @Min(value = 1,message = "售后单id必须大于0")
    private Integer id;

    @ApiModelProperty("取机备注信息")
    private String remark;
}
