package com.jiuji.oa.afterservice.smallpro.yearcardtransfer.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jiuji.oa.oacore.yearcardtransfer.enums.YearPackageTransferStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 年包转赠实体类
 */
@Data
@TableName("year_package_transfer")
public class YearPackageTransferPo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 原始年包ID 表: tiemoCard的id
     */
    @TableField("origin_card_id")
    private Integer originCardId;

    /**
     * 转赠码
     */
    @TableField("transfer_code")
    private String transferCode;

    /**
     * 小件单号
     */
    @TableField("small_id")
    private Integer smallId;

    /**
     * 赠送人ID
     */
    @TableField("sender_id")
    private Integer senderId;

    /**
     * 接收人ID
     */
    @TableField("receiver_id")
    private Integer receiverId;

    /**
     * 状态 {@link YearPackageTransferStatusEnum}
     */
    @TableField("status")
    @EnumValue
    private Integer status;

    /**
     * 生效开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public boolean isEffectiveAt(LocalDateTime checkTime) {
        return (startTime == null || !checkTime.isBefore(startTime))
                && (endTime == null || !checkTime.isAfter(endTime));
    }
}
