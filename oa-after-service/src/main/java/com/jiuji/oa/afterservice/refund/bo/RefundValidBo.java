package com.jiuji.oa.afterservice.refund.bo;

import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 退款验证实体bo
 * <AUTHOR>
 * @since 2022/8/2 15:14
 */
@Data
@Accessors(chain = true)
@ApiModel("获取退款验证实体Bo")
public class RefundValidBo {
    @ApiModelProperty(value = "购买用户ID")
    private Long userid;
    @ApiModelProperty("客户手机")
    private String mobile;
    /**
     * 订单类型
     * @see BusinessTypeEnum
     */
    private Integer businessType;
}
