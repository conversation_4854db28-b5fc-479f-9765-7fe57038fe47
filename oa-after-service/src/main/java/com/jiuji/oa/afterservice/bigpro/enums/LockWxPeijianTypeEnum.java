package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: gengjiaping
 * @date: 2020/3/17
 */
@Getter
@AllArgsConstructor
public enum LockWxPeijianTypeEnum implements CodeMessageEnumInterface {
    LOCK(1,"锁定配件"),
    NO_LOCK(2,"解除锁定配件");
    /**
     * 类别
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
