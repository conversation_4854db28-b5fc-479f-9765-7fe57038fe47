package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.oa.afterservice.bigpro.bo.ShouHouBasketBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouJinshuibaoMapper;
import com.jiuji.oa.afterservice.bigpro.enums.RecoverStateEnum;
import com.jiuji.oa.afterservice.bigpro.po.RecoverSub;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouJinshuibao;
import com.jiuji.oa.afterservice.bigpro.service.RecoverSubService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouJinshuibaoService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.Shouying;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.ShouyingService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproLogService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.enums.EnumUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;


@Service("shouhouJinshuibaoService")
public class ShouhouJinshuibaoServiceImpl extends ServiceImpl<ShouhouJinshuibaoMapper, ShouhouJinshuibao> implements ShouhouJinshuibaoService {

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private SubService subService;
    @Autowired
    private RecoverSubService recoverSubService;
    @Autowired
    private BasketService basketService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private ShouyingService shouyingService;
    @Autowired
    private SmallproLogService smallproLogService;

    @Override
    public R<Integer> addJinshuibao(ShouhouJinshuibao jinshuibao) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        Integer stype = jinshuibao.getServiceType() == null ? BaoXiuTypeEnum.JSB.getCode() : jinshuibao.getServiceType();
        Shouhou shouhou = shouhouService.getOne(new LambdaQueryWrapper<Shouhou>().eq(Shouhou::getId, jinshuibao.getShouhouid()).eq(Shouhou::getXianshi, true).eq(Shouhou::getServiceType, stype));
        if (shouhou == null) {
            return R.error("售后单号错误");
        }

        ShouHouBasketBo basket = getBasketInfoByShouhouId(jinshuibao.getShouhouid());
        if (basket == null) {
            return R.error("原购单号错误");
        }
        if (!basket.getUserId().equals(shouhou.getUserid())) {
            return R.error("新机单用户和售后单用户不一致，请检查");
        }
        Integer areaId = shouhou.getAreaid() == null ? 0 : shouhou.getAreaid();
        if (!areaId.equals(basket.getAreaId())) {
            return R.error("新机单地区错误，请检查");
        }
        List<Integer> subChecks = Arrays.asList(1, 6);
        List<Sub> subs = subService.list(new LambdaQueryWrapper<Sub>().eq(Sub::getSubId, jinshuibao.getReorderid()).in(Sub::getSubCheck, subChecks));
        if (CollectionUtils.isEmpty(subs)) {
            return R.error("新机单号错误，请检查新机单状态是否为已确认或欠款");
        }

        Sub newOrder = subs.get(0);
        if (newOrder.getYingfuM() != null && newOrder.getYingfuM().compareTo(basket.getPrice()) < 0) {
            return R.error("新机单金额不能低于原购机金额");
        }
        List<RecoverSub> recovers = recoverSubService.list(new LambdaQueryWrapper<RecoverSub>()
                .eq(RecoverSub::getSubId, jinshuibao.getReorderid())
                .eq(RecoverSub::getSubCheck, RecoverStateEnum.YRK));
        if (CollectionUtils.isEmpty(recovers)) {
            return R.error("回收单号错误，请检查回收单状态是否为已入库");
        }
        BigDecimal feiyong = calcJinshuibaoFeiyong(basket.getPrice(), basket.getTradeDate(), stype);

        jinshuibao.setDtime(LocalDateTime.now());
        jinshuibao.setServiceType(stype);
        jinshuibao.setIsdel(false);
        jinshuibao.setFeiyong(feiyong);
        Boolean flag = super.save(jinshuibao);
        if (flag) {
            shouhouService.saveShouhouLog(jinshuibao.getShouhouid(), "提交申请", oaUserBO.getUserName(), null, false);
        }

        return R.success("操作成功", jinshuibao.getId());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> checkJinshuibao(Integer id, Integer kind, Integer serviceType) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        LambdaUpdateWrapper<ShouhouJinshuibao> updateWrapper = new LambdaUpdateWrapper<>();

        serviceType = serviceType == null ? BaoXiuTypeEnum.JSB.getCode() : serviceType;
        ShouhouJinshuibao jinshuibao = super.getById(id);
        if (jinshuibao == null || jinshuibao.getIsdel() || !jinshuibao.getServiceType().equals(serviceType)) {
            return R.error("未查询到申请");
        }

        BigDecimal feiyong = jinshuibao.getFeiyong() == null ? BigDecimal.ZERO : jinshuibao.getFeiyong();
        Integer orderId = jinshuibao.getOrderid() == null ? 0 : jinshuibao.getOrderid();
        Integer shouhouId = jinshuibao.getShouhouid() == null ? 0 : jinshuibao.getShouhouid();
        Integer reorderid = jinshuibao.getReorderid() == null ? 0 : jinshuibao.getReorderid();
        Shouhou sh = shouhouService.getById(shouhouId);
        Integer subId = sh.getSubId();
        if (subId == null || subId == 0) {
            return R.error("未查询到原订单");
        }
        //进水保ppid
        Integer ppid = 55428;
        if (serviceType.equals(BaoXiuTypeEnum.YHDX.getCode())) {
            //以换代修ppid
            ppid = 67776;
        }
        List<Basket> basketList = basketService.list(new LambdaQueryWrapper<Basket>().eq(Basket::getSubId, subId)
                .and(bo -> bo.eq(Basket::getIsdel, false).or().isNull(Basket::getIsdel))
                .eq(Basket::getPpriceid, ppid));
        BigDecimal jinshuibaoPrice = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(basketList)) {
            jinshuibaoPrice = basketList.get(0).getPrice();
        }
        if (feiyong.compareTo(BigDecimal.valueOf(1)) < 0) {
            return R.error("回收补价金额错误");
        }
        Sub subDt = subService.getOne(new LambdaQueryWrapper<Sub>().eq(Sub::getSubId, subId).in(Sub::getSubCheck, Arrays.asList(1, 6)));
        if (subDt == null) {
            return R.error("新机单号查询失败");
        }
        BigDecimal subPrice = subDt.getYingfuM().subtract(subDt.getYifuM());
        if (subPrice.compareTo(BigDecimal.valueOf(1)) < 0) {
            return R.error("新机单金额错误，请检查");
        }
        if (reorderid == null || reorderid == 0) {
            return R.error("回收单错误，请核对");
        }

        Boolean isReOrderEnd = false;
        List<RecoverSub> reorderList = recoverSubService.list(new LambdaQueryWrapper<RecoverSub>().eq(RecoverSub::getSubId, reorderid));
        if (CollectionUtils.isEmpty(reorderList)) {
            return R.error("回收单错误，请核对");
        } else {
            isReOrderEnd = reorderList.get(0).getSubCheck() != null && reorderList.get(0).getSubCheck() == 3;
        }
        if (1 == kind) {
            if (!oaUserBO.getRank().contains("6a2")) {
                return R.error("您没有审核权限 (6a2)");
            }
            updateWrapper.set(ShouhouJinshuibao::getCheckUser, oaUserBO.getUserName()).set(ShouhouJinshuibao::getCheckTime, LocalDateTime.now());
        } else if (2 == kind) {
            if (!oaUserBO.getRank().contains("6a3")) {
                return R.error("您没有审核权限 (6a2)");
            }
            updateWrapper.set(ShouhouJinshuibao::getCheckUser2, oaUserBO.getUserName()).set(ShouhouJinshuibao::getCheckTime2, LocalDateTime.now());
        } else if (3 == kind) {
            if (!oaUserBO.getRank().contains("6a4")) {
                return R.error("您没有审核权限 (6a4)");
            }
            updateWrapper.set(ShouhouJinshuibao::getCheckUser3, oaUserBO.getUserName()).set(ShouhouJinshuibao::getCheckTime3, LocalDateTime.now());
        }

        updateWrapper.eq(ShouhouJinshuibao::getId, id).and(bo -> bo.eq(ShouhouJinshuibao::getServiceType, BaoXiuTypeEnum.JSB.getCode()).or().isNull(ShouhouJinshuibao::getServiceType));

        //服务办理检查
        if (3 == kind) {
            //服务办理
            if (subPrice.compareTo(feiyong) >= 0) {
                //支付并交易完成回收单，如果回收单已完成则不在操作
                if (!isReOrderEnd) {
                    //自营生成凭证 加盟不生成凭证
                    //todo recoverPay 等C#提供接口
                }
                Boolean flag = false;
                List<SubLogsNewReq> subLogs = new LinkedList<>();
                Integer subAreaId = subDt.getAreaId();

                R<AreaInfo> subAreaR = areaInfoClient.getAreaInfoById(subAreaId);
                if (subAreaR.getCode() != ResultCode.SUCCESS || subAreaR.getData() == null) {
                    return R.error("获取门店信息出错");
                }
                AreaInfo subArea = subAreaR.getData();

                flag = subService.update(new LambdaUpdateWrapper<Sub>().setSql("yifuM = yifuM +" + feiyong)
                        .in(Sub::getSubCheck, Arrays.asList(1, 6)).eq(Sub::getSubId, orderId));

                if (!flag) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error("转订金出错！");
                }
                Sub dt2 = subService.getById(orderId);

                SubLogsNewReq subLog = new SubLogsNewReq();
                subLog.setSubId(orderId);
                subLog.setShowType(false);
                subLog.setInUser(oaUserBO.getUserName());
                subLog.setType(1);
                subLog.setComment("");
                String comment = "收到" + EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, serviceType) + "换机：" + feiyong + "元" +
                        "，售后单号：<a href='/shouhou/edit/" + shouhouId + "' target='_blank'>" + shouhouId + "</a>" +
                        "，回购单号：<a href='/recoverIndex/detail?id=" + reorderid + "' target='_blank'>" + reorderid + "</a>";
                subLog.setComment(comment);
                subLogs.add(subLog);

                if (dt2.getYifuM().compareTo(dt2.getYingfuM()) == 0 && 6 == dt2.getSubCheck()) {
                    //如果款项已清，自动转为 已出库状态
                    subService.update(new LambdaUpdateWrapper<Sub>().set(Sub::getSubCheck, 2)
                            .eq(Sub::getSubCheck, 6)
                            .eq(Sub::getSubId, orderId));

                    subLog = new SubLogsNewReq();
                    subLog.setSubId(orderId);
                    subLog.setShowType(false);
                    subLog.setInUser(oaUserBO.getUserName());
                    subLog.setType(1);
                    subLog.setComment("");
                    subLog.setComment("款项已清，订单自动从【欠款】转为【已出库】状态");
                    subLogs.add(subLog);
                }

                //添加收银记录
                Shouying shouying = new Shouying();
                shouying.setSubId(Long.valueOf(orderId));
                shouying.setHejim(feiyong);
                shouying.setShouxum(BigDecimal.ZERO);
                shouying.setShouxum1(BigDecimal.ZERO);
                shouying.setInuser(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, serviceType) + "换机");
                shouying.setAreaid(oaUserBO.getAreaId());
                shouying.setDtime(LocalDateTime.now());
                shouying.setShouyingType("订金");
                shouying.setSubPay01(BigDecimal.ZERO);
                shouying.setSubPay02(BigDecimal.ZERO);
                shouying.setSubPay03(BigDecimal.ZERO);
                shouying.setSubPay04(BigDecimal.ZERO);
                shouying.setSubPay05(BigDecimal.ZERO);
                shouying.setSubPay06(feiyong);
                shouying.setSubPay07(BigDecimal.ZERO);
                shouying.setSubPay08(BigDecimal.ZERO);

                shouyingService.save(shouying);

                if (flag) {
                    comment = EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, serviceType) + "换机服务办理，新机单：<a href=\"/addOrder/editOrder?SubID=" + orderId + "\" target=\"_blank\">" + orderId + "</a>，支付订单订金：" + feiyong;
                    shouhouService.saveShouhouLog(shouhouId, comment, oaUserBO.getUserName(), null, false);

                    for (SubLogsNewReq sublog : subLogs) {
                        smallproLogService.addLogs(sublog.getSubId(), sublog.getComment(), oaUserBO.getUserName(), sublog.getShowType() ? 1 : 0);
                    }
                }
            } else {
                return R.error("新机单金额不能低于原购机金额");
            }
        }

        List<Integer> kinds = Arrays.asList(1, 2, 3);
        if (kinds.contains(kind)) {
            this.update(updateWrapper);
        }

        return R.success("操作成功");
    }

    @Override
    public R<String> delJinshuibao(Integer id, Integer shouhouId, Integer serviceType) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        serviceType = serviceType == null ? BaoXiuTypeEnum.JSB.getCode() : serviceType;

        Boolean flag = this.update(new LambdaUpdateWrapper<ShouhouJinshuibao>()
                .set(ShouhouJinshuibao::getIsdel, true)
                .and(bo -> bo.eq(ShouhouJinshuibao::getServiceType, BaoXiuTypeEnum.JSB.getCode()).or().isNull(ShouhouJinshuibao::getServiceType))
                .eq(shouhouId > 0, ShouhouJinshuibao::getShouhouid, shouhouId)
                .eq(id > 0, ShouhouJinshuibao::getId, id));

        if (flag) {
            shouhouService.saveShouhouLog(shouhouId, "撤销" + EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, serviceType), oaUserBO.getUserName(), null, false);
        }
        return R.success("撤销成功");
    }

    /**
     * 计算进水保费用
     *
     * @param price
     * @param tradedate
     * @param serviceType
     * @return
     */
    private BigDecimal calcJinshuibaoFeiyong(BigDecimal price, LocalDateTime tradedate, Integer serviceType) {
        BigDecimal feiyong = BigDecimal.ZERO;
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDateTime tradedateAtEndOfDay = CommonUtils.getEndOfDay(tradedate);
        if (serviceType.equals(BaoXiuTypeEnum.JSB.getCode())) {
            //实际到期后延3天 算为服务截止时间
            Integer offsetDay = 3;
            if (nowTime.isBefore(tradedateAtEndOfDay.plusMonths(3).plusDays(offsetDay))) {
                feiyong = price.multiply(BigDecimal.valueOf(0.35));
            } else if (nowTime.isBefore(tradedateAtEndOfDay.plusMonths(6).plusDays(offsetDay))) {
                feiyong = price.multiply(BigDecimal.valueOf(0.32));
            } else if (nowTime.isBefore(tradedateAtEndOfDay.plusMonths(9).plusDays(offsetDay))) {
                feiyong = price.multiply(BigDecimal.valueOf(0.27));
            } else {
                feiyong = price.multiply(BigDecimal.valueOf(0.24));
            }
        } else if (serviceType.equals(BaoXiuTypeEnum.YHDX.getCode())) {
            Duration duration = Duration.between(LocalDateTime.now(), tradedateAtEndOfDay);
            Long days = duration.toDays();
            Integer month = days.intValue() / 30 - 1;
            feiyong = price.multiply(BigDecimal.valueOf(0.8 - month * 0.02));
        }
        return feiyong;
    }

    @Override
    public ShouHouBasketBo getBasketInfoByShouhouId(Integer shouhouId) {
        return baseMapper.getBasketInfoByShouhouId(shouhouId);
    }
}
