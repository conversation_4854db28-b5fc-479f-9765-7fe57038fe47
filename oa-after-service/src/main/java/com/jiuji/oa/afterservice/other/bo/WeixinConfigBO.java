package com.jiuji.oa.afterservice.other.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 11:55
 * @Description 微信支付模型
 */
@Data
@Accessors(chain = true)
public class WeixinConfigBO implements Serializable {
    private static final long serialVersionUID = -1195854345104194665L;
    /**
     * appId
     */
    private String appId;

    private String appSecret;

    private String mchId;

    private String partnerKey;
    /**
     * 系统记录名称
     */
    private String payName;
    /**
     * nickName
     */
    private String nickName;
    /**
     * 地区限制
     */
    private String limitArea;
    private List<Integer> limitAreaArr;
    /// 租户
    /**
     * 租户
     */
    private Integer xTenant;
    /**
     * 授权id
     */
    private Integer authorizeId;
    /**
     * kinds
     * 1 公众号 2 小程序 3、苹果商城  4 APP 、5 回收 、6 秒付
     */
    private Integer kinds;
    /**
     * 科目
     */
    private String keMu;
    /**
     * 证书路径
     */
    private String certPath;
    /**
     * 地址
     */
    private String hostUrl;
    /**
     * wxId
     */
    private Integer wxId;
}
