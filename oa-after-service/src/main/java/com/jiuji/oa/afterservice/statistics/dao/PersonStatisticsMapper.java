package com.jiuji.oa.afterservice.statistics.dao;

import com.jiuji.oa.afterservice.statistics.bo.person.UserDepartInfoBO;
import com.jiuji.oa.afterservice.statistics.vo.req.PersonStatisticsReq;
import com.jiuji.oa.afterservice.statistics.vo.res.PersonStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * description: <  >
 * translation: <  >
 * date : 2020-10-10 14:14
 *
 * <AUTHOR> leee41
 **/
@Mapper
@Primary
public interface PersonStatisticsMapper {
    /**
     * selectWithNoJoin1
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin1(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin2
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin2(@Param("req") PersonStatisticsReq req,@Param("officeName") String officeName);

    /**
     * selectWithNoJoin3
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin3(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin4
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin4(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin5
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin5(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin6
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin6(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin7
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin7(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin8
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin8(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin9
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin9(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin10
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin10(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin11
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin11(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin12
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin12(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin13
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin13(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin14
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin14(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin15
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin15(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin16
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin16(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin17
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin17(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin18
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin18(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin19
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin19(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin20
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin20(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin21
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin21(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin22
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin22(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin23
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin23(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin24
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin24(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin25
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin25(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin26
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin26(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin27
     *
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin27(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin28
     *
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin28(@Param("req") PersonStatisticsReq req);

    /**
     * selectWithNoJoin28
     *
     * @param req PersonStatisticsReq
     * @return List<PersonStatisticsVO>
     */
    List<PersonStatisticsVO> selectWithNoJoin29(@Param("req") PersonStatisticsReq req);

    /**
     * 查询用户大小区门店信息
     */
    List<UserDepartInfoBO> selectUserDepartInfo(@Param("req") PersonStatisticsReq req);
}
