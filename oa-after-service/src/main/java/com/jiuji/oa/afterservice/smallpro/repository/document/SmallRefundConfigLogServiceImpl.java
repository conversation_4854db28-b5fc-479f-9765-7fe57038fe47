package com.jiuji.oa.afterservice.smallpro.repository.document;

import com.jiuji.oa.afterservice.smallpro.repository.SmallRefundConfigLogRepository;
import com.jiuji.oa.afterservice.smallpro.repository.SmallRefundConfigLogService;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
@Slf4j
public class SmallRefundConfigLogServiceImpl implements SmallRefundConfigLogService {
    @Resource
    private SmallRefundConfigLogRepository smallRefundConfigLogRepository;

    @Autowired
    @Qualifier("ch999oaMongoTemplate")
    private MongoTemplate mongoTemplate;


    @Override
    public void saveSmallProConfigLog(SmallRefundConfigLogDocument smallProConfigLogDocument) {
        Long mkcId = smallProConfigLogDocument.getConfigId();
        List<SmallRefundConfigLogDocument.Conts> cons = smallProConfigLogDocument.getCons();
        Query query = new Query(Criteria.where("_id").is(mkcId));
        Update update = new Update();
        cons.forEach(item -> update.push("conts", item));
        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, SmallRefundConfigLogDocument.class);
        if (updateResult.getModifiedCount() == 0) {
            SmallRefundConfigLogDocument log = new SmallRefundConfigLogDocument();
            log.setConfigId(mkcId).setCons(cons);
            smallRefundConfigLogRepository.save(log);
        }
        log.info("保存日志成功");
    }
}