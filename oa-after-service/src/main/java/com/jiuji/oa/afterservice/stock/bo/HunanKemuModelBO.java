package com.jiuji.oa.afterservice.stock.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * @Description: 湖南科目类
 * @Author: xiaojianbing
 * @Date 2020/9/23
 */
@Data
@Accessors(chain = true)
public class HunanKemuModelBO implements Serializable {
    private static final long serialVersionUID = -4458456048875887363L;
    private Integer authorizeid;
    private Integer ztid;
    private Map<Integer, String> otherZtInfo;
}
