package com.jiuji.oa.afterservice.machine.query;

import com.jiuji.oa.afterservice.common.vo.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2021/8/12 16:01
 */
@Getter
@Setter
@ToString
@ApiModel("分类配置")
public class TestServerCfgProPageQuery extends PageReq {
    @ApiModelProperty("服务id")
    private Integer serverId;
}
