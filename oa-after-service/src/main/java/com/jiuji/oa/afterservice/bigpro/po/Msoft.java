package com.jiuji.oa.afterservice.bigpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 软件接件
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("msoft")
public class Msoft extends Model<Msoft> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 故障描述
     */
    private String problem;

    /**
     * 操作人
     */
    private String inuser;

    /**
     * 串号
     */
    private String imei;

    /**
     * 交易时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradedate;

    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modidate;

    /**
     * 门店
     */
    private String area;

    /**
     * 客户id
     */
    private Long userid;

    /**
     * 商品规格id
     */
    private Integer ppriceid;

    /**
     * 购买地区
     */
    private String buyarea;

    /**
     * 是否提成
     */
    private Boolean isticheng;

    /**
     * 评价
     */
    private Integer pingjia;

    /**
     * 是否返回给客户
     */
    private Boolean isfan;

    /**
     * 当前接件门店id
     */
    private Integer areaid;

    /**
     * 购买门店id
     */
    private Integer buyareaid;

    /**
     * 接件拍照图
     */
    private String imeifid;

    /**
     * 订单id
     */
    private Long subId;

    /**
     * 动态问题id
     */
    private Long evaluateid;

    /**
     * 是否是回收机
     */
    private Integer ishuishou;

    /**
     * 额外服务类型， 1=手机清洁服务
     */
    @TableField("mobileServeiceType")
    private Integer mobileServeiceType;

    /**
     * 故障类型
     */
    private String questionType;

    /**
     * 软件接件拍照图
     */
    private String fidlist;

    /**
     * 现场处理
     */
    private Boolean isxianchang;

    /**
     * 客户姓名
     */
    private String username;

    /**
     * 客户手机号
     */
    private String usermobile;

    /**
     * 预计取机时间
     */
    private String expectdate;

}
