package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouChaoshizengpinMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouChaoshizengpin;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouChaoshizengpinService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


@Service("shouhouChaoshizengpinService")
public class ShouhouChaoshizengpinServiceImpl extends ServiceImpl<ShouhouChaoshizengpinMapper, ShouhouChaoshizengpin> implements ShouhouChaoshizengpinService {


    @Override
    public List<String> judgeZengpinValue(Integer ppriceid, BigDecimal csDays) {
        return baseMapper.judgeZengpinValue(ppriceid,csDays);
    }

    @Override
    public List<String> judgeZengpinValue4Jiuji(Integer ppriceid) {
        return baseMapper.judgeZengpinValue4Jiuji(ppriceid);
    }

    @Override
    public List<String> getZengpinName(Integer ppriceid) {
        return baseMapper.getZengpinName(ppriceid);
    }

    @Override
    public Integer listGift(Integer shouhouid, Integer type) {
        return baseMapper.listGift(shouhouid,type);
    }
}