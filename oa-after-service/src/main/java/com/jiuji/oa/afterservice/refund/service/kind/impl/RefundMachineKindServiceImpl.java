package com.jiuji.oa.afterservice.refund.service.kind.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouReturncb;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouReturncbService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.refund.bo.BuyPriceSubInfoBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.RefundMachineService;
import com.jiuji.oa.afterservice.refund.service.kind.RefundMachineKindService;
import com.jiuji.oa.afterservice.refund.service.way.WechatAlipaySecondsRefundService;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SubCheckStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/12/5 19:32
 */
@Service
@Slf4j
public class RefundMachineKindServiceImpl  extends ParentTuiHuanKindServiceImpl implements RefundMachineKindService {

    @Resource
    private RefundMachineService refundMachineService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(GroupTuihuanFormVo tuihuanForm) {

    }

    @Override
    public List<TuihuanKindEnum> myKind() {
        return Arrays.asList(TuihuanKindEnum.TK,TuihuanKindEnum.TK_LP,TuihuanKindEnum.HQTXH);
    }

    @Override
    protected RefundSubInfoBo getSuInfoWithMaxRefundPrice(Integer subId, TuihuanKindEnum tuihuanKindEnum) {
        RefundMachineService refundMachineService = SpringUtil.getBean(RefundMachineService.class);
        Shouhou shouhou = refundMachineService.getShouhouById(subId);
        ShouhouTuiHuanPo shouhouTuiHuanPo = refundMachineService.getShouhouTuihuanByShouhouId(subId);
        if(shouhouTuiHuanPo != null){
            RefundSubInfoBo rfsi = new RefundSubInfoBo();
            rfsi.setInPrice(shouhouTuiHuanPo.getInprice());
            rfsi.setOrderId(shouhou.getSubId());
            rfsi.setTotalPrice(shouhouTuiHuanPo.getBuypriceM());
            rfsi.setAreaId(shouhou.getBuyareaid());
            rfsi.setSubCheck(SubCheckStatusEnum.FINISHED.getCode());
            rfsi.setBusinessType(DecideUtil.iif(Objects.equals(0,shouhou.getIshuishou()), BusinessTypeEnum.NEW_ORDER.getCode(),BusinessTypeEnum.LP_ORDER.getCode()));
            rfsi.setShouhouAreaId(shouhou.getToareaid());
            rfsi.setUserId(Convert.toInt(shouhou.getUserid()));
            rfsi.setYifuM(shouhouTuiHuanPo.getBuypriceM());
            rfsi.setYingfuM(shouhouTuiHuanPo.getBuypriceM());
            //最大退款直接从请求对象中获取
            rfsi.setMaxRefundPrice(SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE))
                    .map(smrp -> (BigDecimal)smrp).orElseThrow(()-> new CustomizeException("折价后的退款金额不能为空")));
            return rfsi;
        }
        //查询购买价,如果查询不到订单信息,应该返回为空
        BuyPriceSubInfoBo buyPriceAndSubInfo = this.refundMachineService.getBuyPriceAndSubInfo(subId, tuihuanKindEnum);

        if (Objects.isNull(buyPriceAndSubInfo)) {
            return null;
        }
        RefundSubInfoBo rfsi = new RefundSubInfoBo();
        rfsi.setInPrice(buyPriceAndSubInfo.getCostPrice());
        rfsi.setOrderId(buyPriceAndSubInfo.getOrderId());
        rfsi.setTotalPrice(buyPriceAndSubInfo.getTotalPrice());
        rfsi.setAreaId(buyPriceAndSubInfo.getAreaId());
        rfsi.setSubCheck(buyPriceAndSubInfo.getSubCheck());
        rfsi.setBusinessType(buyPriceAndSubInfo.getBusinessType());
        rfsi.setShouhouAreaId(buyPriceAndSubInfo.getShouhouAreaId());
        rfsi.setUserId(buyPriceAndSubInfo.getUserId());
        rfsi.setYifuM(buyPriceAndSubInfo.getYifuMoney());
        rfsi.setYingfuM(buyPriceAndSubInfo.getYifuMoney());
        rfsi.setBasketIds(Collections.singletonList(shouhou.getBasketId()));
        //设置交易时间
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.GROUP_REFUND_ORDER_TRADEDATE, buyPriceAndSubInfo.getTradeDate()));
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.GROUP_REFUND_ORDER_BASKET_IDS, rfsi.getBasketIds()));
        //最大退款直接从请求对象中获取
        rfsi.setMaxRefundPrice(SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE))
                .map(smrp -> (BigDecimal)smrp).orElseThrow(()-> new CustomizeException("折价后的退款金额不能为空")));
        return rfsi;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> checkSubmitCheckAndSet(TuiHuanCheckVo tuiHuanCheckVo) {
        ShouhouTuiHuanPo tuiHuanPo = tuiHuanCheckVo.getTuiHuanPo();
        ShouhouService shouhouService = SpringUtil.getBean(ShouhouService.class);
        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId());
        switch (tuiHuanCheckVo.getProcessStatusEnum()){
            case CHECK1:
                //插入shouhou_returnCb数据
                Shouhou shouhou = shouhouService.getById(tuiHuanPo.getShouhouId());
                Optional<Productinfo> productinfoOpt = Optional.ofNullable(shouhou.getPpriceid()).map(SpringUtil.getBean(ProductinfoService.class)::getProductinfoByPpid);
                ShouhouReturncb shouhouReturncb = new ShouhouReturncb();
                shouhouReturncb.setOldshouhouId(tuiHuanPo.getShouhouId());
                shouhouReturncb.setShzrPrice(productinfoOpt.map(Productinfo::getMemberprice).orElse(null));
                shouhouReturncb.setPpid(shouhou.getPpriceid());
                shouhouReturncb.setTuihuanId(tuiHuanPo.getId());
                shouhouReturncb.setTuihuanKind(tuiHuanPo.getTuihuanKind());
                shouhouReturncb.setGzinfo(tuiHuanPo.getGzinfo());
                shouhouReturncb.setDtime(LocalDateTime.now());
                shouhouReturncb.setInuser(oaUserOpt.map(OaUserBO::getUserName).orElse("系统"));
                SpringUtil.getBean(ShouhouReturncbService.class).saveShouhouReturncb(shouhouReturncb);
                break;
            case CHECK3:
                R<Boolean> srlCheck = SpringUtil.getBean(WechatAlipaySecondsRefundService.class).secondsRefundLimit(tuiHuanCheckVo);
                if(!srlCheck.isSuccess()){
                    //校验不同过记录日志
                    shouhouService.saveShouhouLog(tuiHuanPo.getShouhouId(),srlCheck.getUserMsg(), oaUserOpt.map(OaUserBO::getUserName)
                            .orElse("系统"), null, false);
                    return R.error(srlCheck.getUserMsg());
                }
                break;
            default:
                break;
        }
        return R.success(null);
    }

    @Override
    public ShouhouRefundDetailVo.ProcessStatus processStatusRank(ShouhouRefundDetailVo.ProcessStatus nextStatusParam,
                                                                 ShouhouTuiHuanPo tuiHuanPo, List<ShouhouTuihuanDetailPo> tuihuanDetailPos) {
        List<String> ranks = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId())
                .map(OaUserBO::getRank).orElseGet(Collections::emptyList);
        if (ranks.isEmpty()) {
            return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
        }
        // 九机的权限
        if (XtenantEnum.isJiujiXtenant()){
            return processStatusRank9Ji(ranks, nextStatusParam, tuiHuanPo, tuihuanDetailPos);
        }
        ShouhouRefundDetailVo.ProcessStatus nextStatus = nextStatusParam;
        switch (nextStatus){
            case CHECK1:
                if(
                        Stream.of(RankEnum.SMALL_PRO_CALL_CENTER,RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2,RankEnum.SHOUHOU_TUIHUAN_CHECK2).noneMatch(r -> ranks.contains(r.getCode()))
                ){
                    Shouhou shouhou = SpringUtil.getBean(ShouhouService.class).getById(tuiHuanPo.getShouhouId());
                    if (Duration.between(shouhou.getTradedate(),shouhou.getModidate()).toDays()<= NumberConstant.FIFTEEN){
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"需要权值[{}]/[{}]/[{}]", RankEnum.SMALL_PRO_CALL_CENTER.getCode(),
                                RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2.getCode(),RankEnum.SHOUHOU_TUIHUAN_CHECK2.getCode());
                        return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                    }
                }
                break;
            case CHECK2:
                if(
                        Stream.of(RankEnum.SMALL_PRO_CALL_CENTER,RankEnum.BATCH_TUIHUAN_CHECK2).noneMatch(r -> ranks.contains(r.getCode()))
                ){
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"需要权值[{}]/[{}]", RankEnum.SMALL_PRO_CALL_CENTER.getCode(),
                            RankEnum.BATCH_TUIHUAN_CHECK2.getCode());
                    return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                }
                break;
            case CHECK3:
                // 退款方式的权限细化判断
                for (ShouhouTuihuanDetailPo td : tuihuanDetailPos) {
                    ShouhouRefundDetailVo.ProcessStatus notAllowWork = getNotWorkProcessStatus(ranks, td);
                    if (notAllowWork != null){
                        return notAllowWork;
                    }
                }
                break;
        }
        return nextStatus;
    }

    /**
     * 九机的权限判断
     * @param nextStatusParam
     * @param tuiHuanPo
     * @param tuihuanDetailPos
     * @return
     */
    private ShouhouRefundDetailVo.ProcessStatus processStatusRank9Ji(List<String> ranks, ShouhouRefundDetailVo.ProcessStatus nextStatusParam,
                                                                  ShouhouTuiHuanPo tuiHuanPo, List<ShouhouTuihuanDetailPo> tuihuanDetailPos){

        ShouhouRefundDetailVo.ProcessStatus nextStatus = nextStatusParam;
        switch (nextStatus){
            case CHECK1:
                if(
                        Stream.of(RankEnum.SMALL_PRO_CALL_CENTER,RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK1_9JI).noneMatch(r -> ranks.contains(r.getCode()))
                ){
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"需要权值[{}]/[{}]", RankEnum.SMALL_PRO_CALL_CENTER.getCode(),
                            RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK1_9JI.getCode());
                    return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                }
                break;
            case CHECK2:
                // 特殊退换
                if(ShouhouConstants.TE_SHU_TUI_HUAN.equals(tuiHuanPo.getCheckType())){

                    if(
                            Stream.of(RankEnum.SMALL_PRO_CALL_CENTER,RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_SPECIAL).noneMatch(r -> ranks.contains(r.getCode()))
                    ){
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"需要权值[{}]/[{}]", RankEnum.SMALL_PRO_CALL_CENTER.getCode(),
                                RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_SPECIAL.getCode());
                        return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                    }
                }else if(ShouhouConstants.ZHENG_CHANG_TUI_HUAN.equals(tuiHuanPo.getCheckType())){
                    if(ShouhouConstants.HAS_FAULT.equals(tuiHuanPo.getFaultType())){
                        if(
                                Stream.of(RankEnum.SMALL_PRO_CALL_CENTER,RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_REGULAR_HAS_FAULT).noneMatch(r -> ranks.contains(r.getCode()))
                        ){
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"需要权值[{}]/[{}]", RankEnum.SMALL_PRO_CALL_CENTER.getCode(),
                                    RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_REGULAR_HAS_FAULT.getCode());
                            return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                        }
                    }else if (ShouhouConstants.NO_FAULT.equals(tuiHuanPo.getFaultType())){
                        if(
                                Stream.of(RankEnum.SMALL_PRO_CALL_CENTER,RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_REGULAR_NO_FAULT).noneMatch(r -> ranks.contains(r.getCode()))
                        ){
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"需要权值[{}]/[{}]", RankEnum.SMALL_PRO_CALL_CENTER.getCode(),
                                    RankEnum.SHOUHOU_TUIHUAN_MACHINE_CHECK2_9JI_REGULAR_NO_FAULT.getCode());
                            return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                        }
                    }else {
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"数据异常，故障类型异常，无法判断权限[{}]", tuiHuanPo.getFaultType());
                        return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                    }
                }else {
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.NOT_WORK_RANK,"数据异常，审核类型异常，无法判断权限[{}]", tuiHuanPo.getCheckType());
                    return ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK;
                }
                break;
            case CHECK3:
                // 退款方式的权限细化判断
                for (ShouhouTuihuanDetailPo td : tuihuanDetailPos) {
                    ShouhouRefundDetailVo.ProcessStatus notAllowWork = getNotWorkProcessStatus(ranks, td);
                    if (notAllowWork != null){
                        return notAllowWork;
                    }
                }
                break;
        }
        return nextStatus;
    }
    @Override
    public void forEachThirdRefund(ThirdOriginRefundVo tor, TuihuanKindEnum tuihuanKindEnum) {
        super.forEachThirdRefund(tor, tuihuanKindEnum);
        DouYinCouponLogRes.SubKindEnum subKindsEnum = null;
        switch (tuihuanKindEnum){
            case TK:
            case HQTXH:
                subKindsEnum = DouYinCouponLogRes.SubKindEnum.ORDINARY;
                break;
            case TK_LP:
                subKindsEnum = DouYinCouponLogRes.SubKindEnum.QUALITY;
                break;
            default:
                break;
        }
        //设置抖音不可退
        setDouYinNotRefund(tor, subKindsEnum, true);
    }

}
