package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.RecoverMarketsubinfoMapper;
import com.jiuji.oa.afterservice.bigpro.po.RecoverMarketsubinfo;
import com.jiuji.oa.afterservice.bigpro.service.IRecoverMarketsubinfoService;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 转售详情单（良品详情订单）[责任小组:回收] 服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-04-04
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class RecoverMarketsubinfoServiceImpl extends ServiceImpl<RecoverMarketsubinfoMapper, RecoverMarketsubinfo> implements IRecoverMarketsubinfoService {

    @Override
    public void updatePrice(Integer recoverBasketId, BigDecimal returnPrice) {
        this.lambdaUpdate().eq(RecoverMarketsubinfo::getBasketId,recoverBasketId)
                .set(RecoverMarketsubinfo::getReturnPrice, returnPrice)
                .update();
    }

    @Override
    public List<RecoverMarketsubinfo> selectResale(String imei) {

        return this.baseMapper.selectResale(imei);
    }
    @Override
    public LocalDateTime selectLastReSale(String imei) {

        return this.baseMapper.selectLastReSale(imei);
    }
}
