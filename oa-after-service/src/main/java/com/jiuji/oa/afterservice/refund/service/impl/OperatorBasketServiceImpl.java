package com.jiuji.oa.afterservice.refund.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.refund.dao.OperatorBasketMapper;
import com.jiuji.oa.afterservice.refund.po.OperatorBasket;
import com.jiuji.oa.afterservice.refund.service.OperatorBasketService;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.service.SmallproBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OperatorBasketServiceImpl extends ServiceImpl<OperatorBasketMapper, OperatorBasket> implements OperatorBasketService {

    @Resource
    private SmallproBillService smallproBillService;

    @Override
    public Boolean isCarrierDeduction(List<SmallproBill> smallproBillList) {
        if(CollUtil.isEmpty(smallproBillList)){
            return Boolean.FALSE;
        }
        List<Integer> basketIds = smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList());
        return CollUtil.isNotEmpty(selectOperatorByBasketId(basketIds));
    }

    @Override
    public List<OperatorBasket> selectOperatorByBasketId(List<Integer> basketIdList) {
        return this.lambdaQuery().in(OperatorBasket::getBasketId,basketIdList).list();
    }

    @Override
    public Boolean isCarrierDeduction(Integer smallProId) {
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallProId);
        List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        return isCarrierDeduction(smallproBillList);
    }
}
