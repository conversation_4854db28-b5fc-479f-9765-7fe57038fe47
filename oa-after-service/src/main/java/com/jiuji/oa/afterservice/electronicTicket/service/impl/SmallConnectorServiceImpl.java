package com.jiuji.oa.afterservice.electronicTicket.service.impl;


import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.electronicTicket.enums.ElectronicTicketOrderTypeEnum;
import com.jiuji.oa.afterservice.electronicTicket.enums.ElectronicTicketSourceTypeEnum;
import com.jiuji.oa.afterservice.electronicTicket.service.ConnectorService;
import com.jiuji.oa.afterservice.electronicTicket.vo.*;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 小件接件相关业务
 * <AUTHOR>
 */
@Slf4j
@Service(value = "SmallConnectorServiceImpl")
public class SmallConnectorServiceImpl extends CommonSmallServiceImpl implements ConnectorService  {


    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;


    @Override
    public AfterSalesPropellingMovement createAfterSalesPropellingMovement(FactoryRoute factoryRoute) {
        //获取小件接件信息
        Smallpro smallpro = structureAfterSales(factoryRoute);
        StructureVo structureVo = new StructureVo();
        structureVo.setUserId(smallpro.getUserId())
                .setOrderId(smallpro.getId())
                .setOrderStatus(smallpro.getStats())
                .setOrderType(ElectronicTicketOrderTypeEnum.ORDER_TYPE_ONE.getCode())
                .setIsMobile(Boolean.FALSE);
        //日志进程记录
        TicketLog ticketLog = new TicketLog();
        OaUserBO currentStaffId = currentRequestComponent.getCurrentStaffId();
        ticketLog.setInUser(currentStaffId.getUserName())
                .setMessage("发送接件电子小票")
                .setOrderId(smallpro.getId())
                .setShowType(0);
        saveLog(ticketLog);
        //构建微信推送所需要的数据
        return structurePropellingMovement(structureVo);

    }

    @Override
    public ElectronicTicketAfterShow selectElectronicTicket(FactoryRoute factoryRoute) {
        ElectronicTicketAfterShow electronicTicketAfterShow = structureSmallElectronicTicket(factoryRoute);
        //构建电子小票保存信息
        electronicTicketAfterShow.setAfterElectronicTicket(structureElectronicTicket(factoryRoute));
        return electronicTicketAfterShow;
    }


    @Override
    public void saveVerifyStatusLog(VerifyStatusLog verifyStatusLog) {
        TicketLog ticketLog = new TicketLog();
        String msg;
        if (ElectronicTicketSourceTypeEnum.SCAN.getCode().equals(verifyStatusLog.getSourceType())
                || ElectronicTicketSourceTypeEnum.LOCAL.getCode().equals(verifyStatusLog.getSourceType())) {
            String sourceType = ElectronicTicketSourceTypeEnum.getMessage(verifyStatusLog.getSourceType());
            msg = String.format("用户签署方式：%s，签署信息： <a target='_blank' href='%s'>点击查看</a> ", sourceType, verifyStatusLog.getVerifyUrl());
            ticketLog.setInUser("系统")
                    .setShowType(1)
                    .setIsWeb(true)
                    .setOrderId(verifyStatusLog.getOrderId())
                    .setMessage("用户签署接件小票");
            saveLog(ticketLog);
        } else {
            msg = String.format("接件小票，客户已核实完毕 <a target='_blank' href='%s'>查看签字</a> ", verifyStatusLog.getVerifyUrl());
        }
        ticketLog.setInUser("系统")
                .setShowType(0)
                .setOrderId(verifyStatusLog.getOrderId())
                .setIsWeb(false)
                .setMessage(msg);
        saveLog(ticketLog);
    }

    @Override
    public String getPushUrl(FactoryRoute factoryRoute) {
        //获取小件接件信息
        Smallpro smallpro = structureAfterSales(factoryRoute);
        StructureVo structureVo = new StructureVo();
        structureVo.setUserId(smallpro.getUserId())
                .setOrderId(smallpro.getId())
                .setOrderStatus(smallpro.getStats())
                .setOrderType(ElectronicTicketOrderTypeEnum.ORDER_TYPE_ONE.getCode())
                .setIsMobile(Boolean.FALSE);
        return structureUrl(structureVo);
    }


}
