package com.jiuji.oa.afterservice.bigpro.statistics.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.ShouhouInventoryQueryReq;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ShouhouInventoryRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @Date 2021/11/4 19:23
 * @Description
 */
@Mapper
public interface ShouhouInventoryMapper {

    /**
     * @param req
     * @return
     */
    Page<ShouhouInventoryRes> getShouhouInventory(Page<ShouhouInventoryRes> page,@Param("req") ShouhouInventoryQueryReq req);
}
