package com.jiuji.oa.afterservice.bigpro.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 绑定商品信息Bo
 * @author: gengjiaping
 * @date: 2021/07/13
 */
@Getter
@Setter
@ToString
@ApiModel("绑定商品信息Bo")
@NoArgsConstructor
public class BindPpidInfoBo {
    /**
     * 绑定id
     */
    @ApiModelProperty("绑定id")
    private Integer id;
    /**
     * ppid
     */
    @ApiModelProperty(value = "ppid")
    private Integer ppid;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String name;
    /**
     * 剩余可用库存
     */
    @ApiModelProperty(value = "剩余可用库存")
    private Integer leftCount;
    /**
     * 允许最大出库数量
     */
    @ApiModelProperty(value = "允许最大出库数量")
    private Integer outPutLimit;
    /**
     * 是否允许负库存操作
     */
    @ApiModelProperty(value = "是否允许负库存操作")
    private Boolean negative;
    /**
     * 出库数量
     */
    @ApiModelProperty("出库数量")
    private Integer outPutNumber;

    public static BindPpidInfoBo of(Integer id, Integer ppid, Integer outPutNumber) {
        BindPpidInfoBo bindPpidInfoBo = new BindPpidInfoBo();
        bindPpidInfoBo.setId(id);
        bindPpidInfoBo.setPpid(ppid);
        bindPpidInfoBo.setOutPutNumber(outPutNumber);
        return bindPpidInfoBo;
    }
}
