package com.jiuji.oa.afterservice.bigpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 售后测试结果
 * @TableName shouhou_test_result
 */
@TableName(value ="shouhou_test_result")
@Data
public class ShouhouTestResult implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 售后测试id
     */
    private Long shouhouTestInfoId;

    /**
     * 测试项id
     */
    private Long testAttrId;

    /**
     * 测试项名称
     */
    private String testAttrName;

    /**
     * 测试项类型
     */
    private String testAttrType;
    /**
     * 是否必填
     */
    private Integer isRequired;
    /**
     * 测试项值标签
     */
    private String attrItemLable;
    /**
     * 测试值
     */
    private String testValue;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableLogic
    private Integer isDelete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}