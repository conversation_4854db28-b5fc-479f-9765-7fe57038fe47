package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.YuyueLockProductInfoBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouYuyuelockppidsMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyuelockppids;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyuelockppidsService;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */
@Service
public class ShouhouYuyuelockppidsServiceImpl extends ServiceImpl<ShouhouYuyuelockppidsMapper, ShouhouYuyuelockppids> implements ShouhouYuyuelockppidsService {

    @Override
    public Boolean isLockWxPeijian(Integer yyid, Integer ppid) {
       Integer count = baseMapper.selectCount(new LambdaQueryWrapper<ShouhouYuyuelockppids>().eq(ShouhouYuyuelockppids::getYyid,yyid).eq(ShouhouYuyuelockppids::getPpid,ppid));
        if(count > 0){
            return true;
        }
       return false;
    }

    @Override
    public Boolean saveYuyueLockppids(Integer yyid, Integer ppid, Integer areaId) {
        ShouhouYuyuelockppids shouhouYuyuelockppids = new ShouhouYuyuelockppids();
        shouhouYuyuelockppids.setYyid(yyid);
        shouhouYuyuelockppids.setAreaid(areaId);
        shouhouYuyuelockppids.setPpid(ppid);
        return this.save(shouhouYuyuelockppids);
    }

    @Override
    public Boolean delYuyueLockppids(Integer yyid, Integer ppid) {
        return this.remove(new LambdaQueryWrapper<ShouhouYuyuelockppids>().eq(ShouhouYuyuelockppids::getYyid,yyid).eq(ShouhouYuyuelockppids::getPpid,ppid));
    }

    @Override
    public List<YuyueLockProductInfoBo> getYuyueLockProductList(Integer yyId,Integer areaId) {
        if(CommenUtil.isNullOrZero(yyId)){
            return null;
        }
        return baseMapper.getYuyueLockProductList(yyId,areaId);
    }

}
