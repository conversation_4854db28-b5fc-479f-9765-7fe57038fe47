package com.jiuji.oa.afterservice.bigpro.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 锁定的维修配件
 * @author: gengjiaping
 * @date: 2020/3/13
 */
@ApiModel
@Data
public class LockWxpjBo {
    @ApiModelProperty(value = "商品id")
    private String pid;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品规格")
    private String productColor;
    @ApiModelProperty(value = "ppid")
    private Integer ppid;
    /**
     * 锁定的门店id
     */
    @ApiModelProperty(value = "锁定的门店")
    private Integer lockAreaId;

    @ApiModelProperty(value = "门店")
     private String area;

   /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String faultDes;
    @ApiModelProperty(value = "商品价格")
    private BigDecimal price;
    /**
     * 会员价格
     */
    private BigDecimal memberPrice;
    @ApiModelProperty(value = "标题")
    private String repParentTitle;
    @ApiModelProperty(value = "故障id")
    private Integer toubleId;
    @ApiModelProperty(value = "库存")
    private Integer stock;
    @ApiModelProperty(value = "配件是否锁定")
    private Boolean isLock;
    @ApiModelProperty(value = "实时库存数量")
    private Integer currentStock;
    /**
     * 商品条码
     */
    private List<String> productbarcodeList;
}
