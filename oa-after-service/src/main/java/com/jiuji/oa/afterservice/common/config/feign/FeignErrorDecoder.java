package com.jiuji.oa.afterservice.common.config.feign;

import cn.hutool.core.util.StrUtil;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * @Description:
 * @author: Li quan
 * @date: 2020/7/27 16:37
 */

@Configuration
@Slf4j
public class FeignErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, Response response) {
        try {
            // 这里直接拿到我们抛出的异常信息
            String message = StrUtil.format("响应内容: {}",Util.toString(response.body().asReader()));
            log.error(message);
            return new RuntimeException(message);
        } catch (IOException ignored) {
        }
        return decode(methodKey, response);
    }

}
