package com.jiuji.oa.afterservice.stock.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_kc_pan_set")
public class ProductKcPanSet extends Model<ProductKcPanSet> {

    private static final long serialVersionUID = 5140990717349435150L;

    private Integer areaid;

    @TableField("lastTime")
    private LocalDateTime lastTime;

    private LocalDateTime dtime;

    private String inuser;

    private String ppriceids;

    private String kinds;

    private Boolean islock;

    @TableField("lockTime")
    private LocalDateTime lockTime;

    @TableField("lockUser")
    private String lockUser;

    @Override
    protected Serializable pkVal() {
        return this.areaid;
    }

}
