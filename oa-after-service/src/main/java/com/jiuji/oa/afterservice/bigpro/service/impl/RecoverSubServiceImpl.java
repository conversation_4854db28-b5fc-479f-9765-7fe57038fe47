package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.RecoverSubMapper;
import com.jiuji.oa.afterservice.bigpro.po.RecoverSub;
import com.jiuji.oa.afterservice.bigpro.service.RecoverSubService;
import org.springframework.stereotype.Service;


@Service("recoverSubService")
public class RecoverSubServiceImpl extends ServiceImpl<RecoverSubMapper, RecoverSub> implements RecoverSubService {

}