package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.RepairPlanDetailBO;
import com.jiuji.oa.afterservice.bigpro.dto.RepairPlanSaveDTO;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlanMaster;
import com.jiuji.oa.afterservice.bigpro.vo.RepairPlanDetailVO;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanConfirmReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.RepairPlanDetailReq;

/**
 * <p>
 * 维修方案主表 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
public interface RepairPlanMasterService extends IService<RepairPlanMaster> {

    /**
     * 根据售后单ID查询维修方案详情
     *
     * @param shouHouId 售后单ID
     * @return 维修方案详情VO
     */
    RepairPlanDetailVO getRepairPlanDetailByShouHouId(RepairPlanDetailReq req);
    
    /**
     * 保存或更新维修方案（包含故障和方案）
     *
     * @param saveDTO 维修方案保存DTO
     * @return 主表ID
     */
    Integer saveOrUpdateRepairPlan(RepairPlanSaveDTO saveDTO);
    
    /**
     * 确认维修方案
     *
     * @param req 确认维修方案请求
     * @return 成功确认的维修方案数量
     */
    Integer confirmRepairPlan(RepairPlanConfirmReq req);

    /**
     * 大疆消息推送
     * @param shouHouId
     */
    void sendDjMsg(Integer shouHouId);
} 