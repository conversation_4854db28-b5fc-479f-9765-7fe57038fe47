package com.jiuji.oa.afterservice.bigpro.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 帮定阿里用户表
 * <AUTHOR>
 * @since 2021/5/28 17:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shouhou_waisong_bing_user")
@ApiModel("外送报账绑定阿里用户PO")
public class WaiSongAlipayBindUserPO extends Model<WaiSongAlipayBindUserPO> {
    @ApiModelProperty("ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("维修ID")
    private Integer wxId;
    @ApiModelProperty("维修配件ID")
    private Integer wxkId;
    @ApiModelProperty("支付宝用户ID")
    private String userId;
    @ApiModelProperty("支付宝用户图片")
    private String userAvatar;
    @ApiModelProperty("支付宝用户昵称")
    private String nickname;
    @ApiModelProperty("物流id")
    @TableField("logistics_id")
    private Integer logisticsId;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("创建人")
    private String createUser;
    @ApiModelProperty("创建人id")
    private Integer createUserId;
    @ApiModelProperty("是否删除")
    @TableField("is_del")
    @TableLogic(value = "0",delval = "1")
    private Boolean del;
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
