package com.jiuji.oa.afterservice.stock.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.stock.bo.ProductKcCheckStatus;
import com.jiuji.oa.afterservice.stock.po.ProductMkc;
import com.jiuji.oa.afterservice.stock.vo.AggregatedStockVO;
import com.jiuji.oa.afterservice.stock.vo.ShouhouMkcPageVO;
import com.jiuji.oa.afterservice.stock.vo.req.AggregatedStockHttpReq;
import com.jiuji.oa.afterservice.stock.vo.req.ShouhouMkcQuery;
import com.jiuji.oa.afterservice.stock.vo.res.MkcDelQueryRes;
import com.jiuji.oa.afterservice.stock.vo.res.MkcInpriceInfoRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2020/3/20
 */
@Mapper
public interface ProductMkcMapper extends BaseMapper<ProductMkc> {

    int batchInsert(List<ProductMkc> list);

    Integer countShouhouMkc(@Param("req") ShouhouMkcQuery req);

    List<ProductMkc> listByMkcId(@Param("mkcIdList") Collection<Integer> mkcIdList);

    /**
     * 售后转出-分页查询
     *
     * @param current
     * @param size
     * @param req
     * @return
     */
    List<ShouhouMkcPageVO> pageShouhouMkc(@Param("current") long current, @Param("size") long size, @Param("req") ShouhouMkcQuery req);

    /**
     * 售后批量转现
     *
     * @param mkcIds
     * @return
     */
    Integer afterBatchZx(@Param("mkcIds") List<Integer> mkcIds);

    /**
     * 通过串号获取mkcId
     *
     * @param imei
     * @return
     */
    List<Integer> getMkcIdByImei(@Param("imei") String imei);

    Integer aggregateCountSmallAndRepair(@Param("req") AggregatedStockHttpReq req);

    List<AggregatedStockVO> getStockHQSmallAndRepair(@Param("offset") long offset, @Param("pSize") long pSize, @Param("req") AggregatedStockHttpReq req);

    AggregatedStockVO sumAll(@Param("req") AggregatedStockHttpReq req);

    String company();

    List<Integer> queryInStock(@Param("batchReturnMkcId") List<Integer> batchReturnMkcId);

    String getMkcKindsValue(@Param("id") Integer id);

    MkcDelQueryRes getMkcDealLogsInfo(@Param("id") Integer id);

    MkcDelQueryRes getRecoverMkcDealLogsInfo(@Param("id") Integer id);

    MkcDelQueryRes getRecoverMkcDealLogCheck(@Param("mkcId") Integer mkcId,@Param("kinds") String kinds,@Param("curAreaId") Integer curAreaId);

    MkcDelQueryRes getRecoverMkcDealLogXcjCheck(@Param("mkcId") Integer mkcId,@Param("kinds") String kinds,@Param("curAreaId") Integer curAreaId);

    /**
     * 获取库存成本价
     * @param ppid
     * @return
     */
    MkcInpriceInfoRes getInPriceByPpid(@Param("ppid") Integer ppid);

    /**
     * 记录退换亏损管理中售后转出时的售价
     * @param mkcId
     * @return
     */
    Boolean updateShouhouReturnCbShzcPrice(@Param("mkcId") Integer mkcId);

    ProductKcCheckStatus getProductKcCheckStatus(@Param("mkcId") Integer mkcId);

    /**
     * 获取原订单号
     * @param mkcId
     * @return
     */
    Integer getSubIdByMkcId(@Param("mkcId") Integer mkcId);

    /**
     * 根据mkcId 获取售后基本信息
     * @param mkcId
     * @return
     */
    Shouhou getShouHouBasicInfoByMkcId(@Param("mkcId") Integer mkcId);

    /**
     * 校验是否有在进行中的退款信息
     * @param shouhouId
     * @return
     */
    Integer checkTuiHuanByShouHouId(@Param("shouhouId") Integer shouhouId);

    /**
     * 校验时候有转地区的售后单
     * @param shouhouId
     * @return
     */
    Integer checkToareaByShouHouId(@Param("shouhouId") Integer shouhouId);

    /**
     * 查询三个月的mkcId
     * @param imei
     * @param startTime
     * @param endTime
     * @return
     */
    List<Integer> LoadImeiThreeMonthCache(@Param("imei") String imei, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 跟新售后库存
     *
     * @param areaId 门店id
     * @param mkcId 库存记录id
     * @return
     */
    int updateShouhouMkc(@Param("areaId") Integer areaId, @Param("mkcId") Long mkcId);
}
