package com.jiuji.oa.afterservice.bigpro.statistics.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.ShouhouLossCheckEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.ShouhouLossOldStatsEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.ShouhouLossServiceTypeEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.service.ShouhouLossCheckService;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.ShouhouLossListReq;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ShouhouLossListRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 售后维修单亏损额度审核相关
 * @author: Li quan
 * @date: 2020/8/21 16:38
 */

@RestController
@RequestMapping("/api/bigpro/shouhou/lossCheck")
@Api(tags = "售后统计: 维修单亏损额度审核")
public class ShouhouLossCheckController {

    @Autowired
    private ShouhouLossCheckService shouhouLossCheckService;

    @GetMapping("/getEnums")
    @ApiOperation(value = "获取查询枚举")
    public R<Map<String, List<EnumVO>>> getEnums() {
        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        List<EnumVO> checkEnum = EnumUtil.toEnumVOList(ShouhouLossCheckEnum.class);
        List<EnumVO> oldStatsEnum = EnumUtil.toEnumVOList(ShouhouLossOldStatsEnum.class);
        List<EnumVO> serviceTypeEnum = EnumUtil.toEnumVOList(ShouhouLossServiceTypeEnum.class);
        enumMap.put("checkEnum",checkEnum);
        enumMap.put("oldStatsEnum",oldStatsEnum);
        enumMap.put("serviceTypeEnum",serviceTypeEnum);
        return R.success(enumMap);
    }

    @PostMapping("/getShouhouLossList")
    @ApiOperation(value = "亏损额度查询列表")
    public R<Page<ShouhouLossListRes>> getShouhouLossList(@RequestBody ShouhouLossListReq req) {
        return shouhouLossCheckService.getShouhouLossList(req);
    }

}
