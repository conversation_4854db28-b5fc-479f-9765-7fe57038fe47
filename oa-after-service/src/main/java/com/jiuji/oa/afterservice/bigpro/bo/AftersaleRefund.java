package com.jiuji.oa.afterservice.bigpro.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Description: 售后代用机押金退款
 * @author: Li quan
 * @date: 2020/7/14 19:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AftersaleRefund {
    /**
     * 支付/退款方式(alipay,weixin)
     */
    private String payport;

    /**
     * 退款流水号
     */
    private String trade_no;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 退款单号
     */
    private String sub_id;

    /**
     * 备注
     */
    private String reason;

    /**
     * 退款方式
     */
    private String paytype;

    /**
     * 租户
     */
    private Integer xtenant;
}
