package com.jiuji.oa.afterservice.channel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.po.Ok3wQudao;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.Ok3wQudaoService;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.vo.res.QudaoUserRes;
import com.jiuji.oa.afterservice.channel.GPSConverterUtils;
import com.jiuji.oa.afterservice.channel.bo.GPS;
import com.jiuji.oa.afterservice.channel.dao.ShouhouChannelMapper;
import com.jiuji.oa.afterservice.channel.enums.*;
import com.jiuji.oa.afterservice.channel.po.ShouhouChannel;
import com.jiuji.oa.afterservice.channel.service.ShouhouChannelService;
import com.jiuji.oa.afterservice.channel.vo.req.ShouhouChannelQueryReq;
import com.jiuji.oa.afterservice.channel.vo.req.ShouhouJieJianMaterialQueryReq;
import com.jiuji.oa.afterservice.channel.vo.res.BrandInfo;
import com.jiuji.oa.afterservice.channel.vo.res.ShouhouChannelInfo;
import com.jiuji.oa.afterservice.channel.vo.res.ShouhouChannelListRes;
import com.jiuji.oa.afterservice.channel.vo.res.ShouhouJieJianMaterial;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.enums.EnumUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 售后渠道扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-25
 */
@Service
public class ShouhouChannelServiceImpl extends ServiceImpl<ShouhouChannelMapper, ShouhouChannel> implements ShouhouChannelService {

    @Autowired
    private Ok3wQudaoService ok3wQudaoService;
    @Autowired
    private ShouhouService shouhouService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private ProductinfoService productinfoService;

    @Override
    public Page<ShouhouChannelListRes> getShouhouChannelListPage(ShouhouChannelQueryReq req) {

        Page<ShouhouChannelListRes> page = new Page<>();
        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        page = baseMapper.getChannelListPage(page, req);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        List<ShouhouChannelListRes> channelList = page.getRecords();
        channelList = channelList.stream().map(e -> buildChannelListResult(e)).collect(Collectors.toList());

        page.setRecords(channelList);
        return page;
    }

    @Override
    public ShouhouChannelInfo getShouhouChannelDetail(Integer channelId) {
        ShouhouChannelInfo res = new ShouhouChannelInfo();
        ShouhouChannelInfo.ShouhouChannelCompanyInfo companyInfo = new ShouhouChannelInfo.ShouhouChannelCompanyInfo();
        ShouhouChannelInfo.ShouhouFinanceInfo financeInfo = new ShouhouChannelInfo.ShouhouFinanceInfo();

        LambdaQueryWrapper<ShouhouChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShouhouChannel::getChannelId, channelId);
        ShouhouChannel channelInfo = super.getOne(queryWrapper);
        Ok3wQudao ok3wQudao = ok3wQudaoService.getById(channelId);

        if (ok3wQudao != null) {
            BeanUtils.copyProperties(ok3wQudao, companyInfo);
            BeanUtils.copyProperties(ok3wQudao, financeInfo);
            if (channelInfo == null){
                channelInfo = new ShouhouChannel();
                channelInfo.setChannelId(channelId);
            }else{
                if (channelInfo.getCooperationStatus() != null){
                    channelInfo.setCooperationStatusText(EnumUtil.getMessageByCode(ShouhouChannelCooperationStatusEnum.class,channelInfo.getCooperationStatus()));
                }
            }
            res.setChannelInfo(channelInfo);
            res.setCompanyInfo(companyInfo);
            res.setFinanceInfo(financeInfo);
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> saveOrUpdateChannelInfo(ShouhouChannelInfo channelInfo) {
        //保存或修改售后渠道信息
        ShouhouChannelInfo.ShouhouChannelCompanyInfo companyInfo = channelInfo.getCompanyInfo();
        Ok3wQudao ok3wQudao = ok3wQudaoService.getById(companyInfo.getId());
        if (ok3wQudao == null) {
            return R.error("渠道信息不存在");
        }

        ShouhouChannel channel = channelInfo.getChannelInfo();
        ShouhouChannelInfo.ShouhouFinanceInfo financeInfo = channelInfo.getFinanceInfo();
        BeanUtils.copyProperties(companyInfo, ok3wQudao);
//        BeanUtils.copyProperties(financeInfo, ok3wQudao);
        boolean flag = ok3wQudaoService.saveOrUpdate(ok3wQudao);

        if (flag && channel != null) {
            //高德地图坐标系转换
            GPS gps = GPSConverterUtils.gcj_To_Gps84(Double.valueOf(channel.getLatitude()), Double.valueOf(channel.getLongitude()));
            channel.setLongitude(String.valueOf(gps.getLon()));
            channel.setLatitude(String.valueOf(gps.getLat()));
            super.saveOrUpdate(channel);
        }
        return flag ? R.success("保存或更新成功") : R.error("保存或更新失败");
    }

    @Override
    public R<List<ShouhouJieJianMaterial>> getJieJianMaterial(ShouhouJieJianMaterialQueryReq req) {
        LambdaQueryWrapper<Ok3wQudao> qudaoQueryWrapper = new LambdaQueryWrapper<>();
        String trimBrand = req.getBrand().trim();
        qudaoQueryWrapper.like(Ok3wQudao::getCompany, trimBrand);
        qudaoQueryWrapper.eq(req.getCityId() != null, Ok3wQudao::getCityid, req.getCityId());
        List<Ok3wQudao> qudaoList = ok3wQudaoService.list(qudaoQueryWrapper);
        if (CollectionUtils.isEmpty(qudaoList)) {
            return R.error("当前品牌渠道信息不存在");
        }
        List<ShouhouJieJianMaterial> resList = null;
        LambdaQueryWrapper<ShouhouChannel> channelQueryWrapper = new LambdaQueryWrapper<>();
        List<Integer> qudaoIdList = qudaoList.stream().map(e -> e.getId()).collect(Collectors.toList());
        channelQueryWrapper.in(ShouhouChannel::getChannelId, qudaoIdList);
        List<ShouhouChannel> channelList = super.list(channelQueryWrapper);

        resList = qudaoList.stream().map(e -> {
            ShouhouJieJianMaterial res = new ShouhouJieJianMaterial();
            BeanUtils.copyProperties(e, res);
            ShouhouChannel channel = channelList.stream().filter(c -> Objects.equals(e.getId(), c.getChannelId())).findFirst().orElse(null);
            if (channel != null) {
                BeanUtils.copyProperties(channel, res);
            }
            if (e.getIspass() != null) {
                res.setCooperationStatusText(EnumUtil.getMessageByCode(ShouhouChannelCooperationStatusEnum.class, e.getIspass()));
            }
            res.setChannelId(e.getId());
            return res;
        }).collect(Collectors.toList());

        return R.success(resList);
    }

    @Override
    public R<List<BrandInfo>> getBrandList(String key) {
        return R.success(baseMapper.getBrandList(key, CommenUtil.isNumer(key)));
    }

    @Override
    public R<List<QudaoUserRes>> autoQudaoUserList(Integer shouhouId) {
        Shouhou shouhou = shouhouService.getById(shouhouId);
        if (shouhou == null) {
            return R.error("维修单号错误");
        }
        Integer brandId = shouhou.getBrandId();
        String productName = shouhou.getName();
        Integer areaId = shouhou.getAreaid();

        //该接口需要兼容老的数据
        if (StringUtils.isEmpty(productName) && CommenUtil.isNullOrZero(brandId)) {
            return R.error("无法匹配到当前机型信息");
        }
        CompletableFuture<R<AreaInfo>> future = CompletableFuture.supplyAsync(() -> areaInfoClient.getAreaInfoById(areaId));
        List<Ok3wQudao> ok3wQudaoList = new LinkedList<>();
        if (CommenUtil.isNotNullZero(brandId)) {
            //查询对应的品牌机型名称
            List<BrandInfo> brandInfoList = baseMapper.getBrandList(brandId.toString(), Boolean.TRUE);
            BrandInfo brandInfo = brandInfoList.stream().filter(b -> Objects.equals(brandId, b.getBrandId())).findAny().orElse(null);
            if (brandInfo != null) {
                //根据品牌名查询售后渠道信息
                String name = brandInfo.getName();
                ok3wQudaoList = getQudaoListByBrandName(name, future);
            }
        } else {
            List<BrandInfo> brandList = baseMapper.getBrandList(null, null);
            BrandInfo brandInfo = brandList.stream().filter(t -> productName.contains(t.getName()))
                    .findAny().orElse(null);
            if (brandInfo != null) {
                //根据品牌名查询售后渠道信息
                String name = brandInfo.getName();
                ok3wQudaoList = getQudaoListByBrandName(name, future);
            }
        }

        if (CollectionUtils.isEmpty(ok3wQudaoList)) {
            return R.error("无法自动匹配到当前机型渠道信息，请手动选择");
        }

        List<QudaoUserRes> resList = ok3wQudaoList.stream().map(e -> {
            QudaoUserRes res = new QudaoUserRes();
            BeanUtils.copyProperties(e, res);
            return res;
        }).collect(Collectors.toList());

        return R.success(resList);
    }

    @Override
    public R<List<QudaoUserRes>> getQudaoListByPpidAndAreaId(Integer ppid, Integer areaId) {

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
        Integer cityId= 0;
        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null){
            cityId = areaInfoR.getData().getCityId();
        }

        List<Productinfo> list = productinfoService.list(new LambdaQueryWrapper<Productinfo>()
                .select(Productinfo::getProductName, Productinfo::getPpriceid).eq(Productinfo::getPpriceid, ppid));
        if (CollectionUtils.isEmpty(list)){
            return R.error("商品信息不存在");
        }
        Productinfo productinfo = list.get(0);

        String productName = productinfo.getProductName()
                .replaceAll("（","")
                .replaceAll("）","")
                .replaceAll("\\(","")
                .replaceAll("\\)","");
        List<BrandInfo> brandList = baseMapper.getBrandList(null, null);
        List<Integer> brandIdList = brandList.stream().filter(t -> productName.contains(t.getName()))
                .map(t->t.getBrandId()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(brandIdList)){
            return R.error("当前机型渠道信息不存在");
        }
        List<Ok3wQudao> ok3wQudaoList = ok3wQudaoService.list(new LambdaQueryWrapper<Ok3wQudao>()
                .in(Ok3wQudao::getBrandid, brandIdList)
                .likeRight(CommenUtil.isNotNullZero(cityId),Ok3wQudao::getCityid,cityId));

        List<QudaoUserRes> resList = ok3wQudaoList.stream().map(e -> {
            QudaoUserRes res = new QudaoUserRes();
            BeanUtils.copyProperties(e, res);
            return res;
        }).collect(Collectors.toList());

        return R.success(resList);
    }


    private ShouhouChannelListRes buildChannelListResult(ShouhouChannelListRes e) {
        if (e.getIspass() != null) {
            e.setCooperationStatusText(EnumUtil.getMessageByCode(ShouhouChannelCooperationStatusEnum.class, e.getIspass()));
        }
        return e;
    }

    private List<Ok3wQudao> getQudaoListByBrandName(String name, CompletableFuture<R<AreaInfo>> future) {
        List<Ok3wQudao> ok3wQudaoList = null;
        LambdaQueryWrapper<Ok3wQudao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Ok3wQudao::getKinds, 8);
        if (name.indexOf("（") > 0 && name.indexOf("）") > 0) {
            String frontName = name.substring(0, name.indexOf("（"));
            String backName = name.substring(name.indexOf("（") + 1, name.indexOf("）"));
            queryWrapper.like(Ok3wQudao::getCompany, frontName).or().like(Ok3wQudao::getCompany, backName);
        } else {
            queryWrapper.like(Ok3wQudao::getCompany, name);
        }

        ok3wQudaoList = ok3wQudaoService.list(queryWrapper);
        R<AreaInfo> areaInfoR = future.join();
        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
            Integer cityId = areaInfoR.getData().getCityId();
            List<Ok3wQudao> filterList = ok3wQudaoList.stream().filter(e -> Objects.equals(e.getCityid(), cityId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)) {
                ok3wQudaoList = filterList;
            }
        }
        return ok3wQudaoList;
    }

}
