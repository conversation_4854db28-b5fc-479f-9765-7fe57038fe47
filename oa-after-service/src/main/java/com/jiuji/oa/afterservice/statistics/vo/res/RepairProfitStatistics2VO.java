package com.jiuji.oa.afterservice.statistics.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

/**
 * description: < 维修毛利分析VO >
 * translation: <  >
 * date : 2021-04-09 16:23
 *
 * <AUTHOR> xxk
 **/
@Data
@ApiModel(value = "维修毛利分析第二版VO")
@Slf4j
@Accessors(chain = true)
public class RepairProfitStatistics2VO implements Serializable {
    private static final long serialVersionUID = -8797439569239740664L;

    @ApiModelProperty(value = "地区")
    private String area;
    @ApiModelProperty(value = "地区ID")
    private Integer areaId;
    private Integer departRank;
    private String areaLevel1Code;
    private String areaLevel2Code;

    private Integer bigAreaDeptId;

    private Integer smallAreaDept;

    private String bigArea;

    private String smallArea;
    private Integer dataType;

    /**
     * 每个类型对应的统计信息
     */
    private Map<String,StatisticsVO> statisticsMap;

    @Data
    public static class StatisticsVO{
        @ApiModelProperty(value = "有毛利数量")
        private Integer hasProfitNum;
        @ApiModelProperty(value = "无毛利数量")
        private Integer noProfitNum;
        @ApiModelProperty(value = "毛利")
        @JSONField(format = "0.##@HALF_UP")
        private BigDecimal profit;

        /**
         * 单毛=毛利/有毛利销量
         */
        private BigDecimal singleGrossProfit;

        public static StatisticsVO from(RepairProfitStatisticsVO repairProfitStatistics){
            StatisticsVO statisticsVO = new StatisticsVO();
            Optional<RepairProfitStatisticsVO> rpsOpt = Optional.ofNullable(repairProfitStatistics);

            statisticsVO.setProfit(rpsOpt.map(RepairProfitStatisticsVO::getProfit).orElse(BigDecimal.ZERO));
            statisticsVO.setHasProfitNum(rpsOpt.map(RepairProfitStatisticsVO::getHasProfitNum).orElse(0));
            statisticsVO.setNoProfitNum(rpsOpt.map(RepairProfitStatisticsVO::getNoProfitNum).orElse(0));
            return statisticsVO;
        }
    }


}
