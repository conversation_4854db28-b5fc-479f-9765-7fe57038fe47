package com.jiuji.oa.afterservice.bigpro.statistics.enums.strategy.statistics;

import com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes;

/**
 * 预约单量
 * 预约单完成量
 * 预约单取消量
 */
public class YuYueDanStrategy implements StatisticsStrategy {
    @Override
    public ShouHouStatisticsRes copyProperties(ShouHouStatisticsRes res, ShouHouStatisticsRes target) {
        target.setYuYueDanLiang(res.getYuYueDanLiang());
        target.setYuYueDanWanChengLiang(res.getYuYueDanWanChengLiang());
        target.setYuYueDanQuXiaoLiang(res.getYuYueDanQuXiaoLiang());
        return target;
    }
}
