package com.jiuji.oa.afterservice.common.util;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.google.common.collect.Maps;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.common.bo.Platform;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import jodd.typeconverter.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * @author: gengjiaping
 * @date: 2020/4/2
 */
@Slf4j
public class CommenUtil {

    private final static String CHARACTERS = "jab1cfG0gRh7iHklnSo3pZqMrstFuUv6wBxzAEC2eDImKyLdNJ8P4QV5W9XTOY";

    private final static Map<Character, Integer> CHINA_NUMBER_MAP;

    private final static Integer JIU_JI_MAX_XTENANT = 1000;

    static {
        HashMap<Character, Integer> chinaNumMap = new HashMap<>(9);
        chinaNumMap.put('一', 1);
        chinaNumMap.put('二', 2);
        chinaNumMap.put('三', 3);
        chinaNumMap.put('四', 4);
        chinaNumMap.put('五', 5);
        chinaNumMap.put('六', 6);
        chinaNumMap.put('七', 7);
        chinaNumMap.put('八', 8);
        chinaNumMap.put('九', 9);
        CHINA_NUMBER_MAP = Collections.unmodifiableMap(chinaNumMap);
        ;
    }

    /**
     * 门店 printName 转换
     *
     * @param printName
     * @return
     */
    public static String toPrintName(String printName) {
        if (StringUtils.isEmpty(printName)) {
            return printName;
        }
        if ("九机网".equals(printName)) {
            return "九机";
        }
        return printName;
    }

    /**
     * 判断Integer类型值是否为空和0
     *
     * @param data
     * @return
     */
    public static boolean isNotNullZero(Integer data) {
        if (data != null && data != 0) {
            return true;
        }
        return false;
    }

    public static boolean isNullOrZero(Integer data) {
        if (data == null || data == 0) {
            return true;
        }
        return false;
    }

    public static boolean isNotNullZero(Long data) {
        if (data != null && data != 0L) {
            return true;
        }
        return false;
    }

    public static boolean isNullOrZero(Long data) {
        if (data == null || data == 0L) {
            return true;
        }
        return false;
    }

    public static boolean isJiuJiXtenant(Integer xtenant) {
        return xtenant < JIU_JI_MAX_XTENANT;
    }

    /**
     * 是否是数字
     *
     * @param str
     * @return
     */
    public static boolean isNumer(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        } else {
            for (int index = 0; index < str.length(); ++index) {
                if (!Character.isDigit(str.charAt(index))) {
                    return false;
                }
            }

            return true;
        }
    }

    public static boolean isCheckTrue(Boolean flag) {
        if (flag != null && flag) {
            return true;
        }
        return false;
    }

    /**
     * 对象装换map
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> transBean2Map(Object obj) {
        if (obj == null) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                // 过滤class属性
                if (!key.equals("class") && !key.equals("pageNo") && !key.equals("pageSize")) {
                    // 得到property对应的getter方法
                    Method getter = property.getReadMethod();
                    Object value = getter.invoke(obj);
                    map.put(key, value);
                }
            }
        } catch (Exception e) {
            System.out.println("transBean2Map Error " + e);
        }
        return map;
    }

    /**
     * 发送响应流方法
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            log.error("encoder error -> fileName:" + fileName);
        }
    }

    public static List<List<String>> getOverInList(List<String> inUserNameList) {
        List<String> temp = new ArrayList<>();
        List<List<String>> res = new ArrayList<>();
        for (String s : inUserNameList) {
            temp.add(s);
            if (temp.size() > 2000) {
                res.add(temp);
                temp.clear();
                temp = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(temp)) {
            res.add(temp);
        }
        return res;
    }

    /**
     * 根据map的key排序
     *
     * @param map    待排序的map
     * @param isDesc 是否降序，true：降序，false：升序
     * @return 排序好的map
     * <AUTHOR> 2019/04/08
     */
    public static <K extends Comparable<? super K>, V> Map<K, V> sortByKey(Map<K, V> map, boolean isDesc) {
        Map<K, V> result = Maps.newLinkedHashMap();
        if (isDesc) {
            map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByKey().reversed())
                    .forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
        } else {
            map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByKey())
                    .forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
        }
        return result;
    }

    /**
     * 根据map的value排序
     *
     * @param map    待排序的map
     * @param isDesc 是否降序，true：降序，false：升序
     * @return 排序好的map
     * <AUTHOR> 2019/04/08
     */
    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map, boolean isDesc) {
        Map<K, V> result = Maps.newLinkedHashMap();
        if (isDesc) {
            map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByValue().reversed())
                    .forEach(e -> result.put(e.getKey(), e.getValue()));
        } else {
            map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByValue())
                    .forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
        }
        return result;
    }

    /**
     * 去重
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }


    public static Boolean compareList(List<Integer> aList, List<Integer> bList) {
        if (CollectionUtils.isEmpty(aList) || CollectionUtils.isEmpty(bList)) {
            return false;
        }
        Boolean flag = false;
        for (int i = 0; i < aList.size(); i++) {
            if (bList.contains(aList.get(i))) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * 判断字符串是否为合法手机号 11位 13 14 15 18开头
     *
     * @param str
     * @return boolean
     */
    public static boolean isMobile(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return Pattern.matches("^1[3-9]\\d{9}$", str);
    }

    /**
     * @param num  分的份数
     * @param list 需要分的集合
     */
    public static Map<Integer, List<Integer>> splitList(List<Integer> list, Integer num) {

        //list 长度
        int listSize = list.size();
        //用户封装返回的多个list
        HashMap<Integer, List<Integer>> map = new HashMap<Integer, List<Integer>>();
        List<Integer> longList = new ArrayList<Integer>();
        ;

        for (int i = 0; i < listSize; i++) {
            longList.add(list.get(i));
            if (((i + 1) % num == 0) || (i + 1 == listSize)) {
                map.put(i, longList);
                longList = new ArrayList<Integer>();
            }
        }
        return map;
    }

    public static String getEncryptStr(Integer number) {
        if (isNullOrZero(number)) {
            return "";
        }
        Integer length = CHARACTERS.length();
        List<String> list = new LinkedList<>();
        long t = number;
        while (t > 0) {
            long mod = t % length;
            t = Math.abs(t / length);
            String s = String.valueOf(CHARACTERS.charAt((int) mod));
            list.add(s);
        }
        Collections.reverse(list);
        String result = String.join("", list);
        return result;
    }

    /**
     * 包含中文一~九的compare
     */
    public static int compareChinaNum(String str1, String str2, boolean isNullGreater) {

        str1 = replaceChinaNum(str1);
        str2 = replaceChinaNum(str2);
        return CompareUtil.compare(str1, str2, isNullGreater);
    }

    private static String replaceChinaNum(String origin) {
        if (origin != null) {
            char[] chars = origin.toCharArray();
            for (int i = 0; i < chars.length; i++) {
                char c = chars[i];
                Integer n = CHINA_NUMBER_MAP.get(c);
                if (n != null) {
                    chars[i] = (n + "").charAt(0);
                }
            }
            origin = new String(chars);
        }
        return origin;
    }

    public static Platform getPlatform(String platform) {
        if (StringUtils.isBlank(platform)) {
            return new Platform();
        }
        String[] args = StringUtils.split(platform, "\\/");
        if (args.length == 2) {
            return new Platform(args[0], args[1]);
        } else if (args.length == 3) {
            return new Platform(args[0], args[1], args[2]);
        } else if (args.length == 4) {
            return new Platform(args[0], args[1], args[2], args[3]);
        }
        return new Platform();
    }

    public static Platform getCurrentPlatform() {
        if (RequestContextHolder.getRequestAttributes() == null) {
            return new Platform();
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String platform = request.getHeader("Platform");
        return getPlatform(platform);
    }

    /**
     * 数字表示多选项  1 个位 2 十位 3 百位  4 千位 ....
     * 选中为 1 未选中为 0
     *
     * @param multipleNumber
     * @return
     */
    public static List<Integer> intToMultiple(int multipleNumber) {
        List<Integer> result = new LinkedList<>();
        //按位数挨个提取
        int ten = 10;
        for (int i = multipleNumber, j = 1; i > 0; i = i / ten, j += 1) {
            if (i % 10 > 0) {
                result.add(j);
            }
        }

        return Collections.unmodifiableList(result);
    }


    /**
     * 多选转为int表示
     *
     * @param list 元素必须大于0
     * @return
     * @see CommenUtil#intToMultiple(int)
     */
    public static int multipleToInt(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        int result = 0;
        for (Integer i : list) {
            if (i > 0) {
                result += Math.pow(10, i - 1);
            }
        }
        return result;
    }

    public static List<Integer> toIntList(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return new LinkedList<>();
        }
        return Arrays.asList(ids.split(","))
                .stream()
                .filter(StringUtils::isNotEmpty)
                .filter(CommenUtil::isNumer)
                .map(Integer::valueOf).collect(Collectors.toList());
    }

    public static List<String> toStrList(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return new LinkedList<>();
        }
        return Arrays.asList(ids.split(","));
    }

    /**
     * 优惠码分割
     * @param ids
     * @return
     */
    public static List<String> toYouHuiMaList(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return new LinkedList<>();
        }
        return Arrays.stream(ids.split("\\|"))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    /**
     * 防止大数据in查询报错封装
     * @param inList
     * @param fun
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T,R> List<R> bigDataInQuery(Collection<T> inList,Function<Collection<T>,Collection<R>> fun){
        List<T> distList = inList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        return IntStream.rangeClosed(0,(int)Math.ceil(distList.size()*1.0/ NumberConstant.ONE_THOUSAND)-1).parallel()
                .mapToObj(p->fun.apply(distList.stream().skip((long)p *NumberConstant.ONE_THOUSAND).limit(NumberConstant.ONE_THOUSAND)
                        .collect(Collectors.toList())))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * fid路径信息
     * @param fileReq
     */
    public static Optional<String> fidPath(FileReq fileReq){
        return Optional.ofNullable(fileReq).map(f->{
            if(StrUtil.isBlank(fileReq.getFid())){
                return null;
            }
            String extension = Optional.ofNullable(fileReq.getSuffix()).filter(StrUtil::isNotEmpty).orElseGet(()->
                    Optional.ofNullable(FileUtil.extName(fileReq.getFileName())).filter(StrUtil::isNotEmpty)
                            .map(en->SignConstant.POINT+en).orElse(""));
            return Optional.ofNullable(fileReq.getFid()).filter(fid->fid.contains(SignConstant.POINT)).orElseGet(()->
                String.format("%s%s",fileReq.getFid(),extension)).replace(',','/');
        });
    }

    public static String joinInteger(List<Integer> joinList, String separator) {
        if (CollectionUtils.isEmpty(joinList)) {
            return null;
        }
        return StringUtils.join(joinList, separator);
    }
    public static String joinLong(List<Long> joinList, String separator) {
        if (CollectionUtils.isEmpty(joinList)) {
            return null;
        }
        return StringUtils.join(joinList, separator);
    }

    public static String joinString(List<String> joinList, String separator) {
        if (CollectionUtils.isEmpty(joinList)) {
            return null;
        }
        return StringUtils.join(joinList, separator);
    }



    /**
     * 自动查询历史库(没有查到再查)
     * @param queryFun
     * @param <T>
     * @return
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun){
        return autoQueryHist(queryFun,null, (Long) null);
    }

    /**
     *  自动查询历史库
     * @param queryFun
     * @param tableInfoEnum
     * @param tableId 识别参数唯一id
     * @param types 对应类型值, 可以多个
     * @return
     * @param <T>
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun, MTableInfoEnum tableInfoEnum, Integer tableId, Collection types){
        return autoQueryHist(queryFun, tableInfoEnum, Convert.toLong(tableId), ObjectUtil.defaultIfNull(types, Collections.emptyList()).toArray());
    }

    /**
     *  自动查询历史库
     * @param queryFun
     * @param tableInfoEnum
     * @param tableId 识别参数唯一id
     * @param types 对应类型值, 可以多个
     * @return
     * @param <T>
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun, MTableInfoEnum tableInfoEnum, Integer tableId, Object ... types){
        return autoQueryHist(queryFun, tableInfoEnum, Convert.toLong(tableId), types);
    }

    /**
     * 自动查询历史库(没有查到再查)
     * @param queryFun
     * @param tableInfoEnum
     * @param tableId
     * @param types  对应类型值, 可以多个
     * @return
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun, MTableInfoEnum tableInfoEnum, Long tableId, Object ... types){
        Supplier<T> queryHistFun = () -> MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> queryFun.get());
        if(tableId != null && XtenantEnum.isJiujiXtenant()){
            if(tableInfoEnum == null){
                throw new CustomizeException("主键不为空, 表信息枚举不能为空");
            }
            boolean isHistory = tableInfoEnum.isHistory(tableId,types);
            if(isHistory){
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "表[{}]{}为: {},类型:{}, 从历史库查询", tableInfoEnum.getCode(),
                        tableInfoEnum.getMmColumnName(), tableId, types);
                //历史库存查询
                return queryHistFun.get();
            }else{
                //实时库查询
                return queryFun.get();
            }
        }
        T t = queryFun.get();
        if(XtenantEnum.isJiujiXtenant() && ObjectUtil.isEmpty(t)){
            return queryHistFun.get();
        }
        return t;
    }


    /**
     * 自动写入历史库(为null 或者false 自动写入)
     * @param queryFun
     * @param <T>
     * @return
     */
    public static <T> T autoWriteHist(Supplier<T> queryFun){
        T t = queryFun.get();
        if(XtenantEnum.isJiujiXtenant() && (ObjectUtil.isEmpty(t) || Boolean.FALSE.equals(t))){
            AtomicReference<T> result = new AtomicReference<>();
            MultipleTransaction.build()
                    .execute(DataSourceConstants.OA_NEW_HIS_WRITE, () -> result.set(queryFun.get()))
                    .commit();
            return result.get();
        }
        return t;
    }


    /**
     * 同时查实时库和历史库
     * @param queryFun
     * @param <T>
     * @return
     */
    public static <T> Collection<T> autoQueryMergeHist(Supplier<Collection<T>> queryFun){
        Collection<T> t = queryFun.get();
        if(XtenantEnum.isJiujiXtenant()){
            return Stream.concat(t.stream(),MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> queryFun.get()).stream())
                    .collect(Collectors.toList());
        }
        return t;
    }

    /**
     * 转地区后获取当前门店id
     * @param toAreaId
     * @param areaId
     * @return
     */
    public static Integer currAreaId(Integer toAreaId,Integer areaId){
        if(ObjectUtil.defaultIfNull(toAreaId,0) > 0){
            return toAreaId;
        }
        return areaId;
    }



    /**
     * 转地区后获取当前门店id
     * @param id
     * @param defaultId
     * @return
     */
    public static Integer currId(Integer id,Integer defaultId){
        if(ObjectUtil.defaultIfNull(id,0) > 0){
            return id;
        }
        return defaultId;
    }

    /**
     * 根据请求替换处理日志中的url
     * @param msg
     * @return
     */
    public static String handleLogMsgUrl(String msg) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            final String platform = Optional.ofNullable(request.getHeader("Platform")).orElse("");
            //替换成moa地址
            if (XtenantEnum.isJiujiXtenant()) {
                if (platform.contains("MOA")) {
                    msg = StrUtil.replace(msg, "/staticpc/#/logistics/wuliu?wuliuid=", "/new/#/logistics/logistics-bill/");
                    msg = StrUtil.replace(msg, "/addOrder/wuliu?wuliuid=", "/new/#/logistics/logistics-bill/");
                    msg = StrUtil.replace(msg, "/addOrder/editOrder", "/order/editOrder");
                    msg = StrUtil.replace(msg, "/staticpc/#/after-service/bespeak/", "/new/#/afterService/detail/");
                }
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("处理替换日志中的url", msg, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        return msg;
    }


    public static <T> Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> isNullOrEq(SFunction<T, ?> column, Object value){
        return cnd -> cnd.eq(column, value).or().isNull(column);
    }

    /**
     * 获取短信通知的体系名称
     * @param printName
     * @return
     */
    public static String getSmsPrintName(String printName){
        if("九机网".equals(printName)){
            return "九机";
        }
        return printName;
    }
}
