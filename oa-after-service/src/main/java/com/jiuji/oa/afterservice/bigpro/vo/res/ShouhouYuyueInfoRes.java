package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.oa.afterservice.bigpro.bo.AddinfopsBo;
import com.jiuji.oa.afterservice.bigpro.bo.LockWxpjBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouHouYuYueWuLiuBo;
import com.jiuji.oa.afterservice.bigpro.po.Trouble;
import com.jiuji.oa.afterservice.bigpro.po.YuyueLogs;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/3/13
 */
@ApiModel
@Data
public class ShouhouYuyueInfoRes {
    @ApiModelProperty(value = "预约单编号")
    private Integer id;

    @ApiModelProperty(value = "订单号")
    private Integer subId;

    @ApiModelProperty(value = "客户手机")
    private String mobile;

    @ApiModelProperty(value = "客户手机")
    private String userName;

    @ApiModelProperty(value = "会员等级")
    private String memberLevel;

    @ApiModelProperty(value = "会员等级")
    private Integer memberLevelCode;

    @ApiModelProperty(value = "故障描述")
    private String problem;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "预约开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime  startTime;

    @ApiModelProperty(value = "预约结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime kdtime;

    private Integer kdtype;

    @ApiModelProperty(value = "提交人")
    private String submitUser;

    @ApiModelProperty(value = "提交时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "是否数据备份")
    private Integer isBakData;

    @ApiModelProperty(value = "处理方式")
    private Integer dealWay;

    @ApiModelProperty(value = "服务方式")
    private Integer servicesWay;

    @ApiModelProperty(value = "处理地区id")
    private Integer areaId;

    @ApiModelProperty(value = "处理地区")
    private String area;

    @ApiModelProperty(value = "地址信息")
    private AddinfopsBo addressInfo;

    @ApiModelProperty(value = "送件人")
    private Integer delivery;

    @ApiModelProperty(value = "维修配件")
    private List<LockWxpjBo> wxpjList;

    @ApiModelProperty(value = "进程日志")
    private List<YuyueLogs> logs;

    @ApiModelProperty(value = "故障类型")
    private List<Trouble> troubles;

    @ApiModelProperty(value = "维修类别")
    private List<RepairClassifyInfo> repairType;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品规格")
    private String productColor;

    @ApiModelProperty(value = "商品id")
    private Integer pid;

    @ApiModelProperty(value = "商品价格id")
    private Integer ppid;

    @ApiModelProperty(value = "串号")
    private String imei;

    @ApiModelProperty(value = "是否预约过代用机")
    private Boolean isYuyueDaiyonji;

    @ApiModelProperty(value = "是否中邮")
    private Boolean iszy;

    @ApiModelProperty(value = "小件维修id")
    private Integer smallproId;

    @ApiModelProperty(value = "是否大件")
    private Boolean isMobile;

    @ApiModelProperty(value = "无理由")
    private String wuliyou;

    @ApiModelProperty(value = "小件退换商品")
    private ShouhouYuyueReq.TuiData tuidata;

    @ApiModelProperty(value = "附件信息")
    private List<FileReq> attachment;

    @ApiModelProperty(value = "是否绑定微信")
    private Boolean isBindWx;

    @ApiModelProperty(value = "物流信息")
    private ShouHouYuYueWuLiuBo wuliuInfo;

    @ApiModelProperty(value = "用户id")
    private Integer userId;
    @ApiModelProperty(value = "优惠码")
    private String youhuima;
    @ApiModelProperty(value = "售后id")
    private Integer shouhouId;

    @ApiModelProperty(value = "快递公司")
    private String kuaidigongsi;
    @ApiModelProperty(value = "快递单号")
    private String kuaididan;

    @ApiModelProperty(value = "订单来源")
    private Integer fromSource;

    @ApiModelProperty(value = "订单来源说明")
    private String fromSourceDesc;

    private AreaInfo areaSubject;
    private Boolean isQuJianddress;

    @Data
    public static class TuiData{
        @ApiModelProperty(value = "订单id")
        private Integer sub;
        @ApiModelProperty(value = "退款金额")
        private BigDecimal tuim;
        @ApiModelProperty(value = "折价")
        private BigDecimal zhem;
        @ApiModelProperty(value = "退款方式")
        private String way;
        @ApiModelProperty(value = "开户行")
        private String bname;
        @ApiModelProperty(value = "账户名")
        private String bfm;
        @ApiModelProperty(value = "卡号")
        private String bid;
        @ApiModelProperty(value = "订单basket信息")
        private List<Basket> basket;

        @Data
        public static class Basket{
            @ApiModelProperty(value = "basketId")
            private Integer id;
            @ApiModelProperty(value = "退货数量")
            private Integer count;
        }
    }



    /**
     * 维修故障类别
     */
    public static class  RepairClassifyInfo{
        private Integer id;
        private Integer parentid;
        /**
         * 故障名称
         */
        private String title;
        private Integer sort;
        private List<RepairClassifyInfo> child;
    }


}
