package com.jiuji.oa.afterservice.util;

import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/10 15:10
 */

public class EnumUtil {

    public static <E extends CodeMessageEnumInterface> List<ListBean.OptionsBean> enumToOptionList(Class<E> enumClass) {
        List<ListBean.OptionsBean> result = new ArrayList<>();
        for (CodeMessageEnumInterface item : enumClass.getEnumConstants()) {
            ListBean.OptionsBean optionsBean = new ListBean.OptionsBean();
            optionsBean.setValue(String.valueOf(item.getCode()));
            optionsBean.setLabel(item.getMessage());
            result.add(optionsBean);
        }
        return result;
    }
}
