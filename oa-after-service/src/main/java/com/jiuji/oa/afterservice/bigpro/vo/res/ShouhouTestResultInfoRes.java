package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.jiuji.oa.afterservice.bigpro.machinehero.enums.MachineHeroReportHandleTypeEnum;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/21 11:14
 */
@Data
public class ShouhouTestResultInfoRes {
    private Long id;
    private Integer shouhouId;
    private Boolean testResult;
    private String testResultDes;
    private Integer testType;
    private String testTypeName;
    /**
     * 1-app测试
     * 2-无法测试
     * 处理类型
     * @see MachineHeroReportHandleTypeEnum
     */
    private Integer handleType;

    private String handleTypeValue;

    private String remark;
    private String inuser;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
