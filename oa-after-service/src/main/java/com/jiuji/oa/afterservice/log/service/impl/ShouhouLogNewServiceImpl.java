package com.jiuji.oa.afterservice.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogBo;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.log.dao.ShouhouLogNewMapper;
import com.jiuji.oa.afterservice.log.po.ShouhouLogNew;
import com.jiuji.oa.afterservice.log.service.ShouhouLogNewService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 售后日志迁移
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service
public class ShouhouLogNewServiceImpl extends ServiceImpl<ShouhouLogNewMapper, ShouhouLogNew> implements ShouhouLogNewService {


    @Override
    public void saveShouhouLog(Integer shouhouId, String comment, String inUser, Integer type, Boolean isWeb) {
        if (StringUtils.isEmpty(comment) || CommenUtil.isNullOrZero(shouhouId)) {
            return;
        }
        if (isWeb == null) {
            isWeb = Boolean.FALSE;
        }
        ShouhouLogNew shouhouLogNew = new ShouhouLogNew();
        shouhouLogNew.setContent(comment)
                .setDTime(LocalDateTime.now())
                .setInUser(inUser)
                .setShowType(isWeb)
                .setType(type);
        super.save(shouhouLogNew);
    }

    @Override
    public List<ShouhouLogBo> getShouhouLogs(Integer shouhouId) {
        List<ShouhouLogNew> list = super.list(new LambdaQueryWrapper<ShouhouLogNew>().eq(ShouhouLogNew::getSubId, shouhouId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        list = list.stream().sorted(Comparator.comparing(ShouhouLogNew::getDTime, Comparator.reverseOrder())).collect(Collectors.toList());
        return list.stream().map(e -> {
            ShouhouLogBo shouhouLogBo = new ShouhouLogBo();
            shouhouLogBo.setComment(e.getContent());
            shouhouLogBo.setDTime(DateUtil.localDateTimeToMinutesStr(e.getDTime()));
            shouhouLogBo.setDTimeSecond(DateUtil.getDateTimeAsString(e.getDTime()));
            shouhouLogBo.setInUser(e.getInUser());
            shouhouLogBo.setType(e.getType());
            shouhouLogBo.setIsweb(e.getShowType());
            return shouhouLogBo;
        }).collect(Collectors.toList());
    }
}
