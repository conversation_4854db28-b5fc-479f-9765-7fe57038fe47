package com.jiuji.oa.afterservice.patchsearch.dao;

import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: qiweiqing
 * @Date: 2020/03/05
 * @Description:
 */
@Mapper
public interface PatchSearchDao{

    List<PatchSearchRes> getBasketPage(@Param("req") PatchSearchReq personChangesReq);
}
