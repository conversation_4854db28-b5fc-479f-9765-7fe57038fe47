package com.jiuji.oa.afterservice.smallpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ch999.common.util.tenantconfig.TenantConfigUtil;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.web.ShortUrlParam;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.bo.SubPushMsgBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.BbsxpUserIdConstants;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.*;
import com.jiuji.oa.afterservice.evaluate.dao.EvaluateScoreDao;
import com.jiuji.oa.afterservice.evaluate.po.EvaluateScore;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.bo.PingzhengBO;
import com.jiuji.oa.afterservice.other.bo.PingzhengResultBO;
import com.jiuji.oa.afterservice.other.bo.SmallOldProVirtualInfo;
import com.jiuji.oa.afterservice.other.enums.AreaKindEnum;
import com.jiuji.oa.afterservice.other.enums.CheckCodeEnum;
import com.jiuji.oa.afterservice.other.po.*;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.smallpro.bo.*;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfoForPickupBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.LastExchangeSmallproBo;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoProductBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproMsgCodeSaveInfo;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeAreaInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeSubInfoBO;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProConstant;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.enums.*;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.po.TiemoCardUserLog;
import com.jiuji.oa.afterservice.smallpro.service.*;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.CashOrScrapV2Res;
import com.jiuji.oa.afterservice.smallpro.vo.res.PickUpCheckRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.SplitBasketRes;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.entity.YearPackageTransferPo;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.oa.afterservice.statistics.vo.req.CashOrScrapBatchReq;
import com.jiuji.oa.afterservice.stock.enums.ESubCheckEnum;
import com.jiuji.oa.afterservice.stock.po.ProductKclogs;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.stock.service.ProductKclogsService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import com.jiuji.oa.afterservice.sys.service.Password2ValidService;
import com.jiuji.oa.afterservice.sys.service.RetryService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.sys.service.UserTokenService;
import com.jiuji.oa.afterservice.sys.service.impl.UserTokenServiceImpl;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.nc.SmsConfigCloud;
import com.jiuji.oa.nc.sms.SmsConfigCodeEnum;
import com.jiuji.oa.nc.sms.SmsConfigVO;
import com.jiuji.oa.nc.sms.SmsPushMethodEnum;
import com.jiuji.oa.oacore.oaorder.WuliuCloud;
import com.jiuji.oa.oacore.yearcardtransfer.enums.YearPackageTransferStatusEnum;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.ResultModel;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * description: <小件退换相关Service实现类>1
 * translation: <Small piece return related Service implementation class>
 *
 * <AUTHOR>
 * @date 2019/12/26
 * @since 1.0.0
 */
@Service
@Slf4j
public class SmallproExchangePurchaseServiceImpl implements SmallproExchangePurchaseService {


    // region autowired

    @Autowired
    private SmallproService smallproService;
    @Autowired
    private SmallproBillService smallproBillService;
    @Autowired
    private SmallproLogService smallproLogService;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Autowired
    private ShouhouFanchangService shouhouFanchangService;
    @Autowired
    private SmallproOldProductExService smallproOldProductExService;
    @Autowired
    private SmallproForwardExService smallproForwardExService;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private ShouhouYuyueService shouhouYuyueService;
    @Autowired
    private TiemoCardUserLogService tiemoCardUserLogService;
    @Autowired
    private VoucherService voucherService;
    @Autowired
    private WxsmsService wxsmsService;
    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private EvaluateScoreDao evaluateScoreDao;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private WuliuCloud wuliuCloud;
    @Autowired
    private SmsService smsService;
    @Autowired
    private SmallProConstant smallProConstant;
    @Autowired
    private ShouhouMsgconfigService shouhouMsgconfigService;
    @Autowired
    private SysConfigClient sysConfigClient;
    @Autowired
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private MemberClient memberClient;
    @Resource
    private Password2ValidService password2ValidService;
    @Resource
    private UserTokenService userTokenService;
    @Resource
    private IMCloud imCloud;
    // region 逻辑方法

    // region 取件操作 pickup

    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private ExchangeGoodsService exchangeGoodsService;
    @Resource
    @Lazy
    private SmallproExchangePurchaseService smallproExchangePurchaseService;
    @Resource
    private SmsConfigCloud smsConfigCloud;
    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private WeixinUserService weixinUserService;
    @Resource
    private ShouhouMsgService shouhouMsgService;
    @Resource
    private SubLogsCloud subLogsCloud;




    @Override
    public SmallproNormalCodeMessageRes pickup(Integer smallproId, Integer kind, OaUserBO oaUserBO,PickUpExtendReq pickUpExtendReq) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        if (smallpro == null) {
            result.setCode(500);
            result.setMessage("请检查小件接件ID是否正确！无此ID小件接件单！");
            return result;
        }
        if (XtenantEnum.isJiujiXtenant() && SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode().equals(smallpro.getKind())
                && ObjectUtil.defaultIfNull(smallpro.getOldId(),0) == 0 && RankEnum.XHSH.noHasAuthority(oaUserBO.getRank())) {
            result.setCode(500);
            result.setMessage(StrUtil.format("您没有权限！权值:{}", RankEnum.XHSH.getCode()));
            return result;
        }

        List<ShouhouTuihuan> smallProRefundList = smallproMapper.getSmallProRefundList(smallproId);
        for (ShouhouTuihuan tuihuan : smallProRefundList) {
            if (Objects.isNull(tuihuan.getCheck3()) || Boolean.FALSE.equals(tuihuan.getCheck3())) {
                result.setCode(500);
                result.setMessage("存在未完结的[退订/退款]记录，请完结后再完成订单");
                return result;
            }
        }


        //校验未审核数据
        List<ShouhouTuihuan> smallProRefundNotCheckList = smallproMapper.getSmallProRefundNotCheckList(smallproId);
        for (ShouhouTuihuan tuihuan : smallProRefundNotCheckList) {
            if (Objects.isNull(tuihuan.getCheck1user())) {
                result.setCode(500);
                result.setMessage("审核未完成，请审核完成后再完成订单");
                return result;
            }
        }
        PickUpCheckReq pickUpCheckReq = new PickUpCheckReq();
        PickUpCheckRes pickUpCheckRes = smallproService.pickUpCheck(pickUpCheckReq.setSmallProId(smallproId));
        if (!CheckCodeEnum.SUCCESS.getCode().equals(pickUpCheckRes.getCheckCode())) {
            result.setCode(500);
            result.setMessage(CheckCodeEnum.getValue(pickUpCheckRes.getCheckCode()));
            return result;
        }

        List<SmallproPickUpOutboundInfoBO> outboundInfoList =
                smallproMapper.getOutboundInfo(smallpro.getId());
        boolean b =
                outboundInfoList.stream().anyMatch(e -> e.getPpriceId().equals(94512) || e.getPpriceId().equals(63231));
        if (!b && 3 != kind && 4 != kind && smallpro.getFeiyong() != null
                && smallpro.getFeiyong().compareTo(BigDecimal.valueOf(0.0)) > 0
                && (smallpro.getIsShouyingLock() == null || !smallpro.getIsShouyingLock())) {
            //计算已付金额
            List<Shouying> shouyings = SpringUtil.getBean(ShouyingService.class).lambdaQuery().eq(Shouying::getSubId, smallproId)
                    .in(Shouying::getShouyingType, RefundMoneyUtil.getShouyingTypes(TuihuanKindEnum.SMALL_PRO_REFUND_REPAIR_FEE))
                    .list();
            BigDecimal yifum = shouyings.stream().map(Shouying::getHejim).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if(ObjectUtil.compare(smallpro.getFeiyong(),yifum) == 0){
                //费用相等 取机自动进行收银锁定
                shouyings.stream().sorted(Comparator.comparing(Shouying::getId,Comparator.reverseOrder())).findFirst()
                        .ifPresent(lastShouying -> smallproService.lambdaUpdate().eq(Smallpro::getId,smallproId).set(Smallpro::getIsShouyingLock,Boolean.TRUE)
                                .set(Smallpro::getShouyinDate,lastShouying.getDtime()).set(Smallpro::getShouyinUser,lastShouying.getInuser()).update());
            }else {
                result.setCode(500);
                result.setMessage("请检查维修费是否已收银！收银后才能取件！");
                return result;
            }
        }
        if (smallpro.getStats().equals(SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode())) {
            result.setCode(500);
            result.setMessage("当前小件已经处理完成！无法取件！");
            return result;
        }

        // 加盟店 https://moa.dev.9ji.com/ajaxapi/CheckSmallProJoinArea?smallProId=0
        if(XtenantEnum.isJiujiXtenant()){
            assertJoinArea(smallproId);
        }

        List<SmallproJiujiServiceInpriceInfoBO> jiujiServiceInpriceInfoBOList
                = smallproMapper.getJiujiServiceInPriceInfo(smallproId);

        boolean flag = ((SmallproExchangePurchaseServiceImpl) AopContext.currentProxy()).pickupWrite(result,
                kind, oaUserBO, smallpro, outboundInfoList, jiujiServiceInpriceInfoBOList);
        if (!flag) {
            return result;
        }
        // 判断如果旧件商品全部满足自动报废条件那就不想进行转地区


        // 李飞说：20200424所有取件都不自动跨地区
        // 另外单独开事务
        // 非退货和维修操作，取件后系统自动跨地区操作到D1
        if ((smallpro.getKind() == 2 || smallpro.getKind() == 4) && (smallpro.getServiceType() == null || smallpro.getServiceType() != 4)) {
            AreaBelongsDcHqD1AreaId areaBelongsDcHqD1AreaId = areainfoService.getAreaBelongsDcHqD1AreaId(smallpro.getAreaId());
            //判断系统配置有无配置售后D1处理地区，如果过配置那就修改成配置的值，没有配置保持原来的取值（九机的已经配置，输出还没有配置）
            String valueByCode = sysConfigService.getValueByCode(SysConfigConstant.AFTER_D1_HANDLE_AREA_CODE);
            if(StringUtils.isNotEmpty(valueByCode) && StringUtils.isNumeric(valueByCode)){
                areaBelongsDcHqD1AreaId.setD1AreaId(Integer.valueOf(valueByCode));
            }
            boolean isNotAllowToArea = isNotAllowAutoToArea(oaUserBO,smallpro);
            if(!isNotAllowToArea){
                //通过sysconfig查询不用自动转地区的配置项进行排除操作transferAreaId
                R<String> notAutoTransferArea = sysConfigClient.getValueByCode(SysConfigConstant.SMALL_NOT_AUTO_TRANSFER_AREA);
                if (StringUtils.isNotEmpty(notAutoTransferArea.getData())) {
                    List<Integer> notAutoTransferAreaList = CommenUtil.toIntList(notAutoTransferArea.getData());
                    isNotAllowToArea = notAutoTransferAreaList.contains(smallpro.getAreaId());
                }
            }
            //手动控制是否转地区（如果过是输出 没有手动开关那就都是开，如果是九机那就需要去手动开关的值）
            Boolean transferAreaId = XtenantEnum.isJiujiXtenant()?Convert.toBool(Optional.ofNullable(pickUpExtendReq.getTransferAreaId()).orElse(NumberConstant.ONE)) : Boolean.TRUE ;
            if ((!Objects.equals(areaBelongsDcHqD1AreaId.getD1AreaId(), smallpro.getAreaId())) && !isNotAllowToArea && !isAllScrapProduct(smallpro) && transferAreaId) {
                //模式输出，如果是膜的话不用自动转地区
                if (oaUserBO.getXTenant() < 1000 || (oaUserBO.getXTenant() >= 1000 && isAllowAutoToArea(smallproId)) && CommenUtil.isNotNullZero(areaBelongsDcHqD1AreaId.getD1AreaId())) {
                    smallproService.toArea(smallpro.getId(), smallpro.getAreaId(),
                            areaBelongsDcHqD1AreaId.getD1AreaId(), 1, oaUserBO, false, smallpro);
                }


            }
        }
        // 另外单开事务
        // 年包服务使用完后自动报废
        if (smallpro.getServiceType() != null && smallpro.getServiceType() == 4) {
            LambdaQueryWrapper<ShouhouFanchang> fcQueryWrapper = new LambdaQueryWrapper<ShouhouFanchang>()
                    .eq(ShouhouFanchang::getSmallproid, smallpro.getId());
            List<ShouhouFanchang> fanchangList = shouhouFanchangService.listSqlServer(fcQueryWrapper);
            int num = 0;
            while (CollectionUtils.isEmpty(fanchangList)) {
                fanchangList = shouhouFanchangService.listSqlServer(fcQueryWrapper);
                num++;
                if (num > 10) {
                    break;
                }
                log.warn("年包服务自动报废，查询返厂旧件单-无，重复查询第" + (num + 1) + "次");
            }
            if (CollectionUtils.isNotEmpty(fanchangList)) {
                Integer fcid = fanchangList.get(0).getId();
                SmallproNormalCodeMessageRes baofeiRes = smallproOldProductExService.scrapSmallpro(fcid,
                        oaUserBO.getUserName(), smallpro.getId(), oaUserBO.getAreaId());
                if (baofeiRes != null) {
                    log.debug("年包服务旧件自动报废：" + baofeiRes.toString() + ";smallproId" + smallproId);
                } else {
                    log.error("未进行报废操作！" + fcid);
                }
            } else {
                log.error("年包服务自动报废查询旧件单无法查询到！未能进行报废！smallproId:" + smallproId);
            }
        }
        result.setCode(0);
        result.setMessage("小件取件成功！");
        //接件成功后消息推送 用于记录会员与员工的关系
        Integer ch999Id = Optional.ofNullable(userInfoClient.getCh999UserByUserName(smallpro.getInUser()).getData())
                .map(Ch999UserVo::getCh999Id).orElseGet(() -> oaUserBO.getUserId());
        CompletableFuture.runAsync(() -> smsService.pushMemberScanBind(Math.toIntExact(smallpro.getUserId()), ch999Id, BusinessTypeEnum.AFTER_SMALL_ORDER.getCode(), smallproId, BusinessNodeEnum.BUSINESS_FINISHED.getCode()));
        // 取件成功后，虚拟商品自动报废
        if (!Objects.equals(smallpro.getKind(), NumberConstant.ONE)) {
            virtualProductAutoScrap(smallproId);
        }
        //保护膜旧件商品自动报废功能开发
        if(XtenantEnum.isJiujiXtenant() || SourceTypeEnum.SALE_ORDER_FILM_LOSS.getCode().equals(smallpro.getSourceType())){
            FilmScrapReq filmScrapReq = new FilmScrapReq();
            filmScrapReq.setSmallProId(smallproId);
            filmScrapReq.setPpid(pickUpExtendReq.getPpid());
            PickUpExtendReq req = Optional.ofNullable(pickUpExtendReq).orElse(new PickUpExtendReq());
            //filmScrapReq.setOperationKind(req.getOperationKind());
            filmScrapReq.setLossCount(req.getLossCount());
            Boolean scrap = filmScrap(filmScrapReq);
            //报损成功了才进行消息推送
            pushCashMsg(smallpro,filmScrapReq);

        }
        //小件取件成功,异步调用处理加盟店信息
        if(XtenantEnum.isJiujiXtenant()){
            CompletableFuture.runAsync(()->SpringUtil.getBean(SmallproExchangePurchaseService.class).joinAreaMoneyDeal(smallpro));
        }
        //清除小件不折价缓存
        //获取小件订单关联basketId
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        if (CollUtil.isNotEmpty(smallproBillList)) {
            for (SmallproBill smallproBill : smallproBillList) {
                //小件无折扣退款
                stringRedisTemplate.delete(StrUtil.format(RedisKeys.SMALLPRO_NOT_DISCOUNT_BASKET_KEY, smallproBill.getBasketId()));
            }
        }
        return result;
    }

    /**
     * 判断小件对应旧件是否全部为 自动报废商品 （如果全部满足那就不进行转地区）
     * @return
     */
    private Boolean isAllScrapProduct(Smallpro smallpro){
        List<SmallOldProVirtualInfo> smallProGoods = shouhouFanchangService.getSmallProGoods(smallpro.getId());
        if(CollectionUtils.isEmpty(smallProGoods)){
            return Boolean.FALSE;
        }
        Boolean isAllScrapProduct = Boolean.TRUE;
        if(XtenantEnum.isZlf() || XtenantEnum.isSaasTest()){
            return SourceTypeEnum.SALE_ORDER_FILM_LOSS.getCode().equals(smallpro.getSourceType());
        }
        //当前所在地区为9X_scc, 9X_scc2, 9X_scc3,CH 不进行自动报废
        if(SmallproDetailsExServiceImpl.isSaasAreaId(smallpro)){
            return Boolean.FALSE;
        }

        //判断是否全部商品满足条件
        for (SmallOldProVirtualInfo smallOldProVirtualInfo : smallProGoods){
            Integer ppid = Optional.ofNullable(smallOldProVirtualInfo.getPpid()).orElse(NumberConstant.ZERO);
            String productName = Optional.ofNullable(smallOldProVirtualInfo.getProductName()).orElse("");
            if(BbsxpUserIdConstants.XIAN_HUO.equals(smallpro.getUserId())){
                isAllScrapProduct = categoryService.isAutomaticScrapFilm(ppid) && !productName.contains("全胶");
            } else {
                isAllScrapProduct = categoryService.determineFilmByPpid(ppid);
            }
            if(!isAllScrapProduct){
                break;
            }
        }
        return isAllScrapProduct;
    }

    /**
     * 现货单完成消息推送
     * @param smallpro
     */
    @Override
    public void pushCashMsg(Smallpro smallpro, FilmScrapReq filmScrapReq){
        try {
            OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前用户信息获取失败"));
            //如果不是现货单那就不进行推送
            Integer kind = smallpro.getKind();
            if(!SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode().equals(kind) || !BbsxpUserIdConstants.XIAN_HUO.equals(smallpro.getUserId())){
                return;
            }
            //判断如果接件商品不是贴膜商品，那就不进行推送
            List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallpro.getId())
                    .list();
            if(CollectionUtils.isEmpty(smallproBillList)){
                return;
            }
            Productinfo product = Optional.ofNullable(productinfoService.getProductinfoByPpid(Convert.toInt(smallproBillList.get(NumberConstant.ZERO).getPpriceid()))).orElse(new Productinfo());
            if(!categoryService.tieMoCids().contains(product.getCid())){
                return;
            }
            //查询用户推送信息
            Integer oldId = smallpro.getOldId();
            if(ObjectUtil.isNull(oldId)){
                throw new CustomizeException("现货单完成推送，但是没有关联订单id");
            }
            Integer oldIdType = smallpro.getOldIdType();
            Integer userId = null;
            String subMobile = "";
            //获取门店
            Areainfo areainfo = Optional.ofNullable(areainfoService.getByIdSqlServer(smallpro.getAreaId())).orElse(new Areainfo());
            //获取损耗数量
            Integer num = Optional.ofNullable(filmScrapReq.getLossCount()).orElseGet(()->smallproBillList
                    .stream().map(SmallproBill::getCount).filter(Objects::nonNull).reduce(0, Integer::sum));
            //日志记录 在关联的销售订单/小件售后单增加订单日志
            Integer smallProId = smallpro.getId();
            AtomicReference<String> name = new AtomicReference<>();
            name.set(smallpro.getName());
            Optional.ofNullable(filmScrapReq.getPpid()).ifPresent(ppid->{
                Productinfo productinfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppid)).orElse(new Productinfo());
                name.getAndSet(productinfo.getProductName());
            });
            String smsPrintName = CommenUtil.getSmsPrintName(areainfo.getPrintName());
            String logMsg = String.format("服务类型：贴膜服务，贴膜商品：%s ，贴膜损耗：%s张（贴膜过程中，因产品瑕疵或操作失误，未一次性贴好，产生的损耗称为贴膜损耗，此损耗由%s全额承担，不影响您的贴膜次数 。），服务门店：%s，服务人员：%s",
                    name.get(), num, smsPrintName, areainfo.getAreaName(), userBO.getUserName());

            //订单是否已完成
            Boolean orderIsCompleted = Boolean.FALSE;
            if(OldIdTypeEnum.SMALL_PRO_TYPE.getCode().equals(oldIdType)){
                //小件userId查询
                Smallpro pro = Optional.ofNullable(smallproService.getById(oldId)).orElse(new Smallpro());
                userId = pro.getUserId();
                subMobile = pro.getMobile();
                //判断现货是否完成
                if(ObjectUtil.isNotNull(pro.getQujianDate())){
                    orderIsCompleted = Boolean.TRUE;
                }
                //小件单日志记录
                smallproLogService.addLogs(oldId, logMsg, userBO.getUserName(), 0);
            } else {
                //销售单userId查询
                SubService subService = SpringUtil.getBean(SubService.class);
                Sub sub = Optional.ofNullable(subService.getById(oldId)).orElse(new Sub());
                //判断订单是否已完成
                if(ESubCheckEnum.THREE.getCode().equals(sub.getSubCheck())){
                    orderIsCompleted=Boolean.TRUE;
                }
                userId = Convert.toInt(sub.getUserId());
                subMobile = sub.getSubMobile();
                //销售单日志记录
                SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                subLogsNewReq.setComment(logMsg);
                subLogsNewReq.setSubId(oldId);
                subLogsNewReq.setInUser(userBO.getUserName());
                subLogsNewReq.setShowType(Boolean.FALSE);
                subLogsNewReq.setType(1);
                subLogsCloud.addSubLog(subLogsNewReq);

            }
            if(ObjectUtil.isNull(userId)){
                throw new CustomizeException("现货单完成推送查询不到userId");
            }
            //有微信推送微信，没有微信推送短信(九机才进行推送)
            if(XtenantEnum.isJiujiXtenant()){
                LocalDateTime qujianDate = Optional.ofNullable(smallpro.getQujianDate()).orElse(LocalDateTime.now());
                WeixinUser wxInfo = Optional.ofNullable(weixinUserService.getWxxinUserByUserId(userId)).orElse(new WeixinUser());
                if (StringUtils.isNotBlank(wxInfo.getOpenid()) && Boolean.TRUE.equals(wxInfo.getFollow())) {
                    String url = getUrl(smallpro, orderIsCompleted, areainfo);
                    imCloud.sendAfterServiceProgressMsg(wxInfo.getOpenid(),url,"", "贴膜服务", "已处理", DateUtil.localDateTimeToString(qujianDate),"已处理",
                            "售后人员正在处理",areainfo.getCityid(),XtenantEnum.getXtenant().longValue());
                    log.warn("现货单完成消息推送（微信）：{}，userId:{}，url:{}，小件单号：{}", wxInfo.getOpenid(),userId,url,smallProId);
                } else {
                    //短信推送
                    ShortUrlParam shortUrlParam = LambdaBuild.create(new ShortUrlParam())
                            .set(ShortUrlParam::setUrl, smsService.getLinkWithToken(userId, getUrl(smallpro,orderIsCompleted, areainfo),60))
                            .set(ShortUrlParam::setDescription, "贴膜损耗短链").build();
                    Result<String> urlR = SpringUtil.getBean(RetryService.class).retryByFeignRetryableException(()->
                            SpringUtil.getBean(WebCloud.class).generateShortUrl(shortUrlParam, areainfo.getXtenant()));
                    String shortUrl = urlR.getData();
                    String content = String.format("您于%s月%s日在%s进行贴膜服务，贴膜过程中，因产品瑕疵或操作失误，产生了%s张贴膜损耗，" +
                            "此损耗由%s全额承担，不影响您的贴膜次数。详情请查看:%s",qujianDate.getMonthValue(), qujianDate.getDayOfMonth(),areainfo.getAreaName(),num, smsPrintName,shortUrl);
                    smsService.sendSms(subMobile, content, DateUtil.localDateTimeToString(qujianDate), "系统", smsService.getSmsChannelByTenant(areainfo.getId(), ESmsChannelTypeEnum.YZMTD));
                    log.warn("现货单完成消息推送（短信）：{}，号码：{}，小件单号：{}",content,subMobile,smallProId);
                }
            }
        } catch (Exception e){
            RRExceptionHandler.logError("现货单完成消息推送异常："+e.getMessage(), smallpro, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }

    }

    /**
     * 获取到订单链接
     *
     * @param smallpro
     * @param areainfo
     * @return
     */
    private String getUrl(Smallpro smallpro, Boolean orderIsCompleted, Areainfo areainfo){
        //获取MOA域名
        String host = Optional.ofNullable(sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, areainfo.getXtenant()))
                .map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("现货单完成消息推送获取域名出错"));
        String url = "";
        //落地页面调整为现货小件单
        return host + "/after-service/small-detail/" + smallpro.getId();

//        Integer oldIdType = smallpro.getOldIdType();
//        Integer oldId = smallpro.getOldId();
//        //如果订单状态是已完成
//        if(Boolean.TRUE.equals(orderIsCompleted)){
//            //查询basketId
//            Integer basketId = null;
//            if(OldIdTypeEnum.SMALL_PRO_TYPE.getCode().equals(oldIdType)){
//                //小件单查找basketId的方式（首先查询自己现货单有没有basketId）
//                List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallpro.getId()).list();
//                if(CollectionUtils.isNotEmpty(smallproBillList)){
//                    basketId = smallproBillList.get(NumberConstant.ZERO).getBasketId();
//                }
//                //如果没有查询到basketId 那就直接查询原订单的basketId
//                if(ObjectUtil.isNull(basketId) || NumberConstant.ZERO.equals(basketId)){
//                    List<SmallproBill> originalOrderList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, oldId).list();
//                    if(CollectionUtils.isNotEmpty(originalOrderList)){
//                        basketId = originalOrderList.get(NumberConstant.ZERO).getBasketId();
//                    }
//                }
//            } else {
//                //销售单的情况下（通过ppid和subId进行关联查询  如果存在多个basketId的情况下 那就推最小的basketId）
//                basketId = SpringUtil.getBean(SmallproMapper.class).getMinBasketIdBySmallProId(smallpro.getId());
//            }
//            //如果basketId为空那就进行未完成连接跳转作为兜底逻辑
//            if(ObjectUtil.isNotNull(basketId) && !NumberConstant.ZERO.equals(basketId)){
//                url = host+"/member/guarantee/tempered-film-detail/"+basketId;
//            } else {
//                url = getNotCompletedOrderUrl(host, smallpro);
//            }
//        } else {
//            url = getNotCompletedOrderUrl(host, smallpro);
//        }
//        return url;
    }

    /**
     * 获取未完成时候的连接
     * @param host
     * @param smallpro
     * @return
     */
    private String getNotCompletedOrderUrl(String host, Smallpro smallpro) {
        Integer oldId = smallpro.getOldId();
        if (OldIdTypeEnum.SMALL_PRO_TYPE.getCode().equals(smallpro.getOldIdType())) {
            //小件单链接
            return host + "/after-service/small-detail/" + oldId;
        } else {
            //销售单链接
            return host + "/member/order/detail?orderId=" + oldId;
        }
    }

    @Override
    public void assertJoinArea(Integer smallproId) {
        AtomicReference<String> urlRef = new AtomicReference<>();
        Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).filter(R::isSuccess)
                .map(R::getData)
                .map(url -> {
                    urlRef.set(StrUtil.format("{}/ajaxapi/CheckSmallProJoinArea", url));
                    Dict param = Dict.create().set("smallProId", smallproId);
                    log.warn("加盟店验证: 地址: {} 参数: {}",urlRef.get(),JSON.toJSONString(param));
                    return HttpUtil.createGet(urlRef.get()).form(param).execute();
                })
                .filter(res -> {
                    log.warn("加盟店验证返回结果: {}",res.body());
                    if(res.isOk()){
                        return true;
                    }
                    RRExceptionHandler.logError("加盟店取件验证接口", smallproId,
                            new RuntimeException(StrUtil.format("url: {},响应状态码: {},响应结果: {}", urlRef.get(), res.getStatus(), res.body())),
                            msg -> {
                                throw new CustomizeException(msg);
                            });
                    return false;
                })
                .map(HttpResponse::body)
                .map(rStr ->  {
                    R<?> r = JSON.parseObject(rStr, new TypeReference<R<?>>() {});
                    return r;
                })
                .map(r -> {
                    if(r.isSuccess()){
                        return r;
                    }
                    throw new CustomizeException(r.getUserMsg());
                }).orElseThrow(()->new CustomizeException("调用加盟店验证接口异常"));
    }

    @Override
    public boolean isNotAllowAutoToArea(OaUserBO oaUserBO, Smallpro smallpro) {
        if(Objects.isNull(oaUserBO) || Objects.isNull(smallpro)){
            return false;
        }
        Integer xTenant = oaUserBO.getXTenant();
        if(Objects.equals(TenantConfigUtil.getMainTenantId(xTenant),100000L)){
            return true;
        }
        //九机使用质保换新的且接件地为加盟店的小件单,不转到d1
        Optional<AreaInfo> areaInfoOpt = Optional.ofNullable(areaInfoClient.getAreaInfoById(smallpro.getAreaId())).filter(R::isSuccess)
                .map(R::getData);
        if(XtenantEnum.isJiujiXtenant() && Objects.equals(smallpro.getServiceType(),
                SmallProServiceTypeEnum.SMALL_PRO_KIND_WARRAN_TYRE_PLACEMENT.getCode())
            && (!areaInfoOpt.isPresent() || areaInfoOpt.filter(areaInfo -> Objects.equals(AreaKindEnum.JOIN.getCode(), areaInfo.getKind1())).isPresent())){
            return true;
        }

        return false;

    }

    @Override
    public void virtualProductAutoScrap(Integer smallProId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Smallpro smallpro = smallproService.getByIdSqlServer(smallProId);
        List<SmallOldProVirtualInfo> smallProVirtualGoods = shouhouFanchangService.getSmallProVirtualGoods(smallProId);
        Optional.of(smallProVirtualGoods).filter(CollectionUtils::isNotEmpty)
                .ifPresent(k -> {
                    oaUserBO.setUserName("系统");
                    oaUserBO.setAreaId(smallpro.getAreaId());
                    CashOrScrapBatchReq req = new CashOrScrapBatchReq();
                    req.setType(NumberConstant.TWO);
                    req.setReturnFactoryIds(smallProVirtualGoods.stream().map(SmallOldProVirtualInfo::getFcId).collect(Collectors.toList()));
                    this.CashOrScrapBatch(req, oaUserBO);
                });
    }



    // endregion

    // region 换货审核 checkExchange

    @Override
    public SmallproNormalCodeMessageRes checkExchange(Integer smallproId, String userName) {
        //校验是否可以进行换货审核
        SmallproNormalCodeMessageRes res = new SmallproNormalCodeMessageRes();
        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        //杨继武22-04-26修改 特殊审核去除掉年包
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String log = "换货审核";
        if (!XtenantEnum.isJiujiXtenant()) {
            if (Boolean.TRUE.equals(smallpro.getIsSpecialTreatment())
                    && !Objects.equals(smallpro.getServiceType(), SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode())) {
                if (!oaUserBO.getRank().contains("6f3")) {
                    //换货审核特殊权值查询
                    res.setCode(500);
                    res.setMessage("没有权限进行操作!权值:6f3");
                    return res;
                }
            } else {
                if (!oaUserBO.getRank().contains("6f4")) {
                    res.setCode(500);
                    res.setMessage("您没有权限！权值:6f4");
                    return res;
                }
            }
            if (smallpro.getCodeMsg() == null) {
                res.setCode(500);
                res.setMessage("请先进行手机串号/客户验证码验证，并保存！");
                return res;
            }
        } else {
            if (Boolean.TRUE.equals(smallpro.getIsSpecialTreatment())) {
                if (!oaUserBO.getRank().contains("777")) {
                    //换货审核特殊权值查询
                    res.setCode(500);
                    res.setMessage("该订单为“特殊处理”订单，您暂无审核权限，请联系呼叫中心进行审核!权值:777");
                    return res;
                } else {
                    log = "换货审核，同步审核[特殊处理]类型小件单";
                }
            } else {
                if (!oaUserBO.getRank().contains("6f4")) {
                    res.setCode(500);
                    res.setMessage("您没有权限！权值:6f4");
                    return res;
                }
            }
            PickUpCheckReq pickUpCheckReq = new PickUpCheckReq();
            pickUpCheckReq.setSmallProId(smallproId);
            PickUpCheckRes pickUpCheckRes = smallproService.pickUpCheck(pickUpCheckReq);
            if (smallpro.getCodeMsg() == null && CheckCodeEnum.SUCCESS.getCode().equals(pickUpCheckRes.getCheckCode())) {
                res.setCode(500);
                res.setMessage("请先进行手机串号/客户验证码验证，并保存！");
                return res;
            }
            List<SmallproBill> list = smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getSmallproID, smallproId));
            if (CollectionUtils.isNotEmpty(list)) {
                Integer basketId = list.get(0).getBasketId();
                List<SmallproBill> smallproBillList = smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getBasketId, basketId));
                if (CollectionUtils.isNotEmpty(smallproBillList)) {
                    List<Integer> subIdList = smallproBillList.stream().map(SmallproBill::getSmallproID).distinct().collect(Collectors.toList());
                    List<Smallpro> smallproList = smallproService.lambdaQuery().eq(Smallpro::getKind, SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())
                            .eq(Smallpro::getStats, SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode())
                            .in(Smallpro::getId, subIdList)
                            .list().stream().filter(x -> Objects.isNull(x.getServiceType())
                                    || SmallProServiceTypeEnum.SMALL_PRO_KIND_NULL.getCode().equals(x.getServiceType()))
                            .collect(Collectors.toList());
                    if (smallproList.size() >=3) {
                        if (!oaUserBO.getRank().contains("777")) {
                            //换货超过三次
                            res.setCode(500);
                            res.setMessage("查询该订单为异常操作订单，您暂无审核权限，请联系呼叫中心进行审核!权值:777");
                            return res;
                        } else {
                            log = "换货审核，同步审核[监控异常]类型小件单";
                        }
                    }
                }
            }
        }
        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getSmallproid, smallproId).eq(ShouhouTuihuan::getIsdel, Boolean.FALSE)
                .isNull(ShouhouTuihuan::getCheck1user);
        List<ShouhouTuihuan> shouhouTuihuanList =
                shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper);
        if (shouhouTuihuanList == null || CollectionUtils.isEmpty(shouhouTuihuanList)) {
            res.setCode(500);
            res.setMessage("请检查是否有小件商品，若没有则删除重新接件再操作！");
            return res;
        }
        ShouhouTuihuan shouhouTuihuan = shouhouTuihuanList.get(0);
        boolean updateFlag = shouhouTuihuanService.update(new LambdaUpdateWrapper<ShouhouTuihuan>()
                .set(ShouhouTuihuan::getCheck1, Boolean.TRUE)
                .set(ShouhouTuihuan::getCheck1user, userName)
                .set(ShouhouTuihuan::getCheck1dtime, LocalDateTime.now())
                .eq(ShouhouTuihuan::getId, shouhouTuihuan.getId()));
//        boolean updateFlag = shouhouTuihuanService.updateById(shouhouTuihuan);
        if (updateFlag) {
            res.setCode(0);
            res.setMessage("换货审核成功！");
            // 添加换货审核日志
            smallproLogService.addLogs(smallproId, log, userName, 0);
            virtualProductAutoScrap(smallproId);
        } else {
            res.setCode(500);
            res.setMessage("换货审核数据库更新失败！");
        }
        return res;
    }

    // endregion

    // region 撤销换货审核 cancelCheckExchange

    @Override
    public SmallproNormalCodeMessageRes cancelCheckExchange(Integer smallproId, String userName) {
        SmallproNormalCodeMessageRes res = new SmallproNormalCodeMessageRes();
        // 如果小件单已经完成或者删除, 不允许操作
        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        if (smallpro.getStats().equals(SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode())
                || Boolean.TRUE.equals(smallpro.getIsDel())) {
            res.setCode(500);
            res.setMessage("小件单已完成或已删除，请重新接件再操作！");
            return res;
        }

        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getSmallproid, smallproId)
                .isNotNull(ShouhouTuihuan::getCheck1user);
        List<ShouhouTuihuan> list = shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper);
        if (list == null || CollectionUtils.isEmpty(list)) {
            res.setCode(500);
            res.setMessage("请检查是否有小件商品，当前没有已审核的该小件订单！");
            return res;
        }
        ShouhouTuihuan shouhouTuihuan = list.get(0);
        UpdateWrapper<ShouhouTuihuan> shouhouTuihuanUpdateWrapper = new UpdateWrapper<>();
        shouhouTuihuanUpdateWrapper.lambda().eq(ShouhouTuihuan::getId, shouhouTuihuan.getId())
                .set(ShouhouTuihuan::getCheck1user, null)
                .set(ShouhouTuihuan::getCheck1, Boolean.FALSE)
                .set(ShouhouTuihuan::getCheck1dtime, null);
        boolean flag = shouhouTuihuanService.update(shouhouTuihuanUpdateWrapper);
        if (flag) {
            //撤销成功写入日志
            SmallproAddLogReq smallproAddLogReq = new SmallproAddLogReq();
            smallproAddLogReq.setSmallproId(smallproId);
            smallproAddLogReq.setShowType(1);
            smallproAddLogReq.setToSms(0);
            smallproAddLogReq.setToEmail(0);
            smallproAddLogReq.setToWeixin(0);
            smallproAddLogReq.setComment("撤销申请操作，业务类型：小件换货");
            smallproService.addSmallproLogWithPush(smallproAddLogReq,userName);
            res.setCode(0);
            res.setMessage("撤销换货审核成功！");
        } else {
            res.setCode(500);
            res.setMessage("撤销换货审核数据库更新失败！");
        }
        return res;
    }

    // endregion

    // region 校验验证码 checkCodeMessage
    @Override
    public SmallproNormalCodeMessageRes checkCodeMessage(Integer smallproId, String code, Integer type,Integer validType) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        String codeMsg = code;
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        if (!code.contains("【")) {
            if (Objects.equals(validType, ValidMemberType.SMS_CODE.getCode())) {
                String cacheCode = (String) stringRedisTemplate.opsForHash().get("smallproCodeMsgCache",
                        "msgCode" + type + smallproId);
                if (cacheCode == null) {
                    result.setCode(NumberConstant.FIVE_HUNDRED);
                    result.setMessage("验证码不存在或已过期，请重新发送验证码！");
                    return result;
                }
                if (!cacheCode.equals(code)) {
                    result.setCode(NumberConstant.FIVE_HUNDRED);
                    result.setMessage("验证码错误！");
                    return result;
                }

            }
            //支付密码验证
            if (Objects.equals(validType, ValidMemberType.PAY_PASSWORD.getCode())) {
                Optional<R<ResultModel>> resultModelR = Optional.ofNullable(memberClient.validtPayPwd(smallpro.getUserId(), code));
                if(!resultModelR.isPresent()){
                    result.setCode(NumberConstant.FIVE_HUNDRED);
                    result.setMessage("支付验证接口返回空！");
                    return result;
                }
                boolean isValidSuccess = resultModelR.filter(R::isSuccess).map(R::getData).map(ResultModel::getStats)
                        .filter(stats -> Objects.equals(stats, NumberConstant.ONE)).isPresent();
                if(!isValidSuccess){
                    result.setCode(NumberConstant.FIVE_HUNDRED);
                    result.setMessage("支付验证失败！");
                    return result;
                }
            }
            //会员识别码验证
            if (Objects.equals(validType, ValidMemberType.MEMBER_CODE.getCode())) {
                String validCode;
                if(StrUtil.equalsCharAt(code,0,'a') || StrUtil.equalsCharAt(code,0,'A')){
                    validCode = code.substring(1);
                }else{
                    validCode = code;
                }
                if(StringUtils.isNotBlank(code) && code.length() > UserTokenServiceImpl.SEPARATING_VALUES && XtenantEnum.isJiujiXtenant()){
                    validCode = code;
                }
                Integer userIdByToken = userTokenService.getUserIdByToken(validCode);
                if (CommenUtil.isNotNullZero(userIdByToken)) {
                    boolean isValidSuccess = Objects.equals(Math.toIntExact(smallpro.getUserId()), userIdByToken);
                    if (!isValidSuccess) {
                        result.setCode(NumberConstant.FIVE_HUNDRED);
                        result.setMessage("识别码与用当前户不匹配，请输入本人识别码，重新验证");
                        return result;
                    }
                } else {
                    result.setCode(NumberConstant.FIVE_HUNDRED);
                    result.setMessage("识别码已失效，请输入新的识别码，重新验证");
                    return result;
                }
                //清除缓存
                userTokenService.delUserIdByToken(validCode);
            }
            codeMsg = MessageFormat.format("{0}[{1}] 【{2}】 {3}",EnumUtil.getMessageByCode(ValidMemberType.class,validType), code, oaUserBO.getUserName()
                    , LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            //如果是支付密码验证
            if (Objects.equals(validType, ValidMemberType.PAY_PASSWORD.getCode())) {
                codeMsg = MessageFormat.format("{0}[{1}] 【{2}】 {3}",EnumUtil.getMessageByCode(ValidMemberType.class,validType), "********", oaUserBO.getUserName()
                        , LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
        }
        if (smallpro == null) {
            result.setCode(500);
            result.setMessage("小件售后接件单不存在！");
            return result;
        }
        smallpro.setCodeMsg(codeMsg);
        boolean updateFlag =
                ((SmallproExchangePurchaseServiceImpl) AopContext.currentProxy()).checkCodeMessageWrite(result,
                        smallpro);
        if (!updateFlag) {
            return result;
        }
        if (code.contains("【")) {
            SmallproMsgCodeSaveInfo saveInfo = smallproMapper.getMsgCodeSaveInfo(smallproId);
            if (saveInfo != null && !saveInfo.getUserId().equals(76783) && (saveInfo.getKind().equals(2) || saveInfo.getKind().equals(3))) {
                SmallproSendMsgCodeAreaInfoBO areaInfo =
                        smallproMapper.getMsgCodeSendAreaInfo(saveInfo.getAreaId());
                StringBuilder messageBuilder = new StringBuilder("您送修的");
                messageBuilder.append(saveInfo.getName());
                messageBuilder.append("，小件售后单号【");
                messageBuilder.append(smallproId);
                messageBuilder.append("】正在进行");
                if (saveInfo.getKind() == 2) {
                    messageBuilder.append("换货");
                } else if (saveInfo.getKind() == 3) {
                    messageBuilder.append("退货");
                } else {
                    messageBuilder.append("处理");
                }
                if ("九机网".equals(areaInfo.getPrintName())) {
                    messageBuilder.append("，如有疑问请联系400-008-3939。");
                } else {
                    messageBuilder.append("。");
                }
                String message = messageBuilder.toString();
                if (saveInfo.getOpenId() != null) {
                    String murl = "";
                    if (oaUserBO != null && oaUserBO.getXTenant() != null) {
                        R<String> mUrlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, oaUserBO.getXTenant());
                        if (CommonUtils.isRequestSuccess(mUrlR)) {
                            murl = mUrlR.getData();
                        }
                    }
                    smallproForwardExService.pushInfoToWechatTemplateMessage(saveInfo.getOpenId(), "小件售后办理",
                            String.valueOf(smallproId), messageBuilder.toString(),
                            LocalDateTime.now().format(DateTimeFormatter.ofPattern("M月dd日 HH:mm")),
                            murl + "/after-service/small-detail/" + smallproId, Optional.ofNullable(saveInfo.getWechatId()).orElse(1));
                } else if (saveInfo.getSubMobile() != null && Boolean.TRUE.equals(areaInfo.getIsSend())) {
                    // 获取发送渠道(验证码通道)
                    Long tenant = Namespaces.get();
                    R<String> channelR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_CHANNEL, Math.toIntExact(tenant));
                    String channel = CommonUtils.isRequestSuccess(channelR) ? channelR.getData() : "";
//                    String url = inwcfUrlSource.getNoticeSms(saveInfo.getSubMobile(), message, channel, "");
//                    String respone = HttpClientUtil.get(url);

                    smsService.sendSms(saveInfo.getSubMobile(), message, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(smallpro.getAreaId(), ESmsChannelTypeEnum.YZMTD));
                }
            }
        }
        // 保存验证码成功则清除缓存的验证码
        stringRedisTemplate.opsForHash().delete("smallproCodeMsgCache",
                "msgCode" + type + smallproId);
        result.setCode(0);
        result.setMessage("保存验证码成功！");
        return result;
    }

    // endregion

    // region 发送验证码给用户 pushCodeMessageToUser
    @Override
    public SmallproNormalCodeMessageRes pushCodeMessageToUser(Integer smallproId, Integer areaId, Integer subId,
                                                              Integer type) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        // type:验证码类型[1换货验证码|2退货验证码]
        SmallproSendMsgCodeAreaInfoBO areaInfo = smallproMapper.getMsgCodeSendAreaInfo(areaId);
        if (areaInfo == null) {
            result.setCode(500);
            result.setMessage("没有当前门店信息！");
            return result;
        }
        if (Boolean.TRUE.equals(areaInfo.getIsSend())) {
            SmallproSendMsgCodeSubInfoBO subInfo = smallproMapper.getMsgCodeSendSubInfo(subId);
            // 从老库查
            if (null == subInfo && oaUserBO.getXTenant() < 1000) {
                subInfo = smallproMapper.getMsgCodeSendSubInfoHis(subId);
            }
            if (subInfo == null) {
                result.setCode(500);
                result.setMessage("订单状态错误！");
                return result;
            }
            // 处理门店信息
            if ("九机网".equals(areaInfo.getAreaName())) {
                areaInfo.setAreaName("九机");
            }
            areaInfo.setAreaName(areaInfo.getAreaName().replace("中央", "红河"));
            // 获取发送渠道
            R<String> channelR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_CHANNEL, Math.toIntExact(areaInfo.getXTenant()));
            String channel = CommonUtils.isRequestSuccess(channelR) ? channelR.getData() : "";
            // 构建验证码短信信息
            ThreadLocalRandom random = ThreadLocalRandom.current();
            Integer code = Math.abs(random.nextInt(100000));
            StringBuilder messageBuilder = new StringBuilder("您正在进行");
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            String dateTime = LocalDateTime.now().format(dateTimeFormatter);
            if (type == 1) {
                messageBuilder.append("[售后配件换货服务]操作，验证码");
            } else if (type == 2) {
                messageBuilder.append("[售后退配件服务]操作，验证码");
            }
            messageBuilder.append(code);
            messageBuilder.append("。如非本人操作请忽略。");

            String message = messageBuilder.toString();
            long xtenant = Namespaces.get();
            String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
            if (XtenantEnum.isSaasXtenant()
                    && StringUtils.isNotEmpty(openXtenantStr)
                    && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenant,
                        SmsConfigCodeEnum.CODE_102.getCode());
                if (CommonUtils.isRequestSuccess(smsConfigResult)) {
                    SmsConfigVO smsConfig = smsConfigResult.getData();
                    // 消息内容
                    List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                    String smsMessage = smsConfig.getTemplate();
                    if (StringUtils.isNotEmpty(smsMessage)
                            && CollectionUtils.isNotEmpty(fields)) {
                        for (SmsConfigVO.SmsField field : fields) {
                            if ("<printName>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), areaInfo.getPrintName());
                            }
                            if ("<areaName>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), areaInfo.getAreaName());
                            }
                            if ("<serviceType>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), type == 1 ? "售后配件换货服务" : "售后退配件服务");
                            }
                            if ("<dateTime>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), dateTime);
                            }
                            if ("<code>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), String.valueOf(code));
                            }
                        }
                    }
                    // 推送方式
                    List<Integer> pushMethods = smsConfig.getPushMethod();
                    // sms消息
                    boolean sendSms = CollectionUtils.isNotEmpty(pushMethods)
                            && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                    if (sendSms) {
                        message = smsMessage;
                    }
                }
            }
            R<Boolean> ssR = smsService.sendSms(subInfo.getSubMobile(), message, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.VERIFICATION_CODE));
            if(ssR.isSuccess()){
                // 缓存Code
                stringRedisTemplate.opsForHash().put("smallproCodeMsgCache", "msgCode" + type + smallproId,
                        String.valueOf(code));
                result.setMessage("发送短信验证码成功！");
                result.setCode(0);
            }else{
                result.setMessage(ssR.getUserMsg());
                result.setCode(500);
                ssR.businessLogs().forEach(bl -> SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,bl));
            }
        } else {
            result.setCode(500);
            result.setMessage("当前门店不支持发送验证码！");
            return result;
        }
        return result;
    }

    // endregion

    // endregion

    // region privateMethod


    // endregion

    // region transactional

    // region 保存验证码写操作 checkCodeMessageWrite

    /**
     * description: <保存验证码写操作>
     * translation: <Save verification code write operation>
     *
     * @param result   param
     * @param smallpro param
     * @return boolean
     * <AUTHOR>
     * @date 14:47 2020/4/13
     * @since 1.0.0
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean checkCodeMessageWrite(SmallproNormalCodeMessageRes result, Smallpro smallpro) {
        boolean updateFlag = smallproService.updateById(smallpro);
        if (!updateFlag) {
            result.setCode(500);
            result.setMessage("保存验证码更新数据库失败！");
            return false;
        }
        return true;
    }

    // endregion

    // region 取件写操作 pickupWrite

    /**
     * description: <取件写操作>
     * translation: <Pickup and write operations>
     *
     * @param result                        param
     * @param kind                          param
     * @param oaUserBO                      param
     * @param smallpro                      param
     * @param outboundInfoList              param
     * @param jiujiServiceInpriceInfoBOList param
     * @return boolean
     * <AUTHOR>
     * @date 14:48 2020/4/13
     * @since 1.0.0
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean pickupWrite(SmallproNormalCodeMessageRes result, Integer kind, OaUserBO oaUserBO,
                               Smallpro smallpro, List<SmallproPickUpOutboundInfoBO> outboundInfoList,
                               List<SmallproJiujiServiceInpriceInfoBO> jiujiServiceInpriceInfoBOList) {
        List<SmallproKcReqBO> returnKcReqList = new ArrayList<>();
        boolean flag = false;
        try {

            boolean present = Optional.ofNullable(SpringUtil.getBean(StringRedisTemplate.class).opsForValue()
                    .get(String.format(RedisKeys.NOT_EXCHANGE_GOODS_DISASSEMBLE_KEY, smallpro.getId()))).map(Convert::toBool)
                    .filter(Boolean::booleanValue).isPresent();
            //判断是否为年包
            boolean isYearCard = Optional.ofNullable(smallpro.getServiceType()).orElse(Integer.MAX_VALUE).equals(SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode());
            //判断如果过是年包或者redis 存在的单号那就不进行拆单
            boolean disassembleFlag = !present && !isYearCard;
            //判断如果过是换货的情况下那就进行原始订单的拆单逻辑处理
            if(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(kind) && CollectionUtils.isNotEmpty(outboundInfoList) && disassembleFlag){
                    outboundInfoList.forEach(item->{
                        Integer basketId = item.getBasketId();
                        Integer ppriceId = item.getPpriceId();
                        Integer count = item.getCount();
                        SplitBasketConditionReq splitBasketConditionReq = new SplitBasketConditionReq();
                        splitBasketConditionReq.setBasketId(basketId)
                                .setSmallproId(smallpro.getId())
                                .setPpid(ppriceId)
                                .setCount(count);
                        SplitBasketRes splitBasketRes = exchangeGoodsService.exchangeSplitBasket(splitBasketConditionReq);
                        if(splitBasketRes.getNewBasketId()!=null){
                            smallproBillService.lambdaUpdate().eq(SmallproBill::getId,item.getSmallproBillId())
                                    .set(SmallproBill::getBasketId,splitBasketRes.getNewBasketId())
                                    .update();
                            item.setBasketId(splitBasketRes.getNewBasketId());
                        }
                    });
                }


            flag = SpringUtil.getBean(SmallproExchangePurchaseService.class)
                    .handlePickupWrite(result, kind, oaUserBO, smallpro, outboundInfoList, jiujiServiceInpriceInfoBOList,
                            returnKcReqList);
            if(!flag){
                //没有成功进行回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            }
            //执行延后执行的操作
            SpringContextUtil.runAndRemoveRequestLambda();
            return true;
        }finally {
            if(!flag && !returnKcReqList.isEmpty()){
                //库存操作失败进行事务性补偿
                for (SmallproKcReqBO temp : returnKcReqList) {
                    smallproForwardExService.stockOperations(temp);
                }
            }

        }
    }


    /**
     * 保护膜自动报废
     * @param filmScrapReq
     */
    public Boolean filmScrap(FilmScrapReq filmScrapReq){
        try {
            //小件单查询
            Integer smallProId = filmScrapReq.getSmallProId();
            Smallpro smallpro = Optional.ofNullable(smallproService.getById(smallProId)).orElseThrow(() -> new CustomizeException("取件单查询失败"));
            Boolean allScrapProduct = isAllScrapProduct(smallpro);
            if(!allScrapProduct){
                return Boolean.FALSE;
            }
            OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("取机自动报废登录人失效"));
            //旧件信息查询
            List<SmallOldProVirtualInfo> smallProGoods = shouhouFanchangService.getSmallProGoods(smallProId);
            if(CollectionUtils.isEmpty(smallProGoods)){
                return Boolean.FALSE;
            }
            List<Integer> returnFactoryIds = new ArrayList<>();
            for (SmallOldProVirtualInfo smallOldProVirtualInfo : smallProGoods){
                returnFactoryIds.add(smallOldProVirtualInfo.getFcId());
            }
            //自动报废
            if(CollectionUtils.isNotEmpty(returnFactoryIds)){
                CashOrScrapBatchReq cashOrScrapBatchReq = new CashOrScrapBatchReq();
                cashOrScrapBatchReq.setType(NumberConstant.TWO);
                cashOrScrapBatchReq.setReturnFactoryIds(returnFactoryIds);
                this.CashOrScrapBatch(cashOrScrapBatchReq, userBO);
                return Boolean.TRUE;
            }
        } catch (Exception e){
            RRExceptionHandler.logError("保护膜自动报废异常", filmScrapReq, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            return Boolean.FALSE;
        }
        return Boolean.FALSE;
    }
    /**
     * description: <取件写操作不要升级为public,需要进行事务性补偿回滚>
     * translation: <Pickup and write operations>
     *
     * @param result                        param
     * @param kind                          param
     * @see SmallProKindEnum
     * @param oaUserBO                      param
     * @param smallpro                      param
     * @param outboundInfoList              param
     * @param jiujiServiceInpriceInfoBOList param
     * @param returnKcReqList
     * @return boolean
     * <AUTHOR>
     * @date 14:48 2020/4/13
     * @since 1.0.0
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePickupWrite(SmallproNormalCodeMessageRes result, Integer kind, OaUserBO oaUserBO,
                                     Smallpro smallpro, List<SmallproPickUpOutboundInfoBO> outboundInfoList,
                                     List<SmallproJiujiServiceInpriceInfoBO> jiujiServiceInpriceInfoBOList,
                                     List<SmallproKcReqBO> returnKcReqList) {

        // 取件完成标志
        boolean completeFlag = false;
        AtomicReference<Integer> opCount = new AtomicReference<>(0);
        LambdaUpdateWrapper<Smallpro> updateWrapper = new LambdaUpdateWrapper<>();
        List<LastExchangeSmallproBo> lastChangeSmallPro = Collections.emptyList();
        // 维修取件
        if (kind.equals(1)) {
            if ((smallpro.getIsShouyingLock() == null || smallpro.getIsShouyingLock() == false)
                    && smallpro.getFeiyong() != null && smallpro.getFeiyong().doubleValue() > 0) {
                result.setCode(500);
                result.setMessage("请先收银，才能取件!");
                return false;
            }
        }
        // 非维修取件
        else if (kind.equals(2) || kind.equals(3) || kind.equals(4)) {
            smallpro.setOwenStats(1);
            updateWrapper.set(Smallpro::getOwenStats, 1);
            List<SmallproPickupOrderInfoBO> smallproPickupOrderInfoList = smallproMapper.getSmallproPickupOrderInfo(smallpro.getId());
            if (smallproPickupOrderInfoList.size() <= 0) {
                result.setCode(500);
                result.setMessage("订单相关订单信息错误！");
                return false;
            }
            // 换货判断是否二次换货
            if ((SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(kind) || SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(kind))) {
                // 获取最近一次该basket换货记录
                lastChangeSmallPro = smallproExchangePurchaseService.listLastChangeSmallpro(smallpro.getId(), smallpro.getSubId(),
                        smallproPickupOrderInfoList.stream().map(SmallproPickupOrderInfoBO::getBasketId)
                                .collect(Collectors.toSet()));
                // 获取上一次出库的ppid
                if (!lastChangeSmallPro.isEmpty()) {
                    List<LastExchangeSmallproBo> finalLastChangeSmallPro = lastChangeSmallPro;
                    smallproPickupOrderInfoList
                            .forEach(e -> finalLastChangeSmallPro.stream()
                                    .filter(lcs -> Objects.equals(lcs.getBasketId(),e.getBasketId()))
                                    .filter(lcs -> Objects.nonNull(lcs.getPpriceId()))
                                    .findFirst()
                                    .ifPresent(lcs -> {
                                        e.setPpriceId(lcs.getPpriceId());
                                        e.setInprice(ObjectUtil.defaultIfNull(lcs.getInprice(),e.getInprice()));
                                    }));
                }
            }


            // 遍历插入shouhou_fanchang表
            smallproPickupOrderInfoList.forEach(bo -> {
                for (int topi = 0; topi < bo.getCount(); topi++) {
                    ShouhouFanchang insertTemp = new ShouhouFanchang();
                    insertTemp.setDtime(LocalDateTime.now())
                            .setRstats(0)
                            .setSmallproid(bo.getSmallproId())
                            .setPpid(bo.getPpriceId())
                            .setPpid1(Optional.ofNullable(smallpro.getChangePpriceid()).filter(CommenUtil::isNotNullZero).orElse(bo.getPpriceId1()))
                            .setInprice(bo.getInprice())
                            .setBillId(bo.getSmallproBillId())
                            .setBasketId(bo.getBasketId());
                    shouhouFanchangService.save(insertTemp);
                    opCount.set(opCount.get() + 1);
                }
            });
        }

        // 取件更新状态
        updateWrapper.set(Smallpro::getStats, 1)
                .set(Smallpro::getQujianDate, LocalDateTime.now()).eq(Smallpro::getId, smallpro.getId()).ne(Smallpro::getStats, 1);
        smallpro.setStats(1).setQujianDate(LocalDateTime.now());
        boolean flag = smallproService.update(updateWrapper);
        if (!flag) {
            result.setCode(500);
            result.setMessage("小件取件操作失败！数据库更新失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }

        // 更新操作完成
        AtomicReference<BigDecimal> inprice = new AtomicReference<>(BigDecimal.ZERO);
        // 2换货4现货 出库
        if ((kind.equals(2) && !smallproService.isHqtxh(smallpro)) || kind.equals(4)) {
            if (outboundInfoList != null && outboundInfoList.size() > 0) {
                outboundInfoList.forEach(bo -> {
                    StringBuilder commentBuilder = new StringBuilder(
                            (kind == 2 ? "售后小件换货取件出库，数量：" : "售前小件转入售后出库，数量："));
                    // kind=4 现货，出入库记录关联的basketId=SmallproBill.id
                    // kind=2 换货，出入库记录关联的basketId=Smallpro.id
                    Stream.Builder<SmallProKindEnum> billIdKindBuilder = Stream.builder();
                    billIdKindBuilder.add(SmallProKindEnum.SMALL_PRO_KIND_SPOT);
                    if(XtenantEnum.isJiujiXtenant()){
                        billIdKindBuilder.add(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE);
                    }
                    Integer kcBasketId = billIdKindBuilder.build().anyMatch(ske -> ske.getCode().equals(kind)) ? bo.getSmallproBillId() : bo.getSmallproId();
                    commentBuilder.append(bo.getCount());
                    SmallproKcReqBO smallproKcReqBO = new SmallproKcReqBO();
                    smallproKcReqBO.setPpriceId(bo.getPpriceId1()).setCount(bo.getCount() * -1)
                            .setInPrice(BigDecimal.valueOf(0)).setAreaId(bo.getAreaId())
                            .setUserName(oaUserBO.getUserName()).setInsource("")
                            .setComment(commentBuilder.toString()).setBasketId(kcBasketId)
                            .setCheck1(1).setCheck2(1).setShouhouId(null).setIsLp(false).setDiaoboFlag(false);
                    LocalDateTime kcStartTime = LocalDateTime.now();
                    SmallproNormalCodeMessageRes kcResult = smallproForwardExService.stockOperations(smallproKcReqBO);
                    if (kcResult == null || kcResult.getCode() != 0) {
                        result.setCode(501);
                        result.setMessage(Optional.ofNullable(kcResult).map(SmallproNormalCodeMessageRes::getMessage)
                                .filter(StrUtil::isNotBlank).orElse("出库失败，无法取件，请确保当前地区库存充足"));
                        Optional<BigDecimal> outKcInpriceOpt = Optional.empty();
                        if(kcResult == null){
                            //连接发生异常了,独立事务查询下数据,如果出库成功了,需要放到回滚列表
                            RRExceptionHandler.logError("小件出库",smallproKcReqBO,new CustomizeException("出库连接发生异常"),smsService::sendOaMsgTo9JiMan);
                            outKcInpriceOpt = MultipleTransaction.query(DataSourceConstants.DEFAULT,
                                    () -> SpringUtil.getBean(ProductKclogsService.class).lambdaQuery()
                                            .eq(ProductKclogs::getPpriceid, smallproKcReqBO.getPpriceId())
                                            .eq(ProductKclogs::getAreaid, smallproKcReqBO.getAreaId())
                                            .eq(ProductKclogs::getCount, smallproKcReqBO.getCount())
                                            .eq(ProductKclogs::getBasketId, smallproKcReqBO.getBasketId())
                                            .eq(ProductKclogs::getComment, smallproKcReqBO.getComment())
                                            .ge(ProductKclogs::getDtime, kcStartTime).select(ProductKclogs::getInprice)
                                            .orderByDesc(ProductKclogs::getId).list()
                                            .stream().map(ProductKclogs::getInprice).findFirst());
                        }
                        if(outKcInpriceOpt.isPresent()){
                            //出库成功放入回滚数据
                            smallproKcReqBO.setCount(smallproKcReqBO.getCount() * -1)
                                    .setComment(ProductKcService.TRANS_ROLLBACK_COMMENT_START + smallproKcReqBO.getComment());
                            smallproKcReqBO.setInPrice(outKcInpriceOpt.get());
                            returnKcReqList.add(smallproKcReqBO);
                        }
                        return;
                    } else {
                        JSONObject resultData = JSONObject.parseObject(kcResult.getMessage());
                        JSONObject data = JSONObject.parseObject(resultData.get("data").toString());
                        inprice.set(new BigDecimal(data.get("inprice").toString()));
                        smallproKcReqBO.setCount(smallproKcReqBO.getCount() * -1)
                                .setComment(ProductKcService.TRANS_ROLLBACK_COMMENT_START + smallproKcReqBO.getComment());
                        smallproKcReqBO.setInPrice(inprice.get());
                        returnKcReqList.add(smallproKcReqBO);
                    }
                    opCount.set(opCount.get() + 1);
                });
            }
            if (result.getCode() != null && (result.getCode() == 500 || result.getCode() == 501)) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            } else {
                //出库成功，要将成功后的库存成本更新至返厂表和smallproBill表
                for (SmallproKcReqBO temp : returnKcReqList) {
                    BigDecimal inPrice = temp.getInPrice();
                    if (inPrice != null) {
                        //更新成本
                        shouhouFanchangService.lambdaUpdate().set(ShouhouFanchang::getInprice, inPrice)
                                .eq(ShouhouFanchang::getSmallproid, smallpro.getId())
                                .eq(ShouhouFanchang::getPpid1, temp.getPpriceId()).update();
                    }
                }
            }
        }
        if (smallpro.getStats() == 1) {
            completeFlag = true;
            if (smallpro.getServiceType() != null && smallpro.getServiceType() != 0) {
                QueryWrapper<SmallproBill> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(SmallproBill::getSmallproID, smallpro.getId());
                // 使用九机服务
                List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(wrapper);
                if (smallproBillList == null || smallproBillList.isEmpty()) {
                    result.setCode(500);
                    result.setMessage("查无小件订单商品信息！");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
                if (smallproBillList.size() > 1) {
                    result.setCode(500);
                    result.setMessage("九机服务限制小件单订单商品为1！");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
                if (smallpro.getKind() == 1 && smallpro.getServiceType() == 2
                        && (smallpro.getCostPrice() == null || smallpro.getCostPrice().doubleValue() == 0.0)) {
                    result.setCode(500);
                    result.setMessage("请填写维修成本！");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
                if (smallpro.getServiceType() == 4) {
                    IYearPackageTransferService yearPackageTransferService = SpringUtil.getBean(IYearPackageTransferService.class);
                    Optional<YearPackageTransferDetailDto> transferDetailOpt = Optional.ofNullable(smallpro.getImei())
                            .filter(StrUtil::isNotBlank).map(yearPackageTransferService::getTransferDetail);
                    FilmCardInfoForPickupBO filmCardInfoForPickupBO = transferDetailOpt.isPresent() ? null :
                            smallproMapper.selectTiemoCardInfoByPickup(smallproBillList.get(0).getBasketId());
                    if (filmCardInfoForPickupBO == null && !transferDetailOpt.isPresent()
                         || filmCardInfoForPickupBO != null && !transferDetailOpt.isPresent()
                            && ObjectUtil.notEqual(filmCardInfoForPickupBO.getUserId(), smallpro.getUserId())) {
                        result.setCode(500);
                        result.setMessage("没有找到年包信息或当前年包次数已用完，请核对！");
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return false;
                    }
                    Integer updateSize;
                    //
                    if (filmCardInfoForPickupBO != null){
                        //年包过期时间点以接件时间做校验
                        LocalDateTime endTime = LocalDateTime.of(filmCardInfoForPickupBO.getETime().getYear(), filmCardInfoForPickupBO.getETime().getMonth(),
                                filmCardInfoForPickupBO.getETime().getDayOfMonth(), 23, 59, 59);
                        if (smallpro.getInDate().toInstant(ZoneOffset.of("+8")).toEpochMilli() >
                                endTime.plusDays(SmallproFilmCardService.FILM_CARD_OVER_DAY_LINE).toInstant(ZoneOffset.of("+8")).toEpochMilli()) {
                            result.setCode(500);
                            result.setMessage("年包已过期，请审核！");
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return false;
                        }
                        if (filmCardInfoForPickupBO.getUseCount() >= filmCardInfoForPickupBO.getAllCount()) {
                            result.setCode(500);
                            result.setMessage("年包次数已使用完！");
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return false;
                        }
                        //当年包已过期，还在使用时

                        if (smallpro.getInDate().toInstant(ZoneOffset.of("+8")).toEpochMilli() > endTime.toInstant(ZoneOffset.of("+8")).toEpochMilli()){
                            updateSize = smallproMapper.updateTiemoCardByPickUp(filmCardInfoForPickupBO.getId(),smallpro.getInDate(),true);
                        }else {
                            updateSize = smallproMapper.updateTiemoCardByPickUp(filmCardInfoForPickupBO.getId(),smallpro.getInDate(),false);
                        }
                    }else{
                        YearPackageTransferDetailDto transferDetailDto = transferDetailOpt.get();
                        R<?> checkTransferR = yearPackageTransferService.validateYearPackageTransfer(transferDetailDto, false);
                        if(!checkTransferR.isSuccess()){
                            result.setCode(500);
                            result.setMessage(checkTransferR.getUserMsg());
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return false;
                        }
                        //更新使用状态
                        updateSize = Convert.toInt(yearPackageTransferService.lambdaUpdate().eq(YearPackageTransferPo::getId, transferDetailDto.getId())
                                .eq(YearPackageTransferPo::getStatus, transferDetailDto.getStatus())
                                .set(YearPackageTransferPo::getStatus, YearPackageTransferStatusEnum.USED.getCode())
                                .set(YearPackageTransferPo::getUpdateTime, LocalDateTime.now())
                                .set(YearPackageTransferPo::getSmallId, smallpro.getId())
                                .update());

                    }
                    if (updateSize > 0) {
                        if(filmCardInfoForPickupBO != null){
                            TiemoCardUserLog tiemoCardUserLog = new TiemoCardUserLog();
                            tiemoCardUserLog.setCardId(filmCardInfoForPickupBO.getId()).setSmallProId(smallpro.getId()).setDtime(LocalDateTime.now()).setInuser(oaUserBO.getUserName());
                            tiemoCardUserLogService.save(tiemoCardUserLog);
                            // 旧凭证, 不再推送
                            if (false && inprice.get().compareTo(BigDecimal.ZERO) > 0) {
                                Areainfo areainfo = areainfoService.getByIdSqlServer(filmCardInfoForPickupBO.getAreaId());
                                if (areainfo.getKind1() == 1) {
                                    String kemu = "", jief = "", daif = "", fzhs = "", infotext = "";
                                    String infoText = LocalDateTime.now().format(smallProConstant.getYYYY_MM_DD()) +
                                            "钢化膜年包服务出险，小件单号" + smallpro.getId();
                                    infotext = infoText + "|" + infoText;
                                    kemu = "640118|140502";
                                    jief = inprice.get().setScale(2, RoundingMode.HALF_UP).doubleValue() + "|0";
                                    daif = "0|" + inprice.get().setScale(2, RoundingMode.HALF_UP).doubleValue();
                                    fzhs = areainfo.getArea() + "|无";
                                    PingzhengBO pingzhengBO = voucherService.buildPingzheng(infotext, kemu,
                                            jief, daif, fzhs);
                                    pingzhengBO.setIsaudit("1");
                                    PingzhengResultBO voucherResult =
                                            voucherService.addPingZheng(pingzhengBO);
                                    if (!voucherResult.getFlag()) {
                                        result.setCode(500);
                                        result.setMessage("年包凭证生成失败，请核对！");
                                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                        return false;
                                    }
                                    smallproLogService.addLogs(smallpro.getId(), StrUtil.format("年包凭证,凭证id:{}"
                                            ,voucherResult.getPzId()), oaUserBO.getUserName(), 0);
                                }
                            }
                        }
                    }else{
                        result.setCode(500);
                        result.setMessage("年包已超期或次数已使用完！");
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return false;
                    }

                } else if (smallpro.getServiceType() == 1) {
                    BigDecimal costPrice = BigDecimal.ZERO;
                    if (smallpro.getKind() == 1) {
                        costPrice = smallpro.getCostPrice();
                    } else if (smallpro.getKind() == 2) {
                        if (jiujiServiceInpriceInfoBOList != null && jiujiServiceInpriceInfoBOList.size() > 0) {
                            SmallproJiujiServiceInpriceInfoBO inpriceInfo = jiujiServiceInpriceInfoBOList.get(0);
                            costPrice = inpriceInfo.getInPrice();
                            if (smallpro.getServiceType() == 1) {
                                BigDecimal[] basketPrice = new BigDecimal[]{inpriceInfo.getPrice()};
                                if(basketPrice[0] == null){
                                    //从历史库查询
                                    BasketService basketService = SpringUtil.getBean(BasketService.class);
                                    basketPrice[0] = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () ->
                                            Optional.ofNullable(basketService.getByIdSqlServer(inpriceInfo.getBasketId())).map(Basket::getPrice)
                                            .orElseThrow(() -> new CustomizeException("订单商品价格不能为空")));
                                }
                                costPrice = costPrice.subtract(basketPrice[0].multiply(BigDecimal.valueOf(0.2)));
                            }
                        }
                    }
                    if (costPrice.doubleValue() <= 0) {
                        result.setCode(500);
                        result.setMessage("九机服务成本异常!");
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return false;
                    }
                    SmallproUpdateJiujiServiceBO updateJiujiServiceBO = new SmallproUpdateJiujiServiceBO();
                    updateJiujiServiceBO.setCostPrice(costPrice).setBasketId(smallproBillList.get(0).getBasketId())
                            .setServiceType(smallpro.getServiceType() == null ? null :
                                    smallpro.getServiceType().byteValue());
                    if (smallpro.getServiceType() == 1) {
                        List<Integer> serviceTypeList = new ArrayList<>(2);
                        serviceTypeList.add(15);
                        serviceTypeList.add(17);
                        updateJiujiServiceBO.setServiceTypeList(serviceTypeList);
                    } else if (smallpro.getServiceType() == 2 || smallpro.getServiceType() == 3) {
                        if (smallpro.getServiceType() == 2) {
                            updateJiujiServiceBO.setServiceTypeResult(16);
                        } else if (smallpro.getServiceType() == 3) {
                            updateJiujiServiceBO.setServiceTypeResult(14);
                        }
                    }
                    int count = smallproMapper.updateJiujiService(updateJiujiServiceBO);
                    if (count <= 0) {
                        result.setCode(500);
                        result.setMessage("九机服务出险记录失败!");
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return false;
                    }
                }
            }
        }

        // 取件发送评价推送 维修||换货||退货
        if (completeFlag && (smallpro.getKind() == 1 || smallpro.getKind() == 2 || smallpro.getKind() == 3)) {
            //发送评价,不影响主流程
            try {
                sendEvaluateInfo(smallpro,oaUserBO);
            } catch (Exception e) {
                RRExceptionHandler.logError("发送评价信息",Dict.create().set("smallpro",smallpro),e,smsService::sendOaMsgTo9JiMan);
            }
        }

        if (smallpro.getKind() == 1 && smallpro.getStats() == 1) {
            smallproLogService.addLogs(smallpro.getId(), "已取件", oaUserBO.getUserName(), 0);
        }
        if(!lastChangeSmallPro.isEmpty()){
            String logContent = lastChangeSmallPro.stream()
                    .map(lcs -> StrUtil.format("basketId[{}]上一次换货的小件id:{},ppid:{}", lcs.getBasketId(), lcs.getSmallproId(), lcs.getPpriceId()))
                    .collect(Collectors.joining(" "));
            smallproLogService.addLogs(smallpro.getId(), logContent, oaUserBO.getUserName(), 0);
        }

        // 如果取件完成，则自动完成物流单
        if (completeFlag) {
            // 判断是否有售后预约单，有则直接完成
            UpdateWrapper<ShouhouYuyue> yuyueCompleteWrapper = new UpdateWrapper<>();
            yuyueCompleteWrapper.lambda().set(ShouhouYuyue::getStats, 3)
                    .eq(ShouhouYuyue::getSmallproid, smallpro.getId())
                    .and(wrapper -> wrapper.eq(ShouhouYuyue::getStats, 6).or().eq(ShouhouYuyue::getStats, 7))
                    .and(wrapper -> wrapper.eq(ShouhouYuyue::getIszy, 0).or().isNull(ShouhouYuyue::getIszy));
            boolean yueyuUpdateFlag = shouhouYuyueService.update(yuyueCompleteWrapper);

            StringBuilder msgBuilder = new StringBuilder();
            Areainfo areainfo = areainfoService.getByIdSqlServer(smallpro.getAreaId());
            String printName = (areainfo == null || areainfo.getPrintName() == null) ? "九机网" : areainfo.getPrintName();
            ShouhouMsgconfig shouhouMsgconfig = null;
            switch (smallpro.getKind()) {
                case 2:
                    /*msgBuilder.append("尊敬的");
                    msgBuilder.append(printName);
                    msgBuilder.append("会员您好，您的");
                    msgBuilder.append(smallpro.getName());
                    msgBuilder.append("商品");
                    String messageByCode = getNameByServiceType(smallpro.getServiceType());
                    if (StringUtils.isNotEmpty(messageByCode)) {
                        msgBuilder.append("经检测可享受" + messageByCode + "服务，");
                    }
                    msgBuilder.append("已为您换新处理。");*/
                    shouhouMsgconfig =
                            shouhouMsgconfigService.getShouhouMsgconfigByLogType(ShouHouMsgConfigEnum.TYPE3.getCode());
                    break;
                case 3:
                  /*  msgBuilder.append("尊敬的");
                    msgBuilder.append(printName);
                    msgBuilder.append("会员您好，您的");
                    msgBuilder.append(smallpro.getName());
                    msgBuilder.append("商品经检测为");
                    msgBuilder.append(smallpro.getProblem());
                    msgBuilder.append("商品经检测故障属实，已为您退款处理。");*/
                    shouhouMsgconfig =
                            shouhouMsgconfigService.getShouhouMsgconfigByLogType(ShouHouMsgConfigEnum.TYPE4.getCode());
                    break;
                default:
                    msgBuilder.append("您送修的");
                    msgBuilder.append(smallpro.getName());
                    msgBuilder.append("维修单号");
                    msgBuilder.append(smallpro.getId());
                    msgBuilder.append("已经处理完毕。感谢您的认可与支持");
                    break;
            }
            if (null != shouhouMsgconfig) {
                smallproService.sendSmallProWeiXinMsg(smallpro.getUserId(), smallpro.getAreaId(), shouhouMsgconfig,
                        shouhouMsgconfig.getMsgcontent(), smallpro.getMobile(), smallpro.getId());
                smallproLogService.addLogs(smallpro.getId(), shouhouMsgconfig.getMsgcontent(), oaUserBO.getUserName()
                        , 1);
            } else {
                String msg = msgBuilder.toString();
                smallproLogService.addLogs(smallpro.getId(), msg, oaUserBO.getUserName(), 1);
            }
            if (smallpro.getWuliuId() != null) {
                // 自动完成物流单
                wuliuCloud.smallproAutoComplete(smallpro.getWuliuId(), smallpro.getId(),
                        oaUserBO.getUserName());

            }

            // 小件退货记录日志
            if (smallpro.getKind() == 3 && smallpro.getSubId() > 0) {
                SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                subLogsNewReq.setComment(StrUtil.indexedFormat("小件退货：<a target=''_blank'' href=''/staticpc/#/small-refund/{0,number,#}''>{0,number,#}</a>",smallpro.getId()));
                subLogsNewReq.setSubId(smallpro.getSubId());
                subLogsNewReq.setInUser(oaUserBO.getUserName());
                subLogsNewReq.setShowType(Boolean.FALSE);
                subLogsNewReq.setType(1);
                try {
                    SpringUtil.getBean(SubLogsCloud.class).addSubLog(subLogsNewReq);
                } catch (Exception e) {
                    RRExceptionHandler.logError("小件退款增加订单日志",subLogsNewReq,e,msg->smsService.sendOaMsgTo9JiMan(msg));
                }
            }
            if (opCount.get() > 0) {
                String id = smallproLogService.addLogs
                        (smallpro.getId(), "已取件", oaUserBO.getUserName(), 0);
            }
        }

        // 如果区间完成，则自动完成备货单，换货时才判断，即kind=2
        if (smallpro.getKind() == 2) {
            if (smallpro.getHhSubId() != null && smallpro.getHhSubId() > 0) {
                List<SmallproStockingInfoBO> smallproStockingInfoBOList =
                        smallproMapper.getSmallproStockingInfoBo(smallpro.getHhSubId());
                if (smallproStockingInfoBOList != null && smallproStockingInfoBOList.size() > 0) {
                    HashMap<String, String> params = new HashMap<>(2);
                    params.put("userName", oaUserBO.getUserName());
                    String delPJUrl = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST).getData() + "/oaApi.svc/rest/PJbeihuoDel";
                    for (SmallproStockingInfoBO temp : smallproStockingInfoBOList) {
                        params.put("basket_id", String.valueOf(temp.getBasketId()));
                        String response = HttpClientUtil.post(delPJUrl, params);
                    }
                    String delOrderUrl = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST).getData() + "/oaApi.svc/rest/delOrderByCh999User";
                    List<String> rank = new ArrayList<>(3);
                    rank.add("777");
                    rank.add("52");
                    rank.add("55");
                    params = new HashMap<>(4);
                    params.put("id", String.valueOf(smallpro.getHhSubId()));
                    params.put("userName", oaUserBO.getUserName());
                    params.put("rank", JSONObject.toJSONString(rank));
                    params.put("type", String.valueOf(1));
                    String response = HttpClientUtil.post(delOrderUrl, params);
                }
            }
        }
         //推送mq信息,九机自营门店做凭证
        if(completeFlag && XtenantEnum.isJiujiXtenant()){
            RabbitTemplate oaAsyncRabbitTemplate = SpringUtil.getBean(RabbitTemplate.class);
            oaAsyncRabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, JSON.toJSONString(new SubPushMsgBO()
                    .setAct("smallProCompletePush1").setData(smallpro.getId())));
        }
        return true;
    }

    @Override
    public void sendEvaluateInfo(Smallpro smallpro, OaUserBO oaUserBO){
        List<SmallproSendReviewInfoBO> sendReviewInfoList = smallproMapper.getSendReviewInfo(smallpro.getId());
        SmallproSendReviewInfoBO sendReviewInfo = null;
        SmallproSendMsgCodeAreaInfoBO areaInfo = null;
        SmallproInfoInuserInfoBO inuserInfo = null;
        if (sendReviewInfoList != null && CollectionUtils.isNotEmpty(sendReviewInfoList)) {
            sendReviewInfo = sendReviewInfoList.get(0);
            //这里 重新实现了数据库dbo.encrypt()函数，输出系统没有这个函数
            sendReviewInfo.setNum(CommenUtil.getEncryptStr(Integer.valueOf(sendReviewInfo.getNum())));
            if (sendReviewInfo != null) {
                areaInfo = smallproMapper.getMsgCodeSendAreaInfo(sendReviewInfo.getAreaId());
                if (areaInfo != null && areaInfo.getIsSend()) {
                    inuserInfo = smallproMapper.getInUserInfoByMobile(sendReviewInfo.getMobile());
                }
            }
        }
        if (sendReviewInfoList != null && sendReviewInfoList.size() > 0) {
            if (sendReviewInfo != null) {
                if (areaInfo != null && areaInfo.getIsSend() && !StrUtil.splitTrim(sysConfigClient.
                        getValueByCode(SysConfigConstant.EVALUATE_NO_PUSH_AREAS).getData(),StringPool.COMMA).contains(Convert.toStr(sendReviewInfo.getAreaId()))) {
                    //门店可以发送短信且门店不在不推送评价列表里面
                    if (inuserInfo == null) {
                        boolean filmFlag = StrUtil.splitTrim(sendReviewInfo.getPath(), StringPool.COMMA).contains("63");
                        SmallproSendReviewUserWechatInfoBO userWechatInfo = smallproMapper.getUserWechatInfo(sendReviewInfo.getUserId());
                        R<String> printNameR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.PRINT_NAME, Math.toIntExact(areaInfo.getXTenant()));
                        if (!CommonUtils.isRequestSuccess(printNameR)) {
                            log.warn("门店[{}]租户[{}]获取不到打印名称,不进行推送",areaInfo.getAreaName(),areaInfo.getXTenant());
                            return;
                        }
                        //todo 需要适配
                        R<String> valueByCode = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, oaUserBO.getXTenant());
                        String host = valueByCode.getData();
//                            String host = ("丫丫".equals(printNameR.getData())) ? "yaya.cn" : "9ji.cn";
                        String url = host + SmallProRelativePathConstant.CUSTOMER_EVALUATION;
                        Integer type;
                        Integer job;
                        if (filmFlag) {
                            //膜评价
                            url = String.format(url, "KT", sendReviewInfo.getNum());
//                                url = url + "/KT" + sendReviewInfo.getNum();
                            type = 14;
                            job = 23;
                        } else {
                            //普通小件评价
                            url = String.format(url, "KX", sendReviewInfo.getNum());
//                                url = url + "/KX" + sendReviewInfo.getNum();
                            type = 13;
                            job=20;
                        }

                        ShortUrlParam shortUrlParam = LambdaBuild.create(new ShortUrlParam())
                                .set(ShortUrlParam::setUrl, url).set(ShortUrlParam::setDescription, "售后小件推送评价短链").build();
                        Result<String> urlR = SpringUtil.getBean(RetryService.class).retryByFeignRetryableException(()->
                                SpringUtil.getBean(WebCloud.class).generateShortUrl(shortUrlParam, XtenantEnum.getXtenant()));
                        url = urlR.getData();

                        StringBuilder messageBuilder = new StringBuilder("亲爱的");
                        Optional<BbsxpUsers> bbsxpUsersOpt = Optional.ofNullable(SpringUtil.getBean(BbsxpUsersService.class).getById(sendReviewInfo.getUserId()));
                        messageBuilder.append(BusinessUtil.getUserNickName(bbsxpUsersOpt.map(BbsxpUsers::getUserclass).orElse(null), sendReviewInfo.getNickName()));
                        messageBuilder.append("，为给您提供更好的消费体验，现邀请您对本次售后服务的人员做出评价。");
                        messageBuilder.append(url);
                        if(!urlR.isSuccess()){
                            RRExceptionHandler.logError("售后小件推送评价生成短链",Dict.create().set("shortUrlParam",shortUrlParam)
                                            .set("sendReviewInfo",sendReviewInfo).set("userWechatInfo",userWechatInfo)
                                            .set("messageBody",messageBuilder.toString()),
                                    new CustomizeException(JSON.toJSONString(urlR)),smsService::sendOaMsgTo9JiMan);
                        }
                        EvaluateScore evaluateScore = new EvaluateScore();
                        evaluateScore.setRelateCh999Id(Optional.ofNullable(smallpro.getInUser()).map(userInfoClient::getCh999UserByUserName)
                                .filter(R::isSuccess).map(R::getData).map(Ch999UserVo::getCh999Id).orElse(oaUserBO.getUserId()))
                                .setJob(job).setSubId(smallpro.getId()).setType(type).setUserid(smallpro.getUserId())
                                .setDtime(LocalDate.now()).setAreaid(smallpro.getAreaId());
                        evaluateScoreDao.insertEvaluateScore(evaluateScore,SpringUtil.getBean(JiujiSystemProperties.class).getOfficeName());
                        // 获取短信配置
                        if (XtenantEnum.isJiujiXtenant()) {
                            // 发送消息
                            if (userWechatInfo != null && userWechatInfo.getOpenId() != null && urlR.isSuccess()) {
                                // 直接插入wxsms表，自动定时推送微信消息
                                sendWxMessage(userWechatInfo.getOpenId(), messageBuilder.toString(), url,
                                        sendReviewInfo.getInUser(), sendReviewInfo.getUserId(), userWechatInfo.getWechatId());
                            } else {
                                if (sendReviewInfo.getMobile() != null && urlR.isSuccess()) {
                                    sendSmsMessage(messageBuilder.toString(), sendReviewInfo.getMobile(), smallpro.getAreaId());
                                }
                            }
                        } else {
                            long xtenant = Namespaces.get();
                            String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                            if (StringUtils.isNotEmpty(openXtenantStr)
                                    && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                                R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(Namespaces.get(),
                                        SmsConfigCodeEnum.CODE_99.getCode());
                                if (CommonUtils.isRequestSuccess(smsConfigResult)) {
                                    SmsConfigVO smsConfig = smsConfigResult.getData();
                                    // 消息内容
                                    List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                                    String message = smsConfig.getTemplate();
                                    if (StringUtils.isNotEmpty(message)
                                            && CollectionUtils.isNotEmpty(fields)) {
                                        for (SmsConfigVO.SmsField field : fields) {
                                            if ("<nickName>".equals(field.getValue())) {
                                                message = message.replace(field.getValue(),
                                                        BusinessUtil.getUserNickName(bbsxpUsersOpt.map(BbsxpUsers::getUserclass).orElse(null), sendReviewInfo.getNickName()));
                                            }
                                            if ("<url>".equals(field.getValue())) {
                                                message = message.replace(field.getValue(), url);
                                            }
                                        }
                                    }
                                    // 推送方式
                                    List<Integer> pushMethods = smsConfig.getPushMethod();
                                    // 微信消息
                                    boolean sendWx = userWechatInfo != null
                                            && userWechatInfo.getOpenId() != null
                                            && urlR.isSuccess()
                                            && CollectionUtils.isNotEmpty(pushMethods)
                                            && pushMethods.contains(SmsPushMethodEnum.WECHAT_MSG.getCode());
                                    if (sendWx) {
                                        // 直接插入wxsms表，自动定时推送微信消息
                                        sendWxMessage(userWechatInfo.getOpenId(), message, url,
                                                sendReviewInfo.getInUser(), sendReviewInfo.getUserId(), userWechatInfo.getWechatId());
                                    }
                                    // sms消息
                                    boolean sendSms = sendReviewInfo.getMobile() != null
                                            && urlR.isSuccess()
                                            && CollectionUtils.isNotEmpty(pushMethods)
                                            && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                                    if (sendSms) {
                                        sendSmsMessage(message, sendReviewInfo.getMobile(), smallpro.getAreaId());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            log.warn("发送评价信息时无相关记录！无法发送！SmallproId:{}", smallpro.getId());
        }
    }

    /**
     * 发送微信消息，直接插入wxsms表，自动定时推送微信消息
     *
     * @param inUser 接件用户
     * @param userId 用户id
     * @param wxId 微信id
     * @return
     */
    private void sendWxMessage(String openId, String message, String url,
                               String inUser, Integer userId, Integer wxId) {
        Wxsms wxsms = new Wxsms().setOpenid(openId)
                .setSendtime(LocalDateTime.now())
                .setMsg(message)
                .setDelivery(3)
                .setInuser(inUser)
                .setUrl(url)
                .setType(1)
                .setUserid(userId).setWxid(wxId);
        wxsmsService.save(wxsms);
        stringRedisTemplate.opsForHash().increment("wxMsg", "SendOrderPay", 1L);
    }

    /**
     * 发送短信消息
     *
     * @param message 销售内容
     * @param mobile 手机号
     * @param areaId 门店id
     */
    private void sendSmsMessage(String message, String mobile, Integer areaId) {
        smsService.sendSms(mobile, message,
                DateUtil.localDateTimeToString(LocalDateTime.now().plusMinutes(30L)),
                "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
    }

    @Override
    public void joinAreaMoneyDeal(Smallpro smallpro) {
        // https://moa.dev.9ji.com/ajaxapi/SmallProJoinAreaMoneyDeal?smallProId=0
        AtomicReference<String> urlRef = new AtomicReference<>();
        Dict formParam = Dict.create().set("smallProId", smallpro.getId());
        Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).filter(R::isSuccess)
                .map(R::getData)
                .map(url -> {
                    urlRef.set(StrUtil.format("{}/ajaxapi/SmallProJoinAreaMoneyDeal",url));
                    return HttpUtil.createPost(urlRef.get()).form(formParam).execute();
                })
                .ifPresent(res -> {
                    log.debug("加盟店小件取件处理结果:{}",res.body());
                    RuntimeException ex = new RuntimeException(StrUtil.format("url:{},响应结果:{}", urlRef.get(), res.body()));
                    if(!res.isOk()){
                        RRExceptionHandler.logError("加盟店凭证接口", formParam,ex,
                            SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                    }
                    R r = JSON.parseObject(res.body(),R.class);
                    if (!r.isSuccess()){

                        RRExceptionHandler.logError("加盟店凭证接口", formParam,ex, msg->
                                SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan("{},错误提示:{}",msg,r.getUserMsg()));
                    }
                });
    }

    private String getNameByServiceType(Integer serviceType) {
        if (null == serviceType) {
            return null;
        }
        switch (serviceType) {
            case 1:
                return "意外换新";
            case 4:
                return "年包";
            case 5:
                return "质保";
            default:
                return null;
        }
    }

    // endregion

    // endregion

    @Override
    public SmallproNormalCodeMessageRes checkPwd2(Integer userId, String pwd2) throws
            UnsupportedEncodingException {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        String key = DigestUtils.md5DigestAsHex(pwd2.getBytes("UTF-8"));
        int count = smallproMapper.checkUserPwd2(userId, key);
        if (count <= 0) {
            result.setCode(500);
            result.setMessage("二级密码错误！");
        } else if (count == 1) {
            result.setCode(0);
            result.setMessage("二级密码校验成功！");
        } else {
            result.setCode(500);
            result.setMessage("未知错误！检查二级密码有多条数据！");
        }
        return result;
    }


    @Override
    public R<Boolean> smallproRefund(SmallproRefundReq refundReq) {
        if (refundReq == null) {
            return R.success("原路径退款请先确定退款方式", false);
        }
        Object refundWayFlag = EnumUtil.getCodeByMessage(SmallproRefundWayEnum.class, refundReq.getRefundWay());
        Boolean isPayOnline = false;
        if (refundWayFlag != null) {
            isPayOnline = true;
        }
        if (!isPayOnline || CollectionUtils.isEmpty(refundReq.getNetPays())) {
            return R.success("原路径退款请先确定交易号！", false);
        }
        return null;
    }

    @Override
    public SmallproNormalCodeMessageRes checkTemperedFilmBinding(String imei, Integer smallproId, Long basketId,
                                                                 String fid, Integer kind) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        if (StringUtils.isNotEmpty(imei) && "CHECK_BASKETID_BIND".equals(imei) && basketId > 0) {
            List<Integer> count = CommenUtil.autoQueryHist(()->smallproMapper.checkBasketBindRecord(null, basketId));
            if (CollectionUtils.isEmpty(count)) {
                result.setCode(0);
                result.setMessage("钢化膜串号未绑定，请到原始订单进行绑定");
                return result;
            } else {
                result.setCode(1);
                result.setMessage("验证成功");
                return result;
            }
        }
        if (StringUtils.isEmpty(imei) || smallproId == 0 || basketId == 0) {
            result.setCode(0);
            result.setMessage("参数错误");
            return result;
        }
        List<Integer> count = CommenUtil.autoQueryHist(()->smallproMapper.checkBasketBindRecord(imei, basketId));
        if (CollectionUtils.isNotEmpty(count)) {
            OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
            String dateTime = LocalDateTime.now()
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String codeMsg;
            if(StringUtils.isNotEmpty(imei) && XtenantEnum.isJiujiXtenant()){
                codeMsg = MessageFormat.format("扫码验证[app 扫码验证],串号：{0} 【{1}】 {2}",imei,oaUser.getUserName(), dateTime);
            } else {
                codeMsg = MessageFormat.format("扫码验证[app 扫码验证] 【{0}】 {1}", oaUser.getUserName(), dateTime);
            }
            if(StringUtils.isNotEmpty(imei)){
                Optional.ofNullable(smallproId).ifPresent(item-> smallproService.lambdaUpdate().eq(Smallpro::getId,item).set(Smallpro::getImei, imei).update());
            }
            int update = smallproMapper.updateCodeMsgAndFid(codeMsg, fid, smallproId);
            result.setCode(update > 0 ? 1 : 0);
            if (result.getCode() == 1 && kind == 2) {
                Smallpro byId = smallproService.getById(smallproId);
                if (null != byId) {
                    int areaId = byId.getAreaId();
                    Areainfo areainfo = areainfoService.getById(areaId);
                    //这些加盟地区不推送
                    List<Integer> authorizeids = Arrays.asList(57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71
                            , 72);
//                    AreaSubject areaSubject = getAreaSubject(areainfo);
                    if (areainfo != null && !authorizeids.contains(areainfo.getAuthorizeid()) && areainfo.getIsSend()) {
                        List<BasketBO> basketBO = CommenUtil.autoQueryHist(()->smallproMapper.getBasketMessage(basketId));
                        if (CollectionUtils.isNotEmpty(basketBO)) {
                            BasketBO basketBO1 = basketBO.get(0);
                            String areaName = areainfo.getAreaName();
                            String wxMsg = "您购买的" + basketBO1.getProductName() + "在" + areaName + "进行免费换新服务";
                            String msg = wxMsg += "，如非本人办理请联系客服。";
                            if (areainfo.getXtenant().equals(0)) {
                                msg = wxMsg += "，如非本人办理请致电客服400-008-3939。";
                            }
                            long xtenantValue = Namespaces.get();
                            String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                            if (XtenantEnum.isSaasXtenant()
                                    && StringUtils.isNotEmpty(openXtenantStr)
                                    && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenantValue)) {
                                R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenantValue,
                                        SmsConfigCodeEnum.CODE_98.getCode());
                                if (CommonUtils.isRequestSuccess(smsConfigResult)) {
                                    SmsConfigVO smsConfig = smsConfigResult.getData();
                                    // 消息内容
                                    List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                                    String smsMessage = smsConfig.getTemplate();
                                    if (StringUtils.isNotEmpty(smsMessage)
                                            && CollectionUtils.isNotEmpty(fields)) {
                                        for (SmsConfigVO.SmsField field : fields) {
                                            if ("<productName>".equals(field.getValue())) {
                                                smsMessage = smsMessage.replace(field.getValue(), basketBO1.getProductName());
                                            }
                                            if ("<areaName>".equals(field.getValue())) {
                                                smsMessage = smsMessage.replace(field.getValue(), areaName);
                                            }
                                        }
                                    }
                                    // 推送方式
                                    List<Integer> pushMethods = smsConfig.getPushMethod();
                                    // sms消息
                                    if (CollectionUtils.isNotEmpty(pushMethods)
                                            && pushMethods.contains(SmsPushMethodEnum.SMS.getCode())) {
                                        msg = smsMessage;
                                    }
                                }
                            }
                            Long userId = basketBO1.getUserId();
                            WxBO wxBO = getMyWx(userId);
                            if (StringUtils.isNotEmpty(wxBO.getOpenid())) {
                                int useCount = smallproMapper.getUseCount(basketId);
                                wxMsg += "，已使用" + useCount + "次。";
                                String remark = "";
                                if (areainfo.getXtenant().equals(0)) {
                                    remark = "如非本人操作可直接投诉或联系我们400-008-3939";
                                }
                                // todo 发送微信消息

                            } else {
                                Integer xtenant = areainfo.getXtenant();
                                R<String> channelR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_CHANNEL, Math.toIntExact(xtenant));
                                String channel = CommonUtils.isRequestSuccess(channelR) ? channelR.getData() : null;
                                if (null != channel) {
                                    smsService.sendSms(basketBO1.getSubMobile(), msg,
                                            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd " +
                                                    "HH:mm:ss")), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
                                }
                            }
                        }
                    }
                }
            }
        } else {
            result.setCode(0);
            result.setMessage("串号校验失败，请检查串号是否正确");
        }
        return result;
    }

    @Override
    public R<CashOrScrapRes> CashOrScrap(int smallproId) {
        CashOrScrapRes cashOrScrapRes = new CashOrScrapRes();
        Smallpro smallPro = smallproService.getById(smallproId);
        List<SmallproBill> smallproBills =
                smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getSmallproID,
                        smallproId));
        QueryWrapper<ShouhouFanchang> shouhouFanchangQueryWrapper = new QueryWrapper<>();
        shouhouFanchangQueryWrapper.lambda().eq(ShouhouFanchang::getSmallproid, smallproId);
        List<ShouhouFanchang> shouhouFanchangList =
                Optional.ofNullable(shouhouFanchangService.listSqlServer(shouhouFanchangQueryWrapper))
                        .orElseGet(ArrayList::new);
        if (null == smallPro) {
            return R.error("未找到小件单");
        }
        //校验小件的地区是否为接件地区
        //获取小件转地区的areaid
        Optional<Integer> toAreaId = Optional.ofNullable(smallPro.getToAreaId());
        //获取当前门店id
        Integer isThisAreaId = abstractCurrentRequestComponent.getCurrentStaffId().getAreaId();
        //当转地区id不为空 不等于接件地区时
        if (toAreaId.isPresent() && (!Objects.equals(toAreaId.get(), isThisAreaId))) {
            String area = areaInfoClient.getAreaInfoById(toAreaId.orElse(smallPro.getAreaId())).getData().getArea();
            return R.error("该小件单归属"+area+"门店，无法进行操作，请核实地区是否一致");
        }

        AtomicBoolean falgHas = new AtomicBoolean(false);
        shouhouFanchangList.forEach(e -> {
            if (SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode().equals(e.getRstats())) {
                falgHas.set(true);
            }
        });
        boolean falg = false;
        if (CollectionUtils.isNotEmpty(shouhouFanchangList) && falgHas.get()) {
            falg = true;
        }
        cashOrScrapRes.setKindName(EnumUtil.getMessageByCode(SmallProKindEnum.class, smallPro.getKind()));
        cashOrScrapRes.setSmallProId(smallproId);
        Integer kind = smallPro.getKind();
        if (SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode().equals(kind) && !smallPro.getStats().equals(SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode()) && falg) {
            return getCashOrScrapResR(cashOrScrapRes, shouhouFanchangList);
        } else if (SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(kind) && smallPro.getStats().equals(SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode()) && falg) {
            QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
            shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getSmallproid, smallproId)
                    .and(wrapper -> wrapper.eq(ShouhouTuihuan::getIsdel, 0).or().isNull(ShouhouTuihuan::getIsdel))
                    .orderByDesc(ShouhouTuihuan::getId);
            List<ShouhouTuihuan> shouhouTuihuanList =
                    Optional.ofNullable(shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper))
                            .orElseGet(ArrayList::new);
            if (CollectionUtils.isNotEmpty(shouhouTuihuanList)) {
                ShouhouTuihuan shouhouTuihuan = shouhouTuihuanList.get(0);
                if (StringUtils.isNotEmpty(shouhouTuihuan.getCheck1user())) {
                    return getCashOrScrapResR(cashOrScrapRes, shouhouFanchangList);
                }
            }
        } else if (SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(kind) && falg && null != smallPro.getQujianDate()) {
            return getCashOrScrapResR(cashOrScrapRes, shouhouFanchangList);
        }
        return R.error("该小件单不能转现报废");
    }

    /**
     *
     * @param orderId 小件单/物流单
     * @return
     */
    @Override
    public R<List<CashOrScrapV2Res>> cashOrScrapV2(int orderId) {
        //首先开始查找物流相关信息
        List<Smallpro> smallProList = smallproService.listSqlServer(new LambdaQueryWrapper<Smallpro>().eq(Smallpro::getWuliuId, orderId));
        List<CashOrScrapV2Res> scrapV2ResList = new ArrayList<>();
        Map<String,Object> exData = new HashMap<>(NumberConstant.ONE);
        //如果根据物流单号查询数据不为空
        if (CollectionUtils.isNotEmpty(smallProList)) {
            //过滤出所有的小件单
            List<Integer> integerStream = smallProList.stream().filter(Objects::nonNull).distinct().map(Smallpro::getId).collect(Collectors.toList());
            //组装参数，并把所有小件单设置为待选取
            List<ShouhouFanchang> shouhouFanchangList = shouhouFanchangService.listSqlServer(new LambdaQueryWrapper<ShouhouFanchang>().in(ShouhouFanchang::getSmallproid, integerStream));
            for (Smallpro smallPro : smallProList) {
                //判断售后返厂表中 小件单状态为0的数据
                List<ShouhouFanchang> collect = shouhouFanchangList.stream().filter(s -> Objects.equals(s.getSmallproid(), smallPro.getId())).collect(Collectors.toList());
                boolean flag = collect.stream().filter(Objects::nonNull).filter(sh -> Objects.equals(sh.getSmallproid(), smallPro.getId()))
                        .anyMatch(sh -> Objects.equals(sh.getRstats(), SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode()));
                CashOrScrapV2Res cashOrScrapResV2 = new CashOrScrapV2Res();
                cashOrScrapResV2.setKindName(EnumUtil.getMessageByCode(SmallProKindEnum.class, smallPro.getKind()))
                        .setSmallProId(smallPro.getId()).setSelectType(NumberConstant.TWO);
                R<CashOrScrapV2Res> cashOrScrapV2ResR = getCashOrScrapV2ResR(smallPro.getId(), smallPro, collect, flag, cashOrScrapResV2);
                if (cashOrScrapV2ResR.getCode()!=NumberConstant.ZERO){
                    continue;
                }
                scrapV2ResList.add(cashOrScrapV2ResR.getData());
            }
            R<List<CashOrScrapV2Res>> r = R.success(scrapV2ResList);
            exData.put("orderType",NumberConstant.TWO);
            r.setExData(exData);
            return r;
        }
        //添加小件单逻辑，用户添加或扫描小件单id时，把小件单的状态设置为已选取
        Smallpro smallPro = smallproService.getById(orderId);
        if (null == smallPro) {
            return R.error("未找相关单号");
        }
        //校验小件的地区是否为接件地区
        //获取小件转地区的areaid
        Optional<Integer> toAreaId = Optional.ofNullable(smallPro.getToAreaId());
        //获取当前门店id
        Integer isThisAreaId = abstractCurrentRequestComponent.getCurrentStaffId().getAreaId();
        //当转地区id不为空 不等于接件地区时
        if (toAreaId.isPresent() && (!Objects.equals(toAreaId.get(), isThisAreaId))) {
            String area = areaInfoClient.getAreaInfoById(toAreaId.orElse(smallPro.getAreaId())).getData().getArea();
            return R.error("该小件单归属"+area+"门店，无法进行操作，请核实地区是否一致");
        }
        //当转地区id为空并且当前操作人员不在当前接件门店时
        if (!toAreaId.isPresent() && (!Objects.equals(smallPro.getAreaId(), isThisAreaId))) {
            String area = areaInfoClient.getAreaInfoById(toAreaId.orElse(smallPro.getAreaId())).getData().getArea();
            return R.error("该小件单归属"+area+"门店，无法进行操作，请核实地区是否一致");
        }
        List<ShouhouFanchang> shouhouFanchangList = shouhouFanchangService.listSqlServer(new LambdaQueryWrapper<ShouhouFanchang>().eq(ShouhouFanchang::getSmallproid, orderId));
        boolean flag = shouhouFanchangList.stream().filter(Objects::nonNull).anyMatch(sh -> Objects.equals(sh.getRstats(), SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode()));
        CashOrScrapV2Res cashOrScrapResV2 = new CashOrScrapV2Res();
        cashOrScrapResV2.setKindName(EnumUtil.getMessageByCode(SmallProKindEnum.class, smallPro.getKind()))
                .setSmallProId(orderId).setSelectType(NumberConstant.ONE);
        R<CashOrScrapV2Res> cashOrScrapV2ResR = getCashOrScrapV2ResR(orderId, smallPro, shouhouFanchangList, flag, cashOrScrapResV2);
        if (cashOrScrapV2ResR.getCode()!=NumberConstant.ZERO){
            return R.error(cashOrScrapV2ResR.getUserMsg());
        }
        scrapV2ResList.add(cashOrScrapV2ResR.getData());
        R<List<CashOrScrapV2Res>> r = R.success(scrapV2ResList);
        exData.put("orderType",NumberConstant.ONE);
        r.setExData(exData);
        return r;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Boolean> abnormalBatch(List<Integer> smallproIds) {
        List<Smallpro> smallproList = new ArrayList<>();
        Smallpro smallpro;
        for (Integer smallproId : smallproIds) {
            smallpro = new Smallpro();
            smallpro.setId(smallproId);
            smallpro.setAbnormalType(NumberConstant.ONE);
            smallproList.add(smallpro);
        }
        boolean b = smallproService.updateBatchById(smallproList);
        //查询
        List<Smallpro> smallpros = smallproService.list(new LambdaQueryWrapper<Smallpro>().in(Smallpro::getId,smallproIds).last("and isnull(isdel,0)=0"));
        if (CollUtil.isNotEmpty(smallpros)){
            List<Smallpro> collect = smallpros.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                    () -> new TreeSet<>(Comparator.comparing(Smallpro::getInUser))), ArrayList::new));
            smallpros.forEach(sm -> smallproLogService.addLogs(sm.getId(), "物流单关联的其他小件旧件已处理完成，该小件d1未收到实物，请及时对接d1同事落实处理。", sm.getInUser(), 1));
            List<Integer> smallproIdList = new ArrayList<>();
            collect.forEach(co ->{
                R<Ch999UserVo> ch999UserByUserName = userInfoClient.getCh999UserByUserName(co.getInUser());
                if (ch999UserByUserName.getCode() == ResultCode.SUCCESS && ch999UserByUserName.getData() != null) {
                    smallpros.forEach(sm ->{
                        if (Objects.equals(co.getInUser(),sm.getInUser())){
                            smallproIdList.add(sm.getId());
                        }
                    });
                    String host = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL).getData();
                    String link = host + "/new/%23/small-refund/";

                    StringBuilder messageContent = new StringBuilder("【单号：[");
                    for (int i = 0; i < smallproIdList.size(); i++) {
                        messageContent.append("<a href=\"");
                        messageContent.append(link);
                        messageContent.append(smallproIdList.get(i));
                        messageContent.append("\">");
                        messageContent.append(smallproIdList.get(i));
                        messageContent.append("</a>");
                        if (i != smallproIdList.size()-1){
                            messageContent.append(",");
                        }
                    }
                    messageContent.append("],该小件d1未收到实物，涉嫌虚假发货，请及时对接d1同事复核，超时库存将转回门店报损出库。】");
                    smsService.sendOaMsg(messageContent.toString(),"",
                            String.valueOf(ch999UserByUserName.getData().getCh999Id()), OaMesTypeEnum.SYSTEM);
                }
            });
        }
        return R.success(b);
    }

    private R<CashOrScrapV2Res> getCashOrScrapV2ResR(int orderId, Smallpro smallPro, List<ShouhouFanchang> shouhouFanchangList, boolean flag, CashOrScrapV2Res cashOrScrapResV2) {
        if (SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode().equals(smallPro.getKind())
                && !smallPro.getStats().equals(SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode())
                && flag) {
            return getCashOrScrapV2(cashOrScrapResV2, shouhouFanchangList);
        } else if (SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallPro.getKind())
                && smallPro.getStats().equals(SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode())
                && flag) {
            List<ShouhouTuihuan> shouhouTuihuanList = shouhouTuihuanService.listSqlServer(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getSmallproid, orderId)
                    .and(wrapper -> wrapper.eq(ShouhouTuihuan::getIsdel, 0).or().isNull(ShouhouTuihuan::getIsdel))
                    .orderByDesc(ShouhouTuihuan::getId));
            if (CollectionUtils.isNotEmpty(shouhouTuihuanList)) {
                ShouhouTuihuan shouhouTuihuan = shouhouTuihuanList.get(0);
                if (StringUtils.isNotEmpty(shouhouTuihuan.getCheck1user())) {
                    return getCashOrScrapV2(cashOrScrapResV2, shouhouFanchangList);
                }
            }
        } else if (SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(smallPro.getKind()) && flag && null != smallPro.getQujianDate()) {
            return getCashOrScrapV2(cashOrScrapResV2, shouhouFanchangList);
        }
        return R.error("单号：【"+smallPro.getId()+"】不能进行转现/报废操作！");
    }

    @Override
    public List<Integer> CashOrScrapBatch(CashOrScrapBatchReq cashOrScrapBatchReq, OaUserBO oaUserBO) {
        List<Integer> ids = new ArrayList<>();
        Map<Integer, Smallpro> returnSmallProMap = new HashMap<>();
        List<ShouhouFanchang> shouhouFanchangList =CommenUtil.bigDataInQuery(cashOrScrapBatchReq.getReturnFactoryIds(),
                rfIds -> Optional.ofNullable(shouhouFanchangService.listSqlServer(shouhouFanchangService.lambdaQuery().in(ShouhouFanchang::getId,rfIds).getWrapper()))
                        .orElseGet(ArrayList::new));
        List<Integer> smallProIds =
                shouhouFanchangList.stream().map(ShouhouFanchang::getSmallproid).distinct().collect(Collectors.toList());
        Map<Integer, Smallpro> smallproMap = null;
        if (CollectionUtils.isNotEmpty(smallProIds)) {
            smallproMap = smallproService.list(new LambdaQueryWrapper<Smallpro>().in(Smallpro::getId,
                    smallProIds)).stream().collect(Collectors.toMap(Smallpro::getId, bo -> bo));
        }
        Map<Integer, Smallpro> finalSmallproMap = smallproMap;
        shouhouFanchangList.forEach(e -> {
            returnSmallProMap.put(e.getId(), finalSmallproMap.get(e.getSmallproid()));
        });
        Map<Integer, List<ShouhouFanchang>> fanChangMap =
                shouhouFanchangList.stream().collect(Collectors.groupingBy(ShouhouFanchang::getSmallproid));

        // 批量转现
        if (cashOrScrapBatchReq.getType() == 1) {
            fanChangMap.entrySet().forEach(e -> {
                Integer smallProId = e.getKey();
                List<ShouhouFanchang> fanChangIds = e.getValue();
                fanChangIds.forEach(j -> {
                    firstTransferArea(oaUserBO, returnSmallProMap, j, cashOrScrapBatchReq.getFlag());
                    SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                            //默认转现货库存 // TODO: 2021/12/28 需要变更为可选择转现货或者瑕疵
                            smallproOldProductExService.incomingSmallpro(j.getId(), oaUserBO.getUserName(), smallProId, NumberConstant.TWO, true);
                    setResMessage(ids, smallProId, smallproNormalCodeMessageRes, j.getId());
                });
            });
            // 批量报废
        } else if (cashOrScrapBatchReq.getType() == 2) {
            fanChangMap.entrySet().forEach(e -> {
                Integer smallProId = e.getKey();
                List<ShouhouFanchang> fanChangIds = e.getValue();
                fanChangIds.forEach(j -> {
                    firstTransferArea(oaUserBO, returnSmallProMap, j, cashOrScrapBatchReq.getFlag());
                    SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                            smallproOldProductExService.scrapSmallpro(j.getId(), oaUserBO.getUserName(), smallProId,
                                    oaUserBO.getAreaId());
                    setResMessage(ids, smallProId, smallproNormalCodeMessageRes, j.getId());
                });
            });
        }
        return ids;
    }

    @Override
    public Integer correctStatus() {
        return smallproMapper.correctStatus();
    }

    @Override
    public Boolean checkPpid(List<String> ppIds, Integer type) {
        if (CollectionUtils.isEmpty(ppIds)) {
            return false;
        }
        // ppid
        if (2 == type) {
            List<Integer> tags = smallproMapper.checkPpid(ppIds);
            if (CollectionUtils.isEmpty(tags)) {
                return false;
            } else {
                return true;
            }
            // 条码
        } else if (3 == type) {
            for (String ppId : ppIds) {
                List<Integer> tags = smallproMapper.checkBarcode(ppId);
                if (CollectionUtils.isNotEmpty(tags)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void firstTransferArea(OaUserBO oaUserBO, Map<Integer, Smallpro> returnSmallProMap, ShouhouFanchang j,
                                   String flag) {
        Smallpro smallpro = returnSmallProMap.get(j.getId());
        Integer areaId = smallpro.getAreaId();
        AreaBelongsDcHqD1AreaId areaBelongsDcHqD1AreaId = areainfoService.getAreaBelongsDcHqD1AreaId(smallpro.getAreaId());
        Integer toAreaId = areaBelongsDcHqD1AreaId.getD1AreaId();
        // 未转地区的先转地区
        if (null != smallpro && smallpro.getIsToArea()) {
            if (areaId.equals(toAreaId)) {
                return;
            }
            // 当前操作人在D1才能转
            if (StringUtils.isEmpty(flag) && !oaUserBO.getAreaId().equals(toAreaId)) {
                return;
            }
            smallproService.toArea(smallpro.getId(), areaId,
                    toAreaId, 2, oaUserBO, true, smallpro);
        }
    }

    private List<Integer> setResMessage(List<Integer> res, Integer smallProId,
                                        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes, Integer id) {
        if (smallproNormalCodeMessageRes.getCode() != 0) {
            res.add(id);
        }
        return res;
    }

    private R<CashOrScrapRes> getCashOrScrapResR(CashOrScrapRes cashOrScrapRes,
                                                 List<ShouhouFanchang> shouhouFanchangs) {
        if (CollectionUtils.isNotEmpty(shouhouFanchangs)) {
            List<Long> collect =
                    shouhouFanchangs.stream().filter(a -> a.getRstats().equals(SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode())).map(e -> e.getPpid().longValue()).distinct().collect(Collectors.toList());
            List<SmallproInfoProductBO> smallproShopInfo = smallproMapper.getSmallproShopInfo(collect);
            Map<Integer, List<SmallproInfoProductBO>> ppIdMap =
                    smallproShopInfo.stream().collect(Collectors.groupingBy(SmallproInfoProductBO::getPpriceId));
            List<SmallproCashOrScrapBO> bos = new ArrayList<>();
            List<ShouhouFanchang> collect1 =
                    shouhouFanchangs.stream().filter(e -> e.getRstats().equals(SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode())).collect(Collectors.toList());
            collect1.forEach(e -> {
                SmallproCashOrScrapBO smallproCashOrScrapBO = new SmallproCashOrScrapBO();
                List<SmallproInfoProductBO> smallproInfoProductBOS = ppIdMap.get(e.getPpid());
                SmallproInfoProductBO bo = smallproInfoProductBOS.get(0);
                smallproCashOrScrapBO.setProductName(bo.getProductName() + " " + (StringUtils.isEmpty(bo.getProductColor()) ? "" : bo.getProductColor()));
                smallproCashOrScrapBO.setReturnFactoryId(e.getId());
                bos.add(smallproCashOrScrapBO);
            });
            cashOrScrapRes.setSmallProShopInfo(bos);
        }
        return R.success(cashOrScrapRes);
    }

    private R<CashOrScrapV2Res> getCashOrScrapV2(CashOrScrapV2Res cashOrScrapRes,
                                                 List<ShouhouFanchang> shouhouFanchangs) {
        if (CollectionUtils.isEmpty(shouhouFanchangs)) {
            return R.success(cashOrScrapRes);
        }
        List<Long> collect = shouhouFanchangs.stream()
                        .filter(a -> Objects.equals(a.getRstats(),(SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode())))
                        .map(e -> e.getPpid().longValue()).distinct().collect(Collectors.toList());
        Map<Integer, List<SmallproInfoProductBO>> ppIdMap =
                smallproMapper.getSmallproShopInfo(collect).stream().collect(Collectors.groupingBy(SmallproInfoProductBO::getPpriceId));
        List<SmallproCashOrScrapBO> smallproCashOrScrapBOList = new ArrayList<>();
        List<ShouhouFanchang> collect1 = shouhouFanchangs.stream()
                        .filter(e -> Objects.equals(e.getRstats(),SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode()))
                        .collect(Collectors.toList());
        collect1.forEach(item -> {
            SmallproCashOrScrapBO smallproCashOrScrapBO = new SmallproCashOrScrapBO();
            List<SmallproInfoProductBO> smallproInfoProductBOS = ppIdMap.get(item.getPpid());
            SmallproInfoProductBO bo = smallproInfoProductBOS.get(0);
            smallproCashOrScrapBO.setProductName(bo.getProductName() + " " + (StringUtils.isEmpty(bo.getProductColor()) ? "" : bo.getProductColor()));
            smallproCashOrScrapBO.setReturnFactoryId(item.getId());
            smallproCashOrScrapBOList.add(smallproCashOrScrapBO);
        });
        cashOrScrapRes.setSmallProShopInfo(smallproCashOrScrapBOList);
        return R.success(cashOrScrapRes);
    }
    /**
     * 获取用户微信信息
     *
     * @param userId
     * @return
     */
    private WxBO getMyWx(Long userId) {
        List<WxBO> wxBOS = smallproMapper.getWeixinUser(userId);
        if (CollectionUtils.isNotEmpty(wxBOS)) {
            return wxBOS.get(0);
        }
        return new WxBO();
    }

//    private AreaSubject getAreaSubject(Areainfo areainfo) {
//        AreaSubject areaSubject = new AreaSubject();
//        if (areainfo != null) {
//            areaSubject.setIsSend(areainfo.getIsSend());
//            areaSubject.setIs9JiSubject("九机网".equals(areainfo.getPrintName()));
//            areaSubject.setMUrl(getUrlByXtenant(areainfo.getXtenant()));
//            areainfo.setPrintName(StringUtils.isEmpty(areainfo.getPrintName()) ? "九机网" : areainfo.getPrintName());
//        } else {
//            areaSubject.setIsSend(true);
//            areaSubject.setIs9JiSubject(true);
//            areaSubject.setMUrl(getUrlByXtenant(0));
//        }
//        return areaSubject;
//    }

//    private String getUrlByXtenant(Integer xtenant) {
//        switch (xtenant) {
//            case 0:
//                return "https://m.9ji.com";
//            case 2:
//                return "https://huawei.ch999.com.cn";
//            case 1:
//                return "https://m.yaya.cn";
//            default:
//                return "";
//        }
//    }

    /**
     * 是否允许自动转地区操作
     *
     * @param smallProId
     * @return
     */
    private Boolean isAllowAutoToArea(Integer smallProId) {
        //全部是膜允许自动转地区 商品中如果不是膜，不能自动转地区
        List<SmallproBill> list = smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().eq(SmallproBill::getSmallproID, smallProId));
        if (CollectionUtils.isEmpty(list)) {
            return Boolean.TRUE;
        }
        List<Long> ppids = list.stream().map(SmallproBill::getPpriceid).collect(Collectors.toList());
        Integer count = smallproMapper.getFilmCountByPpids(ppids);
        Boolean flag = Boolean.FALSE;
        if (!Objects.equals(list.size(), count) || CommenUtil.isNullOrZero(count)) {
            // 多个小件接件,存在非膜的商品或没有膜,才自动转地区, 单个小件,膜不进行转地区
            flag = Boolean.TRUE;
        }
        return flag;
    }

    //检测是否能够进行换货
    public R<Tuple> checkExchange(Integer originPpid, Integer newPpid){
        if(Objects.isNull(originPpid) || Objects.isNull(newPpid) || Arrays.asList(originPpid,newPpid).contains(0)){
            return R.error("换货的ppid不能为0");
        }
        return null;
    }

    @Override
    public List<LastExchangeSmallproBo> listLastChangeSmallpro(Integer smallproId, Integer subId, Set<Integer> basketIds) {
        // 获取最近一次该basket换货记录
        List<LastExchangeSmallproBo> lastChangeSmallPro = smallproMapper.listLastChangeSmallpro(smallproId, subId, basketIds);
        // 获取上一次出库的ppid
        if (!lastChangeSmallPro.isEmpty()) {
            List<LastExchangeSmallproBo> finalLastChangeSmallPro = lastChangeSmallPro;
            for (int i = 0; i < 2 && finalLastChangeSmallPro.stream().map(LastExchangeSmallproBo::getPpriceId).anyMatch(Objects::isNull); i++) {
                // 0 查询主库 1 查询历史库 只有出库ppid还没查询到进行查询
                // 关联库存的时候, 是需要查询出库的ppid
                MultipleTransaction.query(DecideUtil.iif(i == 0, DataSourceConstants.DEFAULT, DataSourceConstants.OA_NEW_HIS),
                        () -> smallproMapper.listLastOutPpriceId(finalLastChangeSmallPro.stream()
                                //过滤出未查询到库存id进行查询
                                .filter(lcs -> Objects.isNull(lcs.getPpriceId()))
                                .map(LastExchangeSmallproBo::getSmallproId)
                                .collect(Collectors.toSet())))
                        //遍历出库的ppid赋值
                        .forEach(ppIdSmallProId -> finalLastChangeSmallPro.stream()
                                .filter(lcs -> Objects.equals(lcs.getSmallproId(), ppIdSmallProId.getSmallproId()))
                                .forEach(lcs -> lcs.setPpriceId(ppIdSmallProId.getPpriceId())));
            }
        }
        return lastChangeSmallPro;
    }
}
