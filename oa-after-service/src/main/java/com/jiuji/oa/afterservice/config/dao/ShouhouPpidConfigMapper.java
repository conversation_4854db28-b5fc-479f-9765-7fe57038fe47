package com.jiuji.oa.afterservice.config.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidConfigQueryReq;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidOutPutConfig;
import com.jiuji.oa.afterservice.config.vo.res.XtenantInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Mapper
public interface ShouhouPpidConfigMapper extends BaseMapper<ShouhouPpidConfig> {

    Page<ShouhouPpidOutPutConfig> getShouhouPpidConfigList(@Param("page") Page<ShouhouPpidOutPutConfig> page,
                                                           @Param("req") ShouhouPpidConfigQueryReq req,@Param("way") Integer way);

    /**
     * 获取租户信息
     * @return
     */
    List<XtenantInfo> getXtenantInfo();

}
