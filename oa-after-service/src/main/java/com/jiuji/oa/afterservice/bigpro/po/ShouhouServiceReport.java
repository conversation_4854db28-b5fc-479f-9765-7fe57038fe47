package com.jiuji.oa.afterservice.bigpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouServiceReportRes;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shouhou_service_report")
public class ShouhouServiceReport implements Serializable {
    private static final long serialVersionUID = 7846112874510433202L;
    /**
     * 自增长
     */
    @ApiModelProperty(value = "自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 受理时间(接件时间)
     */
    @ApiModelProperty(value = "受理时间(接件时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptanceTime;
    /**
     * 受理单号(维修单号)
     */
    @ApiModelProperty(value = "受理单号(维修单号)")
    private Integer shouhouId;
    /**
     * 送修人姓名
     */
    @ApiModelProperty(value = "送修人姓名")
    private String afterUserName;
    /**
     * 送修人电话
     */
    @ApiModelProperty(value = "送修人电话")
    private String afterUserMobile;
    /**
     * 会员等级
     */
    @ApiModelProperty(value = "会员等级")
    private Integer userClass;
    /**
     * 会员等级名称
     */
    @ApiModelProperty(value = "会员等级名称")
    private String userClassName;
    /**
     * imei(串号)
     */
    @ApiModelProperty(value = "imei(串号)")
    private String imei;
    /**
     * 设备名称(型号加规格)
     */
    @ApiModelProperty(value = "设备名称(型号加规格)")
    private String deviceName;
    /**
     * 外观描述
     */
    @ApiModelProperty(value = "外观描述")
    private String appearanceDescription;
    /**
     * 配置
     */
    @ApiModelProperty(value = "配置")
    private String toConfigure;
    /**
     * 购买单号(订单ID)
     */
    @ApiModelProperty(value = "购买单号(订单ID)")
    private Integer subId;
    /**
     * 购买类型(新机/优品/良品)
     * @see PurchaseTypeEnum
     */
    @ApiModelProperty(value = "购买类型(新机/优品/良品)")
    private Integer purchaseType;
    /**
     * 购买日期
     */
    @ApiModelProperty(value = "购买日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchasingDate;
    /**
     * 购买客户姓名
     */
    @ApiModelProperty(value = "购买客户姓名")
    private String purchasingUserName;
    /**
     * 购买客户电话
     */
    @ApiModelProperty(value = "购买客户电话")
    private String purchasingUserMobile;
    /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String faultDescription;
    /**
     * 检测分析
     */
    @ApiModelProperty(value = "检测分析")
    private String faultAnalysis;
    /**
     * 检测结果 枚举值
     * @see FaultResultEnum
     */
    @ApiModelProperty(value = "检测结果")
    private Integer faultResult;
    /**
     * 检测门店(门店id)
     */
    @ApiModelProperty(value = "检测门店(门店id)")
    private Integer areaId;
    /**
     * 检测门店(门店id)
     */
    @ApiModelProperty(value = "检测门店(门店名称)")
    private String areaName;
    /**
     * 联系电话(门店电话)
     */
    @ApiModelProperty(value = "联系电话(门店电话)")
    private String areaMobile;
    /**
     * 检测人员姓名
     */
    @ApiModelProperty(value = "检测人员姓名")
    private String checkUser;
    /**
     * 检测时间
     */
    @ApiModelProperty(value = "检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;
    /**
     * 温馨提示
     */
    @ApiModelProperty(value = "温馨提示")
    private String reminder;
    /**
     * 温馨提示
     */
    @ApiModelProperty(value = "小文件服务器的fid")
    private String fid;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic(value = "0", delval = "1")
    private Boolean isDel;
    /**
     * xtenant
     */
    @ApiModelProperty(value = "xtenant")
    private Integer xtenant;

    @Getter
    @AllArgsConstructor
    public enum PurchaseTypeEnum implements CodeMessageEnumInterface {
        NEW_ORDER(1,"新机订单"),
        LP_ORDER(2,"良品订单"),
        YP_ORDER(3,"优品订单");
        /**
         * 门店
         */
        private Integer code;
        /**
         * 名称
         */
        private String message;

        public static List<ShouhouServiceReportRes.ServiceReportEnum> toEnumVOList() {
            List<ShouhouServiceReportRes.ServiceReportEnum> enumVOS = new ArrayList<>();
            for (PurchaseTypeEnum temp : PurchaseTypeEnum.values()) {
                ShouhouServiceReportRes.ServiceReportEnum vo = new ShouhouServiceReportRes.ServiceReportEnum();
                vo.setLabel(temp.getMessage());
                vo.setValue(temp.getCode());
                enumVOS.add(vo);
            }
            return enumVOS;
        }

    }
    @Getter
    @AllArgsConstructor
    public enum FaultResultEnum implements CodeMessageEnumInterface {
        FAULT_RESULT_1(1,"故障与客户描述一致"),
        FAULT_RESULT_2(2,"故障属实"),
        FAULT_RESULT_3(3,"未见故障"),
        FAULT_RESULT_4(4,"在保"),
        FAULT_RESULT_5(5,"非保")
        ;
        /**
         * 门店
         */
        private Integer code;
        /**
         * 名称
         */
        private String message;

        public static List<ShouhouServiceReportRes.ServiceReportEnum> toEnumVOList() {
            List<ShouhouServiceReportRes.ServiceReportEnum> enumVOS = new ArrayList<>();
            for (FaultResultEnum temp : FaultResultEnum.values()) {
                ShouhouServiceReportRes.ServiceReportEnum vo = new ShouhouServiceReportRes.ServiceReportEnum();
                vo.setLabel(temp.getMessage());
                vo.setValue(temp.getCode());
                enumVOS.add(vo);
            }
            return enumVOS;
        }

    }

}
