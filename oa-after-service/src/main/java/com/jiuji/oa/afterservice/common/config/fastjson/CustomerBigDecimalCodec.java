package com.jiuji.oa.afterservice.common.config.fastjson;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @since 2019/11/4
 */
public class CustomerBigDecimalCodec extends BigDecimalCodec implements ContextObjectSerializer {

    public static final CustomerBigDecimalCodec instance = new CustomerBigDecimalCodec();
    /**数值太长*/
    private static final int NUMBER_TOO_LENGTH = 16;
    private static final BigDecimal LOW = BigDecimal.valueOf(-90071992_54740991L);
    private static final BigDecimal HIGH = BigDecimal.valueOf(90071992_54740991L);

    /**
     * 当BigDecimal类型的属性上有@JsonFiled注解，且该注解中的format有值时，使用该方法进行序列化，否则使用fastjson的
     * BigDecimalCodec中的write方法进行序列化
     * <AUTHOR> 2021-04-12 支持选择进位模式 例子: #.00@HALF_UP  @后面的输入参照枚举: java.math.RoundingMode
     */
    @Override
    public void write(JSONSerializer serializer, Object objectParam, BeanContext context){
        SerializeWriter out = serializer.out;
        Object object = Optional.ofNullable(objectParam).orElse(BigDecimal.ZERO);
        //xxk 增加model的支持
        String[] formats = context.getFormat().split("@");
        BigDecimal val = (BigDecimal) object;
        DecimalFormat decimalFormat = new DecimalFormat(formats[0]);
        if(formats.length>1){
            //指定了格式化方式
            decimalFormat.setRoundingMode(RoundingMode.valueOf(formats[1]));
        }

        int scale = decimalFormat.getMaximumFractionDigits();

        String outText = decimalFormat.format(object);

        if (scale == 0) {
            boolean isTooLength = outText.length() >= NUMBER_TOO_LENGTH
                    && SerializerFeature.isEnabled(context.getFeatures(), SerializerFeature.BrowserCompatible)
                    && (val.compareTo(LOW) < 0
                    || val.compareTo(HIGH) > 0);
            if (isTooLength){
                out.writeString(outText);
                return;
            }
        }

        boolean isDefaultNumber = Optional.ofNullable(context.getAnnation(JSONField.class)).map(JSONField::defaultValue)
                .filter(StrUtil::isNotBlank).map(NumberUtil::isNumber).orElse(Boolean.TRUE);
        if(NumberUtil.isNumber(outText) && isDefaultNumber){
            out.write(outText);
        }else{
            out.writeString(outText);
        }


        if (out.isEnabled(SerializerFeature.WriteClassName) && context.getFieldType() != BigDecimal.class && scale == 0) {
            out.write('.');
        }

    }

}
