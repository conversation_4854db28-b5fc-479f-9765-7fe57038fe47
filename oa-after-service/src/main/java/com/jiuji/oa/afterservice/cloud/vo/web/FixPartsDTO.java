package com.jiuji.oa.afterservice.cloud.vo.web;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 维修配件对象
 *
 * <AUTHOR>
 * @date 2018-01-04
 */
@Data
@ToString
public class FixPartsDTO {

    private Boolean supportService;
    private Integer productId;
    private Integer ppid;
    private BigDecimal price;
    private BigDecimal vipPrice;
    private String name;
    private Integer cid;
    private Integer brandID;
    private String image;
    private Integer troubleId;
    private Integer repParentId;
    private String troubleDes;
    private String repParentTitle;
    private String repParentIcon;
    private String repIntro;
    private String repParentIntro;
    private Boolean checkboxFlag;
    private Integer sort;
    private Boolean selected = false;
    private Integer repairTypeId;
    private String repairTypeName;
    private String repairTypeDes;
    private String productColor;
    private BigDecimal oemPrice;
    /**
     * 小店商品不显示  1--不显示   0--显示
     */
    private Boolean storeNotShow;
    /**
     * 预计维修时间（单位：分钟 ）
     */
    private Integer estimatedTime;
}
