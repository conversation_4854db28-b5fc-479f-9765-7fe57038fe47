package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修组下拉查询条件枚举
 * @author: gengjiaping
 * @date: 2020/3/13
 */
@Getter
@AllArgsConstructor
public enum WeiXiuZuLogsSearchTypeEnum implements CodeMessageEnumInterface {
    FZR("inuser","分组人"),
    IMEI("imei","imei"),
    SHID("shouhou_id","shouhou_id"),
    PRODUCT("product_name","机型");
    /**
     * 状态
     */
    private String code;
    /**
     * 名称
     */
    private String message;
}
