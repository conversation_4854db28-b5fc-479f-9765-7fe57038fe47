package com.jiuji.oa.afterservice.bigpro.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class OneClickUseReq {



    /**
     * 优惠券码
     */
    @NotNull(message = "优惠券码不能为空")
    private String couponMa;

    /**
     * 1- 维修单优惠码
     * 2- 积分兑换券
     * 方案类型
     */
    @NotNull(message = "方案类型不能为空")
    private Integer type;

    @NotNull(message = "维修单id不能为空")
    private Integer shouHouId;

    @NotNull(message = "会员id不能为空")
    private Integer userId;
}
