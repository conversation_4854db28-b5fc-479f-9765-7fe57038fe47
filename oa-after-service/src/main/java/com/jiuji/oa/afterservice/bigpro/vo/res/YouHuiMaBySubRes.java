package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class YouHuiMaBySubRes {
    @J<PERSON>NField(name = "CardID")
    private String cardId;
    @J<PERSON><PERSON><PERSON>(name ="CardIDShow")
    private String cardIdShow;
    @J<PERSON><PERSON><PERSON>(name ="EndTime")
    private String endTime;
    @J<PERSON><PERSON>ield(name ="MonthUseCount")
    private String monthUseCount;
    @JSONField(name ="StartTime")
    private String startTime;
    @JSONField(name ="State")
    private String state;
    @JSONField(name ="areaids")
    private String areaIds;
    @J<PERSON><PERSON>ield(name ="ch999_id")
    private Integer ch999Id;
    private String errorMsg;
    private String gname;
    private Integer id;
    @JSONField(name ="isNewBuy")
    private Boolean isNewBuy;
    @J<PERSON><PERSON>ield(name ="isdjq")
    private String isdjq;
    private String limintClint;
    private String limintClintShow;
    private Integer limit;
    private Integer limit1;
    private String limit1name;
    private Boolean limit2;
    private Integer limitType;
    private String limitWay;
    private String limitids;
    private Double limitprice;
    private String msg;
    private String names;
    private Integer stats;
    private String takeMethod;
    private String takeMethodShow;
    private Double total;
    private Integer type;
    private String excludePpIds;
    private String input;
    private String oldRuleCode;
    protected int userid;
    private int firstOwnerId;
    private Boolean isSelfUnavailable;
    private int rewardPoints;
}
