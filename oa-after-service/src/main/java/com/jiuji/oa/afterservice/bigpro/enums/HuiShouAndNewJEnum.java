package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: gengjiaping
 * @date: 2020/3/20
 */
@Getter
@AllArgsConstructor
public enum HuiShouAndNewJEnum implements CodeMessageEnumInterface {
    NEW_MACHINE(1, "新机"),
    EXCELLENT_PRODUCT(2, "优品"),
    GOOD_PRODUCT(3, "良品"),
    VALUE_ADDED_RECYCLING(5, "回收增值机"),
    EXTERNAL_REPAIR_MACHINE(4, "外修机"),
    GOODS_IN_STOCK(6,"现货"),
    ;

    /**
     * 门店
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
