package com.jiuji.oa.afterservice.patchsearch.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.oa.afterservice.patchsearch.dao.PatchSearchDao;
import com.jiuji.oa.afterservice.patchsearch.enums.SmallSubStateEnum;
import com.jiuji.oa.afterservice.patchsearch.enums.SubStateEnum;
import com.jiuji.oa.afterservice.patchsearch.service.PatchSearchService;
import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Auther: qiweiqing
 * @Date: 2020/02/06
 * @Description:
 */
@Service
@DS("ch999oanew")
public class PatchSearchServiceImpl implements PatchSearchService {

    private static final String SUB_LINK = "%s/addOrder/editOrder?SubID=%s";
    private static final String SMALL_SUB_LINK = "%s/staticpc/#/small-refund/%s";
    @Autowired
    private PatchSearchDao patchSearchDao;
    @Autowired
    private SysConfigClient sysConfigClient;

    @Override
    public List<PatchSearchRes> listPageById(PatchSearchReq personChangesReq) {
        List<PatchSearchRes> records = patchSearchDao.getBasketPage(personChangesReq);
        Optional<String> oaUrlOpt = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL)).map(R::getData);
        if (CollectionUtils.isNotEmpty(records)) {
            for (PatchSearchRes record : records) {
                if ("销售订单".equals(record.getType())) {
                    String messageByCode = EnumUtil.getMessageByCode(SubStateEnum.class, record.getSubCheck());
                    record.setSubCheckName(messageByCode);
                    oaUrlOpt.ifPresent(oaUrl->record.setLink(String.format(SUB_LINK,oaUrl,record.getSubId())));

                } else if ("售后小件订单".equals(record.getType())) {
                    String messageByCode = EnumUtil.getMessageByCode(SmallSubStateEnum.class, record.getSubCheck());
                    record.setSubCheckName(messageByCode);
                    oaUrlOpt.ifPresent(oaUrl->record.setLink(String.format(SMALL_SUB_LINK,oaUrl,record.getSubId())));
                }
            }
        }
        return records;
    }
}
