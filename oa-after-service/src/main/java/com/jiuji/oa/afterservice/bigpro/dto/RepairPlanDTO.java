package com.jiuji.oa.afterservice.bigpro.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 维修方案DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@ApiModel(value = "RepairPlanDTO对象", description = "维修方案DTO")
public class RepairPlanDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID，新增时为null")
    private Integer id;

    @ApiModelProperty(value = "方案名称", required = true)
    private String planName;

    @ApiModelProperty(value = "关联配件类型：1-维修配件（默认），2-维修成本")
    private Integer accessoryType;

    @ApiModelProperty(value = "关联配件ID")
    private Integer accessoryPpid;


    @ApiModelProperty(value = "关联名称")
    private String accessoryName;

    @ApiModelProperty(value = "方案价格")
    private BigDecimal price;

    /**
     * 选中生成配件
     */
    private Boolean isGenerateAccessory;
} 