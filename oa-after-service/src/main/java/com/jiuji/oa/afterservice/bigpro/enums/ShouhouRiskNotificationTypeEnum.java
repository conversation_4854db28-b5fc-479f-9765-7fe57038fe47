package com.jiuji.oa.afterservice.bigpro.enums;

import com.ch999.common.util.utils.CommonUtils;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后大件风险告知书枚举类
 * @author: <PERSON>
 * @date: 2020/5/27
 */
@Getter
@AllArgsConstructor
public enum ShouhouRiskNotificationTypeEnum implements CodeMessageEnumInterface {
    NOT_SEND(0,"不发送", "不发送",100),
    DELIVERY(1,"《九机代送风险确认》", "《九机代送风险确认》",10),
    RETURN_EXCHANGE_DEPOSIT(2,"《退换保证金收取确认》", "《退换保证金收取确认》",20),
    IMMERSION_EQUIPMENT(3,"《进液设备风险确认》", "《进液设备风险确认》",50),
    DATA_RECOVERY(4,"《数据寻回风险确认》", "《数据寻回风险确认》",80),
    NOT_TURN_ON(6,"《不开机/不显示设备风险确认》", "《不开机/不显示设备风险确认》",40),
    CANNOT_SIGNED(5,"设备无法签署", "设备无法签署",90),
    CHANGE_SCREEN(7,"《换外屏风险确认》", "《换外屏风险确认》",30),
    BOARD_REPAIR(8,"《主板维修/存储容量升级风险确认》", "《主板维修/存储容量升级风险确认》",70),
    EQUIPMENT_DEBONDING(9,"《屏幕脱胶风险确认》", "《屏幕脱胶风险确认》",60),
    ;
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;

    private String notificationName;

    private Integer rank;

    public static List<ShouhouRiskNotificationTypeEnum> getEnumByCodes(String codes) {
        return Arrays.stream(values()).filter(v -> CommonUtils.covertIdStr(codes).contains(v.getCode())).collect(Collectors.toList());
    }

    public static ShouhouRiskNotificationTypeEnum getEnumByCode(Integer codes) {
        for (ShouhouRiskNotificationTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(codes)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static List<EnumVO> getEnumVOList(){
        return Arrays.stream(ShouhouRiskNotificationTypeEnum.values()).sorted(Comparator.comparing(ShouhouRiskNotificationTypeEnum::getRank)).map(v->{
            EnumVO enumVO = new EnumVO();
            enumVO.setLabel(v.getMessage());
            enumVO.setValue(v.getCode());
            return enumVO;
        }).collect(Collectors.toList());
    }
}
