package com.jiuji.oa.afterservice.common.util;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.jiuji.oa.afterservice.common.bo.HeaderMergeInfo;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2020/6/24 16:03
 **/
public class NewExcelUtils {
    /**
     * 页面数据导出通用方法 支持表头的合并 （没有任何合并信息的列会自动帮忙处理 不需要定义 不然每一个列都需要定义合并信息）
     *
     * @param response   HttpServletResponse
     * @param data       数据
     * @param header     如果有多层 就是最后一层表头的名称
     * @param mergeInfos 合并信息
     * @param fileName   导出的文件名称
     * @param maxLevel   表头的层级
     * @throws IOException Exception
     */
    public static void export(HttpServletResponse response, List<List<Object>> data, List<String> header, List<HeaderMergeInfo> mergeInfos, String fileName, int maxLevel) throws IOException {
        if (data == null || maxLevel < 0) {
            throw new IllegalArgumentException();
        }
        ExcelWriter writer = ExcelUtil.getWriter(fileName.contains(".xlsx"));
//        writer.setStyleSet(
//                writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER)
//        );
        for (int i = 0; i < maxLevel; i++) {
            writer.writeHeadRow(header);
        }
        if (mergeInfos != null && mergeInfos.size() > 0) {
            for (HeaderMergeInfo mergeInfo : mergeInfos) {
                writer.merge(mergeInfo.getFirstRow(), mergeInfo.getLastRow(), mergeInfo.getFirstColumn(), mergeInfo.getLastColumn(), mergeInfo.getTitle(), true);
            }
            //处理没有任何合并信息的列
            for (int j = 0; j < header.size(); j++) {
                int finalJ = j;
                if (mergeInfos.parallelStream().noneMatch(p -> finalJ >= p.getFirstColumn() && finalJ <= p.getLastColumn())) {
                    writer.merge(0, maxLevel - 1, j, j, header.get(j), true);
                }
            }
        }
        writer.write(data);

        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName.endsWith(".xls") || fileName.endsWith(".xlsx") ? fileName : fileName.concat(".xls"), "UTF-8"));
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);
    }
}
