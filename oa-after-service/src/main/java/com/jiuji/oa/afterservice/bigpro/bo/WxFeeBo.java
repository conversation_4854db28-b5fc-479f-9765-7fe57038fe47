package com.jiuji.oa.afterservice.bigpro.bo;

import cn.hutool.core.clone.CloneSupport;
import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

/**
 * 维修费用
 * @author: gengjiaping
 * @date: 2020/4/14
 */
@Data
@Accessors(chain = true)
@ApiModel("添加维修费Bo")
public class WxFeeBo extends CloneSupport<WxFeeBo> {
    //1 表示仅仅添加维修费 2 表示添加维修配件
    private Integer kinds;
    @ApiModelProperty(hidden = true,value = "操作人区域id")
    private Integer areaId;
    @DecimalMin(value = "0",message = "成本必须大于等于0")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private BigDecimal inprice;
    @DecimalMin(value = "0",message = "价格必须大于等于0")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private BigDecimal price;
    @DecimalMin(value = "0",message = "价格必须大于等于0")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private BigDecimal price1;
    private String productName;
    private Integer shouhouId;
    @ApiModelProperty(hidden = true,value = "操作人名称")
    private String user;
    @ApiModelProperty(hidden = true,value = "租户id")
    private Integer xtenant;
    private Integer ppid;
    private Boolean isyouhuima;
    private Boolean ishexiao;
    @DecimalMin(value = "0",message = "工时费必须大于等于0")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private BigDecimal priceGs;
    private Integer pid;
    /**
     * 分类CID
     */
    private Integer cid;

    /**
     * 是否为服务
     */
    private Integer isService;

    /**
     * 商品规格
     */
    private String color;
    /**绑定配件信息*/
    @ApiModelProperty("绑定配件信息")
    private List<BindPpidInfoBo> bindPpidInfos;

    /**库存操作日志*/
    @ApiModelProperty(hidden = true)
    private String operateProductKcLog;
    /**保存配件之后的id*/
    private Integer id;

    /**
     * 绑定id
     */
    private Integer bindId;
    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.IshuishouEnum
     */
    private Integer ishuishou;

    /**
     * 前置日志
     */
    private String prevLog;

    private String sn;

    private Boolean isSn;

}
