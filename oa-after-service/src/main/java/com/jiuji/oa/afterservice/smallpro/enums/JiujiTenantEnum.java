package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: <九机租户枚举类>
 * translation: <Jiuji tenant enumeration class>
 *
 * <AUTHOR>
 * @date 2020/1/7
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum JiujiTenantEnum implements CodeMessageEnumInterface {

    /**
     * Jiuji租户编码-Jiuji租户信息
     */
    JIUJI_TENANT_NULL(null, ""),
    JIUJI_TENANT_JIUJI(0L, "九机网"),
    JIUJI_TENANT_YAYA(1L, "丫丫网"),
    JIUJI_TENANT_HUAWEI(2L, "华为授权店"),
    JIUJI_TENANT_APPLE(3L, "九讯苹果体验店"),
    JIUJI_TENANT_JDYD(4L, "九电移动"),
    ;

    /**
     * Jiuji租户编码
     */
    private Long code;
    /**
     * Jiuji租户信息
     */
    private String message;
}
