package com.jiuji.oa.afterservice.smallpro.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/6/7 19:45
 */
@Data
@NoArgsConstructor
public class SmallCategoryBaseRes {
    @ApiModelProperty("排名")
    private String orders;

    @ApiModelProperty("品类ID")
    private Integer categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;

    @ApiModelProperty("销量")
    private Integer saleCount;

    @ApiModelProperty("库存量")
    private Integer inventoryCount;

    @ApiModelProperty("库销比")
    private BigDecimal stockToSalesRatio;

    @ApiModelProperty("销售额")
    private BigDecimal saleAmount;

}
