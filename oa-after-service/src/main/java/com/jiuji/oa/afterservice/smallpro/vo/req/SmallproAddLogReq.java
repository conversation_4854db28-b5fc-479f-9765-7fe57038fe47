package com.jiuji.oa.afterservice.smallpro.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * description: <添加小件接件流程日志Req>
 * translation: <Add smallpro process log>
 *
 * <AUTHOR>
 * @date 2019/12/4
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproAddLogReq implements Serializable {

    private static final long serialVersionUID = 2959388275756226921L;
    /**
     * 小件接件Id
     */
    @ApiModelProperty(value = "小件接件Id")
    private Integer smallproId;
    /**
     * 送修小件产品名称，多个用英文","号隔开
     */
    @ApiModelProperty(value = "送修小件产品名称，多个用英文\",\"号隔开")
    private String productName;
    /**
     * 推送到微信[1推送|0不推送]
     */
    @ApiModelProperty(value = "推送到微信[1推送|0不推送]")
    private Integer toWeixin;
    /**
     * 推送到内部邮箱[1推送|0不推送]
     */
    @ApiModelProperty(value = "推送到内部邮箱[1推送|0不推送]")
    private Integer toEmail;
    /**
     * 推送到短信[1推送|0不推送]
     */
    @ApiModelProperty(value = "推送到短信[1推送|0不推送]")
    private Integer toSms;
    /**
     * 是否显示[1显示|0不显示]
     */
    @ApiModelProperty(value = "是否显示[1显示|0不显示]")
    private Integer showType;
    /**
     * 进程记录内容
     */
    @ApiModelProperty(value = "进程记录内容")
    private String comment;
    /**
     * 推送人姓名,只支持单人
     */
    @ApiModelProperty(value = "推送人姓名,支持单人")
    private String userName;


    private List<String> userNameList;

}
