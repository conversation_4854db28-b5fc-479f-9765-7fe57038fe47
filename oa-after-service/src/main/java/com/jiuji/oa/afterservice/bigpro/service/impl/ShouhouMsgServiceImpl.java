package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.WeChatTemplateTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.ExtenAntUrlTypeEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.source.MoaUrlSource;
import com.jiuji.oa.afterservice.common.template.WechatTemplateLog;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.sys.po.SysConfig;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @author: gengjiaping
 * @date: 2020/4/11
 */
@Slf4j
@Service
public class ShouhouMsgServiceImpl implements ShouhouMsgService {

    @Autowired
    private AreaInfoClient areaInfoClient;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private ShouhouQudaoService shouhouQudaoService;
    @Autowired
    private ShouhouMsgrecordService shouhouMsgrecordService;
    @Autowired
    private ShouhouTimerService shouhouTimerService;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Autowired
    private SubCollectionService subCollectionService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private ShouhouOtherService shouhouOtherService;
    @Autowired
    private WeixinUserService weixinUserService;
    @Autowired
    private SmsContentService smsContentService;
    @Resource
    private IMCloud imCloud;
    @Autowired
    private BasketService basketService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private CommonService commonService;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private SysConfigService sysConfigService;


    @Override
    public String getPushMessageContent(ShouhouMsgPushMessageBo pushMsg, AreaInfo areaInfo) {
        OaUserBO currentStaff = abstractCurrentRequestComponent.getCurrentStaffId();
        String wxsendmsg = "";
        String value = "";
        if (areaInfo != null && StrUtil.isNotBlank(areaInfo.getPrintName())){
            value = areaInfo.getPrintName();
        }else if (currentStaff != null){
            value = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.PRINT_NAME,currentStaff.getXTenant());
        }else{
            value = sysConfigService.getValueByCode(SysConfigConstant.PRINT_NAME);
        }

        switch (pushMsg.getMsgId()) {
            //接件通知
            case 1:
            case 44:
                wxsendmsg = "您的售后业务已受理，感谢您选择" + value + "售后";
                break;
            case 4:
                //九机服务出险(延保除外)
                String fuwuname = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("fuwuname")) ?
                        pushMsg.getTmpData().get("fuwuname") : "";
                wxsendmsg = "本次售后服务已为您使用【" + fuwuname + "】服务，承保的配件费用已为您减免，后续无法再次使用，请您悉知。";
                break;
            case 5:
                //九机服务出险(延保)
                String fuwuendtime =
                        (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("fuwuendtime")) ?
                                pushMsg.getTmpData().get("fuwuendtime") : "";
                if (StringUtils.isNotEmpty(fuwuendtime)) {
                    fuwuendtime = "延保时间截止" + fuwuendtime + "，";
                }
                wxsendmsg = "本次售后服务已为您使用【延保】服务，涉及" + areaInfo.getPrintName() + "服务条款内的配件费用已为您承保，" + fuwuendtime +
                        "请您悉知。";
                break;
            case 6:
                //维修配件订购提交
                String wxpeijian = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("wxpeijian")) ?
                        pushMsg.getTmpData().get("wxpeijian") : "";
                String wxliyou = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("wxliyou")) ?
                        pushMsg.getTmpData().get("wxliyou") : "";
                String wxpeijianprice = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey(
                        "wxpeijianprice")) ? pushMsg.getTmpData().get("wxpeijianprice") : "";
                if (StringUtils.isNotEmpty(wxpeijianprice)) {
                    wxpeijianprice = "，费用为" + wxpeijianprice + "元";
                }
                wxsendmsg = "您的设备经检测" + wxliyou + "故障，需要更换" + wxpeijian + wxpeijianprice +
                        "。维修配件已进行订购，可能会对维修进度造成延时，感谢您的耐心等待！";
                break;
            case 7:
                //转地区-提交
            case 18:
                //转地区-提交
                R<AreaInfo> toAreaInfoR = areaInfoClient.getAreaInfoById(pushMsg.getToAreaId());
                AreaInfo toAreaInfo = null;
                if (ResultCode.SUCCESS == toAreaInfoR.getCode() && toAreaInfoR.getData() != null) {
                    toAreaInfo = toAreaInfoR.getData();
                }
                String toareaname = toAreaInfo != null ? toAreaInfo.getAreaName() + "(" + toAreaInfo.getAreaName() +
                        ")" : "";
                //转地区接收
                R<AreaInfo> curAreaDc = areaInfoClient.getCurAreaDC(areaInfo.getId());
                R<AreaInfo> curAreaH1 = areaInfoClient.getCurAreaH1(areaInfo.getId());
                Integer areaIdDc = null;
                Integer areaIdH1 = null;
                if (curAreaDc.getCode() == ResultCode.SUCCESS && curAreaDc.getData() != null) {
                    areaIdDc = curAreaDc.getData().getId();
                }
                if (curAreaH1.getCode() == ResultCode.SUCCESS && curAreaH1.getData() != null) {
                    areaIdH1 = curAreaH1.getData().getId();
                }
                boolean dcToResult = (areaIdDc != null && areaIdDc.equals(pushMsg.getToAreaId()));
                boolean h1ToResult = (areaIdH1 != null && areaIdH1.equals(pushMsg.getToAreaId()));
                if (pushMsg.getIsReceive() || 18 == pushMsg.getMsgId()) {
                    wxsendmsg = "您的设备已到达" + toareaname;
                    if (dcToResult || h1ToResult) {
                        wxsendmsg = "您的设备已到达总部售后技术中心。";
                    }
                } else {
                    //转地区提交
                    String areaname = areaInfo.getAreaName() + "(" + areaInfo.getAreaName() + ")";
                    boolean dcResult = (areaIdDc != null && areaIdDc.equals(pushMsg.getAreaId()));
                    boolean h1Result = (areaIdH1 != null && areaIdH1.equals(pushMsg.getAreaId()));
                    if (dcToResult || h1ToResult) {
                        wxsendmsg = "您的设备由" + areaname + "店发往" + areaInfo.getPrintName() + "售后技术中心进行处理，已发出。";
                    } else if (dcResult || h1Result) {
                        wxsendmsg = "您的设备由" + areaInfo.getPrintName() + "售后技术中心发回" + toareaname + ", 已发出。";
                    } else {
                        wxsendmsg = "您的设备由" + areaname + "发往" + toareaname + "进行处理, 已发出。";
                    }
                    //预计送达时间
                    String reachtime = shouhouService.getReachTime(pushMsg.getAreaId(), pushMsg.getToAreaId(), null);
                    if (StringUtils.isNotEmpty(reachtime)) {
                        wxsendmsg += "预计到达时间：" + reachtime;
                    }
                }
                break;
            case 17:
                String wxpiejian = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("wxpiejian")) ?
                        pushMsg.getTmpData().get("wxpiejian") : "";
                wxsendmsg = "您订购的维修配件" + wxpiejian + "已到货，工程师正在处理中。";
                break;

            case 19:
                //修改组别
                String groupname1 = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("groupname1"))
                        ? pushMsg.getTmpData().get("groupname1") : "";
                String groupname2 = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("groupname2"))
                        ? pushMsg.getTmpData().get("groupname2") : "";
                wxsendmsg = "批量维修小组由【" + groupname1 + "】变为【" + groupname2 + "】";
                break;
            case 20:
                //添加备用机
                String beiyongji = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("beiyongji")) ?
                        pushMsg.getTmpData().get("beiyongji") : "";
                String beiyongjiYajin = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey(
                        "beiyongjiYajin")) ? pushMsg.getTmpData().get("beiyongjiYajin") : "";
                String shoptel = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("shoptel")) ?
                        pushMsg.getTmpData().get("shoptel") : "";
                wxsendmsg =
                        "我们为您免费提供【" + beiyongji + "】备用机，收取押金：" + beiyongjiYajin + "元。备用机使用过程中如有问题可及时与我们联系。" + shoptel;
                break;
            case 21:
                //添加外送渠道
                String qudaoname = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("qudaoname")) ?
                        pushMsg.getTmpData().get("qudaoname") : "";
                wxsendmsg = "您的设备已送往【" + qudaoname + "服务中心】，预计48小时内工作人员将与你联系。";
                break;
            case 22:
                //添加工程师
                String weixiuren = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("weixiuren")) ?
                        pushMsg.getTmpData().get("weixiuren") : "";
                wxsendmsg = "您好，您的设备由" + weixiuren + "工程师为您受理。";
                break;
            case 23:
                //已修好
                wxsendmsg = "您好，您的设备故障已处理完成。";
                break;
            case 24:
                //测试通过
                String testname = (pushMsg.getTmpData() != null && pushMsg.getTmpData().containsKey("testuser")) ?
                        pushMsg.getTmpData().get("testuser") : "";
                wxsendmsg = "您的设备经测试员" + testname + "检测，送检故障已经解决。";
                break;
            case 25:
                //测试不通过
                wxsendmsg = "您的设备测试故障未完全排除，我们正在加急为您处理。";
                break;
            case 26:
                //退换机 换机头
                wxsendmsg = "您好，已为您的设备办理换新。";
                break;
            case 27:
                //退换机 退款
                wxsendmsg = "您好，已为您的设备办理退款。";
                break;
            case 28:
                //退换机	换其它
                wxsendmsg = "您好，已为您的设备办理更换其它机型。";
                break;
            default:
                break;
        }
        return wxsendmsg;
    }

    @Override
    public void addShoushouMsgRecord(ShouhouMsgRecordBo record, Integer timeoutHour) {
        if (record == null) {
            return;
        }
        if (timeoutHour == null) {
            timeoutHour = 0;
        }
        Boolean isWaisong = false;
        Integer itemid = record.getMsgId();
        if (timeoutHour > 0) {
            Integer timeOut = timeoutHour * 60;
            LocalDateTime dateTime = null;
            //当前地接件时间/接件时间
            if (2 == itemid || 3 == itemid) {
                dateTime = shouhouService.getJiejianTime(record.getShouhouId());
            } else if (Arrays.asList(8, 9, 10, 33, 11, 12, 13, 14, 15, 16, 32).contains(itemid)) {
                ShouhouQudao shouhouQudao = shouhouQudaoService.getShouhouQudaoByShouhouId(record.getShouhouId());
                if (shouhouQudao != null) {
                    dateTime = shouhouQudao.getDtime();
                    isWaisong = true;
                }
            }
            if (dateTime != null) {
                Duration d = Duration.between(dateTime, record.getPushtime());
                Integer time = (int) d.toMinutes();
                if (time > timeOut) {
                    record.setTimeOut(time - timeOut);
                }
            }
        }
        shouhouMsgrecordService.saveShouhouMsgRecord(record.getShouhouId(), record.getMsgId(), record.getPushtime(), record.getTimeOut(), record.getInuser(), record.getMsgContent());
        if (isWaisong && record.getTimeOut() > 0) {
            ShouhouTimer shouhouTimer = new ShouhouTimer();
            shouhouTimer.setShouhouid(record.getShouhouId());
            shouhouTimer.setWaisongmsg(record.getTimeOut());
            shouhouTimerService.addShouhouTimer(shouhouTimer);
        }
    }

    @Override
    public void sendCollectMsg(Integer wxId, String mgs, String user) {
        String url = moaUrlSource.getWxDetail(String.valueOf(wxId));
        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        url = jiujiSystemProperties.getMoa() + "/mshouhou/edit/" + wxId.toString();
        if (StringUtils.isNotEmpty(host)){
            url = host + "/mshouhou/edit/" + wxId.toString();
        }
        List<Integer> userIds = subCollectionService.getSubCollectionUserIds(wxId);
        if(CollUtil.isEmpty(userIds)){
            return;
        }
        Integer xTenant = XtenantEnum.getXtenant();
        String sendMsg = "亲爱的" + sysConfigService.getWebNameByXtenant(xTenant) + "人，您关注的维修单【<a href=\"" + jiujiSystemProperties.getMoa() + "/mshouhou/edit/" + wxId + "\">" + wxId + "</a>】，【" + user + "】" + mgs;
        String wxMsg = "亲爱的" + sysConfigService.getWebNameByXtenant(xTenant) + "人，您关注的维修单：" + wxId + "，【" + user + "】" + mgs;
        for (Integer e : userIds) {
            weixinUserService.senWeixinAndOaMsg(wxMsg, sendMsg, url, e.toString(), OaMesTypeEnum.GZDDTZ.getCode().toString());
        }
    }

    @Override
    public void sendWeixinMsg(Integer wxid, String wxMsg, Boolean isZhongyou, Boolean isSendMsg) {
        ShouHouNowAreaBo shouhouNowAreaInfo = shouhouOtherService.getNowAreaInfo(wxid);
        if (shouhouNowAreaInfo == null) {
            return;
        }
        //软件单接件时间在1分钟内的不推送消息
        Shouhou shouhou = Optional.ofNullable(shouhouService.getById(wxid)).orElse(new Shouhou());
        Duration duration = Duration.between(Optional.ofNullable(shouhou.getModidate()).orElse(LocalDateTime.now()), LocalDateTime.now());
        if (Boolean.TRUE.equals(shouhou.getIssoft()) && duration.toMinutes() <= 1) {
            return;
        }
        List<Integer> areaIdList = Arrays.asList(shouhouNowAreaInfo.getAreaId(), shouhouNowAreaInfo.getNowArea());
        List<AreaInfo> areaInfos = Optional.ofNullable(areaInfoClient.listAreaInfo(areaIdList))
                //获取数据成功,且nowArea和areaid都匹配
                .filter(R::isSuccess).map(R::getData).orElseGet(Collections::emptyList);
        if (areaInfos.isEmpty()) {
            //查询不到门店直接返回
            return;
        }
        Optional<AreaInfo> nowAreaInfoOpt = areaInfos.stream().filter(areaInfo -> Objects.equals(areaInfo.getId(),shouhouNowAreaInfo.getNowArea())).findFirst();
        Optional<AreaInfo> areaInfoOpt = areaInfos.stream().filter(areaInfo -> Objects.equals(areaInfo.getId(),shouhouNowAreaInfo.getAreaId())).findFirst();
        if(!nowAreaInfoOpt.isPresent() || !areaInfoOpt.isPresent()){
            //任意一个门店不存在直接返回
            return;
        }
        AreaInfo nowAreaInfo = nowAreaInfoOpt.get();
        AreaInfo areaInfo = nowAreaInfoOpt.get();
        Long xtenant = areaInfoOpt.map(AreaInfo::getXtenant).map(Convert::toLong).orElse(0L);
        String url = commonService.getUrlByXtenant(xtenant, ExtenAntUrlTypeEnum.MURL.getCode());
        url = url + "/after-service/detail/" + wxid;
        if (isZhongyou) {
            url = "http://fix.999buy.com/orderDetail/" + wxid + "/1";
        }
        String wxName = shouhouNowAreaInfo.getName();
        if (StringUtils.isBlank(nowAreaInfo.getPrintName()) || nowAreaInfo.getPrintName().contains("中邮") || isZhongyou) {
            wxMsg = MessageFormat.format("亲，您维修的{0}进程更新：", wxName) + wxMsg + " 点击可查看详细！";
            if (isSendMsg) {
                smsService.sendZyWeixinMsg(wxid, shouhouNowAreaInfo.getUserId(), "售后维修进度提醒", wxName, wxMsg, url, areaInfo.getCityId());
            }
        } else if (nowAreaInfo.getIsSend()) {
            wxMsg = String.format("亲，您的售后维修单：%s，维修进程更新：", wxid.toString()) + wxMsg + " 点击可查看详细！";

            WeixinUser wxInfo = weixinUserService.getWxxinUserByUserId(shouhouNowAreaInfo.getUserId());
            if (wxInfo == null) {
                return;
            }
            if (StringUtils.isNotBlank(wxInfo.getOpenid())) {
                this.sendShouHouNotify(wxInfo.getOpenid(), url, wxMsg, "", "售后服务", "处理中", LocalDateTime.now(), "售后人员正在处理", wxid, xtenant, nowAreaInfo.getCityId());
            }
        }
    }

    @Override
    public Boolean sendShouHouNotify(String openId, String url, String subTitle, String remark, String handleType,
                                     String status, LocalDateTime createTime, String logs, Integer wxid, Long xtenant, Integer cityId) {
        //售后进程通知 微信模板通知 模板类型为3

        Result<String> result = imCloud.sendAfterServiceProgressMsg(openId, url, subTitle, handleType, status, DateUtil.localDateTimeToString(createTime), logs, remark, cityId, xtenant);
        return result.getCode() == ResultCode.SUCCESS;
    }

    @Override
    public R<Boolean> sendMsms7(Integer wxId, String mobile, String smsContent, Integer already, Integer type) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        already = already == null ? 0 : already;
        type = type == null ? 0 : type;
        String msm7 = "";
        if (type == 2) {
            msm7 = smsContent;
        } else {
            String sms = getMsms7();
            sms = sms == null ? "" : sms;
            msm7 = sms.replace("<product_name>", smsContent);
        }

        if (StringUtils.isNotBlank(mobile) && msm7.length() > 5) {
            SmsAjaxBo smsAjaxBo = new SmsAjaxBo();
            smsAjaxBo.setActionName("send_wxtz");
            smsAjaxBo.setWxId(wxId);
            smsAjaxBo.setAlready(already == 1 ? true : false);
            smsAjaxBo.setPhoneNum(mobile);
            smsAjaxBo.setMsg(msm7);
            return this.smsAjax(smsAjaxBo, oaUserBO.getUserName());
        } else {
            return R.error(oaUserBO.getArea() + "内容错误！");
        }

    }

    /**
     * 获得处理进程添加处发送sms
     *
     * @return
     */
    @Override
    public String getMsms7() {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(oaUserBO.getAreaId());
        if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() == null) {
            return null;
        }
        AreaInfo areaInfo = areaInfoR.getData();
        List<SmsContent> smsContentList = smsContentService.list(new LambdaQueryWrapper<SmsContent>().eq(SmsContent::getAreaid, oaUserBO.getAreaId()));
        if (CollectionUtils.isEmpty(smsContentList)) {
            return null;
        }
        String msm7 = Optional.ofNullable(smsContentList.get(0)).map(SmsContent::getSms7).map(msm->msm.replace("九机网", areaInfo.getPrintName())).orElse("");
        if (areaInfo.getPrintName().equals("九机网")) {
            msm7 = msm7.replace("服务电话：400-008-3939", "").replace("，若有疑问请致电:400-008-3939", "").replace("查询电话：400-008-3939", "").replace("若有疑问请致电:400-008-3939", "").replace("咨询电话0871-68393939", "");
        }
        return msm7;
    }

    @Override
    public String servicesCompleteNotice(String openId, String url, String title, String subId, String servicesName,
                                         LocalDateTime serviceTime, String remark, Integer wxId, Long xtenant, Integer cityId) {

        Result<String> sendR = imCloud.sendServicesCompleteMsg(openId, url, title, subId, servicesName, DateUtil.localDateTimeToString(serviceTime), remark, cityId, xtenant);
        return sendR.getUserMsg();
    }

    @Override
    public String sendWeixin(String userId, String openId, String msg, Integer utype) {
        utype = utype == null ? 1 : utype;
        //先尝试发送企业微信
        if (StringUtils.isNotEmpty(userId) || StringUtils.isNotEmpty(openId)) {
            Map<String, Object> paramsMap = new HashMap<>();

            paramsMap.put("userid", userId);
            paramsMap.put("openid", openId);
            paramsMap.put("msg", msg);
            paramsMap.put("act", "SendWeinxinNotice");
            paramsMap.put("type", "text");
            paramsMap.put("utype", utype);

            List<SysConfig> sysConfigList = sysConfigService.listAll();
            SysConfig sysConfig = sysConfigList.stream().filter(t -> t.getCode().equals(SysConfigConstant.OA_WCF_HOST)).findAny().orElse(null);
            String host = "http://oawcf.ch999.cn";
            if (sysConfig != null) {
                host = sysConfig.getValue();
                if (sysConfig.getXtenant().equals(0)){
                    host = host +  "/ajax.ashx";
                }else{
                    host = host +  "/ajax.ashx";
                }
            }
            return HttpUtil.post(host, paramsMap);
        }
        return "";
    }

    @Override
    public R<String> sendShouHouYuyueNotify(String openId, String url, String first, String fuwuType, String status,
                                            String createTime, String log, String remark, Integer wxId) {

        Result<String> result = imCloud.sendAfterServiceProgressMsg(openId, url, first, fuwuType, status, createTime, log, remark, null, null);

        WechatTemplateLog msgData = new WechatTemplateLog();
        msgData.setMessage(result.getMsg());
        msgData.setOpenId(openId);
        msgData.setType(WeChatTemplateTypeEnum.BusinessMsg.getCode().toString());
        msgData.setUrl(url);

        weixinUserService.logWechatTemplateMessage(msgData, result.getMsg());

        return result.getCode() == ResultCode.SUCCESS ? R.success(result.getMsg()) : R.error(result.getMsg());
    }

    @Override
    public R<Boolean> smsAjax(SmsAjaxBo smsAjaxBo, String userName) {

        Integer areaId = shouhouService.getAreaIdByShouhouId(smsAjaxBo.getWxId());
        if ("send".equals(smsAjaxBo.getActionName())) {
            if (smsAjaxBo.getBasketId() == null) {
                return R.error("basketid不能为空");
            }
            ShouHouBasketBo shouHouBasketBo = basketService.getShouHouBasketInfoById(smsAjaxBo.getBasketId());
            if (shouHouBasketBo == null) {
                return R.error("basketId错误");
            }
            Integer kcCheck = shouHouBasketBo.getKcCheck();
            areaId = shouHouBasketBo.getAreaId();
            if (kcCheck != 2 && kcCheck != 3 && kcCheck != 7) {
                return R.error("状态错误");
            }

            return smsService.sendSms(smsAjaxBo.getPhoneNum(), smsAjaxBo.getMsg(), DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));

        } else if ("send_fahuo".equals(smsAjaxBo.getActionName())) {
            if (smsAjaxBo.getSubId() != null) {
                return R.error("单号不能为空");
            }
            ShouHouSubBo shouHouSubBo = basketService.getShouhouSubInfoById(smsAjaxBo.getSubId());
            if (shouHouSubBo == null) {
                return R.error("单号错误");
            }

            areaId = shouHouSubBo.getAreaId();
            Integer resCode;
            if (shouHouSubBo.getZitidianId() != null && shouHouSubBo.getDelivery() == 3) {
                List<String> mobileList = basketService.getMobiles(shouHouSubBo.getZitidianId());
                if (CollectionUtils.isNotEmpty(mobileList)) {
                    smsAjaxBo.setPhoneNum(mobileList.get(0));
                }
                if (smsAjaxBo.getAlready() || shouHouSubBo.getIssmsSend1() < 1) {

                    R<Boolean> sendRes = smsService.sendSms(smsAjaxBo.getPhoneNum(), smsAjaxBo.getMsg(), DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
                    resCode = sendRes.getCode();
                    if (resCode != ResultCode.SUCCESS) {
                        return R.error("消息发送失败");
                    } else {
                        basketService.updateMsgSendState(smsAjaxBo.getSubId());
                        return R.success("操作成功");
                    }

                } else {
                    return R.success("already");
                }
            }
        } else if ("send_fahuolp".equals(smsAjaxBo.getActionName())) {
            if (CommenUtil.isNullOrZero(smsAjaxBo.getSubId())) {
                return R.error("单号不能为空");
            }
            ShouHouSubBo recoverMarketInfo = basketService.getRecoverMarketInfoById(smsAjaxBo.getSubId());
            if (recoverMarketInfo == null) {
                return R.error("单号错误");
            }
            areaId = recoverMarketInfo.getAreaId();
            if (recoverMarketInfo.getZitidianId() != null && recoverMarketInfo.getDelivery() == 3) {
                List<String> mobileList = basketService.getMobiles(recoverMarketInfo.getZitidianId());
                if (CollectionUtils.isNotEmpty(mobileList)) {
                    smsAjaxBo.setPhoneNum(mobileList.get(0));
                }
                if (smsAjaxBo.getAlready() || recoverMarketInfo.getIssmsSend1() < 1) {
                    R<Boolean> sendRes = smsService.sendSms(smsAjaxBo.getPhoneNum(), smsAjaxBo.getMsg(), DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
                    Integer resCode = sendRes.getCode();
                    if (resCode != ResultCode.SUCCESS) {
                        return R.error("消息发送失败");
                    } else {
                        basketService.updateRecoverSubAddressMsgState(smsAjaxBo.getSubId());
                        return R.success("操作成功");
                    }

                } else {
                    return R.success("already");
                }
            }

        } else if ("send_wxtz".equals(smsAjaxBo.getActionName())) {

            if (areaId == null) {
                return R.error("区域信息不存在");
            }

            R<Boolean> sendRes = smsService.sendSms(smsAjaxBo.getPhoneNum(), smsAjaxBo.getMsg(), DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
            Integer resCode = sendRes.getCode();
            if (resCode != ResultCode.SUCCESS) {
                return R.error("消息发送失败");
            } else {
                OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
                shouhouService.saveShouhouLog(smsAjaxBo.getWxId(), smsAjaxBo.getMsg(), oaUserBO.getUserName(), null, true);
                // 更新短信跟进时间
                shouhouService.update(new UpdateWrapper<Shouhou>().lambda()
                        .set(Shouhou::getSmstime, LocalDateTime.now())
                        .set(Shouhou::getResultDtime, LocalDateTime.now())
                        .eq(Shouhou::getId, smsAjaxBo.getWxId()));
                return R.success("消息发送成功");
            }
        } else if ("sendsms".equals(smsAjaxBo.getActionName())) {
            R<Boolean> sendRes = smsService.sendSms(smsAjaxBo.getPhoneNum(), smsAjaxBo.getMsg(), DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
            return sendRes;
        }

        return R.error("操作成功");
    }

}
