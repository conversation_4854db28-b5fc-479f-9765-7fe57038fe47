package com.jiuji.oa.afterservice.bigpro.statistics.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wuliu")
public class Wuliu extends Model<Wuliu> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String sname;

    private String smobile;

    private String saddress;

    private String sarea;

    private Integer scityid;

    private String rname;

    private String rmobile;

    private String raddress;

    private String rarea;

    private Integer rcityid;

    private String area;

    private LocalDateTime dtime;

    private LocalDateTime ctime;

    private Double price;

    private Double inprice;

    private String shoujianren;

    private String paijianren;

    private Integer stats;

    private Integer danhaobind;

    private Integer wutype;

    private String comment;

    private String com;

    private String nu;

    private Double weight;

    private String inuser;

    private Integer linktype;

    private LocalDateTime sendtime;

    private Integer areaid;

    private Integer sareaid;

    private Integer rareaid;

    @TableField("receiveUser")
    private String receiveUser;

    @TableField("receiveTime")
    private LocalDateTime receiveTime;

    @TableField("notifyType")
    private Integer notifyType;

    private Integer payMethod;

    @TableField("subKinds")
    private Integer subKinds;

    /**
     * 合并后父级保留物流单号
     */
    private Integer wpid;

    @TableField("LastRouteTime")
    private LocalDateTime LastRouteTime;

    @TableField("EstimatedArrivalTime")
    private LocalDateTime EstimatedArrivalTime;

    @TableField("wCateId")
    private Integer wCateId;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
