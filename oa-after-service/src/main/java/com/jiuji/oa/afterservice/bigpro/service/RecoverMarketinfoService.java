package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.RecoverMarketinfo;


/**
 * ${comments}
 *
 * <AUTHOR> quan
 * @email ${email}
 * @date 2020-06-11 16:27:17
 */
public interface RecoverMarketinfoService extends IService<RecoverMarketinfo> {

    /**
     * 银行转账退款方式校验
     * @param subId
     * @return
     */
    Integer checkBankTransfer(Integer subId);

    /**
     * 通过订单号获取用户id
     * @param subId
     * @return
     */
    Number getUserIdBySubId(Number subId);
}

