package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.jiuji.oa.afterservice.bigpro.entity.Productbarcode;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class AccessoriesBaseProduct {


    private Integer ppid;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品颜色
     */
    private String productColor;
    /**
     * 会员价格
     */
    private BigDecimal price;
    /**
     * 成本
     */
    private BigDecimal inprice;

    /**
     * 图片地址
     */
    private String productImageUrl;
    /**
     * 图片详情
     */
    private String productInfoUrl;

    /**
     * 商品标签
     */
    private Integer productLabel;

    /**
     * 商品标签值
     */
    private String productLabelValue;

    /**
     * 条码
     */
    private List<Productbarcode> productbarcodeList;

    /**
     * 注意事项
     */
    private String descripion;

    /**
     * 是否支持负库存出库
     */
    private Boolean negativeInventory;

    /**
     * 商品是否绑定SN
     */
    private Boolean isSn;
}
