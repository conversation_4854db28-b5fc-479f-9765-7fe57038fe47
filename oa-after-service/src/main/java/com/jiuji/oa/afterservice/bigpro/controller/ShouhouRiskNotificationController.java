package com.jiuji.oa.afterservice.bigpro.controller;

import com.jiuji.oa.afterservice.bigpro.service.ShouhouRiskNotificationService;
import com.jiuji.oa.afterservice.shouhou.vo.req.ShouhouRiskNotificationReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationDataRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationInfoRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationUrlRes;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Api(tags = "售后风险确认书")
@RestController
@RequestMapping("/api/shouhou/riskNotification")
public class ShouhouRiskNotificationController {
    @Resource
    private ShouhouRiskNotificationService shouhouRiskNotificationService;

    @GetMapping("/getRiskNotificationList/v1")
    public R<ShouhouRiskNotificationDataRes> getRiskNotificationList(Integer shouhouId) {
        return R.success(shouhouRiskNotificationService.getRiskNotificationList(shouhouId));
    }

    @PostMapping("/getRiskNotificationSignUrl/v1")
    public R<ShouhouRiskNotificationUrlRes> getRiskNotificationSignUrl(@RequestBody ShouhouRiskNotificationReq req) {
        return R.success(shouhouRiskNotificationService.getRiskNotificationSignUrl(req));
    }

    @PostMapping("/getShouhouRiskNotification/v1")
    public R<ShouhouRiskNotificationInfoRes> getShouhouProductInfo(@RequestBody ShouhouRiskNotificationReq req) {
        return R.success(shouhouRiskNotificationService.getShouhouRiskNotificationInfo(req));
    }

    @PostMapping("/pushRiskNotification/v1")
    public R<String> pushRiskNotification(@RequestBody ShouhouRiskNotificationReq req) {
        return shouhouRiskNotificationService.pushRiskNotification(req);
    }

    @PostMapping("/flunkWeiXiu/v1")
    public R<Boolean> flunkWeiXiu(@RequestBody ShouhouRiskNotificationReq req) {
        return R.success(shouhouRiskNotificationService.flunkWeiXiu(req));
    }

    @GetMapping("/delRiskNotification/v1")
    public R<String> delRiskNotification(@RequestParam(value = "id") Long id) {
        shouhouRiskNotificationService.delRiskNotification(id);
        return R.success("删除成功");
    }
}

