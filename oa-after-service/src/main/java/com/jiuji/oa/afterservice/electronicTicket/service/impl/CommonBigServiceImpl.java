package com.jiuji.oa.afterservice.electronicTicket.service.impl;

import com.ch999.common.util.utils.DateTimeUtils;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.cloud.oaapi.vo.response.OaUserCloudVO;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogNoticeBo;
import com.jiuji.oa.afterservice.bigpro.enums.BaoxiuStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouLogsService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.electronicTicket.entity.PiaoProductInfo;
import com.jiuji.oa.afterservice.electronicTicket.mapper.AfterElectronicTicketMapper;
import com.jiuji.oa.afterservice.electronicTicket.vo.*;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import javax.swing.text.html.HTMLEditorKit;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Optional;

/**
 * 大件订单公共服务
 * <AUTHOR>
 */
@Slf4j
@Component(value = "CommonBigServiceImpl")
public class CommonBigServiceImpl extends CommonElectronicTicketServiceImpl{

    @Resource
    private AreainfoService areainfoService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private ShouhouLogsService shouhouLogsService;
    @Resource
    private AfterElectronicTicketMapper ticketMapper;
    private static final String FORCE_WEB_PAGE_JUMP = "d=forcewebpagejump";
    /**
     * 大件日志添加
     * @param ticketLog
     */
    public void saveLog(TicketLog ticketLog) {
        ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
        shouhouLogNoticeBo.setNeedNotice(Boolean.FALSE);
        shouhouLogsService.addShouhouLog(ticketLog.getInUser(),ticketLog.getOrderId(), ShouHouLogTypeEnum.CLXX.getCode(),ticketLog.getMessage(),shouhouLogNoticeBo,Boolean.FALSE,ticketLog.getShowType(), ticketLog.getIsWeb());

    }
    /**
     * 获取大件接件信息
     * @param factoryRoute
     * @return
     */
    public Shouhou structureShouhou(FactoryRoute factoryRoute){
        Integer orderId = factoryRoute.getOrderId();
        Long userId = factoryRoute.getUserId();
        Shouhou shouhou = shouhouService.lambdaQuery().eq(Shouhou::getId, orderId).eq(Shouhou::getUserid,userId).eq(Shouhou::getXianshi, Boolean.TRUE).one();
        if(shouhou==null){
            throw new CustomizeException("订单查询为空");
        }

        return shouhou;
    }

    /**
     * 构建大件设备信息
     * @param shouhou
     * @return
     */
    public BigEquipment structureBigEquipment(Shouhou shouhou){
        BigEquipment bigEquipment = new BigEquipment();
        BeanUtils.copyProperties(shouhou,bigEquipment);
        Integer isBakData = bigEquipment.getIsBakData();
        //进行转换是否需要数据备份
        if(NumberConstant.ZERO.equals(isBakData)){
            bigEquipment.setIsBakDataValue("不需要");
        } else {
            bigEquipment.setIsBakDataValue("需要");
        }
        //保修进行数据转换
        Integer baoxiu = Optional.ofNullable(bigEquipment.getBaoxiu()).orElse(NumberConstant.ZERO);
        String message = BaoxiuStatusEnum.getMessageByCode(baoxiu);
        bigEquipment.setBaoxiuValue(message);
        return bigEquipment;
    }

    /**
     * 创建大件发票信息
     * @param detail
     * @return
     */
    public AfterElectronicTicketInvoiceInformation structureInvoice(Shouhou detail){
        AfterElectronicTicketInvoiceInformation shouHouDetailVO = new AfterElectronicTicketInvoiceInformation();
        Integer id = detail.getId();
        PiaoProductInfo piaoProductInfo = Optional.ofNullable(ticketMapper.selectpiaoProductInfo(id)).orElse(new PiaoProductInfo());
        Integer flag = piaoProductInfo.getFlag();
        Integer piaoStatus;
        if(NumberConstant.THREE.equals(flag) || NumberConstant.FOUR.equals(flag)){
            piaoStatus=NumberConstant.TWO;
        } else {
            piaoStatus=NumberConstant.ONE;
        }
        if (detail.getIsquji() && detail.getOfftime() != null) {
            String domainName = getDomainName(SysConfigConstant.M_URL);
            Integer piaoid = piaoProductInfo.getPiaoid();
            // 已申请开票
            if (piaoid != null && piaoid != 0) {
                String  link = domainName+"/member/invoice/myInvoice/" + piaoid + "";
                String buttonText;
                String status;
                if (NumberConstant.TWO.equals(piaoStatus)) {
                    status = "已开";
                    buttonText = "查看发票";
                } else {
                    status = "审核中";
                    buttonText = "发票进度";
                }
                shouHouDetailVO.setInvoiceLink(link);
                shouHouDetailVO.setInvoiceStatus(status);
                shouHouDetailVO.setInvoiceBtnText(buttonText);
            } else {
                Calendar now = Calendar.getInstance();
                LocalDateTime dateTime = detail.getOfftime().atZone(ZoneId.systemDefault()).toLocalDateTime();
                BigDecimal price = detail.getFeiyong();
                try {
                    // 判断是否可以开票, 已完成并且交易完成时间小于120天,交易金额大于0
                    if (detail.getModidate() != null && DateTimeUtils.getDaysBetween(DateUtil.localDateTime2Date(dateTime), now.getTime()) < 120
                            && price != null && BigDecimal.ZERO.compareTo(new BigDecimal(price.toString())) < 0) {
                        String link = domainName+"member/invoice/submitInvoice?subId=" + id + "&type=1&" + FORCE_WEB_PAGE_JUMP;
                        shouHouDetailVO.setInvoiceLink(link);
                        shouHouDetailVO.setInvoiceStatus("未开");
                        shouHouDetailVO.setInvoiceBtnText("申请开票");
                        // 标记为申请开票
                        shouHouDetailVO.setInvoiceApply(true);
                    }
                } catch (Exception e) {
                    log.error("error", e);
                }
            }
        }
        return shouHouDetailVO;
    }


    /**
     * 获取大件售后电子小票信息
     * @param factoryRoute
     * @return
     */
    public ElectronicTicketAfterShow structureBigElectronicTicket(FactoryRoute factoryRoute) {
        //获取大件接件信息
        Shouhou shouhou = structureShouhou(factoryRoute);
        //获取会员信息
        OaUserCloudVO memberBasicInfo = getMemberBasicInfo(shouhou.getUserid().intValue());
        //获取门店信息
        Areainfo areaInfo = areainfoService.getByIdSqlServer(shouhou.getAreaid());
        StructureTicketCondition structureTicketCondition = new StructureTicketCondition();
        structureTicketCondition.setShouhou(shouhou)
                .setAreaInfo(areaInfo)
                .setMemberBasicInfo(memberBasicInfo)
                .setIsMobile(Boolean.TRUE);
        ElectronicTicketAfterShow electronicTicketAfterShow = new ElectronicTicketAfterShow();
        //构造电子小票表头
        AfterElectronicTicketTitle afterElectronicTicketTitle = structureTicketTitle(structureTicketCondition);
        //构造电子小票基本信息
        AfterElectronicTicketBaseInfo afterElectronicTicketBaseInfo = structureTicketBaseInfo(structureTicketCondition);
        //构建电子小票设备信息
        BigEquipment bigEquipment = structureBigEquipment(shouhou);
        //构建电子小票发票信息
        AfterElectronicTicketInvoiceInformation afterElectronicTicketInvoiceInformation = structureInvoice(shouhou);
        //实体组装
        electronicTicketAfterShow.setTicketTitle(afterElectronicTicketTitle)
                .setBaseInfo(afterElectronicTicketBaseInfo)
                .setInvoiceInformation(afterElectronicTicketInvoiceInformation)
                .setEquipment(bigEquipment);
        return electronicTicketAfterShow;
    }
}
