package com.jiuji.oa.afterservice.smallpro.vo.req;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2019/11/13
 */
@Data
public class SmallproReturnGoodsReq extends SmallproListBaseReq implements Serializable {
    private static final long serialVersionUID = 79376732726032970L;
    @ApiModelProperty(value = "大于N小时未退换办理")
    private Integer check3dtimeHour;

    @ApiModelProperty(value = "大于N小时未转地区")
    private Integer toAreaTimeHour;

    @ApiModelProperty(value = "是否高级搜索：1是|0否")
    private Integer isSenior;
    /**
     *  shouhou_tuihuan kind为9的判断审核1 是否已审核
     */
    @ApiModelProperty(value = "换货审核状态")
    private Integer exchangeCheckState;

    @ApiModelProperty(value = "退货审核状态")
    private Integer returnCheckState;

    @ApiModelProperty(value = "导出excel限制最近三个月")
    private String startExcelTime;

    private Integer authorizeId;

    @ApiModelProperty(value = "大件换货标识位")
    private Integer mobileExchangeFlag;

    private Boolean isAuthPart;

    /**
     * SmallProServiceTypeEnum
     */
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /**
     * AbnormalTypeEnum
     * 小件是否异常发货状态 1：异常
     */
    @ApiModelProperty(value = "服务类型")
    private Integer abnormalType;


    private List<Integer> smallProIds;

    /**
     * 0- 限制地区
     * 1- 不限制地区
     * 不限制地区
     */

    private Boolean notLimitArea;

    /**
     * 查询条件为唯一标识去掉其他查询条件
     * @param req
     * @return
     */
    public SmallproReturnGoodsReq uniqueClearOtherVariable(SmallproReturnGoodsReq req) {
        //接件单，订单
        boolean uniqueFlag = Arrays.asList("id", "subId").contains(req.getKeyKind());
        if (uniqueFlag && StringUtils.isNotBlank(req.getKey())) {
            SmallproReturnGoodsReq newReq = new SmallproReturnGoodsReq();
            newReq.setKey(req.getKey());
            newReq.setKeyKind(req.getKeyKind());
            return newReq;
        }
        //查询完成，删除状态的数据
        if (req.getStatus() == null || Arrays.asList(1,2).contains(req.getStatus())) {
            if(ObjectUtil.defaultIfNull(req.getIsSenior(), 0) <=0){
                req.setStartTime(null);
                req.setEndTime(null);
            }
            if (req.getTimeKind() == null) {
                req.setStartTime(null);
                req.setEndTime(null);
                req.setTimeKind(1);
            }
            if (StrUtil.isBlank(req.getStartTime()) || StrUtil.isBlank(req.getEndTime())) {
                req.setStartTime(null);
                req.setEndTime(null);
            }
            //角色数据查询
            R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.AFTER_SALES)
                    .getStartTimeFun(() -> DateUtil.parseLocalDateTime(req.getStartTime(), DatePattern.NORM_DATETIME_PATTERN))
                    .getEndTimeFun(() -> DateUtil.parseLocalDateTime(req.getEndTime(),DatePattern.NORM_DATETIME_PATTERN))
                    .setStartTimeFun(startTime -> req.setStartTime(DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN)))
                    .setEndTimeFun(endTime -> req.setEndTime(DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN)))
                    .build(), null);
            if (!dataViewRes.isSuccess()) {
                throw new CustomizeException(dataViewRes.getUserMsg());
            }
            if(req.getStartTime() != null && req.getEndTime() != null && ObjectUtil.defaultIfNull(req.getIsSenior(), 0) <=0){
                req.setIsSenior(1);
            }
        }
        return req;
    }
}
