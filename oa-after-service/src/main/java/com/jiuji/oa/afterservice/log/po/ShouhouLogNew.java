package com.jiuji.oa.afterservice.log.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shouhou_log_new")
public class ShouhouLogNew extends Model<ShouhouLogNew> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;

    /**
     * 业务单号
     */
    private Long subId;

    /**
     * 日志备注信息
     */
    private String content;

    /**
     * 操作人
     */
    private String inUser;

    /**
     * 日志时间
     */
    private LocalDateTime dTime;

    /**
     * 类型 {@link ShouHouLogTypeEnum}
     * 维修进度 isWeb true
     * 处理信息  type not in(1,2) or type is null
     * 维修方案  type case when 软件单 then 2 else 1 end
     */
    private Integer type;

    /**
     * 1 前端后端都展示， 0 仅OA展示
     */
    private Boolean showType;


    public static final String LOG_ID = "log_id";

    public static final String SUB_ID = "sub_id";

    public static final String CONTENT = "content";

    public static final String IN_USER = "in_user";

    public static final String D_TIME = "d_time";

    public static final String TYPE = "type";

    public static final String SHOW_TYPE = "show_type";

    @Override
    protected Serializable pkVal() {
        return this.logId;
    }

}
