package com.jiuji.oa.afterservice.bigpro.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouTestAttrService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouTestResultInfoService;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouTestResultAddReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouTestResultInfoReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestAttrListRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultAddRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultRes;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Api(tags = "售后测试流程业务")
@RestController
@RequestMapping("/api/shouhou/shouhoutest")
public class ShouhouTestInfoController {
    @Resource
    private ShouhouTestAttrService shouhouTestAttrService;
    @Resource
    private ShouhouTestResultInfoService shouhouTestResultInfoService;

    @GetMapping("/getTestAttrList/v1")
    public R<ShouhouTestAttrListRes> getTestAttrList(@RequestParam(value = "type", defaultValue = "1") Integer type,
                                                     @RequestParam(value = "shouhouId", required = false) Integer shouhouId) {
        return R.success(shouhouTestAttrService.getTestAttrList(type, shouhouId));
    }

    @PostMapping("/saveTestResult/v1")
    public R<ShouhouTestResultAddRes> saveTestResult(@RequestBody @Validated ShouhouTestResultAddReq req) {
        return R.success(shouhouTestResultInfoService.saveTestResult(req));
    }

    @PostMapping("/getTestInfoList/v1")
    public R<Page<ShouhouTestResultInfoRes>> getTestInfoList(@RequestBody @Validated ShouhouTestResultInfoReq req) {
        return R.success(shouhouTestResultInfoService.getTestInfoList(req));
    }

    @GetMapping("/getTestInfoListByShouhouId/v1")
    public R<List<ShouhouTestResultInfoRes>> getTestInfoList(@RequestParam(value = "shouhouId") Integer shouhouId) {
        return R.success(shouhouTestResultInfoService.getTestInfoListByShouhouId(shouhouId));
    }

    @GetMapping("/getResultByTestId/v1")
    public R<ShouhouTestResultRes> getResultByTestId(@RequestParam(value = "shouhouTestInfoId") Integer shouhouTestInfoId) {
        return R.success(shouhouTestResultInfoService.getResultByTestId(shouhouTestInfoId));
    }
}
