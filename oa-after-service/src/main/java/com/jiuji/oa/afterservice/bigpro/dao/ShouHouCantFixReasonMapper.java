package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouHouCantFixReason;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ShouHouCantFixReasonMapper extends BaseMapper<ShouHouCantFixReason> {

    /**
     * 售后id查询修不好原因
     * @param shouHouIdList
     * @return
     */
    List<ShouHouCantFixReason> selectByShouHouIdList(@Param("shouHouIdList") List<Integer> shouHouIdList);
}
