package com.jiuji.oa.afterservice.other.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("basket_extend")
public class BasketExtend {


    @TableId(value = "basket_id", type = IdType.AUTO)
    private Integer basketId;
    @TableField(value = "price_kind")
    private Integer priceKind;
    @TableField(value = "original_price")
    private BigDecimal originalPrice;
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    @TableField(value = "bargain_basket_id")
    private Integer bargainBasketId;
    @TableField(value = "union_pro_ppid")
    private Integer unionProPpid;
    @TableField(value = "union_pro_imei")
    private String unionProImei;
    @TableField(value = "union_bsk_id")
    private Integer unionBskId;
    @TableField(value = "member_class")
    private Integer memberClass;
    @TableField(value = "buy_kinds")
    private Integer buyKinds;
    @TableField(value = "product_label")
    private Integer productLabel;
    @TableField(value = "is_promotion")
    private Integer isPromotion;
    @TableField(value = "third_platform_inPrice")
    private BigDecimal thirdPlatformInPrice;
    @TableField(value = "gourp_buy_key")
    private String gourpBuyKey;


}
