package com.jiuji.oa.afterservice.recover.mapstruct;

import com.jiuji.oa.afterservice.recover.po.RecoverPriceReviewConfig;
import com.jiuji.oa.afterservice.recover.vo.req.PriceReviewConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RecoverPriceReviewConfigMapStruct {

    RecoverPriceReviewConfig toRecoverPriceReviewConfig(PriceReviewConfigVO.PriceReviewConfigData priceReviewConfigData);

    List<PriceReviewConfigVO.PriceReviewConfigData> toPriceReviewConfigDataList(List<RecoverPriceReviewConfig> list);

    PriceReviewConfigVO.PriceReviewConfigData toPriceReviewConfigDataList(RecoverPriceReviewConfig list);


}
