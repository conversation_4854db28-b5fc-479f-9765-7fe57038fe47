package com.jiuji.oa.afterservice.cloud.vo.web;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员背景图
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
@Data
@Accessors(chain = true)
public class MemberClubBackground  {



    /**
     * 主键
     */

    private Integer id;

    /**
     * 会员等级
     */

    private Integer code;

    /**
     * 等级名称
     */


    private String name;

    /**
     * 会员图标
     */


    private String memberImg;

    /**
     * 查看全部按钮图标
     */

    private String allBtnImg;

    /**
     * 会员俱乐部背景图
     */


    private String backgroundImg;

    /**
     * 会员俱乐部背景上的图标 双钻会员该字段用作双钻一星
     */


    private String bgIconImg;

    // 2星
    private String bgIconImgV2;

    // 3星
    private String bgIconImgV3;

    // 4星
    private String bgIconImgV4;

    // 5星
    private String bgIconImgV5;

    // 10星
    private String bgIconImgV10;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识：0-未删除，1-删除
     */
    private Integer delFlag;





}
