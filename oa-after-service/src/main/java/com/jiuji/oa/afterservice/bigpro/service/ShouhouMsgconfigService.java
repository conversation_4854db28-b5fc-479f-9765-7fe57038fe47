package com.jiuji.oa.afterservice.bigpro.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouMsgconfig;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-11
 */
public interface ShouhouMsgconfigService extends IService<ShouhouMsgconfig> {
    /**
     * 获取售后信息配置
     * @return
     */
    @Cached(name = "getShouhouMsgconfigListCache",expire = 1,timeUnit = TimeUnit.HOURS)
    List<ShouhouMsgconfig> getShouhouMsgconfigListCache();

    ShouhouMsgconfig getShouhouMsgconfigById(Integer id);


    /**
     *发送取机通知（获取消息列表）
     * @param shouhouId
     * @param logType
     * @param key
     */
    List<ShouhouMsgconfig> getMessageTplList(Integer shouhouId, Integer logType, String key);

    ShouhouMsgconfig getShouhouMsgconfigByLogType(Integer code);
}
