package com.jiuji.oa.afterservice.bigpro.vo.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/4/30
 */
@Data
public class TuihuanConfigRes {
    private Integer id;
    private Integer xtenant;
    private Integer actionName;
    /**
     *  类别 1、新机 2、优品 3、良品
     */
    private Integer kinds;
    private String kindsName;
    private Integer sDay;
    private Integer eDay;
    /**
     * 故障选项
     */
    private String faultOption;

    private List<String> faultOptionArr;

    /**
     * 处理选项
     */
    private String dealOption;
    private  List<String> dealOptionArr;
    private String dealOptionName;
    private String dealOptionNameArr;
    private String checkOption;
    private List<String> checkOptionArr;
    private BigDecimal prices;
}
