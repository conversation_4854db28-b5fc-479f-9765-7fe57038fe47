package com.jiuji.oa.afterservice.statistics.vo.res;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * description: < 毛利分析品牌分类VO >
 * translation: <  >
 * date : 2020-10-29 10:46
 *
 * <AUTHOR> leee41
 **/
@ApiModel(value = "毛利分析品牌分类VO")
@Data
public class RepairProfitBrandVO implements Serializable {
    private static final long serialVersionUID = -7248888590410780219L;
    private Integer id;
    private String brand;
}
