package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMsgrecordMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouMsgrecord;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouMsgrecordService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-11
 */
@Service
public class ShouhouMsgrecordServiceImpl extends ServiceImpl<ShouhouMsgrecordMapper, ShouhouMsgrecord> implements ShouhouMsgrecordService {

    @Override
    public Boolean saveShouhouMsgRecord(Integer shouhouId, Integer msgId, LocalDateTime pushtime, Integer timeout,
                                        String inuser, String msgContent) {
        ShouhouMsgrecord shouhouMsgrecord = new ShouhouMsgrecord();
        shouhouMsgrecord.setShouhouid(shouhouId);
        shouhouMsgrecord.setMsgid(msgId);
        shouhouMsgrecord.setPushtime(pushtime);
        shouhouMsgrecord.setTimeout(timeout);
        shouhouMsgrecord.setInuser(inuser);
        shouhouMsgrecord.setMsgcongent(msgContent);
        return this.save(shouhouMsgrecord);
    }
}
