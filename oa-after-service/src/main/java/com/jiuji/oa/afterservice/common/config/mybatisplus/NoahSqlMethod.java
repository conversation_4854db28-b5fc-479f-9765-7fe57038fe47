package com.jiuji.oa.afterservice.common.config.mybatisplus;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @since 2020/3/21
 */
@Getter
@AllArgsConstructor
public enum NoahSqlMethod {
    /**
     * 插入
     */
    INSERT_BATCH("insertBatch", "插入一条数据（选择字段插入）", "<script>\nINSERT INTO %s %s VALUES %s\n</script>"),

    ;


    private final String method;
    private final String desc;
    private final String sql;
}
