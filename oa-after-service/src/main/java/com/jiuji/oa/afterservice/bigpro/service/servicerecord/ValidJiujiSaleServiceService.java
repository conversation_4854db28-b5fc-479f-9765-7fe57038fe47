package com.jiuji.oa.afterservice.bigpro.service.servicerecord;

import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceReqVo;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceResVo;
import com.jiuji.tc.common.vo.R;

/**
 * 验证九机盾服务类
 * <AUTHOR>
 * @since 2022/2/18 14:22
 */
public interface ValidJiujiSaleServiceService {
    /**
     * 获取验证销售服务验证结果
     * @param saleJiujiServiceReq
     * @return
     */
    R<ValidSaleJiujiServiceResVo> getValidSaleService(ValidSaleJiujiServiceReqVo saleJiujiServiceReq);
}
