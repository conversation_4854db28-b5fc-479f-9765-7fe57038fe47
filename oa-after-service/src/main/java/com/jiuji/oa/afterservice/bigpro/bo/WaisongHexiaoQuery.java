package com.jiuji.oa.afterservice.bigpro.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.bigpro.po.WaisongHexiaoPo;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.common.vo.PageReq;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 外送报账查询实体
 * <AUTHOR>
 * @description
 * @since 2021/5/25 10:41
 */
@Data
@ApiModel("外送报账查询实体")
@EqualsAndHashCode(callSuper = false)
public class WaisongHexiaoQuery extends PageReq {
    /**检索类型 维修单号 机型 配件名称 支出单号 物流单*/
    @ApiModelProperty("检索类型 0 维修单号 1 机型 2 配件名称 3 支出单号 4 物流单")
    private Integer searchType;
    /**检索文本*/
    @ApiModelProperty("检索文本")
    private String searchText;
    /**区域id*/
    @ApiModelProperty("区域id")
    private List<Integer> areaIds;
    /**处理状态 0 待审核 1 已打款 2 待财务审核 3 已结算 4 业务驳回 5 财务驳回 6 删除*/
    @ApiModelProperty("处理状态 0 待审核 1 已打款 2 待财务审核 3 已结算 4 业务驳回 5 财务驳回 6 删除")
    private Integer status;
    /**是否回收票据*/
    @ApiModelProperty("回收票据状态 0 未回收 1 已回收")
    private Integer recoverBillStatus;
    /**时间范围类型*/
    @ApiModelProperty("时间类型 1 提交时间 2 打款时间 3 结算时间")
    private Integer timeType;
    /**添加时间范围*/
    @Size(max = 2)
    @ApiModelProperty("时间范围")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] timeRange;
    @ApiModelProperty(value = "租户id",hidden = true)
    private Integer xtenant;
    /**是否授权隔离*/
    @ApiModelProperty(value = "授权隔离",hidden = true)
    private boolean authPart;
    @ApiModelProperty(value = "当前门店id",hidden = true)
    private Integer currAreaId;
    @ApiModelProperty(value = "授权id",hidden = true)
    private Integer authorizeId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    public void setTimeRange(LocalDateTime[] timeRange) {
        if(timeRange != null){
            timeRange = Arrays.copyOf(timeRange,timeRange.length);
        }
        this.timeRange = timeRange;
    }

    /**
     * 查询条件为唯一标识去掉其他查询条件
     * @param req
     * @return
     */
    public WaisongHexiaoQuery uniqueClearOtherVariable(WaisongHexiaoQuery req) {
        boolean uniqueFlag = Arrays.asList(SearchTypeEnum.REPAIR_ORDER.getCode(),SearchTypeEnum.LOGISTICS_ORDRE.getCode(),SearchTypeEnum.PAY_ORDER.getCode()).contains(req.getSearchType());
        //备用机编号，维修单号查询
        if (uniqueFlag && StringUtils.isNotBlank(req.getSearchText())) {
            WaisongHexiaoQuery newReq = new WaisongHexiaoQuery();
            newReq.setSize(req.getSize());
            newReq.setCurrent(req.getCurrent());
            newReq.setSearchType(req.getSearchType());
            newReq.setSearchText(req.getSearchText());
            return newReq;
        }
        //角色数据查询
        if (req.getStatus() == null
                || Arrays.asList(WaisongHexiaoPo.StatusEnum.FINISHED.getCode(), WaisongHexiaoPo.StatusEnum.DELETED.getCode()).contains(req.getStatus())) {
            if (req.getTimeRange() != null && req.getTimeRange().length == 2) {
                req.setStartTime(req.getTimeRange()[0]);
                req.setEndTime(req.getTimeRange()[1]);
            }
            R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.AFTER_SALES)
                    .getStartTimeFun(req::getStartTime)
                    .getEndTimeFun(req::getEndTime)
                    .setStartTimeFun(req::setStartTime)
                    .setEndTimeFun(req::setEndTime)
                    .build(), null);
            if (!dataViewRes.isSuccess()) {
                throw new CustomizeException(dataViewRes.getUserMsg());
            }
            if ((req.getTimeRange() == null || req.getTimeRange().length != 2) && req.getStartTime() != null && req.getEndTime() != null) {
                req.setTimeRange(new LocalDateTime[]{req.getStartTime(),req.getEndTime()});
            }
        }
        return req;
    }


    /**检索类型 0 维修单号 1 机型 2 配件名称 3 支出单号 4 物流单*/
    @Getter
    @AllArgsConstructor
    public enum SearchTypeEnum implements CodeMessageEnumInterface {
        REPAIR_ORDER(0, "维修单号"),
        MACHINE_MODEL(1, "机型"),
        FITTING_NAME(2, "配件名称"),
        PAY_ORDER(3, "支出单号"),
        LOGISTICS_ORDRE(4, "物流单"),
        ;

        /**
         * 编码
         */
        private Integer code;
        /**
         * 名称
         */
        private String message;

    }

    /**时间类型 1 提交时间 2 打款时间 3 结算时间*/
    @Getter
    @AllArgsConstructor
    public enum TimeTypeEnum implements CodeMessageEnumInterface {
        SUBMIT_TIME(1, "提交时间"),
        PAY_TIME(2, "打款时间"),
        ACCOUNT_TIME(3, "结算时间"),
        ;

        /**
         * 编码
         */
        private Integer code;
        /**
         * 名称
         */
        private String message;

    }
}
