package com.jiuji.oa.afterservice.smallpro.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
/**
 * description: <小件可接件结果Res>
 * translation: <Smallpro can be connected results Res>
 *
 * <AUTHOR>
 * @date 2019/12/2
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproBasketRes implements Serializable {

    private List<SmallproBasket> smallproBasketList;

    @Data
    public static class SmallproBasket {
        /**
         * 订单详情Id
         */
        @ApiModelProperty(value = "订单明细号")
        private Integer basketId;

        @ApiModelProperty(value = "订单号")
        private Long subId;

        private Integer mobileExchangeFlag = 0;



        @ApiModelProperty(value = "skuid")
        private Long ppriceid;


        @ApiModelProperty(value = "商品名称")
        private String productName;

        @ApiModelProperty(value = "商品规格")
        private String productColor;

        /**
         * 成本
         */
        private BigDecimal inprice;

        /**
         * 数量
         */
        private Integer count;

        /**
         * 退款金额
         */
        @ApiModelProperty(value = "退款金额")
        private BigDecimal returnPrice;
    }

}
