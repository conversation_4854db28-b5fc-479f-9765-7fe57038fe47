package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * description: <Owen状态枚举类>
 *
 * <AUTHOR>
 * @date 2024-07-30
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum OwenStatsEnum implements CodeMessageEnumInterface {

    /**
     * Owen状态 - 编码-编码信息
     */
    NORMAL(0, "正常"),
    PROCESSING(1, "处理中"),
    COMPLETED(2, "已完成"),
    CANCELLED(3, "已取消");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举对象
     */
    public static OwenStatsEnum valueOfByCode(Integer code){
        for (OwenStatsEnum temp : OwenStatsEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述信息
     * @param code 编码
     * @return 描述信息
     */
    public static String getMessageByCode(Integer code){
        for (OwenStatsEnum temp : OwenStatsEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp.getMessage();
            }
        }
        return "";
    }
}
