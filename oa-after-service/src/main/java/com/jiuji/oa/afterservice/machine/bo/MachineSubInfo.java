package com.jiuji.oa.afterservice.machine.bo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 订单信息
 * <AUTHOR>
 * @since 2021/8/16 13:40
 */
@NoArgsConstructor
@Getter
@Setter
@ToString
public class MachineSubInfo {
    /**
     * 订单号
     */
    private Integer subId;
    /**
     * basketId
     */
    private Integer basketId;
    /**
     * 商品ppid
     */
    private Integer ppid;
    /**
     * 会员id
     */
    private Integer userId;
}
