package com.jiuji.oa.afterservice.batchreturn.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @since 2020/3/13
 */
@Data
@Accessors(chain = true)
@TableName("batch_return_mkc")
public class BatchReturnMkc extends Model<BatchReturnMkc> {
    private static final long serialVersionUID = -6729414332678331410L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer batchReturnId;

    private Integer mkcId;
    private Integer basketId;

    private Integer ppid;

    private BigDecimal originalPrice;
    private BigDecimal price;

    private String imei;

    private Integer toMkcId;

    private Long voucherId;

    private LocalDateTime toMkcTime;
}

