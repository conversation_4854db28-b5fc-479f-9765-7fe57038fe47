package com.jiuji.oa.afterservice.bigpro.service.newAttachments.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jiuji.oa.afterservice.bigpro.dao.AttachmentsMapper;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.service.newAttachments.AttachmentRepository;
import com.jiuji.oa.afterservice.bigpro.service.newAttachments.HistoryFileService;
import com.jiuji.oa.afterservice.bigpro.service.newAttachments.req.UpdateHistoryFileReq;
import com.jiuji.oa.afterservice.bigpro.service.newAttachments.res.UpdateHistoryFileRes;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2024/9/14 11:31
 * @Description 实时附件仓库实现
 */
@Slf4j
@Component
public class RealTimeAttachmentRepository implements AttachmentRepository {
    private final AttachmentsMapper baseMapper;

    public RealTimeAttachmentRepository(AttachmentsMapper baseMapper) {
        this.baseMapper = baseMapper;
    }

    @Override
    public List<Attachments> newSelectList(Wrapper<Attachments> queryWrapper) {
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<Attachments> getAttachmentList(Integer linkId, Integer type, Integer kind) {
        return baseMapper.getAttachmentList(linkId, type, kind);
    }

    @Override
    public int newDeleteBatchIds(List<Integer> idList, List<Attachments> dbAttachments) {
        if (CollUtil.isEmpty(idList) || CollUtil.isEmpty(dbAttachments)) {
            return 0;
        }
        // 存储历史库附件ID
        List<Integer> isHistoryIdList = new ArrayList<>();
        // 存储实时附件ID
        Collection<Integer> isRealTimeIdList;

        for (Attachments dbAttachment : dbAttachments) {
            // 如果是历史库附件且在要删除的ID列表中，则添加到历史库ID列表
            if (dbAttachment.getIsHistory() && idList.contains(dbAttachment.getId())) {
                isHistoryIdList.add(dbAttachment.getId());
            }
        }
        isRealTimeIdList = CollUtil.subtract(idList, isHistoryIdList);

        Integer quantity = 0;
        // 如果有历史库附件ID，则执行批量删除
        if (CollUtil.isNotEmpty(isHistoryIdList)) {
            HistoryFileService historyFileService = SpringUtil.getBean(HistoryFileService.class);
            List<String> strings = historyFileService.delHistoryFile(isHistoryIdList);
            quantity += strings.size();
        }
        // 如果有实时附件ID，则执行批量删除
        if (CollUtil.isNotEmpty(isRealTimeIdList)) {
            int i = baseMapper.deleteBatchIds(isRealTimeIdList);
            quantity += i;
        }
        return quantity;
    }

    @Override
    public int newUpdateById(Attachments attachments) {
        if (attachments.getIsHistory()) {
            UpdateHistoryFileReq updateHistoryFileReq = new UpdateHistoryFileReq();
            updateHistoryFileReq.setId(attachments.getId());
            updateHistoryFileReq.setFileName(attachments.getFilename());
            HistoryFileService historyFileService = SpringUtil.getBean(HistoryFileService.class);
            List<UpdateHistoryFileRes> updateHistoryFileRes = historyFileService.updateHistoryFile(Collections.singletonList(updateHistoryFileReq));
            return updateHistoryFileRes.size();
        } else {
            return baseMapper.updateById(attachments);
        }
    }

}

