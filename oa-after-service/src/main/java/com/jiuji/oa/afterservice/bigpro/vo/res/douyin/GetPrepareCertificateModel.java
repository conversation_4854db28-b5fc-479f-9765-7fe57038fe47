package com.jiuji.oa.afterservice.bigpro.vo.res.douyin;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class GetPrepareCertificateModel {
    private GetPrepareCertificateDataModel data;

    @Data
    public static class GetPrepareCertificateDataModel {
        @JSONField(name = "error_code")
        private Integer errorCode;

        private String description;

        @JSONField(name = "verify_token")
        private String verifyToken;

        @JSONField(name = "order_id")
        private String orderId;

        private List<GetPrepareCertificateDetailModel> certificates;
    }

    @Data
    public static class GetPrepareCertificateDetailModel {
        @JSONField(name = "encrypted_code")
        private String encryptedCode;

        @JSONField(name = "expire_time")
        private Long expireTime;

        private GetPrepareCertificateDataAmountModel amount;

        private GetPrepareCertificateDataSkuModel sku;
    }


    @Data
    public static class GetPrepareCertificateDataAmountModel {
        @JSO<PERSON>ield(name = "original_amount")
        private BigDecimal originalAmount;

        @JSO<PERSON>ield(name = "pay_amount")
        private BigDecimal payAmount;

        @JSONField(name = "merchant_ticket_amount")
        private BigDecimal merchantTicketAmount;

        @JSONField(name = "payment_discount_amount")
        private BigDecimal paymentDiscountAmount;

        @JSONField(name = "coupon_pay_amount")
        private BigDecimal couponPayAmount;
    }


    @Data
    public static class GetPrepareCertificateDataSkuModel {
        private String title;

        @JSONField(name = "account_id")
        private String accountId;

        @JSONField(name = "market_price")
        private BigDecimal marketPrice;

        @JSONField(name = "sku_id")
        private String skuId;

        @JSONField(name = "sold_start_time")
        private Long soldStartTime;

        @JSONField(name = "third_sku_id")
        private String thirdSkuId;
    }
}
