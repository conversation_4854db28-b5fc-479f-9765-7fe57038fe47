package com.jiuji.oa.afterservice.bigpro.bo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/4/20
 */
@Data
public class SfOrderInfoBo {
    private  OrderInfo orderInfo;
    @Data
    public static class OrderInfo{

        //寄件公司
        private String j_company;

        // 寄件联系人
        private String j_contact;

        // 寄件人联系电话
        private String j_tel;


        // 寄件人手机
        private String j_mobile;


        // 寄件人省
        private String j_province;

        // 寄件人 市
        private String j_city;

        // 寄件人 区/县
        private String j_county;

        // 寄件人详细地址
        private String j_address;

        // 收件人公司
        private String d_company;

        // 收件联系人
        private String d_contact;

        // 收件联系电话
        private String d_tel;

        // 收件方手机号码
        private String d_mobile;

        // 收件人 省份
        private String d_province;


        // 收件人 市
        private String d_city;


        // 收件人 区/县
        private String d_county;


        // 收件人 详细地址
        private String d_address;

        // 订单号(sub_id)
        private String orderid;

        // 业务类型
        private String express_type;

        // 支付类型
        private Integer pay_method;

        // 包裹数
        private Integer parcel_quantity;

        // 月结卡号
        private String custid;

        // 快件总重量
        private String cargo_total_weight;

        // 发货时间(取件时间)
        private String sendstarttime;

        // 订单来源
        private String order_source;

        // 订单备注
        private String remark;

        // 快递公司原寄地编码
        private String orgcode;

        // 快递公司目的地编码
        private String destcode;

        //第三方支付方式 1:寄付月结 2:收件方付 3:第三方付
        private String paytype;


        // 第三方付 月结卡号
        private String yuejiekahao;


        // 快递单号
        private String nu;


        //  在线下单的，docall  为1 ，巴枪就能收到提醒 (巴枪是顺风小哥收件时候拿着的那个东西)  一般HHTwaydil未1，这个参数也给1
        private Integer docall;

        // HHTwaydil是小哥上门收件了，能打单的功能
        private Integer HHTwaydil;

        private String destRouteLabel;

        private String twoDimensionCode;

        private List<SfCargo> list;

        // 物流单类型
        private String WuliuType;

        private boolean docallFlag;

        public OrderInfo() {
            list = new ArrayList<SfCargo>();
        }
    }


    @Data
    public static class SfCargo {

        //商品名称
        private String CargoName;

        //数量
        private String count;

        //单位
        private String unit;

        //单重
        private String weight;

        //单价
        private String amount;

        /**
         * 结算货币（国际件用）
         */
        private String currency;

        /**
         * 原产地(国际件用)
         */
        private String source_area;
    }

}
