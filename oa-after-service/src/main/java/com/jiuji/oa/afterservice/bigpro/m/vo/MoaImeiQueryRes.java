package com.jiuji.oa.afterservice.bigpro.m.vo;

import com.jiuji.oa.afterservice.bigpro.bo.ImeiSearchLogBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxRecordBo;
import com.jiuji.oa.afterservice.bigpro.m.bo.ProductKcStatusInfo;
import com.jiuji.oa.afterservice.bigpro.m.bo.RepairRecords;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel
public class MoaImeiQueryRes {
    @ApiModelProperty(value = "串号查询结果分类  1、外修串号查询成功  2、外修查询失败  3、九机购买  4库存机器")
    private Integer resultType;

    @ApiModelProperty(value = "是否是库存机器")
    private Boolean allowSoftSave = Boolean.FALSE;

    @ApiModelProperty(value = "提示文案")
    private String tips;

    @ApiModelProperty(value = "串号查询记录")
    private List<ImeiSearchLogBo> imeiSearchLogs;

    @ApiModelProperty(value = "库存状态信息")
    private ProductKcStatusInfo productKcStatusInfo;

    @ApiModelProperty(value = "维修记录")
    private List<RepairRecords> repairRecords;

    @ApiModelProperty(value = "其他业务")
    private List<OtherBusiness> otherBusiness;

    @ApiModelProperty(value = "外修查询结果")
    private WxProductColorInfo productColorInfo;

//    @ApiModelProperty(value = "自修查询结果")
//    private List<ImeiQueryRes> memberProductInfo;

    @ApiModelProperty(value = "自修查询结果")
    private ServiceInfoAndSubInfo memberProductInfo;

    @ApiModelProperty(value = "是否允许软件接件")
    private Boolean isSoftwareOrder = Boolean.FALSE;

    @Data
    @Accessors(chain = true)
    @ApiModel
    public static class OtherBusiness{

        @ApiModelProperty(value = "项目名称")
        private String name;

        @ApiModelProperty(value = "参考价")
        private BigDecimal referencePrice;

        @ApiModelProperty(value = "回收跳转链接")
        private String link;

        @ApiModelProperty(value = "是否有效")
        private String filmValid;

    }

    @Data
    @Accessors(chain = true)
    @ApiModel
    public static class WxProductColorInfo{
        @ApiModelProperty(value = "商品名称")
        private String productName;

        @ApiModelProperty(value = "商品规格")
        private List<ProductColorItem> productColors;

        @Data
        @Accessors(chain = true)
        @ApiModel
        public static class ProductColorItem{

            @ApiModelProperty(value = "ppid")
            private Integer ppid;

            @ApiModelProperty(value = "规格名称")
            private String productColor;
        }
    }


}
