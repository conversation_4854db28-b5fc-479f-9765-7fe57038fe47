package com.jiuji.oa.afterservice.sys.vo.req;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeV1Enum;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 12:13
 * @Description
 */
@Data
@Accessors(chain = true)
public class ValidReq<T> implements BaseValid<T>,Serializable {
    @ApiModelProperty(value = "订单号(该订单是验证场景订单)")
    private Long orderId;
    /**
     * @see com.jiuji.oa.afterservice.sys.enums.ValidMemberType
     */
    @ApiModelProperty("验证类型 1会员识别码 2支付密码验证 3短信验证 4 授权验证")
    private Integer validType;
    /**
     * 灵活的参数使支持更多的场景
     */
    private JSONObject json;
    /**
     * @see BusinessTypeV1Enum
     */
    @ApiModelProperty(value = "校验场景类型 枚举类：BusinessTypeV1Enum 未知 0 维修取机操作校验 1 维修费用退款操作校验 2 小件退货操作校验 3 小件换货操作校验 4 退款退订操作校验 5")
    private Integer businessType;
    /**
     * 验证码
     */
    @ApiModelProperty("验证码")
    private String validCode;

    /**
     * 内部流转字段 start
     */
    @ApiModelProperty(value = "验证类型 1会员识别码 2支付密码验证 3短信验证",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private ValidMemberType validTypeEnum;
    @ApiModelProperty(value = "校验场景类型 枚举类：BusinessTypeV1Enum",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private BusinessTypeV1Enum businessTypeEnum;
    /**
     * 灵活的参数使支持更多的场景
     */
    @JSONField(serialize = false,deserialize = false)
    @ApiModelProperty(value = "灵活的参数使支持更多的场景",hidden = true)
    private T t;
    /**
     * 内部流转字段 end
     */
}
