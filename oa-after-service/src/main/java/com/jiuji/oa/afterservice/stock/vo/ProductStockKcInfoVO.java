package com.jiuji.oa.afterservice.stock.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 商品下所有库位库存信息
 * @Author: xiaojianbing
 * @Date 2020/9/25
 */
@Data
@Accessors(chain = true)
public class ProductStockKcInfoVO implements Serializable {

    private static final long serialVersionUID = 4845438658554179165L;
    private Integer ppid;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品颜色
     */
    private String productColor;

    /**
     * 商品条码
     */
    private String barCode;
    /**
     * 库位
     */
    private String locationCode;
    /**
     * 库位库存
     */
    private Integer stockCount;
}
