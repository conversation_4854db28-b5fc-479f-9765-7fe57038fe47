package com.jiuji.oa.afterservice.smallpro.vo.res;

import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproInfoUpdateReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * description: <小件接件详情更新Res>
 * translation: <Smallpro details update Res>
 *
 * <AUTHOR>
 * @date 2019/11/22
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproInfoUpdateRes implements Serializable {

    private static final long serialVersionUID = -989073799811362545L;
    /**
     * 小件接件Id
     */
    @ApiModelProperty(value = "小件接件Id")
    private Integer smallproId;
    /**
     * 保修状态
     */
    @ApiModelProperty(value = "保修状态[1保修|0非保修]")
    private Integer warrantyStatus;
    /**
     * 配置
     */
    @ApiModelProperty(value = "配置")
    private String config;
    /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String problem;
    /**
     * 九机服务
     */
    @ApiModelProperty(value = "九机服务")
    private Byte serviceType;
    /**
     * 处理方式
     */
    @ApiModelProperty(value = "处理方式")
    private Integer kind;
    /**
     * 维修状态
     */
    @ApiModelProperty(value = "维修状态")
    private Integer stats;
    /**
     * 换货验证码
     */
    @ApiModelProperty(value = "换货验证码|退货验证码")
    private String codeMsg;
    /**
     * 处理分组
     */
    @ApiModelProperty(value = "处理分组")
    private Integer group;
    /**
     * 接件备注
     */
    @ApiModelProperty(value = "接件备注")
    private String comment;
    /**
     * 维修费用,old=wxPrice
     */
    @ApiModelProperty(value = "维修费用,old=wxPrice")
    private Double maintainPrice;
    /**
     * 维修渠道,old=wxChannel
     */
    @ApiModelProperty(value = "维修渠道,old=wxChannel")
    private String maintainChannel;
    /**
     * 数据解绑
     */
    @ApiModelProperty(value = "数据解绑")
    private Integer dataRelease;
    /**
     * 外观描述
     */
    @ApiModelProperty(value = "外观描述")
    private String outward;
    /**
     * 外观描述标志
     */
    @ApiModelProperty(value = "外观描述标志")
    private Integer outwardFlag;
    /**
     * 置换商品Ppid
     */
    @ApiModelProperty(value = "置换商品Ppid")
    private Integer changePpriceId;

    /**
     * description: <通过请求参数构建返回结果>
     * translation: <Return results by request parameter construction>
     *
     * @param req        请求Body
     * @param smallproId 小件Id
     * <AUTHOR>
     * @date 18:26 2019/11/25
     * @since 1.0.0
     **/
    public SmallproInfoUpdateRes(SmallproInfoUpdateReq req, Integer smallproId) {
        this.comment = req.getComment();
        this.config = req.getConfig();
        this.dataRelease = req.getDataRelease();
        this.group = req.getGroup();
        this.kind = req.getKind();
        this.maintainPrice = Optional.ofNullable(req.getMaintainPrice()).map(BigDecimal::doubleValue).orElse(null);
        this.outward = req.getOutward();
        this.outwardFlag = req.getOutwardFlag();
        this.problem = req.getProblem();
        this.serviceType = req.getServiceType();
        this.smallproId = smallproId;
        this.stats = req.getStats();
        this.warrantyStatus = req.getWarrantyStatus();
        this.changePpriceId=req.getChangePpriceId();
    }
}
