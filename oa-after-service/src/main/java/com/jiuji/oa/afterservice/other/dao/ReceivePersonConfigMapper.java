package com.jiuji.oa.afterservice.other.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO;
import com.jiuji.oa.afterservice.other.po.ReceivePersonConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@Mapper
public interface ReceivePersonConfigMapper extends BaseMapper<ReceivePersonConfig> {

    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.other.po.ReceivePersonConfig>
     * <AUTHOR>
     * @date 1:02 2020/4/13
     * @since 1.0.0
     **/
    @Select("select * from ReceivePersonConfig with(nolock) ${ew.customSqlSegment}")
    List<ReceivePersonConfig> listSqlServer(@Param(Constants.WRAPPER) Wrapper wrapper);


    /**
     * description: <获取门店最高接收人>
     * translation: <Get the highest recipient in the store>
     *
     * @param areaId 门店Id
     * @return java.util.List<com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO>
     * <AUTHOR>
     * @date 1:51 2020/4/13
     * @since 1.0.0
     **/
    List<LogisticsRecipientBO> getNormalReceiver(@Param("areaId") Integer areaId);


    /**
     * description: 获取店面 店长以及主管
     * translation: <Get the highest recipient in the store>
     *
     * @param areaId 门店Id
     * @return java.util.List<com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO>
     * <AUTHOR>
     * @date 1:51 2020/4/13
     * @since 1.0.0
     **/
    List<LogisticsRecipientBO> getExecutiveDirectorAndShopowner(@Param("areaId") Integer areaId);

    /**
     * 查询对应门店对应小区的"分公司售后主管/售后主管"
     * @param areaId
     * @return
     */
    List<String> getAfterSalesSupervisor(@Param("areaId") Integer areaId);

    /**
     * 查询售后经理
     * @param areaId
     * @return
     */
    List<String> getAfterSalesManager(@Param("areaId") Integer areaId);

    /**
     * 通过名字获取ch999_id
     * @param name
     * @return
     */
    String getCh999IdByName(@Param("name") String name);


    /**
     * 根据门店地区和角色来查询大区角色
     * @param areaId
     * @param mainRole
     * @return
     */
    List<String> selectRegionByAreaIdAndMainRole(@Param("areaId") Integer areaId,@Param("mainRole") Integer mainRole);



}
