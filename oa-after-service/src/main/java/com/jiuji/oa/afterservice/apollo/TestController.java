package com.jiuji.oa.afterservice.apollo;

import com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl.CommonStrategy;
import com.jiuji.oa.afterservice.delayQueuePush.vo.TimeInfo;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("api/apollo")
public class TestController {

    @Resource
    private ApolloEntity apolloEntity;

    @GetMapping("/test/v1")
    public R<String> getReturnInfo() {
        TimeInfo timeInfo = CommonStrategy.getTimeInfo();
        return R.success(timeInfo.toString());
    }


}
