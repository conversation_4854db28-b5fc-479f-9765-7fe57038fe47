package com.jiuji.oa.afterservice.common.source;

import com.jiuji.tc.foundation.message.MessageService;
import com.jiuji.tc.foundation.message.MessageType;

/**
 * description: <tool.ch999.cn跳转链接>
 * translation: <tool.ch999.cn jump link>
 *
 * <AUTHOR>
 * @date 2019/12/3
 * @since 1.0.0
 */
@MessageService(value = "toolurl", type = MessageType.URL)
public interface ToolUrlSource {
    /**
     * description: <获取线上打印机客户端>
     * translation: <Get Online Printer Client>
     *
     * @param area 地区Id
     * @return java.lang.String
     * <AUTHOR>
     * @date 11:08 2019/12/3
     * @since 1.0.0
     **/
    String getPrintClient(String area);
}
