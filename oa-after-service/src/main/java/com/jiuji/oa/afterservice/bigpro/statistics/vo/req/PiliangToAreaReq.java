package com.jiuji.oa.afterservice.bigpro.statistics.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 批量转地区提交请求参数
 * @author: <PERSON> quan
 * @date: 2020/8/31 9:40
 */
@Data
@ApiModel
public class PiliangToAreaReq {

    @ApiModelProperty(value = "售后id")
    private List<Integer> ids;

    @ApiModelProperty(value = "门店")
    private Integer areaId;

    @ApiModelProperty(value = "转向门店id")
    private Integer toAreaId;

    @ApiModelProperty(value = "操作类型（1提交，2接收）")
    private Integer operateType;

    private String user;

    private Boolean iswuliu;
}
