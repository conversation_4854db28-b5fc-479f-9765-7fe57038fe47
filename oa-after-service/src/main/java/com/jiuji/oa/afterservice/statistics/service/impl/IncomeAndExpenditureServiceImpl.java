package com.jiuji.oa.afterservice.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.WorkbookUtil;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.common.vo.res.CommonDataRes;
import com.jiuji.oa.afterservice.other.dao.ShouyingMapper;
import com.jiuji.oa.afterservice.statistics.enums.IncomeAndExpenditureEnum;
import com.jiuji.oa.afterservice.statistics.service.IncomeAndExpenditureService;
import com.jiuji.oa.afterservice.statistics.vo.req.IncomeAndExpenditureReq;
import com.jiuji.oa.afterservice.statistics.vo.res.IncomeAndExpenditureRes;
import com.jiuji.oa.afterservice.statistics.vo.res.IncomeAndExpenditureVO;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 10:08
 * @Description 收支报表统计
 */
@Service
@Slf4j
public class IncomeAndExpenditureServiceImpl implements IncomeAndExpenditureService {
    private static final String PAY_BY_ALIPAY = "支付宝支付";
    private static final String TOTAL_ONLINE_PAYMENT = "网上支付合计";
    private static final String ALIPAY_RETURN = "支付宝返回";
    private static final String ALIPAY_SECOND_REFUND = "支付宝秒退";
    private static final String REPURCHASE_FOR_OTHER = "回购换其他";
    private static final String RECHARGE_TO_BALANCE = "充值到余额";
    private static final String FILL_VALUE = "%s_%s";
    @Resource
    private ShouyingMapper shouyingMapper;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private IncomeAndExpenditureService incomeAndExpenditureService;

    /**
     * 表头数据处理
     *
     * @param incomeAndExpenditureVOList incomeAndExpenditureVOList
     * @return CommonDataRes
     */
    public static CommonDataRes<IncomeAndExpenditureRes> getIncomeAndExpenditureTable(Collection<IncomeAndExpenditureVO> incomeAndExpenditureVOList) {
        CommonDataRes<IncomeAndExpenditureRes> commonDataRes = new CommonDataRes<>();
        //根据查询结构取出一级表头
        List<String> businessTypeNameList = incomeAndExpenditureVOList.stream().map(IncomeAndExpenditureVO::getBusinessTypeName).distinct().collect(Collectors.toList());
        //过滤掉list为空的元素
        businessTypeNameList.removeAll(Collections.singleton(null));
        //添加是否加加粗显示
        incomeAndExpenditureVOList.stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> !Arrays.asList("现金", "刷卡", "余额支付", "其他", TOTAL_ONLINE_PAYMENT, "分期付款合计", "三方收款合计", "收款(总)").contains(i.getProjectName()))
                .forEach(s -> s.setColorFlag(false));
        //遍历一级表头
        businessTypeNameList.forEach(bu -> {
            //插入二级表头
            List<CommonDataRes.ColumnsBean> collect = incomeAndExpenditureVOList.stream().filter(in -> Objects.equals(in.getBusinessTypeName(), bu))
                    .filter(distinctByKey(iae -> String.format(FILL_VALUE, iae.getBusinessTypeName(), iae.getProjectName())))
                    .sorted(Comparator.comparing(IncomeAndExpenditureVO::getSortFlag)).map(e ->
                            CommonDataRes.ColumnsBean.of(e.getProjectName(), String.format(FILL_VALUE, e.getBusinessTypeName(), e.getProjectName()))
                                    .setDescription(e.getProjectDescription()).setColorFlag(e.getColorFlag())
                    ).collect(Collectors.toList());
            commonDataRes.addColumn(CommonDataRes.ColumnsBean.of(bu, bu).setSubTitles(collect));
        });
        return commonDataRes;
    }

    /**
     * 过滤重复数据
     *
     * @param keyExtractor keyExtractor
     * @param <T>          t
     * @return Predicate
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> result = new ConcurrentHashMap<>();
        return t -> result.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 数据合计计算 单独处理
     *
     * @param incomeAndExpenditureVOList incomeAndExpenditureVOList
     * @return Collection<IncomeAndExpenditureVO>
     */
    public static Collection<IncomeAndExpenditureVO> getIncomeAndExpenditureSum(Collection<IncomeAndExpenditureVO> incomeAndExpenditureVOList, List<String> getInstallmentName, List<String> getThreePartyName, List<String> posName) {
        //根据门店id分组处理
        Map<Integer, List<IncomeAndExpenditureVO>> integerListMapByArea = incomeAndExpenditureVOList.stream()
                .collect(Collectors.groupingBy(IncomeAndExpenditureVO::getAreaId));
        //计算合计金额
        integerListMapByArea.entrySet().forEach((Map.Entry<Integer, List<IncomeAndExpenditureVO>> entry) -> extracted(incomeAndExpenditureVOList, entry, getInstallmentName, getThreePartyName, posName));
        //根据key相同的值相加 汇总一行合计数据
        Map<String, List<IncomeAndExpenditureVO>> integerListMap = incomeAndExpenditureVOList.stream()
                .collect(Collectors.groupingBy(iae -> String.format(FILL_VALUE, iae.getBusinessTypeName(), iae.getProjectName())));
        integerListMap.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .forEach((Map.Entry<String, List<IncomeAndExpenditureVO>> entry) -> extracted2(incomeAndExpenditureVOList, entry));
        return incomeAndExpenditureVOList;
    }

    /**
     * 计算合计行的算法 （lambda）
     *
     * @param incomeAndExpenditureVOList incomeAndExpenditureVOList
     * @param entry                      entry
     */
    private static void extracted2(Collection<IncomeAndExpenditureVO> incomeAndExpenditureVOList, Map.Entry<String, List<IncomeAndExpenditureVO>> entry) {
        IncomeAndExpenditureVO incomeAndExpenditure = new IncomeAndExpenditureVO();
        //计算data的和
        BigDecimal reduce = entry.getValue().stream().map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add);
        incomeAndExpenditure.setData(reduce);
        incomeAndExpenditure.setAreaId(-1);
        entry.getValue().stream().findFirst().ifPresent(iae -> incomeAndExpenditure.setBusinessTypeName(iae.getBusinessTypeName()).setProjectName(iae.getProjectName()));
        incomeAndExpenditureVOList.add(incomeAndExpenditure);
    }

    /**
     * 计算收款金额合计 （lambda）
     *
     * @param incomeAndExpenditureVOList 财务收支报表vo
     * @param entry                      计算结果封装
     */
    private static void extracted(Collection<IncomeAndExpenditureVO> incomeAndExpenditureVOList, Map.Entry<Integer, List<IncomeAndExpenditureVO>> entry, List<String> installmentName, List<String> threePartyName, List<String> posName) {
        //分期付款合计 分期支付的key在200-500之间
        IncomeAndExpenditureVO totalInstallment = new IncomeAndExpenditureVO();
        totalInstallment.setAreaId(entry.getKey());
        totalInstallment.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_499.getCode());
        totalInstallment.setBusinessTypeName("收款金额");
        totalInstallment.setProjectName("分期付款合计");
        //过滤 key在200-499之间的 收款金额:bigDecimalTotalInstallment
        BigDecimal bigDecimalTotalInstallment = entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> i.getSortFlag() >= IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_200.getCode() && i.getSortFlag() < IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_499.getCode())
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add);
        totalInstallment.setData(bigDecimalTotalInstallment);
        incomeAndExpenditureVOList.add(totalInstallment);
        //三方收款合计 三方支付的key在500-1000之间
        IncomeAndExpenditureVO totalCollectionParties = new IncomeAndExpenditureVO();
        totalCollectionParties.setAreaId(entry.getKey());
        totalCollectionParties.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_998.getCode());
        totalCollectionParties.setBusinessTypeName("收款金额");
        totalCollectionParties.setProjectName("三方收款合计");
        //过滤 key在200-499之间的 三方收款合计 bigDecimalTotalCollectionParties
        BigDecimal bigDecimalTotalCollectionParties = entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> i.getSortFlag() >= IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_500.getCode() && i.getSortFlag() < IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_998.getCode())
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add);
        totalCollectionParties.setData(bigDecimalTotalCollectionParties);
        incomeAndExpenditureVOList.add(totalCollectionParties);
        //收款(总) = 现金 + 刷卡 + 余额支付 + 网上支付合计 + 分期合计 + 三方收款合计 + 其他
        IncomeAndExpenditureVO receivePayment = new IncomeAndExpenditureVO();
        receivePayment.setAreaId(entry.getKey());
        receivePayment.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_999.getCode());
        receivePayment.setBusinessTypeName("收款金额");
        receivePayment.setProjectName("收款(总)");
        //过滤 key在200-499之间的  收款总：bigDecimalReceivePayment
        BigDecimal bigDecimalAdd = entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Arrays.asList("现金", "刷卡", "余额支付", TOTAL_ONLINE_PAYMENT, "其他").contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal bigDecimalReceivePayment = bigDecimalAdd.add(bigDecimalTotalInstallment).add(bigDecimalTotalCollectionParties);
        receivePayment.setData(bigDecimalReceivePayment);
        incomeAndExpenditureVOList.add(receivePayment);

        //计算 退款金额的 退款（总）Refund
        IncomeAndExpenditureVO refund = new IncomeAndExpenditureVO();
        refund.setAreaId(entry.getKey());
        refund.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_1999.getCode());
        refund.setBusinessTypeName("退款金额");
        refund.setProjectName("退款(总)");
        //计算退款总 ： bigDecimalRefund
        BigDecimal bigDecimalRefund = entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "退款金额"))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add);
        refund.setData(bigDecimalRefund);
        incomeAndExpenditureVOList.add(refund);

        //计算回收付款(总)
        IncomeAndExpenditureVO payBack = new IncomeAndExpenditureVO();
        payBack.setAreaId(entry.getKey());
        payBack.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_2999.getCode());
        payBack.setBusinessTypeName("回收付款");
        payBack.setProjectName("回收付款(总)");
        //回收付款总 ： bigDecimalPayBack
        BigDecimal bigDecimalPayBack = entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "回收付款"))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add);
        payBack.setData(bigDecimalPayBack);
        incomeAndExpenditureVOList.add(payBack);

        extracted1(incomeAndExpenditureVOList, entry);

        //计算 总合计 三方支付 = 收款三方 - 退款三方
        IncomeAndExpenditureVO inTotalThreeParty = new IncomeAndExpenditureVO();
        inTotalThreeParty.setAreaId(entry.getKey());
        inTotalThreeParty.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3008.getCode());
        inTotalThreeParty.setBusinessTypeName("总合计");
        inTotalThreeParty.setProjectName("三方支付");
        //收款金额减去 退款金额的三方
        inTotalThreeParty.setData(bigDecimalTotalCollectionParties.subtract(entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "退款金额"))
                .filter(i -> threePartyName.contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalThreeParty);

        //计算 总合计 分期支付 = 分期支付 - 退款分期
        IncomeAndExpenditureVO inTotalInstallment = new IncomeAndExpenditureVO();
        inTotalInstallment.setAreaId(entry.getKey());
        inTotalInstallment.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3009.getCode());
        inTotalInstallment.setBusinessTypeName("总合计");
        inTotalInstallment.setProjectName("分期支付");
        //收款金额减去 退款金额的分期支付
        inTotalInstallment.setData(bigDecimalTotalInstallment.subtract(entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "退款金额"))
                .filter(i -> installmentName.contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalInstallment);
        //计算 总合计 刷卡支付 = 刷卡支付 - 退款pos机返回
        IncomeAndExpenditureVO inTotalCreditCard = new IncomeAndExpenditureVO();
        inTotalCreditCard.setAreaId(entry.getKey());
        inTotalCreditCard.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3010.getCode());
        inTotalCreditCard.setBusinessTypeName("总合计");
        inTotalCreditCard.setProjectName("刷卡支付");
        //获取收款的刷卡 金额
        BigDecimal bigDecimalCreditCard = entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Objects.equals(i.getProjectName(), "刷卡"))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00));
        //收款金额减去 退款金额的分期支付
        inTotalCreditCard.setData(bigDecimalCreditCard.subtract(entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "退款金额"))
                .filter(i -> posName.contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalCreditCard);

        //计算 总合计 手动支付配置
        IncomeAndExpenditureVO inTotalManualConfig = new IncomeAndExpenditureVO();
        inTotalManualConfig.setAreaId(entry.getKey());
        inTotalManualConfig.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3010.getCode());
        inTotalManualConfig.setBusinessTypeName("总合计");
        inTotalManualConfig.setProjectName("手动退款配置");
        //0 - 退款（除了刷卡、微信、支付宝、..）
        inTotalManualConfig.setData(BigDecimal.valueOf(0.00).subtract(entry.getValue().stream()
                .filter(i -> !Arrays.asList("总合计", "收款金额").contains(i.getBusinessTypeName()))
                .filter(i -> !Arrays.asList(PAY_BY_ALIPAY, "首信", ALIPAY_RETURN, ALIPAY_SECOND_REFUND, "微信支付", "微信返回", "微信秒退", "其他", "余额", "银行转账", "现金", REPURCHASE_FOR_OTHER, RECHARGE_TO_BALANCE).contains(i.getProjectName()))
                .filter(i -> !posName.contains(i.getProjectName()))
                .filter(i -> !installmentName.contains(i.getProjectName()))
                .filter(i -> !threePartyName.contains(i.getProjectName()))
                .filter(i -> !String.valueOf(i.getProjectName()).contains("首信"))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalManualConfig);

        //计算 总合计 总合计 = 收款总 - 回收总 - 退款总
        IncomeAndExpenditureVO inTotalInfo = new IncomeAndExpenditureVO();
        inTotalInfo.setAreaId(entry.getKey());
        inTotalInfo.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3999.getCode());
        inTotalInfo.setBusinessTypeName("总合计");
        inTotalInfo.setProjectName("总合计");
        inTotalInfo.setData(bigDecimalReceivePayment.subtract(bigDecimalPayBack).subtract(bigDecimalRefund));
        incomeAndExpenditureVOList.add(inTotalInfo);
    }

    /**
     * 计算退款金额合计 和 回收金额合计 总合计 （lambda）
     *
     * @param incomeAndExpenditureVOList 财务收支报表vo
     * @param entry                      计算结构封装
     */
    private static void extracted1(Collection<IncomeAndExpenditureVO> incomeAndExpenditureVOList, Map.Entry<Integer, List<IncomeAndExpenditureVO>> entry) {
        //计算 总合计 现金 = 收款金额现金 - 退款金额现金 - 回收金额现金
        IncomeAndExpenditureVO inTotalCash = new IncomeAndExpenditureVO();
        inTotalCash.setAreaId(entry.getKey());
        inTotalCash.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3000.getCode());
        inTotalCash.setBusinessTypeName("总合计");
        inTotalCash.setProjectName("现金");
        //获得收款金额现金
        BigDecimal bigDecimalInTotalCash = entry.getValue().stream().filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额")).filter(i -> Objects.equals(i.getProjectName(), "现金"))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00));
        //减去其他现金的值
        inTotalCash.setData(bigDecimalInTotalCash.subtract(entry.getValue().stream()
                .filter(i -> !Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Objects.equals(i.getProjectName(), "现金")).map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalCash);

        //计算 总合计 其他网络支付 = 收款的其他网络支付
        IncomeAndExpenditureVO inTotalOtherOnline= new IncomeAndExpenditureVO();
        inTotalOtherOnline.setAreaId(entry.getKey());
        inTotalOtherOnline.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3006.getCode());
        inTotalOtherOnline.setBusinessTypeName("总合计");
        inTotalOtherOnline.setProjectName("其他网络支付");
        inTotalOtherOnline.setData(entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Objects.equals(i.getProjectName(), "其他网络支付"))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00)));
        incomeAndExpenditureVOList.add(inTotalOtherOnline);

        //计算 总合计 银行转账 = 0 - 退款金额银行转账 - 回收金额银行转账 -
        IncomeAndExpenditureVO inTotalBacktrace = new IncomeAndExpenditureVO();
        inTotalBacktrace.setAreaId(entry.getKey());
        inTotalBacktrace.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3001.getCode());
        inTotalBacktrace.setBusinessTypeName("总合计");
        inTotalBacktrace.setProjectName("银行转账");
        inTotalBacktrace.setData(BigDecimal.valueOf(0.00).subtract(entry.getValue().stream()
                .filter(i -> !Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Objects.equals(i.getProjectName(), "银行转账"))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalBacktrace);

        extracted3(incomeAndExpenditureVOList, entry);
    }

    /**
     * 总合计计算  sonar检测太长，做拆分
     *
     * @param incomeAndExpenditureVOList incomeAndExpenditureVOList
     * @param entry                      entry
     */
    private static void extracted3(Collection<IncomeAndExpenditureVO> incomeAndExpenditureVOList, Map.Entry<Integer, List<IncomeAndExpenditureVO>> entry) {
        //计算 总合计 余额 = 收款金额余额 - 其他余额
        IncomeAndExpenditureVO inTotalBalance = new IncomeAndExpenditureVO();
        inTotalBalance.setAreaId(entry.getKey());
        inTotalBalance.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3002.getCode());
        inTotalBalance.setBusinessTypeName("总合计");
        inTotalBalance.setProjectName("余额");
        //获得收款金额余额
        BigDecimal bigDecimalInTotalBalance = entry.getValue().stream().filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额")).filter(i -> Objects.equals(i.getProjectName(), "余额支付"))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00));
        inTotalBalance.setData(bigDecimalInTotalBalance.subtract(entry.getValue().stream()
                .filter(i -> !Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Arrays.asList("余额", RECHARGE_TO_BALANCE).contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalBalance);

        //计算 总合计 其它  = 收款金额其他 - 退款金额其他 - 回收金额其他
        IncomeAndExpenditureVO inTotalOther = new IncomeAndExpenditureVO();
        inTotalOther.setAreaId(entry.getKey());
        inTotalOther.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3003.getCode());
        inTotalOther.setBusinessTypeName("总合计");
        inTotalOther.setProjectName("其他");
        //获得收款金额其他
        BigDecimal bigDecimalInTotalOther = entry.getValue().stream().filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额")).filter(i -> Objects.equals(i.getProjectName(), "其他"))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00));

        inTotalOther.setData(bigDecimalInTotalOther.subtract(entry.getValue().stream()
                .filter(i -> !Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Arrays.asList("其他", REPURCHASE_FOR_OTHER).contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalOther);

        //计算 总合计 微信 = 收款金额微信支付 - 退款金额微信支付相关 - 回收金额微信支付
        IncomeAndExpenditureVO inTotalWeChat = new IncomeAndExpenditureVO();
        inTotalWeChat.setAreaId(entry.getKey());
        inTotalWeChat.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3004.getCode());
        inTotalWeChat.setBusinessTypeName("总合计");
        inTotalWeChat.setProjectName("微信");
        //获得收款金额微信支付
        BigDecimal bigDecimalInTotalWeChat = entry.getValue().stream().filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额")).filter(i -> Objects.equals(i.getProjectName(), "微信支付"))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00));
        inTotalWeChat.setData(bigDecimalInTotalWeChat.subtract(entry.getValue().stream()
                .filter(i -> !Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Arrays.asList("微信支付", "微信返回", "微信秒退").contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalWeChat);

        //计算 总合计 支付宝
        IncomeAndExpenditureVO inTotalAlipay = new IncomeAndExpenditureVO();
        inTotalAlipay.setAreaId(entry.getKey());
        inTotalAlipay.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3005.getCode());
        inTotalAlipay.setBusinessTypeName("总合计");
        inTotalAlipay.setProjectName("支付宝");
        //获得收款金额微信支付
        BigDecimal bigDecimalInTotalAlipay = entry.getValue().stream().filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额")).filter(i -> Objects.equals(i.getProjectName(), PAY_BY_ALIPAY))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00));
        inTotalAlipay.setData(bigDecimalInTotalAlipay.subtract(entry.getValue().stream()
                .filter(i -> !Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Arrays.asList(PAY_BY_ALIPAY, ALIPAY_RETURN, ALIPAY_SECOND_REFUND).contains(i.getProjectName()))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalAlipay);

        //计算 总合计 首信易支付
        IncomeAndExpenditureVO inTotalCapital = new IncomeAndExpenditureVO();
        inTotalCapital.setAreaId(entry.getKey());
        inTotalCapital.setSortFlag(IncomeAndExpenditureEnum.INCOME_AND_EXPENDITURE_ENUM_3007.getCode());
        inTotalCapital.setBusinessTypeName("总合计");
        inTotalCapital.setProjectName("首信易");
        //获得收款金额首信易支付
        BigDecimal bigDecimalInTotalCapital = entry.getValue().stream()
                .filter(i -> Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> Objects.equals(i.getProjectName(), "首信易支付"))
                .map(IncomeAndExpenditureVO::getData).findFirst().orElse(BigDecimal.valueOf(0.00));
        inTotalCapital.setData(bigDecimalInTotalCapital.subtract(entry.getValue().stream()
                .filter(i -> !Objects.equals(i.getBusinessTypeName(), "收款金额"))
                .filter(i -> String.valueOf(i.getProjectName()).contains("首信"))
                .map(IncomeAndExpenditureVO::getData).reduce(BigDecimal.ZERO, BigDecimal::add)));
        incomeAndExpenditureVOList.add(inTotalCapital);

    }

    /**
     * 在返回值前面加个a,方便前端解析
     *
     * @param varName varName
     * @return String
     */
    public static String toLogo(String varName) {
        StringBuilder stringBuilder = new StringBuilder(varName);
        return String.valueOf(stringBuilder.insert(0, "a"));
    }



    /**
     * 获取三方支付名称 加缓存
     *
     * @return 三方支付名称
     */
    @Override
    public List<String> getThreePartyName() {
        return shouyingMapper.getThreePartyName();
    }

    /**
     * 获取分期支付名称
     *
     * @return 分期支付名称
     */
    @Override
    public List<String> getInstallmentName() {
        return shouyingMapper.getInstallmentName();
    }

    /**
     * 获取pos返回名称
     *
     * @return pos返回名称
     */
    @Override
    public List<String> getPosName() {
        return shouyingMapper.getPosName();
    }

    /**
     * 返回数据组装
     *
     * @param req 时间范围与门店
     * @return CommonDataRes
     */
    @Override
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    public CommonDataRes<IncomeAndExpenditureRes> getIncomeAndExpenditureList(IncomeAndExpenditureReq req) {
        //源数据查询与处理
        Collection<IncomeAndExpenditureVO> getIncomeAndExpenditureSum = incomeAndExpenditureList(req);
        //根据源数据 组装表头数据
        CommonDataRes<IncomeAndExpenditureRes> incomeAndExpenditureTable = getIncomeAndExpenditureTable(getIncomeAndExpenditureSum);
        //根据门店id转化为map
        Map<Integer, List<IncomeAndExpenditureVO>> integerListMap = getIncomeAndExpenditureSum.stream()
                .collect(Collectors.groupingBy(IncomeAndExpenditureVO::getAreaId));
        //数据封装返回
        List<IncomeAndExpenditureRes> result = new LinkedList<>();
        integerListMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getKey))
                .forEach((Map.Entry<Integer, List<IncomeAndExpenditureVO>> entry) -> resultPackage(result, entry));
        return incomeAndExpenditureTable.setList(result);
    }

    /**
     * 查询数据并处理组装
     *
     * @param req req
     * @return 主体数据
     */
    public Collection<IncomeAndExpenditureVO> incomeAndExpenditureList(IncomeAndExpenditureReq req) {
        //处理时间参数为空或不正确的情况
        if (req.getStartTime() == null || req.getEndTime() == null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime sixDaysBefore = now.minusDays(NumberConstant.SIX).minusHours(now.getHour()).minusMinutes(now.getMinute())
                    .minusSeconds(now.getSecond()).minusNanos(now.getNano());
            //默认查询前5天的数据
            req.setStartTime(sixDaysBefore).setEndTime(now);
        }
        //角色数据查询
        R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.FINANCES)
                .getStartTimeFun(req::getStartTime)
                .getEndTimeFun(req::getEndTime)
                .setStartTimeFun(req::setStartTime)
                .setEndTimeFun(req::setEndTime)
                .build(), null);
        if (!dataViewRes.isSuccess()) {
            throw new CustomizeException(dataViewRes.getUserMsg());
        }
        //用户没有传门店id的时候，默认查询所有门店的合计
        if (Boolean.TRUE.equals(req.isNoParameter())) {
            //获取所有门店的id集合
            List<String> areaInfoListById = areaInfoClient.listAll().getData().stream().map(AreaInfo::getId).map(String::valueOf).collect(Collectors.toList());
            req.setAreaIdList(areaInfoListById);
        }
        //查询报表数据 初始数据
        List<String> areaIdList = req.getAreaIdList();
        //一次in 100条数据
        List<IncomeAndExpenditureVO> incomeAndExpenditureVOList = CommonUtils.bigDataInQuery(NumberConstant.ONE_HUNDRED, areaIdList, ids -> {
            IncomeAndExpenditureReq req2 = new IncomeAndExpenditureReq();
            BeanUtils.copyProperties(req, req2);
            req2.setAreaIdList(ids);
            return shouyingMapper.saleIncomeAndExpenditure(req2);
        });
        req.setAreaIdList(areaIdList);
        //数据组装，计算数据合计
        return getIncomeAndExpenditureSum(incomeAndExpenditureVOList, incomeAndExpenditureService.getInstallmentName(), incomeAndExpenditureService.getThreePartyName(), incomeAndExpenditureService.getPosName());
    }

    /**
     * 返回结果封装 （lambda）
     *
     * @param result result
     * @param entry  entry
     */
    private void resultPackage(List<IncomeAndExpenditureRes> result, Map.Entry<Integer, List<IncomeAndExpenditureVO>> entry) {
        IncomeAndExpenditureRes incomeAndExpenditureRes = new IncomeAndExpenditureRes();
        incomeAndExpenditureRes.setAreaId(String.valueOf(entry.getKey()));
        Map<String, BigDecimal> map = entry.getValue().stream()
                .collect(Collectors.toMap(iae -> String.format(FILL_VALUE, iae.getBusinessTypeName(), iae.getProjectName()),
                        IncomeAndExpenditureVO::getData, (v1, v2) -> v1));
        //根据areaId获取门店名称
        //当门店id是-1 的时候,为最后一行的合计
        if (Objects.equals(-1, entry.getKey())) {
            incomeAndExpenditureRes.setAreaName("合计");
        } else {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(entry.getKey());
            incomeAndExpenditureRes.setAreaName(areaInfoR.getData().getArea());
        }
        // map 转换为 Map<String, BigDecimal> 类型，并且拼接字母用于前端解析
        Map<String, BigDecimal> mapToString = map.entrySet().stream().collect(Collectors.toMap(e -> toLogo(e.getKey()), Map.Entry::getValue));
        incomeAndExpenditureRes.setDataList(mapToString);
        result.add(incomeAndExpenditureRes);
    }

    /**
     * Excel导出
     *
     * @param req req
     * @param out out
     */
    @Override
    public void exportIncomeAndExpenditure(IncomeAndExpenditureReq req, OutputStream out) {
        // 通过工具类创建writer，默认创建xls格式
        try (InputStream excelTempleStream = ResourceUtil.getStream("excel/IncomeAndExpenditure_statistics.xls");
             ExcelWriter writer = new ExcelWriter(WorkbookUtil.createBook(excelTempleStream, true), "Sheet1")) {
            //跳过第一行
            writer.passRows(1);
            CommonDataRes<IncomeAndExpenditureRes> iaeRes = getIncomeAndExpenditureList(req);
            //店名
            writer.addHeaderAlias("s_areaName", "门店");
            //获取当前行（第二行）
            int currentRow = writer.getCurrentRow();
            //获取二级标题
            LinkedList<String> towHeadTitle = new LinkedList<>();
            LinkedList<String> towHeadTitleKey = new LinkedList<>();
            towHeadTitle.add("门店");
            AtomicInteger firstNumber = new AtomicInteger(1);
            iaeRes.getColumns().forEach(co -> {
                //取出一级标题下面子标题的数量
                int size = co.getSubTitles().size();
                int lastNumber = firstNumber.get() + size;
                if (size == 1 || size == 0) {
                    writer.writeCellValue(lastNumber-1, currentRow, co.getTitle());
                } else {
                    writer.merge(currentRow, currentRow, firstNumber.get(), (lastNumber - 1), co.getTitle(), false);
                }
                firstNumber.set(lastNumber);
                //自定义二级标题别名
                secondaryHeading(writer, towHeadTitle,towHeadTitleKey, co);

            });

            //获取除合计之外的其他
            List<Dict> dits = iaeRes.getList().stream().filter(i -> !Objects.equals(i.getAreaId(), "-1")).map(getDitsByTotal(towHeadTitleKey)).collect(Collectors.toList());
            //获取合计
            List<Dict> ditsByTotal = iaeRes.getList().stream().filter(i -> Objects.equals(i.getAreaId(), "-1")).map(getDitsByTotal(towHeadTitleKey)).collect(Collectors.toList());
            //总合计行放最后
            dits.addAll(ditsByTotal);
            //跳过一级表头
            writer.passRows(NumberConstant.TWO);
            writer.autoSizeColumnAll();
            writer.setOnlyAlias(false);
            writer.write(dits, false);
            //重写二级标题行
            writer.setCurrentRow(NumberConstant.TWO);
            writer.writeHeadRow(towHeadTitle);
            writer.flush(out, false);
        } catch (IOException e) {
            log.error("excel模板文件读取异常!", e);
        }
    }

    /**
     *  遍历二级标题
     * @param writer writer
     * @param towHeadTitle towHeadTitle
     * @param co co
     */
    private static void secondaryHeading(ExcelWriter writer, LinkedList<String> towHeadTitle, LinkedList<String> towHeadTitleKey,CommonDataRes.ColumnsBean co) {
        co.getSubTitles().forEach(su -> {
            writer.addHeaderAlias("a" + su.getKey(), su.getKey());
            towHeadTitle.add(su.getTitle());
            towHeadTitleKey.add("a"+su.getKey());
        });
    }

    /**
     * 数据源排序
     * @return 数据源
     */
    private static Function<IncomeAndExpenditureRes, Dict> getDitsByTotal(LinkedList<String> towHeadTitleKey) {
        return iae -> {
            //添加门店
            Dict dic = new Dict().set("s_areaName", iae.getAreaName());
            //过滤不为空的数据
            Optional.ofNullable(iae.getDataList()).filter(CollUtil::isNotEmpty).ifPresent(dList -> {
                for (String s : towHeadTitleKey) {
                    dic.set(s, 0.00);
                    for (Map.Entry<String, BigDecimal> entry : dList.entrySet()) {
                        if (Objects.equals(entry.getKey(), s)) {
                            dic.set(entry.getKey(), entry.getValue());
                        }
                    }
                }
            });
            return dic;
        };
    }

}
