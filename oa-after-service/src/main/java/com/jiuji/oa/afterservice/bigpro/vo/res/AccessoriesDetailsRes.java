package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.jiuji.oa.afterservice.bigpro.po.RepairServiceInfo;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceConfig;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AccessoriesDetailsRes {

    /**
     * 商品基本信息
     */
    private AccessoriesBaseProduct baseProduct;
    /**
     * 服务信息
     */
    private List<ShouhouServiceConfig> serviceInfoList;
    /**
     * 绑定配件信息
     */
    private List<BindPpidKcInfo> bindPpidKcInfo;

    /**
     * 库存情况
     */
    private ProductKc productKc;

}
