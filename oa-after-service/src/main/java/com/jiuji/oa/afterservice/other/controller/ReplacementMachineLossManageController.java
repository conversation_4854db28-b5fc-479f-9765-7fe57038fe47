package com.jiuji.oa.afterservice.other.controller;

import com.jiuji.oa.afterservice.common.vo.res.CommonTitleRes;
import com.jiuji.oa.afterservice.other.service.ReplacementMachineLossManageService;
import com.jiuji.oa.afterservice.other.vo.req.ReplacementMachineLossReq;
import com.jiuji.oa.afterservice.other.vo.res.ReplaceExchangeLossRes;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * 退换机亏损管理前端控制器
 * @version 1.0
 * @date 2021/3/10 14:12
 */
@RestController
@RequestMapping("/api/ReplacementMachineLossManage")
public class ReplacementMachineLossManageController {

    @Autowired
    private ReplacementMachineLossManageService replacementMachineLossManageService;

    /**
     * 退换机亏损管理查询参数下拉数据
     *
     * @return R<CommonTitleRes>
     */
    @GetMapping("/title")
    public R<CommonTitleRes> title() {
        return R.success(replacementMachineLossManageService.getTitle());
    }

    /**
     * 退换机亏损统计
     * @param replacementMachineLossReq
     * @return
     */
    @GetMapping("/searchStatistics")
    @ApiOperation("退换机亏损统计")
    public R<ReplaceExchangeLossRes> searchStatistics(ReplacementMachineLossReq replacementMachineLossReq){
        replacementMachineLossReq = replacementMachineLossReq.uniqueClearOtherVariable(replacementMachineLossReq);
        return R.success(replacementMachineLossManageService.searchStatistics(replacementMachineLossReq));
    }

    /**
     * 导出Excel
     * @param replacementMachineLossReq
     * @return
     */
    @PostMapping(value = "/exportSearch")
    public void exportSearch(@RequestBody ReplacementMachineLossReq replacementMachineLossReq, HttpServletResponse response){
        replacementMachineLossReq = replacementMachineLossReq.uniqueClearOtherVariable(replacementMachineLossReq);
        replacementMachineLossManageService.exportSearch(replacementMachineLossReq,response);
    }

}
