package com.jiuji.oa.afterservice.statistics.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;

/**
 * description: <Class introduction>
 * translation: <Method translation introduction>
 *
 * <AUTHOR>
 * @date 2020/4/29
 * @since 1.0.0
 */
@AllArgsConstructor
@Getter
public enum SmallproStatisticsFieldResEnum implements CodeMessageEnumInterface {

    /**
     * 字段名-字段译名
     */
    SMALLPRO_STATISTICS_FIELD_RES_NULL(null, null),
    SMALLPRO_STATISTICS_FIELD_RES_1("receiveCount", "接件量"),
    SMALLPRO_STATISTICS_FIELD_RES_2("saleCount", "销售量"),
    SMALLPRO_STATISTICS_FIELD_RES_3("saleProfit", "销售利润"),
    SMALLPRO_STATISTICS_FIELD_RES_4("beforeCount", "售前量"),
    SMALLPRO_STATISTICS_FIELD_RES_5("beforeProportion", "售前占比"),
    SMALLPRO_STATISTICS_FIELD_RES_6("afterProportion", "售后占比"),
    SMALLPRO_STATISTICS_FIELD_RES_7("damagedCount", "报损量"),
    SMALLPRO_STATISTICS_FIELD_RES_8("damagedProportion", "报损占比"),
    SMALLPRO_STATISTICS_FIELD_RES_9("damagedAmount", "报损金额"),
    SMALLPRO_STATISTICS_FIELD_RES_10("convertInventoryCount", "转现量"),
    SMALLPRO_STATISTICS_FIELD_RES_11("convertInventoryProportion", "转现占比"),
    SMALLPRO_STATISTICS_FIELD_RES_12("maintainCount", "维修量"),
    SMALLPRO_STATISTICS_FIELD_RES_13("maintainProportion", "维修占比"),
    SMALLPRO_STATISTICS_FIELD_RES_14("processedCount", "待处理");


    /**
     * 字段编码
     */
    private String code;
    /**
     * 字段译名
     */
    private String message;

    public static LinkedHashMap<String, String> getAllField() {
        SmallproStatisticsFieldResEnum[] enums = SmallproStatisticsFieldResEnum.values();
        LinkedHashMap<String, String> result = new LinkedHashMap<>(enums.length);
        for (SmallproStatisticsFieldResEnum temp : enums) {
            if (temp.getCode() != null) {
                result.put(temp.getCode(), temp.getMessage());
            }
        }
        return result;
    }
}
