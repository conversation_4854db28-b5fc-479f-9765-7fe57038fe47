package com.jiuji.oa.afterservice.bigpro.service.servicerecord;

import com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外修服务类
 * <AUTHOR>
 * @since 2022/1/11 10:16
 */
public interface ServiceRecordExternalRepairService {

    /**
     * 获取简单的订单信息
     *
     * @param imei
     * @param userId
     * @param xTenant
     * @param subId
     * @param isEffect true 获取生效中的订单  false 只获取完成的订单
     * @return
     */
    SimpleServiceSubInfoBo getSimpleSubInfo(String imei, Integer userId, Integer xTenant, Integer subId, boolean isEffect);

    /**
     * 获取订单服务信息
     * @param subId
     * @param imei
     * @param isHistory
     * @return
     */
    ServiceInfoVO getServiceInfo(Integer subId, String imei, boolean isHistory);

    /**
     * 获取九机服务记录
     * @param subId
     * @param imei
     * @param isHistory
     * @return
     */
    List<ServiceRecord> list9jiServiceRecord(Integer subId, @Param("imei") String imei, boolean isHistory);

    /**
     * 获取简单的订单信息
     *
     * @param imei
     * @param userId
     * @param isEffect true 获取生效中的订单  false 只获取完成的订单
     * @param subId
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    SimpleServiceSubInfoBo getSimpleNormalSub(String imei, Integer userId, boolean isEffect, Integer subId);

    /**
     * 获取简单的历史存档订单信息
     *
     * @param imei
     * @param userId
     * @param isEffect true 获取生效中的订单  false 只获取完成的订单
     * @param subId
     * @return
     */
    @DS(DataSourceConstants.OA_NEW_HIS)
    SimpleServiceSubInfoBo getSimpleHistorySub(String imei, Integer userId, boolean isEffect, Integer subId);

    /**
     * 获取订单信息
     * @param subId
     * @param imei
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    ServiceInfoVO getNormalSub(Integer subId, String imei);

    /**
     * 获取归档的订单信息
     * @param subId
     * @param imei
     * @return
     */
    @DS(DataSourceConstants.OA_NEW_HIS)
    ServiceInfoVO getHistorySub(Integer subId, String imei);

    /**
     * 获取九机服务记录
     * @param subId
     * @param imei
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<ServiceRecord> listNormal9jiServiceRecord(Integer subId, @Param("imei") String imei);

    /**
     * 获取九机服务记录
     * @param subId
     * @param imei
     * @return
     */
    @DS(DataSourceConstants.OA_NEW_HIS)
    List<ServiceRecord> listHistory9jiServiceRecord(Integer subId, @Param("imei") String imei);

    /**
     * 获取生效的订单信息
     * @param imei
     * @param userId
     * @param xTenant
     * @return
     */
    SimpleServiceSubInfoBo getSimpleSubInfoEffect(String imei, Integer userId, Integer xTenant);
}
