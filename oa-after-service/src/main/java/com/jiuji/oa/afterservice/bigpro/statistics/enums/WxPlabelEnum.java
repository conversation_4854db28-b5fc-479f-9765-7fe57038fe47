package com.jiuji.oa.afterservice.bigpro.statistics.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修配件商品标签枚举
 * @author: <PERSON> quan
 * @date: 2020/16/30
 */
@Getter
@AllArgsConstructor
public enum WxPlabelEnum implements CodeMessageEnumInterface {
    SOCIAL(0,"常规"),
    ORDER(20,"订购A"),
    WAI_SONG(21,"外送"),
    CLEAR_KC(22,"订购B"),
    HOT(23,"热卖"),
    HAO_CAI(24,"耗材"),
    NEW_PRODUCT(25,"新品");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
