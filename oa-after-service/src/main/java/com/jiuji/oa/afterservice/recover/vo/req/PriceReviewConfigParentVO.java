package com.jiuji.oa.afterservice.recover.vo.req;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PriceReviewConfigParentVO {

    private Integer overTime;

    private String notReviewFlag ;

    private Integer evaPriceLimitType;

    private Integer addPriceLimitType;

    /**
     * 估价金额
     */
    private BigDecimal evaPriceLimit ;


    /**
     * 加价金额
     */
    private BigDecimal addPriceLimit ;

}
