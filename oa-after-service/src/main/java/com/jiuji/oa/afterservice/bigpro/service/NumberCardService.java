package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.NumberCard;
import com.jiuji.oa.afterservice.bigpro.vo.req.NumberCardReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.CouponRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.NumberCardRes;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-19
 */
public interface NumberCardService extends IService<NumberCard> {
    /**
     * 取消优化码
     * @param id
     * @return
     */
    Boolean cancelNumerCard(Integer id);

    /**
     * 预约单使用上门快修优惠码
     * @param coupon
     * @param yyid
     * @param inuser
     * @param stype
     * @return
     */
    R<String> useYuyueCoupon(String coupon, Integer yyid, String inuser, Integer stype,Integer areaId);

    /**
     * 判断优惠码是否使用过
     * @param coupon
     * @return true为使用过，false未使用
     */
    Boolean checkCouponIsUsed(String coupon);

    /**
     * 查询未使用过的优惠码
     * @param coupon
     * @return
     */
    List<NumberCard> getUnUsedCoupon(String coupon);

    /**
     * 使用优惠码
     * @param numberCard
     */
    void useCoupon(NumberCard numberCard, Integer subId, Integer areaId, String useName);

    /**
     * 获取用户绑定的优化码
     * @param userId
     */
    List<CouponRes>  getUserBindCoupon(Integer userId);

    /**
     * 优惠码，添加【批量】
     * @param numberCards
     * @return
     */
    R<List<Integer>> addNumberCard(List<NumberCardReq> numberCards);

    /**
     * 更改优惠码使用次数
     * @param cardId
     * @return
     */
    Integer updateNumberCardUseCount(String cardId);

    /**
     * 获取用户绑定优惠码
     * @return
     */
    R<List<NumberCardRes>> getKxYouhuimas(Long userId,Boolean isSoft,Integer shouhouId);

    /**
     * 查询优惠码信息
     * @param cardId
     * @return
     */
    List<NumberCard> getNumberCardByCardId(String cardId);

    /**
     * 校验优惠码是否满足使用条件
     * @param shouhouId
     * @return
     */
    List<Integer> validateYouHuiMaSatisfyUseCondition(List<Integer> limitIds, Integer shouhouId);

    /**
     * 查询有效优惠码
     * @param cardIds
     * @return
     */
    List<String> getYxNumberCardByCardIdList(List<String> cardIds);
}
