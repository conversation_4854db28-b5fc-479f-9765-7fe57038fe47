package com.jiuji.oa.afterservice.bigpro.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 超时赠品赠送请求参数
 * @author: <PERSON>
 * @date: 2020/6/9
 */
@ApiModel
@Data
public class ZengpinZengSongReq implements Serializable {

    private static final long serialVersionUID = -4174244186089685728L;

    @ApiModelProperty(value = "商品规格id")
    private Integer ppid;

    @ApiModelProperty(value = "售后id")
    private Integer shouhouId;

    @ApiModelProperty(value = "赠品名称")
    private String pName;

    @ApiModelProperty(value = "价格")
    private BigDecimal memberprice;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "备注")
    private String remark;

}
