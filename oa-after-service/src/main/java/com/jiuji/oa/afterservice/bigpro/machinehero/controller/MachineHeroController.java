package com.jiuji.oa.afterservice.bigpro.machinehero.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.bigpro.machinehero.service.MachineHeroService;
import com.jiuji.oa.afterservice.bigpro.machinehero.vo.req.ForwardMqttReq;
import com.jiuji.oa.afterservice.bigpro.machinehero.vo.req.MachineHeroReportReq;
import com.jiuji.oa.afterservice.bigpro.machinehero.vo.res.MachineHeroReportRes;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.machine.bo.ServerAnswerBO;
import com.jiuji.oa.afterservice.machine.bo.StartServerBO;
import com.jiuji.oa.afterservice.machine.enums.ServerRecordEnum;
import com.jiuji.oa.afterservice.machine.po.MachineTestServerRecordPO;
import com.jiuji.oa.afterservice.machine.service.impl.ElectronicCheckServiceImpl;
import com.jiuji.oa.afterservice.machine.vo.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 验机controller
 * <AUTHOR>
 * @since 2021/8/9 16:20
 */
@RestController
@RequestMapping("/api/MachineHeroController")
@Slf4j
public class MachineHeroController {

    @Resource
    private MachineHeroService machineHeroService;


    /**
     * 扫码转发MQTT
     * @param startServer
     * @return
     */
    @PostMapping("/forwardMqtt")
    public R<String> forwardMqtt(@Valid @RequestBody ForwardMqttReq req){
        log.warn("扫码转发MQTT 传入参数:{}", JSONUtil.toJsonStr(req));
        String result = machineHeroService.forwardMqtt(req);
        log.warn("扫码转发MQTT 返回结果:{}", result);
        return R.success(result);
    }



    /**
     * 保存机器侠质检报告
     * @param startServer
     * @return
     */
    @PostMapping("/saveMachineHeroReport")
    public R<String> saveMachineHeroReport(@Valid @RequestBody MachineHeroReportReq req){
        log.warn("保存机器侠质检报告 传入参数:{}", JSONUtil.toJsonStr(req));
        machineHeroService.saveMachineHeroReport(req);
        return R.success("保存成功");
    }


    /**
     * 保存机器侠质检报告
     * @param startServer
     * @return
     */
    @GetMapping("/selectMachineHeroReport")
    public R<MachineHeroReportRes> selectMachineHeroReport(@RequestParam(value = "id") Integer id){
        return R.success(machineHeroService.selectMachineHeroReport(id));
    }

}
