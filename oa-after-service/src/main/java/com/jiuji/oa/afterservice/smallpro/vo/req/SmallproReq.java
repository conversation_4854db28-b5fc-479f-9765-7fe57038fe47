package com.jiuji.oa.afterservice.smallpro.vo.req;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproCheckChangePpidBO;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo;
import com.jiuji.oa.oacore.csharp.vo.req.SmallProBeihuoAutoReq;
import com.jiuji.oa.oacore.oaorder.res.MiniFileRes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2019/11/14
 */
@Data
public class SmallproReq extends Smallpro implements Serializable {
    private static final long serialVersionUID = 994041163502212869L;
    /**
     * 小件接件商品绑定信息
     */
    @ApiModelProperty(value = "小件接件商品绑定信息")
    private List<SmallproBill> smallproBillList;
    /**
     * 小件接件情况选项
     */
    @ApiModelProperty(value = "小件接件情况选项")
    private List<Integer> smallProKinds;
    /**
     * 小件接件校验置换商品的请求信息
     */
    @ApiModelProperty(value = "小件接件校验置换商品的请求信息")
    private SmallproCheckChangePpidBO changePpidCheckReq;
    /**
     * 钢化膜30天无理由换新
     */
    @ApiModelProperty(value = "钢化膜30天无理由换新")
    private Boolean isFreeExchange;

    /**
     * 附件列表[fid]
     */
    @ApiModelProperty(value = "附件列表[fid]")
    private List<MiniFileRes> filesList;
    /**
     * 文件信息
     */
    private List<MiniFileResReq> fileResReqImeiList;
    /**
     * 串号识别大图
     */
    private MiniFileRes bigImeiFile;



    /**
     * 是否切膜现货接件
     */
    private Boolean isCutScreenSpotFlag = false;

    /**
     * 现货校验单号类型(2:销售单号 1:小件单号)
     */
    @ApiModelProperty(value = "现货校验单号类型(2:销售单号 1:小件单号)")
    private Integer spotType;

    /**
     * 从扫码接件入口的小件，跳过串号验证
     */
    @ApiModelProperty(value = "是否跳过串号验证")
    private Boolean isAuthority;
    /**
     * 关联small_exchange_config表
     *
     * @see SmallExchangeConfigPo
     */
    private Integer exchangeConfigId;
    /**
     * 保修状态
     */
    @ApiModelProperty(value = "保修状态[1保修|0非保修]")
    private Integer warrantyStatus;

    /**
     * 最后一次换货的ppid, 与接口: /get/changeProduct/last 一致
     */
    @ApiModelProperty(value = "最近一次换货的ppid")
    private Integer lastChangeProductPpid;

    /**
     * 二次确认文案
     */
    private String secondaryConfirmationText;

    /**
     * 损耗数量
     */
    private Integer lossCount;
    /**
     * 接件ppid
     */
    private Integer lossPpid;

    /**
     * 内部流转字段
     * 运营商自动收银
     */
    private Boolean isAutoOperatorShouYin;

    /**内部流转字段 start*/

    /**
     * 自动备货请求参数
     */
    @ApiModelProperty(value = "退款分类",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    @JsonIgnore
    private transient SmallProBeihuoAutoReq beihuoAutoReq;


    /**内部流转字段 end*/

    public String toMd5Hex(){
        return DigestUtil.md5Hex(JSON.toJSONString(this));
    }

}
