package com.jiuji.oa.afterservice.bigpro.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/21 17:40
 */
@Data
public class ShouhouTestResultAddReq {
    @NotNull(message = "售后id不能为空")
    private Integer shouhouId;
    /**
     * 测试结果
     */
    private Boolean testResult;
    /**
     * 测试类型
     */
    private Integer testType;

    private String remark;

    private List<ShouhouTestAttrResultInfo> testResultInfoList;
}
