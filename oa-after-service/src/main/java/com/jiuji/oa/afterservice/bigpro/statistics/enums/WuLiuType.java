package com.jiuji.oa.afterservice.bigpro.statistics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * WuLiuType
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-05
 */
@Getter
@AllArgsConstructor
public enum WuLiuType {

    /**
     * 内部物流
     */
    INTERNAL_LOGISTICS(1,"内部物流"),

    /**
     * 订单派送
     */
    ORDER_DISPATCH(4,"订单派送"),

    /**
     * 维修单派送
     */
    REPAIR_ORDER_DISPATCH(5,"维修单派送"),

    /**
     * 订单加急
     */
    URGENT_DISPATCH(6,"订单加急"),

    /**
     * 上门取件
     */
    PICK_UP(7,"上门取件"),

    /**
     * 其他
     */
    OTHER(8,"其他"),

    /**
     * 良品订单派送
     */
    SECOND_HAND_DISPATCH(9,"良品订单派送"),

    /**
     * 物料派送
     */
    MATERIAL_DISPATCH(10,"物料派送"),

    /**
     * 租机
     */
    RENT(13,"租机");

    private final Integer code;

    private final String message;

}
