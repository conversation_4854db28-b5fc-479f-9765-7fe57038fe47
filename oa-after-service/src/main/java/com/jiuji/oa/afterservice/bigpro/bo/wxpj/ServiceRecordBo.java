package com.jiuji.oa.afterservice.bigpro.bo.wxpj;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2020/5/15 10:48
 */
@Data
public class ServiceRecordBo {

    private Integer id;

    private Integer basketId;

    private Integer serviceType;

    @TableField("servicesTypeBindId")
    private Integer servicesTypeBindId;

    private LocalDateTime tradedate;

    private BigDecimal price;

    private BigDecimal feiyong;
    @ApiModelProperty(value = "服务生效开始时间")
    @TableField("startTime")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "服务生效结束时间")
    @TableField("endTime")
    private LocalDateTime endTime;
    /**
     * 服务绑定的串号信息
     */
    private String imei;
}
