package com.jiuji.oa.afterservice.evaluate.controller;


import com.jiuji.oa.afterservice.evaluate.service.EvaluateScoreService;
import com.jiuji.oa.afterservice.evaluate.vo.req.CallCenterCounterReq;
import com.jiuji.oa.afterservice.evaluate.vo.res.CallCenterCounterVO;
import com.jiuji.oa.afterservice.evaluate.vo.res.SoftTakeOrderVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
@RestController
@RequestMapping("/inapi/evaluateScore")
public class EvaluateScoreController {
    @Resource
    private EvaluateScoreService evaluateScoreService;


    /**
     * @param req
     * @return
     * @see com.jiuji.oa.afterservice.evaluate.client.EvaluateScoreClient#queryCallCenterCounter
     */
    @PostMapping("queryCallCenter")
    public R<List<CallCenterCounterVO>> queryCallCenterCounter(@RequestBody CallCenterCounterReq req) {
        if (req.getStartTime() == null || req.getEndTime() == null) {
            return R.error("参数异常");
        }
        return R.success(evaluateScoreService.countEvaluateForCallCenter(req.getCh999Ids(), req.getStartTime(),
                req.getEndTime()));
    }

    /**
     * @param userId
     * @return
     * @see com.jiuji.oa.afterservice.evaluate.client.EvaluateScoreClient#listSortTake
     */
    @PostMapping("listQuerySort/{userId}")
    public R<List<SoftTakeOrderVO>> listSortTake(@PathVariable("userId") Integer userId) {
        if (userId == null || userId <= 0) {
            return R.error(ResultCode.PARAM_ERROR, "用户id不正确");
        }
        return R.success(evaluateScoreService.listSoftTakeOrderByUserId(userId));
    }


    /**
     * @param userId
     * @return
     * @see com.jiuji.oa.afterservice.evaluate.client.EvaluateScoreClient#listSortTake
     */
    @GetMapping("test")
    public String test(String hello) {
        return hello;
    }

}

