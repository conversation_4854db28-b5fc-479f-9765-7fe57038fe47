package com.jiuji.oa.afterservice.statistics.vo.rent;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 门店租金vo
 * <AUTHOR>
 * @since 2021/6/17 14:55
 */
@Getter
@Setter
@ToString
@ApiModel("门店租金vo")
public class StoreRentStatisticsVo {
    /**
     * 单号
     */
    @ApiModelProperty("单号")
    private Integer orderId;
    /**
     * 订单类型 1：订单，2：维修
     */
    @ApiModelProperty("订单类型 1：订单，2：维修")
    private Integer orderType;
    /**支付宝付款*/
    @ApiModelProperty("支付宝付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal alipayPayment;
    /**现金付款*/
    @ApiModelProperty("现金付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal cashPayment;
    /**
     * POS机刷卡付款
     */
    @ApiModelProperty("POS机刷卡付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal posCardPayment;
    /**
     * 订单总价
     */
    @ApiModelProperty("订单总价")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal totalPrice;
    /**
     * 代金券付款
     */
    @ApiModelProperty("代金券付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal voucherPayment;
    /**
     * 微信付款
     */
    @ApiModelProperty("微信付款")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal wechatPayment;
    /**
     * 其他付款方式
     */
    @ApiModelProperty("其他付款方式")
    @JSONField(format = "0.##@HALF_UP")
    private BigDecimal otherPayment;
    /**
     * 付款时间
     */
    @ApiModelProperty("付款时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentTime;

    /**商品*/
    @ApiModelProperty("商品")
    private List<Commodity> commodities;

    /**状态*/
    @ApiModelProperty("状态")
    private String status;

    /**
     * ‘pos机刷卡’& POS机的类型为‘商场专用’ 标识
     */
    @ApiModelProperty("商场pos机标识")
    private Boolean marketPosFlag;

    /**
     * 代金券支付&代金券活动类型为‘商场活动’ 标识
     */
    @ApiModelProperty("商场代金券标识")
    private Boolean marketVoucherFlag;

    public static StoreRentStatisticsVo from(Map.Entry<Integer, List<StoreRentOrderVo>> entry) {
        if(entry == null || entry.getValue().isEmpty()){
            return null;
        }
        StoreRentStatisticsVo srs = new StoreRentStatisticsVo();
        entry.getValue().stream().findFirst().ifPresent(firstSro-> setStoreRentStatisticsVo(entry, srs, firstSro));

        return srs;
    }

    private static void setStoreRentStatisticsVo(Map.Entry<Integer, List<StoreRentOrderVo>> entry, StoreRentStatisticsVo srs, StoreRentOrderVo firstSro) {
        srs.setOrderId(firstSro.getOrderId());
        //支付宝付款
        srs.setAlipayPayment(firstSro.getAlipayPayment());
        //现金付款
        srs.setCashPayment(firstSro.getCashPayment());
        //POS机刷卡付款
        srs.setPosCardPayment(firstSro.getPosCardPayment());
        //订单总价
        srs.setTotalPrice(firstSro.getTotalPrice());
        //代金券付款
        srs.setVoucherPayment(firstSro.getVoucherPayment());
        //微信付款
        srs.setWechatPayment(firstSro.getWechatPayment());
        //其他付款方式
        srs.setOtherPayment(firstSro.getOtherPayment());
        //付款时间
        srs.setPaymentTime(firstSro.getPaymentTime());
        //订单类型
        srs.setOrderType(firstSro.getOrderType());
        srs.setStatus(firstSro.getStatus());
        srs.setMarketPosFlag(firstSro.getMarketPosFlag());
        srs.setMarketVoucherFlag(firstSro.getMarketVoucherFlag());
        //商品
        srs.setCommodities(entry.getValue().stream().map(Commodity::from).collect(Collectors.toList()));
    }


    /**
     * <AUTHOR>
     * @since 2021/6/17 14:55
     */
    @Getter
    @Setter
    @ToString
    public static class Commodity {
        /**
         * 商品Id
         */
        @ApiModelProperty("商品Id")
        private Integer id;
        /**
         * 商品名
         */
        @ApiModelProperty("商品名")
        private String name;
        /**
         * 价格（定价）
         */
        @ApiModelProperty("价格（定价）")
        @JSONField(format = "0.##@HALF_UP")
        private BigDecimal price;
        /**
         * 商品类型 0：大件，1：小件，2：维修
         */
        @ApiModelProperty("商品类型 0：大件，1：小件，2：维修")
        private Integer type;
        /**商品数量*/
        @ApiModelProperty("商品数量")
        private Integer count;

        public static Commodity from(StoreRentOrderVo storeRentOrderVo) {
            Commodity commodity = new Commodity();
            commodity.setId(storeRentOrderVo.getCommodityId());
            commodity.setName(storeRentOrderVo.getCommodityName());
            commodity.setPrice(storeRentOrderVo.getCommodityPrice());
            commodity.setType(storeRentOrderVo.getType());
            commodity.setCount(storeRentOrderVo.getCommodityCount());
            return commodity;
        }
    }

}
