package com.jiuji.oa.afterservice.statistics.bo.smallpro;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description: <Class introduction>
 * translation: <Method translation introduction>
 *
 * <AUTHOR>
 * @date 2020/4/30
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproSstaticsWithFilterBO extends SmallproStaticsBO {
    /**
     * 地区Id
     */
    @ApiModelProperty("地区Id")
    private Integer areaId;
    /**
     * 地区编码
     */
    @ApiModelProperty("地区编码")
    private String area;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;
    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;
    /**
     * 用户跳转链接
     */
    @ApiModelProperty("用户跳转链接")
    private String userLink;
}
