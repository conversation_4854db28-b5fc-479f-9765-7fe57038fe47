package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileBatchUploadReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.tc.common.vo.R;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
public interface AttachmentsService extends IService<Attachments> {
    /**
     * 保存附件
     *
     * @param files
     * @param linkId
     * @param type
     * @param userId
     * @return
     */
    Boolean saveAttachemnts(List<FileReq> files, Integer linkId, Integer type, Integer userId, LocalDateTime creationTime);

    /**
     * 保存附件
     * 可以置空
     * @param files
     * @param linkId
     * @param type
     * @param userId
     * @return
     */
    Boolean saveAttachemntsV2(List<FileReq> files, Integer linkId, Integer type, Integer userId, LocalDateTime creationTime);

    /**
     * 保存附件
     *
     * @param files
     * @param linkId
     * @param type
     * @param userId
     * @param kind
     * @return
     */
    Boolean saveAttachemnts(List<FileReq> files, Integer linkId, Integer type, Integer userId, int kind, LocalDateTime creationTime);

    /**
     * 保存附件
     *
     * @param files
     * @param linkId
     * @param type
     * @param userId
     * @param kind
     * @return
     */
    Boolean saveAttachemntsV2(List<FileReq> files, Integer linkId, Integer type, Integer userId, int kind, LocalDateTime creationTime);

    /**
     * 保存或更新附件
     *
     * @param files
     * @param linkId
     * @param type
     * @param userId
     * @param kind
     */
    void saveOrUpdate(List<FileReq> files, Integer linkId, Integer type, Integer userId, int kind, LocalDateTime creationTime);

    /**
     * 通过linkId获取附件
     *
     * @param linkId
     * @param type
     * @return
     */
    List<Attachments> getAttachmentsByLinkId(Integer linkId, Integer type, LocalDateTime creationTime);

    /**
     * 通过linkId获取附件
     *
     * @param linkId
     * @param fid
     * @return
     */
    List<Attachments> getAttachmentsByFid(Integer linkId, String fid, Integer type, LocalDateTime creationTime);

    /**
     * 获取附件列表
     *
     * @param linkId
     * @param type
     * @return
     */
    List<FileReq> getAttachmentsList(Integer linkId, Integer type, Integer kind, LocalDateTime creationTime);

    /**
     * 批量保存附件
     *
     * @param list
     * @return
     */
    Boolean saveBatchAttachments(List<Attachments> list);

    /**
     * 断言附件是否允许保存
     *
     * @param attachment
     */
    void assertSave(Attachments attachment);

    /**
     * 根据关联ID、表类型，获取附件信息
     *
     * @param type
     * @param linkIds
     * @return
     */
    List<Attachments> listAttachments(Integer type, Set<Integer> linkIds, LocalDateTime creationTime);


    /**
     * 根据关联ID、表类型，获取附件信息
     *
     * @param linkId
     * @param type
     * @param kind   默认为0
     * @param isAll  默认为false
     * @return
     */
    List<Attachments> getAttachmentsByLinkId(Integer linkId, Integer type, Integer kind, Boolean isAll, LocalDateTime creationTime);

    /**
     * 附件添加
     *
     * @param attachments
     * @param linkId
     * @param attachmentType
     * @param kind           售后标识，1为内部查看，客户不可看见 默认为0
     * @param userId
     * @return
     */
    Boolean attachmentsAdd(List<FileReq> attachments, Integer linkId, Integer attachmentType, Integer kind, Integer userId, LocalDateTime creationTime);

    /**
     * 转换PO  -> REQ
     *
     * @param list
     * @return
     */
    List<FileReq> convertList(List<Attachments> list);

    /**
     * 泛化方法通过linkid获取列表再做集合操作
     *
     * @param ids
     * @return
     */
    List<Attachments> getListByLinkIds(List<Integer> ids, Integer type, LocalDateTime creationTime);

    R<Boolean> saveBatchFiles(FileBatchUploadReq req);

    /**
     * 根据关联ID、表类型，获取附件信息
     * @param queryWrapper
     * @param type
     * @param creationTime
     * @return
     */
    List<Attachments> newList(Wrapper<Attachments> queryWrapper, Integer linkId, Integer type, LocalDateTime creationTime);
}
