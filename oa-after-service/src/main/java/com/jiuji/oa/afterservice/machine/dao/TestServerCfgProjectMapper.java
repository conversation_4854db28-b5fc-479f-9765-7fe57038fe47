package com.jiuji.oa.afterservice.machine.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.machine.bo.ServerProjectBO;
import com.jiuji.oa.afterservice.machine.po.MachineTestServerProjectCfgPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/13 16:48
 */
@Mapper
public interface TestServerCfgProjectMapper extends BaseMapper<MachineTestServerProjectCfgPO> {
    /**
     * 插入步骤
     * @param serverProject
     * @param userName
     * @return
     */
    int insertProject(@Param("serverProject") ServerProjectBO serverProject, @Param("userName") String userName);

    /**
     * 更新步骤
     * @param serverProject
     * @return
     */
    int updateProject(@Param("serverProject") ServerProjectBO serverProject);

    int updateProjectItemRank(@Param("projectItems") List<ServerProjectBO.ProjectItemBO> projectItems, @Param("projectId") Integer projectId);

    /**
     * 获取步骤的更新日志
     * @param serverProject
     * @return
     */
    String selectUpdateProjectLog(@Param("serverProject") ServerProjectBO serverProject);

    /**
     * 获取项目的更新日志
     * @param projectItems
     * @param projectId
     * @return
     */
    String selectUpdateItemLog(@Param("projectItems") List<ServerProjectBO.ProjectItemBO> projectItems, @Param("projectId") Integer projectId);
}
