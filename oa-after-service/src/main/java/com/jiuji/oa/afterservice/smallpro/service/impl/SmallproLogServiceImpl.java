package com.jiuji.oa.afterservice.smallpro.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.oa.afterservice.other.document.SmallproLogDocument;
import com.jiuji.oa.afterservice.other.po.LogParamDocument;
import com.jiuji.oa.afterservice.other.repository.SmallproLogRepository;
import com.jiuji.oa.afterservice.smallpro.service.SmallproLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: gengjiaping
 * @date: 2019/11/14
 */
@Service
public class SmallproLogServiceImpl implements SmallproLogService {

    @Resource
    private SmallproLogRepository smallproLogRepository;

    @Override
    public String addLogs(Integer smallProId, String comment, String inUser, Integer showType) {
        SmallproLogDocument smallProLog = new SmallproLogDocument(null, smallProId, comment, inUser, showType);
        SmallproLogDocument saved = smallproLogRepository.save(smallProLog);
        return saved.getId().toString();
    }

    @Override
    public String addLogs(Integer smallProId, String comment, Map<String, LogParamDocument> params,
                          String inUser, Integer showType) {
        SmallproLogDocument smallProLog = new SmallproLogDocument(null, smallProId, comment, inUser, showType);
        smallProLog.setParams(params);
        smallProLog.setCommentType(1);
        SmallproLogDocument saved = smallproLogRepository.save(smallProLog);
        return saved.getId().toString();
    }

    @Override
    public SmallproLogDocument getSmallproLogById(String id) {
        Optional<SmallproLogDocument> smallproLogDocument = smallproLogRepository.findById(id);
        return smallproLogDocument.orElse(null);
    }

    @Override
    public List<SmallproLogDocument> findBySmallproId(Integer smallproId) {
        List<SmallproLogDocument> logDocuments = smallproLogRepository.findAllBySmallproIdEquals(smallproId);
        if (CollectionUtils.isEmpty(logDocuments)) {
            return new ArrayList<>();
        }
        return logDocuments.stream()
                .sorted(Comparator.comparing(SmallproLogDocument::getInDate))
                .collect(Collectors.toList());
    }

}
