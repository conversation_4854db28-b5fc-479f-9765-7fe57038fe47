package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jiuji.oa.afterservice.bigpro.bo.GetPayAuthUrlBO;
import com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.OpenValidInfoBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper;
import com.jiuji.oa.afterservice.bigpro.enums.PayPortTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.WeixinUser;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.TuiHuanOpenIdService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.bigpro.vo.res.MemberWXAuthRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.ShortXtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.WeixinConfigBO;
import com.jiuji.oa.afterservice.other.service.PayConfigService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 第三方验证服务类
 * <AUTHOR>
 * @since 2021/11/1 16:30
 */
@Slf4j
@Service
public class TuiHuanOpenIdServiceImpl implements TuiHuanOpenIdService {
    @Autowired
    private SysConfigClient sysConfigClient;
    @Autowired
    private ShouhouRefundMapper shouhouRefundMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    /**
     * 获取验证地址
     */
    @Override
    public Optional<OpenIdInfoBo.OpenIdUrlBo> getValidUrl(OpenValidInfoBo validInfoBo, OaUserBO oaUser){
        String url = "";
        int apiType = 0;
        OpenIdInfoBo.OpenType openType = validInfoBo.getOpenType();
        switch (openType){
            case ALIPAY:
                url = StrUtil.format("{}/notifypay/alipayUserInfo/index.aspx?id={}_{}_{}&areaid={}"
                        , sysConfigClient.getValueByCode(SysConfigConstant.M_URL).getData(), validInfoBo.getId(),
                        validInfoBo.getIdTypeEnum().getCode(), oaUser.getUserId(), oaUser.getAreaId());
                break;
            case WE_CHAT:
                //String webUrl 0,Integer wxId 1
                Tuple weiXinTuple = getWeiXinTuple(oaUser);
                String webUrl = weiXinTuple.get(0);
                Integer wxId = weiXinTuple.get(1);
                //检验是否已经绑定微信,没绑定提示进行绑定
                if(ObjectUtil.defaultIfNull(validInfoBo.getUserId(),0)>0 && SpringUtil.getBean(WeixinUserService.class)
                        .lambdaQuery().eq(WeixinUser::getKinds, WeixinUser.WeiXinKindsEnum.WEI_XIN.getCode()).eq(WeixinUser::getUserid,validInfoBo.getUserId())
                        .count()<=0){
                    //生成绑定二维码地址 绑定二维码 apiType = 1
                    apiType = 1;
                    String wechatInfoUrl = StrUtil.format("{}/ajaxOperate.aspx?act=getAlterMemberInfoQR&userid={}", webUrl, validInfoBo.getUserId());
                    log.info("生成微信绑定二维码地址:{}",wechatInfoUrl);
                    url = HttpUtil.get(wechatInfoUrl);
                }else{
                    //生成场景二维码地址
                    String wechatInfoUrl = StrUtil.format("{}/oaapi/api.ashx?act=GetWeixinSceneQrCode&scene=shtuiPay&remark={}&wxId={}", webUrl, validInfoBo.getId(), wxId);
                    log.info("生成微信验证二维码地址:{}",wechatInfoUrl);
                    url = HttpUtil.get(wechatInfoUrl);
                }

                break;
            default:
                break;
        }
        log.info("{}验证地址:{}",openType.getMessage(),url);
        return Optional.of(new OpenIdInfoBo.OpenIdUrlBo().setUrl(url).setUrlType(openType.getUrlType()).setApiType(apiType)
                .setType(openType.getCode()).setName(openType.getMessage()));
    }

    /**
     * 获取验证地址
     */
    @Override
    public Optional<OpenIdInfoBo.OpenIdUrlBo> getValidUrlV2(OpenValidInfoBo validInfoBo, OaUserBO oaUser){
        int apiType = 0;
        OpenIdInfoBo.OpenType openType = validInfoBo.getOpenType();

        GetPayAuthUrlBO.GetPayAuthUrlInput input = LambdaBuild.create(GetPayAuthUrlBO.GetPayAuthUrlInput.class)
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setSubId, validInfoBo.getSubId())
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setSubType, validInfoBo.getOrderBusinessTypeEnum().getCode())
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setOrderId, validInfoBo.getId())
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setType, validInfoBo.getIdTypeEnum().getCode())
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setAreaId, oaUser.getAreaId())
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setPortType, PayPortTypeEnum.TRANSFER_WX.getCode())
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setCh999Id, oaUser.getUserId())
                .set(GetPayAuthUrlBO.GetPayAuthUrlInput::setKinds, validInfoBo.getOpenType().getCode())
                .build();
        GetPayAuthUrlBO param = new GetPayAuthUrlBO();
        param.setInput(input);
        String paramJson = JSONUtil.toJsonStr(param);
        String getPayAuthUrl = StrUtil.format("{}/oaapi.svc/rest/GetPayAuthUrl"
                , sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST).getData());
        HttpResponse response = HttpUtil.createPost(getPayAuthUrl).body(paramJson).execute();
        log.info("获取支付配置url，传入参数：{}，返回结果：{}",paramJson, JSONUtil.toJsonStr(response));
        String url = "";
        if (!response.isOk() || StrUtil.isBlank(response.body())) {
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "调用C#获取支付url异常: 参数{}", paramJson);
        } else {
            R<String> r = JSON.parseObject(response.body(), new TypeReference<R<String>>() {});
            if (r.isSuccess()) {
                url = r.getData();
            } else {
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "调用C#获取支付url失败: 参数{}, 原因{}", paramJson, r.getUserMsg());
            }
        }
        log.info("{}验证地址:{}",openType.getMessage(),url);
        return Optional.of(new OpenIdInfoBo.OpenIdUrlBo().setUrl(url).setUrlType(OpenIdInfoBo.OpenType.WE_CHAT.getUrlType()).setApiType(apiType)
                .setType(openType.getCode()).setName(openType.getMessage()));
    }

    /**
     *
     * @param oaUser
     * @return String webUrl 0,Integer wxId 1
     */
    @Override
    public Tuple getWeiXinTuple(OaUserBO oaUser){
        //获取当前租户的微信id
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        PayConfigService payConfigService = SpringUtil.getBean(PayConfigService.class);
        Integer wxId = sysConfigService.getWxIdByAreaId(oaUser.getAreaId());
        String webUrl = sysConfigClient.getValueByCode(SysConfigConstant.WEB_URL).getData();
        //获取微信支付配置
        int shortXtenant = CommonUtils.toShotWebXtenant(oaUser.getXTenant());
        if (shortXtenant == ShortXtenantEnum.ITENG.getCode() || shortXtenant == ShortXtenantEnum.ZLF.getCode()) {
            List<WeixinConfigBO> weixinConfigs = payConfigService.getWechatConfig().stream()
                    .filter(wcc -> Objects.equals(wcc.getXTenant(), oaUser.getXTenant())).collect(Collectors.toList());
            if(log.isDebugEnabled()){
                log.info("微信配置信息:",JSON.toJSONString(weixinConfigs, SerializerFeature.PrettyFormat));
            }
            Optional<WeixinConfigBO> wxPayOpt = Optional.of(weixinConfigs).filter(wxcs->wxcs.size() < NumberConstant.TWO).map(wxcs->wxcs.stream().findFirst())
                    //超过两个继续过滤
                    .orElseGet(()->weixinConfigs.stream()
                            .filter(wcc -> CommenUtil.isNullOrZero(wcc.getAuthorizeId()) || Objects.equals(wcc.getAuthorizeId(), oaUser.getAuthorizeId()))
                            .filter(wcc -> CollUtil.isEmpty(wcc.getLimitAreaArr()) || CollUtil.contains(wcc.getLimitAreaArr(), oaUser.getAreaId()))
                            .filter(wcc -> wcc.getKinds() == 5).findFirst());
            if (wxPayOpt.filter(wp->StrUtil.isNotBlank(wp.getHostUrl())).isPresent()) {
                webUrl = wxPayOpt.get().getHostUrl();
                wxId = wxPayOpt.get().getWxId();
            }
        }
        return new Tuple(webUrl,wxId);
    }

    /**
     * 获取用户信息
     * @param openType
     * @param validInfoBo
     * @param oaUser
     * @return
     */
    @Override
    public Optional<OpenIdInfoBo> getOpenUserInfo(OpenIdInfoBo.OpenType openType, OpenValidInfoBo validInfoBo, OaUserBO oaUser) {
        Optional<OpenIdInfoBo> userInfoOpt = Optional.ofNullable(shouhouRefundMapper.getRefundUserInfo(validInfoBo.getId(), validInfoBo.getIdTypeEnum().getCode()))
                .map(ui -> ui.setName(openType.getMessage()));
        //不存在从缓存中获取 用户信息
        if (!userInfoOpt.isPresent()) {
            switch (openType) {
                case ALIPAY:
                    userInfoOpt = getMemberAlipayAuthInfo(validInfoBo.getId(),validInfoBo.getIdTypeEnum()).map(maa -> new OpenIdInfoBo().setName(openType.getMessage())
                            .setType(openType.getCode()).setUserId(maa.getUser_id()).setNickName(maa.getNick_name()).setUserAvatar(maa.getAvatar()));
                    break;
                case WE_CHAT:
                    userInfoOpt = getMemberWXAuthInfo(validInfoBo.getPayOpenId(),oaUser)
                            .map(dic -> new OpenIdInfoBo().setName(openType.getMessage()).setType(openType.getCode())
                                    .setUserId(Optional.ofNullable(dic.getStr("Id")).orElse(null))
                                    .setNickName(dic.getStr("nickname")).setUserAvatar(dic.getStr("headimgurl")))
                            .filter(oii -> StrUtil.isNotBlank(oii.getUserId()));
                    break;
                default:
                    break;
            }
        }

        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId());
        //异步将数据写入表,sql控制避免重复写入
        userInfoOpt.filter(userInfo -> Objects.isNull(userInfo.getId()))
                .filter(userInfo -> StrUtil.isNotBlank(userInfo.getUserAvatar()) || StrUtil.isNotBlank(userInfo.getNickName()))
                .ifPresent(userInfo -> {
                    if(log.isDebugEnabled()){
                        log.info("id类型:{},退款id:{},{}用户验证信息{}",validInfoBo.getIdTypeEnum().getMessage(),
                                validInfoBo.getId(),openType.getMessage(),JSON.toJSONString(userInfo));
                    }
                    CompletableFuture.runAsync(() -> shouhouRefundMapper.insertRefundUserInfo(userInfo, validInfoBo.getId()
                            , validInfoBo.getIdTypeEnum().getCode(), oaUserOpt.orElse(null)))
                            .exceptionally(e -> {
                                RRExceptionHandler.logError("写入验证用户信息",Dict.create().set("userInfo",userInfo)
                                        .set("validInfoBo",validInfoBo).set("oaUser",oaUserOpt.orElse(null)),e,
                                        SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                                return null;
                            });
                });

        return userInfoOpt;
    }

    /**
     *  获取微信验证信息
     * @param payOpenId
     * @param oaUser
     * @return
     */
    @Override
    public Optional<Dict> getMemberWXAuthInfo(String payOpenId, OaUserBO oaUser) {
        //String webUrl 0,Integer wxId 1
        Tuple weiXinTuple = getWeiXinTuple(oaUser);
        String webUrl = weiXinTuple.get(0);
        Integer wxId = weiXinTuple.get(1);
        String infoUrl = StrUtil.format("{}/oaapi/api.ashx?act=wxUserInfo&openId={}&wxId={}", webUrl, payOpenId, wxId);
        log.info("微信验证请求url: {}",infoUrl);
        return Optional.ofNullable(HttpUtil.get(infoUrl)).filter(StrUtil::isNotBlank).filter(body -> JSONUtil.isJson(body))
                .map(json -> JSON.parseObject(json,new TypeReference<R<Dict>>(){}))
                .map(R::getData);
    }

    /**
     * 获取阿里验证信息
     * @param tuihuanId
     * @param idTypeEnum
     * @return
     */
    @Override
    public Optional<MemberWXAuthRes.MemberAlipayAuth> getMemberAlipayAuthInfo(Integer tuihuanId, OpenValidInfoBo.IdTypeEnum idTypeEnum) {
        int type = idTypeEnum.getCode();
        return Optional.ofNullable(stringRedisTemplate.opsForValue().get(StrUtil.format("urn:memberalipayauth:{}_{}", tuihuanId,type)))
                .filter(StrUtil::isNotBlank).map(json -> JSON.parseObject(json, MemberWXAuthRes.MemberAlipayAuth.class));
    }

    @Override
    public OpenIdInfoBo.OpenType getOpenType(String tuiWay) {
        OpenIdInfoBo.OpenType openType;
        switch (Optional.ofNullable(tuiWay).orElse("")) {
            case "支付宝秒退":
                openType = OpenIdInfoBo.OpenType.ALIPAY;
                break;
            case "微信秒退":
                openType = OpenIdInfoBo.OpenType.WE_CHAT;
                break;
            default:
                //不用验证的退款方式
                return null;
        }
        return openType;
    }
}
