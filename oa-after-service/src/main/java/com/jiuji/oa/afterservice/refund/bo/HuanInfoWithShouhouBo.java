package com.jiuji.oa.afterservice.refund.bo;

import com.jiuji.oa.afterservice.bigpro.enums.WxStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 退换信息之售后订单
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Data
@Accessors(chain = true)
@ApiModel("退换信息之售后订单信息Bo")
public class HuanInfoWithShouhouBo {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private Integer subId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Integer userId;
    /**
     * 售后单名称
     */
    @ApiModelProperty(value = "售后单名称")
    private String name;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;
    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String productColor;
    /**
     * 串号
     */
    @ApiModelProperty(value = "串号")
    private String imei;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Integer productId;
    /**
     * skuid
     */
    @ApiModelProperty(value = "skuid")
    private Integer ppriceid;
    /**
     * 售后单状态
     *
     * @see WxStatusEnum
     */
    @ApiModelProperty(value = "维修状态：0处理中，1已修好，3修不好")
    private Integer stats;
    /**
     * 是否保修
     */
    @ApiModelProperty(value = "保修状态：0不在保，1在保,2外修")
    private Integer baoxiu;

}
