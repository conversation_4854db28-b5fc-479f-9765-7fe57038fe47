package com.jiuji.oa.afterservice.common.event;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.event.bo.AfterRollBackEvent;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 事务事件监听
 * <AUTHOR>
 * @since 2023/1/4 23:14
 */
@Slf4j
@Component
public class MyTransactionListener {

    /**
     * 事务回滚事件的监听 发布事件的地方需要开启事务
     * @param event
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_ROLLBACK)
    public void onAfterRollBackEvent(AfterRollBackEvent event) {
        try {
            R rbR = event.rollback();
            if(log.isDebugEnabled()){
                log.debug("事务补偿回滚,参数: {} 类: {}",event.getArgs(),event.getSource().getClass().getSimpleName());
            }
            if (!rbR.isSuccess()){
                throw new CustomizeException(JSON.toJSONString(rbR));
            }
        } catch (Throwable e) {
            //回滚异常,发送消息通知
            RRExceptionHandler.logError("事务补偿回滚",event.getArgs(),e,
                    SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }

    }
}
