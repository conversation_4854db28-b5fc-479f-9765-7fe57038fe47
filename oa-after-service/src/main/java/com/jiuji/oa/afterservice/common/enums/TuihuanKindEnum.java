package com.jiuji.oa.afterservice.common.enums;

import com.jiuji.oa.afterservice.bigpro.bo.OpenValidInfoBo;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: gengjiaping
 * @date: 2020/3/16
 */
@Getter
@AllArgsConstructor
public enum  TuihuanKindEnum implements CodeMessageEnumInterface {
    HJT(1,"换机头", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    HZB(2,"换主板", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    TK(3,"退款", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    HQTXH(4,"换其它型号", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    TWXF(5,"退维修费", OpenValidInfoBo.OrderBusinessTypeEnum.WEIXIU),
    // 新机单退订金
    TDJ(6,"退订金", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    // 新机单退配件
    TPJ(7,"退配件", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    TDJ_LP(8,"退订金(良品)", OpenValidInfoBo.OrderBusinessTypeEnum.LIANGPIN),
    SMALL_PRO_REFUND(9,"小件换货", OpenValidInfoBo.OrderBusinessTypeEnum.SHOUHOU_XIAOJIAN),
    SMALL_PRO_REFUND_REPAIR_FEE(10,"小件退维修费", OpenValidInfoBo.OrderBusinessTypeEnum.SHOUHOU_XIAOJIAN),
    TDJ_WXF(11,"退订金(维修费)", OpenValidInfoBo.OrderBusinessTypeEnum.WEIXIU),
    BATCH_TK(12,"大件批量退款", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    SMALL_PRO_HQTXH(13,"小件换货(换其他型号)", OpenValidInfoBo.OrderBusinessTypeEnum.ORDER),
    // 良品退款 这个类型不入库 入库 是 TK
    TK_LP(99,"良品退款", OpenValidInfoBo.OrderBusinessTypeEnum.LIANGPIN)
    ;
    /**
     * 类别
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
    /**
     * 退款三方订单类型枚举
     */
    private OpenValidInfoBo.OrderBusinessTypeEnum orderBusinessTypeEnum;

    public static TuihuanKindEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {

            return null;
        }
        for (TuihuanKindEnum tuihuanKind : values()) {
            if (tuihuanKind.getCode().equals(code)) {

                return tuihuanKind;
            }
        }
        return null;
    }

    public static List<TuihuanKindEnum> listMachineRefundKind(){
        return Stream.of(HJT, HZB, TK, HQTXH).collect(Collectors.toList());
    }
}
