package com.jiuji.oa.afterservice.refund.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 发票类型
 */
@Getter
@AllArgsConstructor
public enum TransportNewSubEnum implements CodeMessageEnumInterface {
    TRANSPORT(0,"是"),
    UNABLE_TO_TRANSFER(1,"否");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**
     * @param code
     * @return
     */
    public static TransportNewSubEnum getEnumByCode(Integer code) {
        TransportNewSubEnum[] value = values();
        for (TransportNewSubEnum e : value) {
            if (Objects.equals(e.getCode(),code)) {
                return e;
            }
        }
        return null;
    }
}
