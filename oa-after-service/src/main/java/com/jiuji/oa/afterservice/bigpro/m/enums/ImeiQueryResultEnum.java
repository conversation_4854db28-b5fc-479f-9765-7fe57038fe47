package com.jiuji.oa.afterservice.bigpro.m.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImeiQueryResultEnum implements CodeMessageEnumInterface {

    STATUS_WX_SUCCESS(1,"外修查询成功","此串号查询结果为非{0}购买"),
    STATUS_WX_FAIL(2,"外修查询失败","IMEI 查询失败，请确认IMEI是否正确，或使用其他途径进行接件"),
    STATUS_ZX(3,"九机卖出",""),
    STATUS_KC(4,"库存机器","此串号查询结果为库存机器")
    ;
    /**
     * 编码
     */
    private Integer code;

    /**
     * 编码对应信息
     */
    private String message;

    /**
     * 提示文案信息
     */
    private String tips;

}
