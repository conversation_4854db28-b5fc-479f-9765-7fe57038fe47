package com.jiuji.oa.afterservice.bigpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description 售后服务拍照上传信息记录表
 * 
 * <AUTHOR> quan
 * @date 2020-05-14 15:53:54
 */
@Data
@TableName("shouhou_fuwupic")
public class ShouhouFuwupic implements Serializable {
	private static final long serialVersionUID = 1L;


	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	/**
	 * 售后id
	 */
	private Integer shouhouid;

	/**
	 * 图片存储ID
	 */
	private Integer picid;

	/**
	 * 图片FID
	 */
	private String fid;

	/**
	 * 是否已上传图片[1=未上传，2=已上传]
	 */
	private Integer fkind;

	/**
	 * 操作人
	 */
	private String inuser;

	/**
	 * 操作时间
	 */
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime indate;

	/**
	 * 取机人
	 */
	private String qujiuser;

	/**
	 * 取机时间
	 */
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime qujidate;

	@TableField(exist = false)
	private String actName;

}
