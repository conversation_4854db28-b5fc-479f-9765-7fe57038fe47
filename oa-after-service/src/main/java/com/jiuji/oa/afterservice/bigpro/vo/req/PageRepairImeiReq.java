package com.jiuji.oa.afterservice.bigpro.vo.req;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageRepairImeiReq {

    /**
     * 串号
     */
    private String imei;

    /**
     * 当前页数
     */
    private Integer current;

    /**
     * 每页数量
     */
    private Integer size;

    /**
     * 返修单id
     */
    private Integer repairOrderId;
    /**
     * 当前售后单id
     */
    private Integer id;
}
