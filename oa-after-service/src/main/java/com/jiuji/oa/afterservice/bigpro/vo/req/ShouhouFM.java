package com.jiuji.oa.afterservice.bigpro.vo.req;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ShouhouFM {
    private String act;
    private Boolean isSoft;
    private Boolean isguanxi;
    private Integer subId;
    private Integer basketId;
    private String comment;
    private String name;
    private String productColor;
    private Long productId;
    private Integer ppriceId;
    private String imei;
    private Long userId;
    private String userClassName;
    private LocalDateTime tradeDate;
    private String mobile;
    private String trueName;
    private String area;
    private String isBaoxiu;
    private String baoxiuEndDate;
    private String isYiwai;
    private String yiwai;
    private String isYanbao;
    private String yanbao;
    private Boolean isXcMkc;
    private String isXcMkcInfo;
    private Integer areaId;
    private Integer mkcId;
    private Integer isHuishou;
    private String isJinshuibao;
    private Integer zpPpid;
    private String zsRemark;
    private Integer mobileServiceType;
}