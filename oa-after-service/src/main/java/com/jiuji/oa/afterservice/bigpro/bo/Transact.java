package com.jiuji.oa.afterservice.bigpro.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description: 售后退换机办理
 * @author: Li quan
 * @date: 2020/7/14 20:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class Transact {

    /**
     * 售后Id
     */
    private Integer shouhouId;
    private Integer basket_id;

    /**
     * 当前登录地区ID
     */
    private Integer areaid;

    /**
     * 当前登录地区ID
     */
    private List<String> rank;

    /**
     * 当前登录地区kind1
     */
    private int area_kind1;

    /**
     * 操作人
     */
    private String user;
}
