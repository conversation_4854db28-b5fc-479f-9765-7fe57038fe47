package com.jiuji.oa.afterservice.smallpro.recover.enums;

import com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceReport;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouServiceReportRes;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.poi.ss.formula.functions.T;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/4 11:02
 * @Description
 */
@Getter
@AllArgsConstructor
public enum ReceiveWTypeEnum implements CodeMessageEnumInterface {
    RECYCLE_OLD_PART(4, "回收旧件","回收"),
    REPAIR_AND_EXCHANGE(15, "换货旧件","换货");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
    /**
     * 编码对应信息
     */
    private String name;

    public static List<EnumVO> toTitleEnumList() {
        List<EnumVO> enumVOList = new ArrayList();
        for (ReceiveWTypeEnum temp : ReceiveWTypeEnum.values()) {
            EnumVO vo = new EnumVO();
            vo.setLabel(temp.getMessage() + "发货");
            vo.setValue(temp.getCode());
            enumVOList.add(vo);
        }
        return enumVOList;
    }
}
