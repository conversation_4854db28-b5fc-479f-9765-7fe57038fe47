package com.jiuji.oa.afterservice.sys.service;

import com.jiuji.oa.afterservice.sys.bo.UserTokenCode;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * 用户信息服务类
 * <AUTHOR>
 * @since 2021/11/30 14:32
 */
public interface UserTokenService {
    /**
     * 通过识别码回去会员id
     * @param code
     * @return
     */
    Integer getUserIdByToken(String code);

    /**
     * 通过识别码删除缓存
     * @param code
     * @return
     */
    R<Boolean> delUserIdByToken(String code);


    /**
     * 获取所有列表
     * @return
     */
    List<UserTokenCode> getUserTokenList();
}
