package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: <小件商品接件-退货处理状态枚举类>
 * translation: <Smallpro Item Pickup - Return Processing Status Enumeration Class>
 *
 * @param null
 * <AUTHOR>
 * @return
 * @date 18:31 2019/11/18
 * @since 1.0.0
 **/
@Getter
@AllArgsConstructor
public enum SmallProStatsReturnEnum implements CodeMessageEnumInterface {

    /**
     * 小件商品接件退货处理状态 - 编码-编码信息
     */
    SMALL_PRO_STATS_NULL(null, ""),
    SMALL_PRO_STATS_ALL(-1, "所有"),
    SMALL_PRO_STATS_SUBMITTED(0, "已提交"),
    //    SMALL_PRO_STATS_AFTER_REVIEW(1, "售后审核"),
    SMALL_PRO_STATS_SECOND_REVIEW(2, "二次审核"),
    SMALL_PRO_STATS_PROCESSED(3, "已办理");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
}
