//package com.jiuji.oa.afterservice.bigpro.service;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
//import com.baomidou.mybatisplus.core.toolkit.StringUtils;
//import com.jiuji.oa.afterservice.bigpro.bo.*;
//import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
//import com.jiuji.oa.afterservice.bigpro.enums.AttachmentsEnum;
//import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
//import com.jiuji.oa.afterservice.bigpro.enums.SubCollectTypeEnum;
//import com.jiuji.oa.afterservice.bigpro.po.*;
//import com.jiuji.oa.afterservice.bigpro.vo.WeixiuzuKindVo;
//import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
//import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouFuwuDrRes;
//import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouInfoRes;
//import com.jiuji.oa.afterservice.bigpro.vo.res.WxConfigRes;
//import com.jiuji.oa.afterservice.common.bo.OaUserBO;
//import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
//import com.jiuji.oa.afterservice.common.config.properties.ImageProperties;
//import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
//import com.jiuji.oa.afterservice.common.enums.UserClassEnum;
//import com.jiuji.oa.afterservice.common.source.MoaUrlSource;
//import com.jiuji.oa.afterservice.common.util.CommenUtil;
//import com.jiuji.oa.afterservice.smallpro.enums.JiujiJumpUrlEnum;
//import com.jiuji.oa.afterservice.smallpro.enums.JiujiSmsChannelEnum;
//import com.jiuji.oa.afterservice.smallpro.enums.JiujiTenantEnum;
//import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
//import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
//import com.jiuji.oa.orginfo.member.client.MemberClient;
//import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
//import com.jiuji.tc.common.vo.R;
//import com.jiuji.tc.common.vo.ResultCode;
//import com.jiuji.tc.utils.enums.EnumUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.time.Duration;
//import java.time.LocalDateTime;
//import java.util.Collections;
//import java.util.List;
//import java.util.Objects;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.Executor;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
//@Service
//@Slf4j
//public class ShouhouInfoServiceImpl implements ShouhouInfoService {
//
//    @Autowired
//    private AttachmentsService attachmentsService;
//    @Resource
//    private MemberClient memberClient;
//    @Autowired
//    private AreaInfoClient areaInfoClient;
//    @Resource
//    private AbstractCurrentRequestComponent currentRequestComponent;
//    @Autowired
//    private WxkcoutputService wxkcoutputService;
//    @Autowired
//    private ShouhouMsgService shouhouMsgService;
//    @Autowired
//    private WeixinUserService weixinUserService;
//    @Autowired
//    private ShouhouServiceConfigService shouhouServiceConfigService;
//    @Autowired
//    private ShouhouHuishouService shouhouHuishouService;
//    @Autowired
//    private ShouhouRomUpgradeService shouhouRomUpgradeService;
//    @Autowired
//    private ShouhouTixingService shouhouTixingService;
//    @Autowired
//    private ShouhouBusinessinfoService shouhouBusinessinfoService;
//    @Autowired
//    private ShouhouExService shouhouExService;
//    @Autowired
//    private WeixiuzuKindService weixiuzuKindService;
//    @Autowired
//    private ShouhouFuwupicService shouhouFuwupicService;
//    @Autowired
//    private WxconfigoptionService wxconfigoptionService;
//    @Autowired
//    private ShouHouPjService shouHouPjService;
//    @Autowired
//    private ShouhouInfoService shouhouInfoService;
//    @Resource
//    private ImageProperties imageProperties;
//    @Qualifier("pushMessageExecutor")
//    @Autowired
//    private Executor executor;
//    @Autowired
//    private ShouhouMapper baseMapper;
//    @Resource
//    private MoaUrlSource moaUrlSource;
//
//    @Override
//    public String getAreaByAreaId(Integer areaId) {
//        if (CommenUtil.isNullOrZero(areaId)) {
//            return "";
//        }
//        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
//        if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
//            return areaInfoR.getData().getArea();
//        }
//        return "";
//    }
//
//    @Override
//    public AreaInfo getAreaInfoByAreaId(Integer areaId) {
//        if (CommenUtil.isNullOrZero(areaId)) {
//            return null;
//        }
//
//        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
//        if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
//            return areaInfoR.getData();
//        }
//        return null;
//    }
//
//    @Override
//    public ShouhouInfoRes buildShouHouInfo(Shouhou shouhou, ShouhouInfoRes shouhouInfoRes) {
//        Long begin = 0L;
//        Integer shouhouId = shouhou.getId();
//        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
//        BeanUtils.copyProperties(shouhou, shouhouInfoRes);
//        CompletableFuture.runAsync(() -> shouHouPjService.insertSearchHistory(oaUserBO.getUserId(), shouhou.getId() + "|2", "oaSearch"), this.executor);
//        shouhou.setIsweixiu(shouhou.getIsweixiu() == null ? false : shouhou.getIsweixiu());
//
//        // 将imei地址转换成http的url
//        shouhouInfoRes.setImeifid(imageProperties.getImgUrlByFid(shouhou.getImeifid()));
//
//        //接件地查询
//        if (StringUtils.isEmpty(shouhouInfoRes.getArea())) {
//            shouhouInfoRes.setArea(this.getAreaByAreaId(shouhouInfoRes.getAreaid()));
//        }
//        begin = System.currentTimeMillis();
//        //会员等级
//        if (shouhouInfoRes.getUserid() != null) {
//            R<MemberBasicRes> memberBasicResR = memberClient.getMemberBasicInfo(shouhouInfoRes.getUserid().intValue());
//            if (ResultCode.SUCCESS == memberBasicResR.getCode() && memberBasicResR.getData() != null) {
//                shouhouInfoRes.setUserClass(memberBasicResR.getData().getUserClass());
//                shouhouInfoRes.setUserClassName(EnumUtil.getMessageByCode(UserClassEnum.class, memberBasicResR.getData().getUserClass()));
//            }
//        }
//        log.info("会员等级 执行时长>>>" + (System.currentTimeMillis() - begin));
//
//        if (StringUtils.isNotEmpty(shouhouInfoRes.getMobile())) {
//            String ch999User = baseMapper.getCh999User(shouhouInfoRes.getMobile());
//            if (StringUtils.isNotEmpty(ch999User)) {
//                shouhouInfoRes.setCh999User(ch999User);
//            }
//        }
//        ShouhouBo info = this.getOne(shouhou.getId());
//        shouhouInfoRes.setUsername2(info.getUsername2());
//        shouhouInfoRes.setUserMobile(info.getUserMobile());
//
//        if (StringUtils.isNotEmpty(info.getShqd2Name())) {
//            ShouhouInfoRes.ShouhouQudao qudaoInfo = new ShouhouInfoRes.ShouhouQudao();
//            qudaoInfo.setShqdId(info.getShqdId());
//            qudaoInfo.setShqd2Id(info.getShqd2Id());
//            qudaoInfo.setShqd2Name(info.getShqd2Name());
//            qudaoInfo.setQudaoAddTime(info.getQudaoAddTime());
//            shouhouInfoRes.setQudaoInfo(qudaoInfo);
//        }
//
//        shouhouInfoRes.setIsqujiE(info.getIsqujiE());
//        shouhouInfoRes.setIsxinjidan(Objects.equals(shouhouInfoRes.getUserid(), 76783L) ? "新机单" : "客户单");
//
//        //维修配件
//        begin = System.currentTimeMillis();
//        List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouhou.getId());
//        shouhouInfoRes.setHexiaoTabs(hexiaoBoList);
//        if (CollectionUtils.isNotEmpty(hexiaoBoList)) {
//            BigDecimal allPrice = BigDecimal.ZERO;
//            BigDecimal allPrice1 = BigDecimal.ZERO;
//            for (HexiaoBo e : hexiaoBoList) {
//                allPrice = allPrice.add(e.getPrice()).add(e.getPriceGs());
//                allPrice1 = allPrice1.add(e.getPrice1()).add(e.getPriceGs());
//            }
//
//            ShouhouInfoRes.HexiaoSum hexiaoSum = new ShouhouInfoRes.HexiaoSum();
//
//            Double ed = 0.00;
//            if (oaUserBO.getRank().contains("6g3")) {
//                ed = 1.0;
//            } else if (oaUserBO.getRank().contains("6g2")) {
//                ed = 0.5;
//            } else if (oaUserBO.getRank().contains("6g1")) {
//                ed = 0.2;
//            }
//            BigDecimal youhuiM = (allPrice1.subtract(info.getCostprice())).multiply(BigDecimal.valueOf(ed)).subtract((allPrice1.subtract(allPrice)));
//            if (youhuiM.compareTo(BigDecimal.ZERO) < 0) {
//                youhuiM = BigDecimal.ZERO;
//            }
//
//            hexiaoSum.setAllPrice(allPrice);
//            hexiaoSum.setSyYouhuiM(ed == 1.0 ? "无限制" : ed == 0 ? "0元" : youhuiM.toString() + "元");
//            hexiaoSum.setYhPrice(allPrice1.subtract(allPrice));
//            shouhouInfoRes.setHexiaoSum(hexiaoSum);
//        }
//
//        log.info("维修配件 执行时长>>>" + (System.currentTimeMillis() - begin));
//        //附件
//        CompletableFuture<List<FileReq>> filesFuture = CompletableFuture.supplyAsync(() -> attachmentsService.getAttachmentsList(shouhouId, AttachmentsEnum.SHOUHOU.getCode(), 0), executor);
//        //files2
//        CompletableFuture<List<FileReq>> attachmentsFuture = CompletableFuture.supplyAsync(() -> attachmentsService.getAttachmentsList(shouhouId, AttachmentsEnum.SHOUHOU.getCode(), 1), executor);
//        List<FileReq> attachments = null;
//        //微信绑定url
//        String wxBindUrl = "";
//        CompletableFuture<String> wxBindUrlFuture = CompletableFuture.supplyAsync(() -> weixinUserService.getWxBindUrl(info.getUserid() == null ? 0 : info.getUserid().intValue(), shouhouInfoRes.getAreaid(), String.valueOf(shouhouId), 2), executor);
//        shouhouInfoRes.setNowareaId(info.getNowarea());
//
//        //当前地区
//        if (CommenUtil.isNotNullZero(info.getNowarea())) {
//            shouhouInfoRes.setNowarea(shouhouInfoService.getAreaByAreaId(info.getNowarea()));
//        }
//
//        if (info.getTuihuanKind() != null) {
//            shouhouInfoRes.setTuihuanKind(info.getTuihuanKind());
//            shouhouInfoRes.setTuihuanKindText(EnumUtil.getMessageByCode(TuihuanKindEnum.class, info.getTuihuanKind()));
//        }
//        begin = System.currentTimeMillis();
//        if (CommenUtil.isNotNullZero(info.getAreaZh())) {
//            shouhouInfoRes.setAreaZh(info.getAreaZh());
//            shouhouInfoRes.setAreaZhName(shouhouInfoService.getAreaByAreaId(info.getAreaZh()));
//        }
//
//        if (info.getToareaZh() != null && info.getToareaZh() != 0) {
//            shouhouInfoRes.setToareaZh(info.getToareaZh());
//            AreaInfo areaInfo = shouhouInfoService.getAreaInfoByAreaId(info.getToareaZh());
//            if (areaInfo != null) {
//                shouhouInfoRes.setToareaZhName(areaInfo.getAreaName());
//                shouhouInfoRes.setToareaZhText(areaInfo.getArea());
//            }
//        }
//        log.info("转地区 执行时长>>>" + (System.currentTimeMillis() - begin));
//        //重大办
//        begin = System.currentTimeMillis();
//        ShouHouImportantBo zdb = baseMapper.isSubCollectZdb(shouhouId);
//        if (zdb != null) {
//            shouhouInfoRes.setZdb(zdb);
//        }
//        log.info("重大办 执行时长>>>" + (System.currentTimeMillis() - begin));
//        // 订单关注情况
//        begin = System.currentTimeMillis();
//        Boolean isSubCollect = this.isSubCollect(shouhouId, oaUserBO.getUserId(), SubCollectTypeEnum.WX.getCode());
//        //  订单是否存在接待记录
//        Boolean isSubReception = this.isSubReception(shouhouId, 2);
//        shouhouInfoRes.setIsSubCollect(isSubCollect);
//        shouhouInfoRes.setIsSubReception(isSubReception);
//        log.info("订单关注情况 执行时长>>>" + (System.currentTimeMillis() - begin));
//        //是否有提醒
//        begin = System.currentTimeMillis();
//        List<ShouhouTixing> txList = shouhouTixingService.getShouhouTixing(shouhouId);
//        shouhouInfoRes.setIsTx(CollectionUtils.isNotEmpty(txList));
//        if (CollectionUtils.isNotEmpty(txList)) {
//            shouhouInfoRes.setShouhouTx(txList.get(0));
//        }
//        log.info("是否有提醒 执行时长>>>" + (System.currentTimeMillis() - begin));
//        //ShouhouRomUpgrade 获取容量升级物流信息
//        List<ShouhouRomUpgrade> sruList = shouhouRomUpgradeService.list(new QueryWrapper<ShouhouRomUpgrade>().lambda().eq(ShouhouRomUpgrade::getShouhouId, shouhouId));
//        if (CollectionUtils.isNotEmpty(sruList)) {
//            shouhouInfoRes.setExpressInfo(sruList.get(0));
//        }
//        //区域对象信息
//        begin = System.currentTimeMillis();
//        if (info.getNowarea() != null) {
//            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(info.getNowarea());
//            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
//                shouhouInfoRes.setAreaInfoOne(areaInfoR.getData());
//            }
//        }
//        AreaInfo areaSubject = this.getAreaSubject(info.getNowarea());
//        shouhouInfoRes.setAreaSubject(areaSubject);
//        log.info("区域对象信息 执行时长>>>" + (System.currentTimeMillis() - begin));
//
//        begin = System.currentTimeMillis();
//        //根据维修单编号获取中邮送修人客户姓名及联系电话
//        R<ShouhouBusinessinfo> ShouhouBusinessinfoR = shouhouBusinessinfoService.getZySxUserinfo(shouhouId);
//        if (ShouhouBusinessinfoR.getCode() == ResultCode.SUCCESS && ShouhouBusinessinfoR.getData() != null) {
//            shouhouInfoRes.setBItem(ShouhouBusinessinfoR.getData());
//        }
//        log.info("中邮送修人 执行时长>>>" + (System.currentTimeMillis() - begin));
//        //售后维修单赠品
//        begin = System.currentTimeMillis();
//        ShouHouZengpinBo zengPin = shouhouExService.getZengpinByShouhouid(shouhouId, 0);
//        ShouHouZengpinBo zengPin1 = shouhouExService.getZengpinByShouhouid(shouhouId, 1);
//        shouhouInfoRes.setZengPin(zengPin);
//        shouhouInfoRes.setZengPin1(zengPin1);
//        log.info("售后维修单赠品 执行时长>>>" + (System.currentTimeMillis() - begin));
//        //获取取机已审核信息
//        begin = System.currentTimeMillis();
//        String checkInfo = shouhouExService.getCheckInfo(shouhouId, info);
//        String qjCheck = shouhouExService.getCheckIng(shouhouId, info);
//        shouhouInfoRes.setCheckInfo(checkInfo);
//        shouhouInfoRes.setQjCheck(qjCheck);
//        log.info("取机已审核信息 执行时长>>>" + (System.currentTimeMillis() - begin));
//
//        //库存ppids
//        begin = System.currentTimeMillis();
//        List<HexiaoBo> hxList = wxkcoutputService.getHexiao(shouhouId);
//        List<Integer> kcOutPpids = Collections.emptyList();
//        if (CollectionUtils.isNotEmpty(hxList)) {
//            kcOutPpids = hxList.stream().filter(e -> e.getPpid() != null).map(e -> e.getPpid()).collect(Collectors.toList());
//            shouhouInfoRes.setKcOutPpids(kcOutPpids);
//        }
//        log.info("库存ppids 执行时长>>>" + (System.currentTimeMillis() - begin));
//        //服务出险记录
//        begin = System.currentTimeMillis();
//        List<ShouhouServiceOutBo> serviceOutInfo = shouhouExService.getServersList(info.getImei(), shouhouId);
//        if (CollectionUtils.isNotEmpty(serviceOutInfo)) {
//            serviceOutInfo = serviceOutInfo.stream().map(s -> {
//                s.setServiceName(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, s.getServiceType()));
//                return s;
//            }).collect(Collectors.toList());
//            shouhouInfoRes.setServiceOutInfo(serviceOutInfo);
//        }
//        log.info("服务出险记录 执行时长>>>" + (System.currentTimeMillis() - begin));
//
//        //维修组
//        begin = System.currentTimeMillis();
//        List<WeixiuzuKindVo> wxzKind = weixiuzuKindService.getWxGroupList();
//        shouhouInfoRes.setWxzKind(wxzKind);
//
//        log.info("维修组 执行时长>>>" + (System.currentTimeMillis() - begin));
//
//        //获取最后一条测试数据信息
//        ShouhoutestInfo testInfo = shouhouExService.getLastTestInfoByShId(shouhouId);
//        if (testInfo != null) {
//            shouhouInfoRes.setTestInfo(testInfo);
//        }
//        //所有故障类型
//        shouhouInfoRes.setTroubleList(shouhouExService.getTroubleList());
//        shouhouInfoRes.setTroubleIds(shouhouExService.getShouhouTroubles(shouhouId));
//
//        // fuwuShowType
//        Integer fuwuShowType = 0;
//        ShouhouFuwupic shPic = shouhouFuwupicService.getFuwuPicInfo(shouhouId);
//
//        Integer baoxiu = shouhouInfoRes.getBaoxiu() == null ? 0 : shouhouInfoRes.getBaoxiu();
//        if (baoxiu != 2) {
//            fuwuShowType = 1;
//            if (shPic != null) {
//                fuwuShowType = 2;
//                ShouhouFuwuDrRes fuwuDr = new ShouhouFuwuDrRes();
//                fuwuDr.setFuwuPicFid(shPic.getFid());
//                fuwuDr.setFuwuPicQuji(shPic.getFkind() == 2 || StringUtils.isNotEmpty(shPic.getFid()) && shouhouInfoRes.getServiceType() != 0);
//                shouhouInfoRes.setFuwuDR(fuwuDr);
//            }
//        }
//        shouhouInfoRes.setFuwuShowType(fuwuShowType);
//        String wxConfig = info.getWxConfig();
//
//        //wxConfig 维修机型配置
//        WxConfigRes wxconfig = new WxConfigRes();
//        if (StringUtils.isNotEmpty(wxConfig)) {
//            // id|cfg1|cfg2|cfg3
//            String[] arr = wxConfig.split("\\|");
//            if (arr.length > 0) {
//                wxconfig = wxconfigoptionService.getWxConfig(Integer.valueOf(arr[0]));
//            } else {
//                //老系统的一个bug  售后单没有配置过 但是因为代码bug 默认给了第一个
//                wxconfig = wxconfigoptionService.getWxConfig(1);
//            }
//            if (arr.length > 1) {
//                wxconfig.setConfig1Options(wxconfigoptionService.getWxConfigOptionBy(arr[1]));
//            }
//            if (arr.length > 2) {
//                wxconfig.setConfig2Options(wxconfigoptionService.getWxConfigOptionBy(arr[2]));
//            }
//            if (arr.length > 3) {
//                wxconfig.setConfig3Options(wxconfigoptionService.getWxConfigOptionBy(arr[3]));
//            }
//            if (arr.length > 4) {
//                wxconfig.setConfig4Options(wxconfigoptionService.getWxConfigOptionBy(arr[4]));
//            }
//        }
//        shouhouInfoRes.setWxconfig(wxconfig);
//        //获取处理进程添加处发送sms
//        String sms7 = shouhouMsgService.getMsms7();
//        shouhouInfoRes.setMSms7(StringUtils.isEmpty(sms7) ? "" : sms7);
//
//        if (CollectionUtils.isNotEmpty(kcOutPpids)) {
//            Duration duration = Duration.between(LocalDateTime.now(), (info.getOfftime() == null ? LocalDateTime.now() : info.getOfftime()));
//            if ((areaSubject.getKind1() != null && areaSubject.getKind1() == 1 || areaSubject.getXtenant() == JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode().intValue()) && (info.getIsqujiE() == 0 && duration.toDays() == 0)) {
//                List<ShouhouServiceConfig> configList = shouhouServiceConfigService.getShouhouServicesList(kcOutPpids);
//                if (CollectionUtils.isNotEmpty(configList)) {
//                    shouhouInfoRes.setConfigList(configList);
//                }
//            }
//        }
//
//        //总计维修款特殊处理
//        shouhouInfoRes.setFeiyong(shouhouInfoRes.getFeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getFeiyong());
//        List<ShouhouHuishou> huishouPj = shouhouHuishouService.getHuishouListBy(shouhouId);
//        BigDecimal huishouPjPrice = huishouPj.stream().filter(e -> e.getPrice() != null).map(e -> e.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal youhuiFeiyong = shouhouInfoRes.getYouhuifeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getYouhuifeiyong();
//        BigDecimal totalFeiyong = shouhouInfoRes.getFeiyong().add(shouhouInfoRes.getYouhuifeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getYouhuifeiyong()).add(huishouPjPrice);
//        BigDecimal yifum = shouhouInfoRes.getYifum() == null ? BigDecimal.ZERO : shouhouInfoRes.getYifum();
//        BigDecimal feiyong = shouhouInfoRes.getFeiyong() == null ? BigDecimal.ZERO : shouhouInfoRes.getFeiyong();
//
//        String feiyongText = shouhouInfoRes.getFeiyong() == null ? "0.00" : shouhouInfoRes.getFeiyong().toString();
//
//        if (youhuiFeiyong.add(huishouPjPrice).compareTo(BigDecimal.ZERO) > 0) {
//            feiyongText = totalFeiyong.toString() + (youhuiFeiyong.compareTo(BigDecimal.ZERO) > 0 ? "-" + youhuiFeiyong.toString() + "(优惠码)" : "") + (huishouPjPrice.compareTo(BigDecimal.ZERO) > 0 ? "-" + huishouPjPrice + "(旧件回收抵扣)" : "") + "=" + shouhouInfoRes.getFeiyong().toString();
//        }
//        shouhouInfoRes.setFeiyongInfoText(feiyongText);
//        shouhouInfoRes.setYingfuM(feiyong.subtract(yifum));
//
//        //维修成本，前端要根据角色做显示隐藏控制，暂时放到后端
//        if (!oaUserBO.getRank().contains("58")) {
//            shouhouInfoRes.setCostprice(BigDecimal.ZERO);
//        }
//        //判断购买地区
//        if (CommenUtil.isNullOrZero(info.getBuyareaid()) && CommenUtil.isNotNullZero(info.getSubId())) {
//            //表示关联了订单，但是没有购买地区(有可能是退换机生成的维修单)
//            //更改售后单购买地区
//            shouhouExService.changeBuyAreaId(shouhouId, info.getSubId(), info.getIshuishou());
//        }
//
//        //获取购买地区门店信息
//        Integer buyAreaId = getBuyAreaIdByShouhouId(shouhouId);
//        if (buyAreaId != null) {
//            shouhouInfoRes.setBuyareaid(buyAreaId);
//        }
//
//        if (CommenUtil.isNotNullZero(shouhouInfoRes.getBuyareaid())) {
//            shouhouInfoRes.setBuyarea(shouhouInfoService.getAreaByAreaId(shouhouInfoRes.getBuyareaid()));
//        }
//
//        //超时天数计算，前端使用
//        if (shouhouInfoRes.getOfftime() != null) {
//            Duration duration = Duration.between(LocalDateTime.now(), shouhouInfoRes.getOfftime());
//            shouhouInfoRes.setOffDays(Math.abs(duration.toDays()));
//        }
//        if (CommenUtil.isNullOrZero(shouhouInfoRes.getServiceType())) {
//            shouhouInfoRes.setServiceType(null);
//        }
//
//        try {
//            attachments = attachmentsFuture.get(3, TimeUnit.SECONDS);
//            List<FileReq> files = filesFuture.get(3, TimeUnit.SECONDS);
//            wxBindUrl = wxBindUrlFuture.get();
//            shouhouInfoRes.setAttachments(attachments);
//            shouhouInfoRes.setFiles(files);
//            shouhouInfoRes.setWxBindUrl(wxBindUrl);
//        } catch (Exception e) {
//            log.error("获取附件files2出错");
//        }
//    }
//
//    private ShouhouBo getOne(Integer shouhouId) {
//        List<ShouhouBo> shouhouBoList = baseMapper.getOne(shouhouId);
//        if (CollectionUtils.isNotEmpty(shouhouBoList)) {
//            return shouhouBoList.get(0);
//        }
//        return null;
//    }
//
//    private boolean isSubCollect(Integer subId, Integer ch999Id, Integer collectType) {
//        Integer res = baseMapper.isSubCollect(subId, ch999Id, collectType);
//        return (res != null && res > 0);
//    }
//
//    private boolean isSubReception(Integer subId, Integer type) {
//        Integer res = baseMapper.isSubReception(subId, type);
//        return (res != null && res > 0);
//    }
//
//    private AreaInfo getAreaSubject(Integer nowArea) {
//        AreaInfo areaInfo = new AreaInfo();
//        if (!Objects.equals(null, nowArea) && nowArea != 0) {
//            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowArea);
//            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null)
//                areaInfo = areaInfoR.getData();
//        } else {
//            areaInfo.setId(nowArea);
//            areaInfo.setIsSend(true);
//            areaInfo.setPrintName("九机网");
//            areaInfo.setXtenant(0);
//            areaInfo.setAuthorizeId(1);
//            areaInfo.setLogo(moaUrlSource.getBasicUrl() + "/static/24,37835b86a5e75b");
//            areaInfo.setSmsChannel(JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI.getCode().toString());
//            areaInfo.setSmsChannelMarketing(JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI_MARKETING.getCode().toString());
//            areaInfo.setMUrl(JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_M_URL.getCode());
//            areaInfo.setWebUrl(JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_WEB_URL.getCode());
//            areaInfo.setHsUrl(JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_HS_URL.getCode());
//        }
//        if (areaInfo.getPrintName().equals("华为授权")) {
//            areaInfo.setSmsChannel("27");
//        }
//        return areaInfo;
//    }
//
//    public Integer getBuyAreaIdByShouhouId(Integer shouhouId) {
//
//        //处理新机单
//        Integer areaId = null;
//        Shouhou shouhou = baseMapper.getById(shouhouId);
//        if (CommenUtil.isNotNullZero(shouhou.getBuyareaid())) {
//            return shouhou.getBuyareaid();
//        }
//        if (CommenUtil.isNotNullZero(shouhou.getFromshouhouid())) {
//            Shouhou sh = super.getById(shouhou.getFromshouhouid());
//            if (CommenUtil.isNotNullZero(sh.getSubId())) {
//                shouhou = sh;
//            }
//        }
//
//        if (CommenUtil.isNotNullZero(shouhou.getSubId())) {
//            Integer isHuishou = shouhou.getIshuishou();
//
//            if (CommenUtil.isNotNullZero(isHuishou) && isHuishou.equals(1)) {
//                areaId = shouhouExMapper.getBuyAreaIdByRecoverMarketInfo(shouhou.getSubId());
//            } else {
//                areaId = shouhouExMapper.getBuyAreaIdBySubId(shouhou.getSubId());
//            }
//        }
////        else {
////            areaId = shouhou.getAreaid();
////        }
//
//        return areaId;
//    }
//
//}
