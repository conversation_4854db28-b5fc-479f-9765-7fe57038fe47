package com.jiuji.oa.afterservice.statistics.dao;

import com.jiuji.oa.afterservice.statistics.vo.req.RepairProfitStatisticsReq;
import com.jiuji.oa.afterservice.statistics.vo.res.RepairProfitStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * description: <  >
 * translation: <  >
 * date : 2020-10-21 11:28
 *
 * <AUTHOR> leee41
 **/
@Mapper
public interface RepairProfitStatisticsMapper {

    /**
     * 统计
     *
     * @param req
     * @return
     */
    List<RepairProfitStatisticsVO> profit(@Param("req") RepairProfitStatisticsReq req, @Param("cid") String cid);

    /**
     * base areas
     *
     * @param req
     * @return
     */
    List<RepairProfitStatisticsVO> areaSelect(@Param("req") RepairProfitStatisticsReq req);
}
