package com.jiuji.oa.afterservice.test.event;

import com.jiuji.oa.afterservice.bigpro.po.ShouhouHuishou;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouHuishouService;
import com.jiuji.oa.afterservice.test.dto.RecoverSmallProSqueezeFinishDTO;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 压屏完成事件监听
 */
@Component
public class RecoverSmallProSqueezeFinishEventListener{

    @Autowired
    private ShouhouHuishouService shouhouHuishouService;

    @EventListener(value = {RecoverSmallProSqueezeFinishEvent.class})
    public void recoverSmallProSqueezeFinish(RecoverSmallProSqueezeFinishEvent event){
        RecoverSmallProSqueezeFinishDTO data = event.getData();
        List<Integer> ids = data.getIds();
        shouhouHuishouService.lambdaUpdate()
                .set(ShouhouHuishou::getYaping,2)
                .in(ShouhouHuishou::getId,ids)
                .eq(ShouhouHuishou::getYaping,1)
                .eq(ShouhouHuishou::getToareaid,360)
                .ge(ShouhouHuishou::getBindtoolcount,0);
    }
}
