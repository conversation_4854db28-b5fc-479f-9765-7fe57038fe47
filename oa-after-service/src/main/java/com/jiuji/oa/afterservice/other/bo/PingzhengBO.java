package com.jiuji.oa.afterservice.other.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * description: <凭证信息BO>
 * translation: <Voucher information BO>
 *
 * <AUTHOR>
 * @date 2020/3/3
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("凭证Bo")
public class PingzhengBO implements Serializable {
    private static final long serialVersionUID = -7059110665054919752L;
    /**业务类型*/
    @ApiModelProperty("业务类型")
    private String act;
    /**凭证时间*/
    @ApiModelProperty("凭证时间")
    private String pzdate;
    /**凭证名称*/
    @ApiModelProperty("凭证名称")
    private String pzname;
    /**凭证摘要*/
    @ApiModelProperty("凭证摘要")
    private String zhaiyao;
    /**科目*/
    @ApiModelProperty("科目")
    private String kemu;
    /**辅助核算*/
    @ApiModelProperty("辅助核算")
    private String fzhs;
    /**借*/
    @ApiModelProperty("借")
    private String jief;
    /**贷*/
    @ApiModelProperty("贷")
    private String daif;
    /**操作人*/
    @ApiModelProperty("操作人")
    private String inuser;
    /**账套id*/
    @ApiModelProperty("账套id")
    private String ztid;
    private String isaudit;
}
