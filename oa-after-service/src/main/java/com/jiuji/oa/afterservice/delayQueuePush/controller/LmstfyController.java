package com.jiuji.oa.afterservice.delayQueuePush.controller;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.jiuji.oa.afterservice.bigpro.po.WeixinUser;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.delayQueuePush.service.AppointmentFormPushService;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AppointmentFormPush;
import com.jiuji.oa.afterservice.refund.vo.req.CommonValidVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/LmstfyController")
public class LmstfyController {


    @Resource
    private AppointmentFormPushService appointmentFormPushService;
    @Resource
    private IMCloud imCloud;
    @Resource
    private WeixinUserService weixinUserService;
    @Resource
    private SmsService smsService;

    /**
     * 延迟队列测试
     * @param appointmentFormPush
     * @return
     */
    @PostMapping("/test")
    public R<String> pushMsgTest(@RequestBody AppointmentFormPush appointmentFormPush){
        appointmentFormPushService.pushMsg(appointmentFormPush);
        return R.success("推送成功");
    }



    @GetMapping("/pushWeiXin")
    public R<String> pushWeiXin(){
        CommonValidVo.BusinessEnum msoftAddOrder = CommonValidVo.BusinessEnum.MSOFT_ADD_ORDER;
        String msg = StrUtil.format(msoftAddOrder.getSmsFormat(), Dict.create().set("validCode", 234234)
                .set("orderId", 789789).set("businessName", msoftAddOrder.getMessage())
                .set("paramBusinessName", msoftAddOrder.getCode()));
         smsService.sendSms("***********", msg, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(NumberConstant.TWO)),
                "刘昊楠", smsService.getSmsChannelByTenant(949, ESmsChannelTypeEnum.VERIFICATION_CODE));
        return R.success("推送成功");
    }



}
