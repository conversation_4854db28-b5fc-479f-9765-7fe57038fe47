package com.jiuji.oa.afterservice.bigpro.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/4/16
 */
@Data
public class YuyueTuiDataBo {
    private Integer sub;
    private BigDecimal tuim;
    private BigDecimal zhem;
    private String way;
    private String bname;
    private String bfm;
    private String bid;
    private List<Basket> basket;

    @Data
    public static class Basket{
        private Integer id;
        private Integer count;
    }
}
