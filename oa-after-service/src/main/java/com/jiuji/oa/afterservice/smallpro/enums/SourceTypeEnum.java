package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * description: <贴膜损耗来源类型枚举类>
 *
 * <AUTHOR>
 * @date 2024-07-30
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum SourceTypeEnum implements CodeMessageEnumInterface {

    /**
     * 贴膜损耗来源类型 - 编码-编码信息
     */
    SALE_ORDER_FILM_LOSS(1, "销售单贴膜损耗"),
    SMALL_ORDER_FILM_LOSS(2, "小件单贴膜损耗");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举对象
     */
    public static SourceTypeEnum valueOfByCode(Integer code){
        for (SourceTypeEnum temp : SourceTypeEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述信息
     * @param code 编码
     * @return 描述信息
     */
    public static String getMessageByCode(Integer code){
        for (SourceTypeEnum temp : SourceTypeEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp.getMessage();
            }
        }
        return "";
    }
}
