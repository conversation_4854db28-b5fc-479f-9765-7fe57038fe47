package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouTestResultBo;
import com.jiuji.oa.afterservice.bigpro.dao.CaigouBasketRefShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.po.CaigouBasketRefShouhou;
import com.jiuji.oa.afterservice.bigpro.service.CaigouBasketRefShouhouService;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
@Service
public class CaigouBasketRefShouhouServiceImpl extends ServiceImpl<CaigouBasketRefShouhouMapper, CaigouBasketRefShouhou> implements CaigouBasketRefShouhouService {

    @Autowired
    @Qualifier("pushMessageExecutor")
    private ThreadPoolTaskExecutor executor;

    //设置查询阈值，超过这个数量则进行拆分
    private static final Integer THRESHOLD_VALUE = 500;


    @Override
    public List<Integer> getPeijianfahuo(List<Integer> shouhouIds) {

        // 参数个数超过2100个会报错，所以拆分查询
        Map<Integer, List<Integer>> queryMap = new HashMap<>();
        if (shouhouIds.size() > THRESHOLD_VALUE) {
            queryMap = CommenUtil.splitList(shouhouIds, shouhouIds.size() / THRESHOLD_VALUE + 1);
        }
        List<Integer> resList = new LinkedList<>();
        if (!queryMap.isEmpty()) {
            Map<Integer, List<Integer>> queryIdMap = queryMap;
            List<CompletableFuture<List<Integer>>> completableFutureList = queryIdMap
                    .keySet().stream().map(t -> CompletableFuture.supplyAsync(() -> baseMapper.getPeijianfahuo(queryIdMap.get(t)), this.executor))
                    .collect(Collectors.toList());
            //等待所有任务执行完毕
//            completableFutureList.forEach(CompletableFuture::join);
            completableFutureList.forEach(future -> {
                resList.addAll(future.join());
            });
        } else {
            List<Integer> ids =
                    baseMapper.getPeijianfahuo(shouhouIds);
            resList.addAll(ids);
        }

        return resList;
    }
}
