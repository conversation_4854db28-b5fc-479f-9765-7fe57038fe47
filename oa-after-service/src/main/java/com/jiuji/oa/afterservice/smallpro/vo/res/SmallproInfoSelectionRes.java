package com.jiuji.oa.afterservice.smallpro.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;

/**
 * description: <小件接件详情页选择项列表>
 * translation: <Smallpro detail page selection list>
 *
 * <AUTHOR>
 * @date 2019/11/25
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproInfoSelectionRes implements Serializable {
    private static final long serialVersionUID = 5862476571318971270L;
    /**
     * 保修状态
     */
    @ApiModelProperty(value = "保修状态[1保修|0非保修]")
    private HashMap<String, Object> warrantyStatusSelection;
    /**
     * 九机服务
     */
    @ApiModelProperty(value = "九机服务")
    private HashMap<String, Object> serviceTypeSelection;
    /**
     * 处理方式
     */
    @ApiModelProperty(value = "处理方式")
    private HashMap<String, Object> kindSelection;
    /**
     * 处理分组
     */
    @ApiModelProperty(value = "处理分组")
    private HashMap<String, Object> groupSelection;
    /**
     * 数据解绑
     */
    @ApiModelProperty(value = "数据解绑")
    private HashMap<String, Object> dataReleaseSelection;
    /**
     * 情况选项,old=shouqiandiv
     */
    @ApiModelProperty(value = "情况选项,old=shouqiandiv")
    private HashMap<String, Object> situationKindBeforeSelection;

    /**
     * 情况选项,old=shouhoudiv
     */
    @ApiModelProperty(value = "情况选项,old=shouhoudiv")
    private HashMap<String, Object> situationKindAfterSelection;

    /**
     * 外观选项[1磨损|0完好]
     */
    @ApiModelProperty(value = "外观选项[1磨损|0完好]")
    private HashMap<String, Object> outwardFlagSelection;
    /**
     * 小件旧件返厂状态
     */
    @ApiModelProperty(value = "小件旧件返厂状态")
    private HashMap<String, Object> returnToFactoryStatus;
    /**
     * 小件接件状态
     */
    @ApiModelProperty(value = "小件接件状态")
    private HashMap<String, Object> status;
    /**
     * 小件接件维修状态
     */
    @ApiModelProperty(value = "小件接件维修状态")
    private HashMap<String, Object> maintainState;
    /**
     * 小件收款状态
     */
    @ApiModelProperty(value = "小件收款状态")
    private HashMap<String, Object> collectionStatus;
}
