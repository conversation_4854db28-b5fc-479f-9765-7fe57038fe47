package com.jiuji.oa.afterservice.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.api.dao.TaxPiaoMapper;
import com.jiuji.oa.afterservice.api.po.TaxPiao;
import com.jiuji.oa.afterservice.api.service.TaxPiaoService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发票服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-20
 */
@Service
public class TaxPiaoServiceImpl extends ServiceImpl<TaxPiaoMapper, TaxPiao> implements TaxPiaoService {

    @Override
    public TaxPiao getInvoice(Long subId) {
        TaxPiao result = null;
        // 由于特殊原因  subId为0会查到发票记录 按理说订单号一定大于0
        if (subId > 0) {
            result = this.baseMapper.getInvoiceBySubId(subId);
        }
        return result;
    }

    @Override
    public TaxPiao getpiaoinfoBySubId(Long subId, Integer type) {
        return baseMapper.getpiaoinfoBySubId(subId,type);
    }

    @Override
    public TaxPiao getpiaoinfoByUserIdAndTradedate(Long userId, String tradeDate) {
        return baseMapper.getpiaoinfoByUserIdAndTradedate(userId, tradeDate);
    }
}
