package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceVO;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/27 12:31
 * @Description
 */

@Data
public class JiuJiFuWuChuXianRes {
    private static final long serialVersionUID = 3831409651813352057L;

    @ApiModelProperty(value = "服务出险标题")
    private String title;

    @ApiModelProperty(value = "九机服务")
    private List<ServiceVO> serviceList;

    @ApiModelProperty(value = "维修配件")
    private List<HexiaoBo> maintainTabs;

    @ApiModelProperty(value = "实例图片")
    private String instance;

    @ApiModelProperty(value = "九机服务枚举")
    private List<EnumVO> bxTypeEnum;

    @ApiModelProperty(value = "九机服务质保链接")
    private List<Link> linkList;

    @Data
    public static class Link{
        @ApiModelProperty(value = "九机服务质保链接")
        private String link;

        @ApiModelProperty(value = "九机服务类型")
        private String type;
    }
}
