package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: <小件商品接件-保修状态枚举类>
 * translation: <Smallpro pickup - warranty status enumeration class>
 *
 * <AUTHOR>
 * @date 2019/11/25
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum SmallproWarrantyStatusEnum implements CodeMessageEnumInterface {

    /**
     * 小件商品保修状态 编码-编码信息
     */
    SMALL_PRO_WARRANTY_NULL(null,""),
    SMALL_PRO_WARRANTY_WARRANTY(1,"在保"),
    SMALL_PRO_WARRANTY_NO_WARRANTY(0,"非保");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
}
