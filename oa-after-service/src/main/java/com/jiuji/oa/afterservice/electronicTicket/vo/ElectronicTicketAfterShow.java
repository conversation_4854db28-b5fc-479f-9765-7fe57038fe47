package com.jiuji.oa.afterservice.electronicTicket.vo;

import com.jiuji.oa.afterservice.electronicTicket.entity.AfterElectronicTicket;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ElectronicTicketAfterShow {

    /**
     * 电子小票表头
     */
    private AfterElectronicTicketTitle ticketTitle;

     /**
     * 基本信息
     */
    private AfterElectronicTicketBaseInfo baseInfo;

    /**
     * 设备信息
     */
    private AfterElectronicTicketEquipment equipment;

    /**
     * 发票信息
     */
    private AfterElectronicTicketInvoiceInformation invoiceInformation;

    /**
     * 维修内容
     */
    private AfterElectronicTicketRepairInfo repairInfo;


    private AfterElectronicTicket afterElectronicTicket;
}
