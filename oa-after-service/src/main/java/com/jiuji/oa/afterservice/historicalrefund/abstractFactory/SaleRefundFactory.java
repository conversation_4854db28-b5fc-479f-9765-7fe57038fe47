package com.jiuji.oa.afterservice.historicalrefund.abstractFactory;

import com.jiuji.oa.afterservice.historicalrefund.service.RefundService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 销售工厂
 * <AUTHOR>
 */
@Component(value = "SaleRefundFactory")
public class SaleRefundFactory implements HistoricalRefundAbstractFactory {

    @Resource(name = "SaleRefundServiceImpl")
    private RefundService refundService;

    @Override
    public RefundService createRefundService() {
        return refundService;
    }
}
