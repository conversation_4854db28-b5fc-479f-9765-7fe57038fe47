package com.jiuji.oa.afterservice.bigpro.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后配件回收列表
 * @author: LiQuan
 * @date: 2020/4/30
 */
@ApiModel
@Data
public class ShouhouHuishouListBo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "附件编号")
    private String topfid;

    private String productName;

    private String productColor;

    private String pLabel;

    private String pjkinds;

    private String problem;

    private BigDecimal huanhuiprice;

    private String companyJc;

    private String weixiuren;

    private Integer id;

    @ApiModelProperty(value = "售后ID")
    private Integer shouhouId;

    @ApiModelProperty(value = "配件商品ID")
    private Integer ppid;

    @ApiModelProperty(value = "回收商品名称")
    private String name;

    @ApiModelProperty(value = "回收价格")
    private Double price;

    @ApiModelProperty(value = "回收库存数量，默认1个")
    private Integer kcount;

    @ApiModelProperty(value = "回收操作人")
    private String inuser;

    @ApiModelProperty(value = "回收时间")
    private LocalDateTime indate;

    @ApiModelProperty(value = "是否已删除")
    private Boolean isdel;

    @ApiModelProperty(value = "是否已经销售")
    private Boolean issale;

    @ApiModelProperty(value = "二次销售价格")
    private Double saleprice;

    @ApiModelProperty(value = "销售到的公司")
    private Integer companyId;

    @ApiModelProperty(value = "销售操作人")
    private String saleuser;

    @ApiModelProperty(value = "销售时间")
    private LocalDateTime saledate;

    private String area;

    @ApiModelProperty(value = "是否返回给客户")
    private Boolean isfan;

    @ApiModelProperty(value = "返修审核")
    private Boolean fancheck;

    @ApiModelProperty(value = "审核人")
    private String checkUser;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime checkDate;

    @ApiModelProperty(value = "是否转地区中")
    private Boolean istoarea;

    @ApiModelProperty(value = "转到地区")
    private String toarea;

    private String suser;

    private LocalDateTime sdtime;

    private String ruser;

    private LocalDateTime rdtime;

    @ApiModelProperty(value = "置换确认")
    private Boolean confirm;

    @ApiModelProperty(value = "置换完成")
    private Boolean complete;

    @ApiModelProperty(value = "置换完成时间")
    private LocalDateTime completedate;

    private String completeuser;

    @ApiModelProperty(value = "是否是置换")
    private Boolean ishuan;

    @ApiModelProperty(value = "抵扣费用类型  1:维修成本 0 or null维修费")
    private Integer dktype;

    @ApiModelProperty(value = "是否换货配件")
    private Integer ishuanhuo;

    @ApiModelProperty(value = "换货渠道")
    private String qudao;

    @ApiModelProperty(value = "换货人")
    private String huanhuoUser;

    @ApiModelProperty(value = "换货提交时间")
    private LocalDateTime huanhuoTime;

    @ApiModelProperty(value = "退换货表ID")
    private Integer returnBasketid;

    @ApiModelProperty(value = "地区ID")
    private Integer areaid;

    @ApiModelProperty(value = "转地区ID")
    private Integer toareaid;

    private Boolean issalecheck;

    @ApiModelProperty(value = "物流编号")
    private Integer wuliuid;

    private Integer wxkcid;

    private Integer kind;

    private Long pzid;

    private Integer yaping;

    private Double inprice;

    @ApiModelProperty(value = "耗材绑定总数量")
    private Integer bindtoolcount;

    @ApiModelProperty(value = "耗材绑定价格")
    private Double bindtoolprice;

    private LocalDateTime baofeitime;

    private LocalDateTime srktime;

    @ApiModelProperty(value = "预售渠道")
    private Integer PreSaleChannel;

    private Long baofeipz;

    private Long baofeipq;

    private String salePiqianid;

    private Long salePzid;

}
