package com.jiuji.oa.afterservice.cloud.vo;

import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SelectsVo {

    private String type;
    private List<SelectVo> select;

    @Getter
    @Setter
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SelectVo {
        private String value;
        private String label;
    }

    @Data
    public static class OptionsSelectVo {
        private String value;
        private String label;
        private List<SelectVo> options = new ArrayList<>();
        private List<SelectVo> option = new ArrayList<>();
    }

    @Data
    public static class DayAndTime {
        private List<SelectVo> day = new ArrayList<>();
        private List<SelectVo> time = new ArrayList<>();
        private String nowTime;
    }

    @Data
    public static class AfterTimeVO {
        //顺丰上门时间
        private DayAndTime expressTime;
        //预约到店时间
        private DayAndTime toShopTime;
        //上门时间
        private DayAndTime goHomeTime;
    }
}
