package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.SubCollectionMapper;
import com.jiuji.oa.afterservice.bigpro.po.SubCollection;
import com.jiuji.oa.afterservice.bigpro.service.SubCollectionService;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Service
public class SubCollectionServiceImpl extends ServiceImpl<SubCollectionMapper, SubCollection> implements SubCollectionService {

    @Override
    public List<Integer> getSubCollectionUserIds(Integer subId) {
        if(CommenUtil.isNullOrZero(subId)){
            return null;
        }
        return baseMapper.getSubCollectionUserIds(subId);
    }
}
