package com.jiuji.oa.afterservice.sub.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.sub.po.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 相关子分类查询
     * @param cid
     * @return
     */
    List<Integer> selectCategoryChildrenByCid(@Param("cid")Integer cid);

}
