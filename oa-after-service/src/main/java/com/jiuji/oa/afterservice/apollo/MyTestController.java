package com.jiuji.oa.afterservice.apollo;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.infra.lmstfy.consume.BaseConsumer;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiModelProperty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.MessageListenerContainer;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.concurrent.ScheduledExecutorService;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("api/apollo/")
public class MyTestController {


    @Resource
    private SmsService smsService;

    @GetMapping("/stopMqListener")
    @SneakyThrows
    public R<?> stopMqListener() {
        Thread.sleep(1000);
        Integer num = 0;
        log.warn("停止所有的mq消费者");
        Collection<MessageListenerContainer> listenerContainers = SpringUtil.getBean(RabbitListenerEndpointRegistry.class).getListenerContainers();
        listenerContainers.forEach(cus -> cus.stop());
        num = listenerContainers.size();

        log.warn("停止所有的延迟队列消费者");
        Thread.sleep(1000);
        ScheduledExecutorService executorService = (ScheduledExecutorService) ReflectUtil.getStaticFieldValue(ReflectUtil.getField(BaseConsumer.class, "executorService"));
        for (int i = 0; i < 5; i++) {
            executorService.shutdownNow();
            executorService.shutdown();
            Thread.sleep(3000);
        }
        log.warn("停止延迟队列结果: {}",executorService.isShutdown());
        return R.success(num);
    }

    @GetMapping("/test")
    @ApiModelProperty("测试")
    public R<String> test() {
        String msg = String.format("您正在进行[订单%s退款]操作，验证码%s。如非本人操作请忽略。",123, 123);
        smsService.sendSms("18008733159", msg, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(2)), "系统", smsService.getSmsChannelByTenant(258, ESmsChannelTypeEnum.VERIFICATION_CODE));
        return R.success("发送成功");
    }
}