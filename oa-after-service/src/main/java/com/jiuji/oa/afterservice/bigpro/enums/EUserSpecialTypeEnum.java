package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: gengjiaping
 * @date: 2020/4/3
 */
@AllArgsConstructor
@Getter
public enum EUserSpecialTypeEnum implements CodeMessageEnumInterface {
    QXJF(1,"取消积分"),
    QXGZJD(2,"取消黑钻接待"),
    QXPJXSYX(4,"取消评价系数影响"),
    HUANGNIU(8,"黄牛");
    /**
     * 类型
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
}
