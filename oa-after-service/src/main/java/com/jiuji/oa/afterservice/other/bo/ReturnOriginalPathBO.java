package com.jiuji.oa.afterservice.other.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description: <退款原路径返回信息BO>
 * translation: <Refund original path return information BO>
 *
 * <AUTHOR>
 * @date 2020/3/25
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ReturnOriginalPathBO {

    /**
     * 原路径返回act标志
     * 原路径返回推送到RabbitMq-oaAsync-oaAsync队列
     */
    private String act = "PayRefundByReturnidPush";

    /**
     * shouhouTuihuanId
     */
    private Integer id;

}
