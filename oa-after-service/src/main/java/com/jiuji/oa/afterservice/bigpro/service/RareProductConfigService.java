package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.rare.RareProductInfo;
import com.jiuji.oa.afterservice.bigpro.po.RareProductConfig;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface RareProductConfigService extends IService<RareProductConfig> {


    /**
     * 获取稀缺商品基本信息
     * @return
     */
    List<RareProductInfo> getRareProductInfo();

}
