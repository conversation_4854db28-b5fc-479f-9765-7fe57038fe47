package com.jiuji.oa.afterservice.bigpro.vo.statistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 备货订单统计列表展示实体
 * <AUTHOR>
 * @since 2021/6/3 10:58
 */
@Data
@ApiModel("备货订单统计列表展示实体")
public class StoreUpStatisticsVO {
    /**大区*/
    @ApiModelProperty("大区")
    private String bigArea;
    /**小区*/
    @ApiModelProperty("小区")
    private String smallArea;
    /**门店ID*/
    @ApiModelProperty("门店ID")
    private Integer areaId;
    /**门店*/
    @ApiModelProperty("门店")
    private String area;
    /**门店级别*/
    @ApiModelProperty("门店级别")
    private String areaLevel;
    /**销售*/
    @ApiModelProperty("销售")
    private String salesman;
    /**备货数量*/
    @ApiModelProperty("备货数量")
    private Integer storeUpCount;
    /**库存数量*/
    @ApiModelProperty("库存数量")
    private Integer inventoryCount;
    /**库存数量url*/
    @ApiModelProperty("库存数量订单url")
    private String inventoryOrderUrl;
    /**在途数量*/
    @ApiModelProperty("在途数量")
    private Integer onPassageCount;
    /**在途数量订单url*/
    @ApiModelProperty("在途数量订单url")
    private String onPassageOrderUrl;
    /**门店级别*/
    public static final String AREA_LEVEL = "areaLevel";
    /**销售员工*/
    public static final String SALES_MAN = "salesman";
}
