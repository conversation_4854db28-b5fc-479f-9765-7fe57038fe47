package com.jiuji.oa.afterservice.bigpro.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.oa.afterservice.bigpro.statistics.dao.ShouhouStatisticsMapper;
import com.jiuji.oa.afterservice.bigpro.statistics.service.DaQu;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.departinfo.vo.DepartInfoVO;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description: <  >
 * translation: <  >
 * date : 2020-10-21 10:58
 *
 * <AUTHOR> leee41
 **/
@Service
@Slf4j
public class StatisticsCommonServiceImpl {
    @Autowired
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private ShouhouStatisticsMapper shouhouStatisticsMapper;
    @Autowired
    private DepartInfoClient departInfoClient;
    @Resource
    private AreainfoService areainfoService;

    public void dealDaQuReq(DaQu req) {
        OaUserBO cur = this.abstractCurrentRequestComponent.getCurrentStaffId();
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(cur.getAreaId());
        List<Long> daquList = this.getCurUserDaQuAreaCodes(cur.getAreaId());
        if (CollectionUtils.isEmpty(req.getAreaCodes()))
            if (!Objects.equals(backEndInfo.getH1AreaId(),cur.getAreaId())
                    && !Objects.equals(backEndInfo.getDcAreaId(),cur.getAreaId())
                    && !CollUtil.contains(backEndInfo.getHqAreaIds(),cur.getAreaId())
                    //角色中包含分公司售后经理
                    && !cur.getMainRole().equals(314)
            ) {
                //非总部后端人员只能查看自己大区的数据横向对比
                req.setAreaIds(daquList);
            } else {
                Set<Long> areaIdSet = new HashSet<>();
                areaIdSet.addAll(req.getAreaCodes().stream().filter(s -> !s.startsWith("a"))
                        .map(Long::valueOf).filter(daquList::contains).collect(Collectors.toList()));
                areaIdSet.addAll(getAreaIdsByCodes(req.getAreaCodes().stream().filter(s -> s.startsWith("a")).collect(Collectors.toSet())));
                req.setAreaIds(new ArrayList<>(areaIdSet));
            }
    }


    public List<Long> getCurUserDaQuAreaCodes(Integer id) {
        return this.shouhouStatisticsMapper.getCurUserDaQuAreaCodes(id).stream().map(Long::valueOf).collect(Collectors.toList());
    }

    public List<Long> getAreaIdsByCodes(Set<String> aCodes) {
        R<List<DepartInfoVO>> departR = departInfoClient.listAll();
        return departR.getData().stream().filter(departInfoVO -> !Objects.equals(null, departInfoVO.getCode1()))
                .filter(departInfoVO -> aCodes.contains(departInfoVO.getCode1().toString()))
                .map(DepartInfoVO::getCode1).collect(Collectors.toList());
    }


}
