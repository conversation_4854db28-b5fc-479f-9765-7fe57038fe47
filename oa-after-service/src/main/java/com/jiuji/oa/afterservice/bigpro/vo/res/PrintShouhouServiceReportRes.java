package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceReport;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 10:41
 * @Description
 */
@Data
public class PrintShouhouServiceReportRes implements Serializable {
    private static final long serialVersionUID = -3207204162416107024L;
    /**
     * 自增长
     */
    @ApiModelProperty(value = "质检编号")
    private Integer id;
    /**
     * 受理时间(接件时间)
     */
    @ApiModelProperty(value = "受理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptanceTime;
    /**
     * 受理单号(维修单号)
     */
    @ApiModelProperty(value = "受理单号")
    private Integer shouhouId;
    /**
     * 送修人姓名
     */
    @ApiModelProperty(value = "送修人姓名")
    private String afterUserName;
    /**
     * 送修人电话
     */
    @ApiModelProperty(value = "送修人电话")
    private String afterUserMobile;
    /**
     * 会员等级名称
     */
    @ApiModelProperty(value = "会员等级")
    private String userClassName;
    /**
     * imei(串号)
     */
    @ApiModelProperty(value = "设备IMEI")
    private String imei;
    /**
     * 设备名称(型号加规格)
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 外观描述
     */
    @ApiModelProperty(value = "外观描述")
    private String appearanceDescription;
    /**
     * 配置
     */
    @ApiModelProperty(value = "配置")
    private String toConfigure;
    /**
     * 购买单号(订单ID)
     */
    @ApiModelProperty(value = "购买单号")
    private Integer subId;
    /**
     * 购买类型(新机/优品/良品)
     */
    @ApiModelProperty(value = "购买类型(新机/优品/良品)")
    private Integer purchaseType;
    /**
     * 购买类型(新机/优品/良品)
     * @see ShouhouServiceReport.PurchaseTypeEnum
     */
    @ApiModelProperty(value = "购买类型文字")
    private String purchaseTypeName;
    /**
     * 购买日期
     */
    @ApiModelProperty(value = "购买日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchasingDate;
    /**
     * 购买客户姓名
     */
    @ApiModelProperty(value = "购买客户姓名")
    private String purchasingUserName;
    /**
     * 购买客户电话
     */
    @ApiModelProperty(value = "购买客户电话")
    private String purchasingUserMobile;
    /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String faultDescription;
    /**
     * 检测分析
     */
    @ApiModelProperty(value = "检测分析")
    private String faultAnalysis;
    /**
     * 检测结果 枚举值
     * @see ShouhouServiceReport.FaultResultEnum
     */
    @ApiModelProperty(value = "检测结果")
    private Integer faultResult;
    /**
     * 检测结果 枚举值
     * @see ShouhouServiceReport.FaultResultEnum
     */
    @ApiModelProperty(value = "检测结果")
    private String faultResultName;
    /**
     * 检测门店(门店id)
     */
    @ApiModelProperty(value = "检测门店(门店id)")
    private Integer areaId;
    /**
     * 检测门店(门店id)
     */
    @ApiModelProperty(value = "检测门店(门店名称)")
    private String areaName;
    /**
     * 检测门店(门店id)
     */
    @ApiModelProperty(value = "检测门店(门店编号)")
    private String area;
    /**
     * 联系电话(门店电话)
     */
    @ApiModelProperty(value = "联系电话(门店电话)")
    private String areaMobile;
    /**
     * 检测人员姓名
     */
    @ApiModelProperty(value = "检测人员姓名")
    private String checkUser;
    /**
     * 检测人员姓名
     */
    @ApiModelProperty(value = "检测人员工号")
    private Integer checkUserId;
    /**
     * 检测时间
     */
    @ApiModelProperty(value = "检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;
    /**
     * 温馨提示
     */
    @ApiModelProperty(value = "温馨提示")
    private String reminder;
    /**
     * 质检报告fid
     */
    @ApiModelProperty(value = "质检报告fid")
    private String fid;
}
