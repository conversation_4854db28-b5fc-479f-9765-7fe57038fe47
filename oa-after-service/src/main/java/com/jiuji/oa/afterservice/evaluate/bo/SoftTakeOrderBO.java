package com.jiuji.oa.afterservice.evaluate.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @since 2019/12/17
 */
@Data
public class SoftTakeOrderBO implements Serializable {
    private static final long serialVersionUID = -2723514366466382231L;

    private Integer id;
    private Integer subId;
    private Boolean isHuishou;
    private LocalDateTime modiDate;
    private String imei;
}
