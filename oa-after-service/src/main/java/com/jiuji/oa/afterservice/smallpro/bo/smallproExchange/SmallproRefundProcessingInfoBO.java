package com.jiuji.oa.afterservice.smallpro.bo.smallproExchange;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description: <小件接件退款流程需要信息BO>
 * translation: <The small piece pick-up refund process requires information BO>
 *
 * <AUTHOR>
 * @date 2020/4/7
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproRefundProcessingInfoBO implements Serializable {
    private static final long serialVersionUID = -2979356032075153537L;

    private Long basketId;

    private Integer basketCount;

    private BigDecimal price;

    private String productName;

    private String productColor;

    private Integer isMobile;

    private Integer ppriceId;

    private BigDecimal oemPrice;

    private Integer areaId;

    private Integer userId;

    private Integer ppriceId1;

    private Integer cId;

    private Integer basketCount1;

    private Integer subId;

    private BigDecimal inPrice;

    private BigDecimal shouxuM;

    private BigDecimal feeM;

    private String subMobile;

    private BigDecimal coinM;

    private Integer type;

    private String pjType;

}
