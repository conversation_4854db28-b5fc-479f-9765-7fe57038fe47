package com.jiuji.oa.afterservice.electronicTicket.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AfterSalesPropellingMovement {
    /**
     * 微信用户openid
     */
    private String openId;
    /**
     * 消息连接地址
     */
    private String url;
    /**
     * 微信推送标题
     */
    private MsgBaseInfo first;
    /**
     * 微信推送服务类型
     */
    private MsgBaseInfo handleType;
    /**
     * 微信推送处理状态
     */
    private MsgBaseInfo status;
    /**
     * 微信推送提交时间
     */
    private MsgBaseInfo rowCreateDate;
    /**
     * 微信推送当前状态
     */
    private MsgBaseInfo logType;
    /**
     * 微信推送信息备注
     */
    private MsgBaseInfo remark;
}
