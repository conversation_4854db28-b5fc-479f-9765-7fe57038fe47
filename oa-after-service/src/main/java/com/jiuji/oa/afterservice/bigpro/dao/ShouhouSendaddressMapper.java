package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouSendaddress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-18
 */
@Mapper
public interface ShouhouSendaddressMapper extends BaseMapper<ShouhouSendaddress> {
    /**
     *校验是否存在售后寄送地址
     * @param linkid
     * @param kind 1预约，2售后
     * @return
     */
    Integer checkHasShouhouSendaddress(@Param("linkid") int linkid,@Param("kind") int kind);
}
