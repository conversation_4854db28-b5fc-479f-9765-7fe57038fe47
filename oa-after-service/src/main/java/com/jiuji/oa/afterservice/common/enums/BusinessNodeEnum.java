package com.jiuji.oa.afterservice.common.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * description: <业务节点枚举>
 *
 * <AUTHOR> quan
 * @date 2021/06/22
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum BusinessNodeEnum implements CodeMessageEnumInterface {
    /**
     * 业务节点
     */
    BUSINESS_UNKNOWN(0, ""),
    BUSINESS_BEGIN(1, "产生单据（接件、下单）"),
    BUSINESS_FINISHED(2, "业务单据完成（取机、交易完成）");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;


    public static BusinessNodeEnum valueOfByCode(Integer code){
        for (BusinessNodeEnum enumConstant : BusinessNodeEnum.class.getEnumConstants()) {
            if(Objects.equals(enumConstant.getCode(),code)){
                return enumConstant;
            }
        }
        return null;
    }
}
