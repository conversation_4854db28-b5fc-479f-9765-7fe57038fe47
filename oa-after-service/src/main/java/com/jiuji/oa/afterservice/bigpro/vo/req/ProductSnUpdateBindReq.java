package com.jiuji.oa.afterservice.bigpro.vo.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品SN码绑定状态更新请求对象
 */
@Data
@Accessors(chain = true)
public class ProductSnUpdateBindReq {
    
    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空")
    private Integer ppid;
    
    /**
     * 序列号
     */
    @NotBlank(message = "序列号不能为空")
    private String sn;
    
    /**
     * 篮式ID
     */
    @NotNull(message = "篮式ID不能为空")
    private Integer basketId;


    /**
     * 1-新增
     * 2-换绑
     * 3-解绑
     * 操作类型
     * @see OperationTypeEnum
     */
    private Integer operationType;
} 