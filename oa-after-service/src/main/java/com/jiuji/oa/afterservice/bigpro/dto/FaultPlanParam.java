package com.jiuji.oa.afterservice.bigpro.dto;

import com.jiuji.oa.afterservice.bigpro.po.RepairFault;
import com.jiuji.oa.afterservice.bigpro.po.RepairPlan;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 处理故障和方案的参数类
 */
@Data
public class FaultPlanParam {
    private String currentUser;
    private LocalDateTime now;
    private Integer shouHouId;
    private Map<Integer, RepairFault> existingFaultMap = new HashMap<>();
    private Map<Integer, Map<Integer, RepairPlan>> existingPlanMap = new HashMap<>();
    private Set<Integer> processedFaultIds = new HashSet<>();
    private Set<Integer> processedPlanIds = new HashSet<>();

    public FaultPlanParam(String currentUser, LocalDateTime now, Integer shouHouId,
                      Map<Integer, RepairFault> existingFaultMap,
                      Map<Integer, Map<Integer, RepairPlan>> existingPlanMap) {
        this.currentUser = currentUser;
        this.now = now;
        this.shouHouId = shouHouId;
        this.existingFaultMap = existingFaultMap;
        this.existingPlanMap = existingPlanMap;
    }


} 