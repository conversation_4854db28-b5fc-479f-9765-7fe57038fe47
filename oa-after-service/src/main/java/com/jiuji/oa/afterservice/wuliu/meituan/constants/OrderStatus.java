package com.jiuji.oa.afterservice.wuliu.meituan.constants;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态
 */
@Getter
@AllArgsConstructor
public enum OrderStatus implements CodeMessageEnumInterface {
    CREATED(0, "待调度"),
    ACCEPTED(20, "已接单"),
    FETCHED(30, "已取货"),
    DELIVERED(50, "已送达"),
    CANCELED(99, "已取消");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 描述
     */
    private String message;
}
