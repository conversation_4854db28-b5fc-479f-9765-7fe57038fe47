package com.jiuji.oa.afterservice.api.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> <EMAIL>
 * @date 2020/12/30 11:06
 **/
@Data
@TableName("userPointPushRecord")
public class UserPointPushRecordEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String openid;
    @TableField("leftPoint")
    private String leftPoint;
    @TableField("pushState")
    private Integer pushState;
    @TableField("pushMsg")
    private String pushMsg;
    @TableField("userName")
    private String userName;
    @TableField("reducePoint")
    private String reducePoint;

}
