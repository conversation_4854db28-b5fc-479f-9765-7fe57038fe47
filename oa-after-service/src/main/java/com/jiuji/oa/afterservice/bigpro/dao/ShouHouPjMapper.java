package com.jiuji.oa.afterservice.bigpro.dao;

import cn.hutool.core.lang.Dict;
import com.jiuji.oa.afterservice.bigpro.bo.shouhou.RProductResultBo;
import com.jiuji.oa.afterservice.bigpro.bo.wxpj.*;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  Mapper 售后维修配件相关
 * </p>
 *
 * <AUTHOR>
 * @since 2020-5-14
 */
@Mapper
public interface ShouHouPjMapper {

    /**
     * 维修库存查询
     * @param id
     */
    List<WxKcBo> listWxkcOutput(@Param("ids") List<Integer> ids, @Param("wxId") Integer wxId);

    /**
     * 获取售后服务记录信息
     * @param wxId
     * @param wxKcId
     * @param serviceTypes
     * @return
     */
    List<ServiceRecordBo> listServiceRecordInfo(@Param("wxId") Integer wxId, @Param("wxKcId") Integer wxKcId, @Param("serviceTypes") List<Integer> serviceTypes);

    /**
     * 查询售后维修服务机型详情信息
     * @param wxId
     * @param serviceTypes
     * @return
     */
    List<ShServiceDataInfoBo> getServiceDataList(@Param("wxId") Integer wxId, @Param("serviceTypes") List<Integer> serviceTypes);

    /**
     * 根据ids查询维修库存信息
     * @param ids
     * @return
     */
    List<WxKcBo> getWxkcOutputByIds(@Param("ids")List<Integer> ids);

    /**
     * 根据ids查询cid
     * @param ids
     * @param wxId
     * @return
     */
    List<Integer> getCidByIds(@Param("wxId")Integer wxId , @Param("ids")List<Integer> ids);


    /**
     * 根据wxId查询cid
     * @param wxId
     * @param wxId
     * @return
     */
    List<Integer> getCidByWxIds(@Param("wxId")Integer wxId);

    /**
     * 更新服务记录删除状态
     * @param id
     */
    int updateServiceRecordDelFlagById(@Param("ids") Collection<Integer> ids);

    /**
     * 更新维修费用
     * @param inprice
     * @param feiyong
     * @return
     */
    Integer updateCostPriceById(@Param("inprice")BigDecimal inprice, @Param("feiyong")BigDecimal feiyong,@Param("wxId")Integer wxId );

    /**
     * 更新维修费用
     * @param inprice
     * @param feiyong
     * @return
     */
    void updateCostPriceByWxId(@Param("inprice")BigDecimal inprice, @Param("feiyong")BigDecimal feiyong,@Param("wxId")Integer wxId );

    /**
     * 更新维修库存出库状态
     * @param ids
     */
    void updateWxkcoutputStats( @Param("ids")List<Integer> ids);

    /**
     * @param ids
     * @return
     */
    Boolean deleteWxkcoutputByIds(@Param("ids")List<Integer> ids);

    /**
     * 获取优惠码表主键id
     * @param wxId
     * @param cardId
     * @return
     */
    List<Integer> getCardIdfromCardLogsByWxId(@Param("wxId") Integer wxId, @Param("liangpin") Integer liangpin, @Param("cardId") Integer cardId);

    /**
     * 查询收银lock状态
     * @param wxId
     * @return
     */
    Integer getShouYinLockById(@Param("wxId")Integer wxId);

    /**
     * 更新价格信息
     * @param wxId
     * @return
     */
    Integer updateWxkcoutputPriceByWxId(@Param("wxId")Integer wxId);

    /**
     * 查询维修配件记录数
     * @param wxId
     */
    Integer getWxPjCountByWxId(@Param("wxId")Integer wxId);

    /**
     * 更新
     * @param wxId
     * @return
     */
    Integer updateNumberCardInfoByWxId(@Param("wxId")Integer wxId);

    /**
     * 获取更新的id信息
     * @param wxId
     * @return
     */
    List<Dict> getUpdatedNumberCardIdByWxId(@Param("wxId")Integer wxId);

    /**
     * 计算维修费用合计
     * @param wxId
     * @return
     */
    WxFeiYongBo getWxFeiYongSum(@Param("wxId")Integer wxId);

    /**
     * 智乐方维修费用计算
     * @param wxId
     * @return
     */
    WxFeiYongBo getWxFeiYongSumZlf(@Param("wxId")Integer wxId);

    /**
     * 检查是否是23分类下的子分类
     * @param limitIds
     * @return
     */
    Integer checkSubCategory(@Param("limitIds") List<Integer> limitIds);

    /**
     * 取机查询
     * @param id
     * @return
     */
    QujiBo getQuji(@Param("id") Integer id);

    /**
     * 取机检查
     * @param id
     * @return
     */
    Integer checkQuji(@Param("id") Integer id);

    /**
     * 取机转地区检查
     * @param id
     * @return
     */
    Integer checkQujiToArea(@Param("id") Integer id);

    /**
     * 取机时查询是否有在使用代用机
     * @return
     */
    Shouhou queryDaiyongjiUseInfo(@Param("shouhouId") Integer shouhouId);

    /**
     * 更新售后取机状态信息
     * @param shouhouId
     * @return
     */
    Integer updateShouhouQujiStats(@Param("shouhouId") Integer shouhouId);

    /**
     * 获取售后预约id
     * @param shouhouId
     * @return
     */
    Integer getShouhouYuyueId(@Param("shouhouId") Integer shouhouId);

    /**
     * 更新优惠码使用情况
     * @param shouhouId
     * @return
     */
    Integer updateNumberCardInfo(@Param("shouhouId") Integer shouhouId);

    /**
     * 查询待取消优惠码信息
     * @param yuyueId
     * @return
     */
    YouHuiMaBo getCancelYouhuimaIdInfo(@Param("yuyueId") Integer yuyueId);

    /**
     * 查询售后收件信息
     * @param shouhouId
     * @return
     */
    Shouhou queryShouhouShoujianInfo(@Param("shouhouId") Integer shouhouId);

    /**
     * 查询售后提成
     * @param imei
     * @return
     */
    Shouhou getShouhouIsticheng(@Param("imei") String imei);

    /**
     * 审核
     * @param id
     * @return
     */
    Integer acept(@Param("id") Integer id);

    /**
     * 根据维修ID获取优惠费用 通过代码来过滤
     * @param wxId
     * @return
     */
    @Deprecated
    BigDecimal getYouhuiTotalByWxId(@Param("wxId") Integer wxId);

    /**
     * 软删除售后订单信息
     * @param shouhouId
     * @param sapLogMsg
     * @return
     */
    int updateToDelShouhouApply(@Param("shouhouId") Integer shouhouId, @Param("sapLogMsg") String sapLogMsg);

    /**
     * 是否存在库存出库记录
     * @param ppid
     * @param shouhouId
     * @param wxkcId
     * @return
     */
    boolean existKcOut(@Param("ppid") Integer ppid, @Param("shouhouId") Integer shouhouId, @Param("wxkcId") Integer wxkcId);

    /**
     * 配件配置列表信息,优先匹配与主商品相匹配的项
     * @param productId
     * @return
     */
    @DS(DataSourceConstants.WEB999)
    List<RProductResultBo> listPjRproduct(@Param("productId") Integer productId, @Param("pjProductIds") List<Integer> pjProductIds);
}
