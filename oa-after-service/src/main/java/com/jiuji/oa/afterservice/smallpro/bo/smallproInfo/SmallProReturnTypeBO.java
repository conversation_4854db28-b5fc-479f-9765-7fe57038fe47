package com.jiuji.oa.afterservice.smallpro.bo.smallproInfo;

import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * description: <小件接件详情页退款方式BO>
 * translation: <Small piece connection details page booking method BO>
 *
 * <AUTHOR>
 * @date 2020/4/20
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallProReturnTypeBO implements Serializable {

    private static final long serialVersionUID = -337492725351085269L;
    /**
     * 退款方式名称
     */
    @ApiModelProperty(value = "退款方式名称")
    private String returnName;

    /**
     * 是否有支付单号
     */
    @ApiModelProperty(value = "是否有支付单号")
    private Boolean isHavePayInfo;

    @ApiModelProperty(value = "当前退款方式对应的订单支付记录")
    private List<SmallproPayInfoBO> netPayRecordList;

    private Boolean isOriginPath;
}
