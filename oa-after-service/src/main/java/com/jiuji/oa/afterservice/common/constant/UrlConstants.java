package com.jiuji.oa.afterservice.common.constant;

/**
 * @author: gengjiaping
 * @date: 2020/3/13
 */
public class UrlConstants {
    /**
     * 获取维修类别
     */
    public static final String REPAIR_TYPE_URL = "%s/oaapi/api.ashx?act=GetRepairClassify&rd=%s";

    /**
     * 保修信息查询
     */
    public static final String REPAIR_SERVICE_URL = "%s/ajax.ashx?act=getrecord&imei=%s&xtenant=%s";

    /**
     * 获取预约配件
     */
    public static final String GET_YUYUE_PEIJIAN = "/oaapi/api.ashx?act=GetFixParts&pid=%s&ppid=%s&troubleId=%s&rd=%s";

    /**
     * oa消息推送
     */
    public static final String OA_MSG_GET_URL = "http://inwcf.ch999.cn/ajax.ashx?act=oaMessagePush&content=%s&link=%s&ch999ids=%s&msgType=%s";

    /**
     * 保修和购买信息URL
     */
    public static final String BAOXIU_BUY_RUL = "%s/ajax.ashx?act=getrecord&imei=%s&xtenant=%s";

    /**
     * 获取百度坐标URL
     */
    @Deprecated
    public static final String BAIDU_GPS_URL = "http://api.map.baidu.com/geocoder/v2/?address=%s&city=&output=json&ak=3XTBZ-Y2TKR-OAVWG-WRAB2-XWWQH-O6BYJ&ret_coordtype=WGS84";

    /**
     * 获取定时门店列表URL
     */
    public static final String TIMING_AREAS_URL = "https://www.9ji.com/api/3_0/areaHandler.ashx?act=PlanShopNodes";

    /**
     * 售后碎屏保服务介绍url 81682
     */
    public static final String AFTER_SALE_BROKEN_SCREEN_INSURANCE = "/doc/1/%s";

    /**
     * 售后电池保服务介绍url 81683
     */
    public static final String AFTER_SALE_BATTERY_INSURANCE = "/doc/1/%s";

    /**
     * 售后后该保服务介绍URL 110939
     */
    public static final String BACK_COVER = "/doc/11/%s";

    /**
     * 通过物流单号取消物流单
     */
    public static final String LOGISTICS_CENTER_CANCEL_ORDER = "{}/cloudapi_nc/logistics/api/logistics-center/cancel-order/v2?xservicename=logistics-service";

}
