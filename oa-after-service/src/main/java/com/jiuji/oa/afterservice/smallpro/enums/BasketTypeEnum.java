package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: <订单类别枚举类>
 * translation: <Order category enumeration class>
 *
 * <AUTHOR>
 * @date 16:09 2019/11/13
 * @since 1.0.0
 **/
@AllArgsConstructor
@Getter
public enum BasketTypeEnum implements CodeMessageEnumInterface {

    /**
     * 订单类别 编码-信息
     */
    BASKET_TYPE_NULL(null, ""),
    BASKET_TYPE_NETWORK(0, "正常购买"),
    BASKET_TYPE_GIFT(1, "赠品"),
    BASKET_TYPE_JIUJI_SERVICE(2, "九机服务"),
    BASKET_TYPE_LIANTONG_CONTRACT(3, "联通合约"),
    BASKET_TYPE_DIANXIN_CONTRACT(4, "电信合约"),
    BASKET_TYPE_AUCTION(6, "拍卖"),
    BASKET_TYPE_GROUP(7, "团购"),
    BASKET_TYPE_YIDONG_PACKAGE(8, "移动套餐"),
    BASKET_TYPE_LIANG(9, "靓号"),
    BASKET_TYPE_COMBINATION_PURCHASE(10, "组合购买"),
    BASKET_TYPE_PANIC_BUYING(11, "抢购"),
    BASKET_TYPE_RESERVATION_PRODUCT(12,"预约商品"),
    BASKET_TYPE_MEMBER_CLUB_POINT_MALL(13,"会员俱乐部积分商城"),
    BASKET_TYPE_TRY(14, "试用"),
    BASKET_TYPE_WINNING_PRODUCT(15, "中奖商品"),
    BASKET_TYPE_RECHARGE(16, "话费充值"),
    BASKET_TYPE_CROWDFUNDING_PRODUCT(17, "众筹商品"),
    BASKET_TYPE_WEBSITE_PROMOTION(18, "网站促销"),
    BASKET_TYPE_ASSEMBLY_MACHINE(19, "组装机"),
    BASKET_TYPE_TRAFFIC_RECHARGE(20, "流量充值"),
    BASKET_TYPE_OFFICIAL_SERVICE(21, "官方服务"),
    BASKET_TYPE_DEFECT_MACHINE(22, "瑕疵机"),
    BASKET_TYPE_ZERO_DELIVERY(23, "零点配送"),
    BASKET_TYPE_TRADE_IN(24, "以旧换新"),
    BASKET_TYPE_DEFECT_PRODUCT_PANIC_BUY(25, "瑕疵商品抢购"),
    BASKET_TYPE_DEPOSIT_RESERVATION(26, "订金预定"),
    BASKET_TYPE_DEPOSIT_RESERVATION_ZERO_DELIVERY(27, "订金预定且需要零点配送"),
    BASKET_TYPE_GIFT_BOX(28, "礼品盒"),
    BASKET_TYPE_EMPLOYEE_POINTS(29, "员工积分兑换"),
    BASKET_TYPE_UNKNOWN_30(30, "Unknown"),
    BASKET_TYPE_UNKNOWN_78(78, "Unknown"),
    BASKET_TYPE_30PART_OFF_MACHINE(80, "7折购机"),
    BASKET_TYPE_HALF_PRICE_FOR_NEW(81, "半价换新"),
    BASKET_TYPE_LONG_RENT(82, "长租"),
    BASKET_TYPE_DISPLAY_INVENTORY_SALE(83, "陈列库存销售"),
    BASKET_TYPE_GIFT_INVENTORY_SALE(84, "赠品库存销售"),
    BASKET_TYPE_BUY_FILM_YEAR_CARD(85, "购买贴膜年卡"),
    BASKET_TYPE_TRIAL_FILM_YEAR_CARD(86, "使用贴膜年卡"),
    BASKET_TYPE_ACCESSORY_GIFT(90, "配件赠送"),
    BASKET_TYPE_STEEL_FILM_HALF_PRICE_REPURCHASE(91, "钢化膜半价复购"),
    BASKET_TYPE_MOBILE_POINT_REDEMPTION(92, "移动积分兑换"),
    BASKET_TYPE_LIMITED_TIME_PURCHASE(93, "限时购"),
    BASKET_TYPE_CUT_PRICE(94, "砍价购");


    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;
}
