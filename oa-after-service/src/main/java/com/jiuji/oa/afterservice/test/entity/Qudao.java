/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.jiuji.oa.afterservice.test.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 渠道表
 *
 * <AUTHOR>
 * @date 2020-10-25 15:11:23
 */
@Data
@TableName("shouhou_qudao")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "渠道表")
public class Qudao extends Model<Qudao> {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId
    @ApiModelProperty(value = "$column.comments")
    private Integer id;
    /**
     * 外送渠道类别
     */
    @ApiModelProperty(value = "外送渠道类别")
    private Integer shqdid;
    /**
     * 外送渠道ID
     */
    @ApiModelProperty(value = "外送渠道ID")
    private Integer shqd2id;
    /**
     * 外送渠道名称
     */
    @ApiModelProperty(value = "外送渠道名称")
    private String shqd2name;
    /**
     * 售后id
     */
    @ApiModelProperty(value = "售后id")
    private Integer shouhouid;
    /**
     * 提交人
     */
    @ApiModelProperty(value = "提交人")
    private String inuser;
    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private LocalDateTime dtime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime starttime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endtime;
    /**
     * 类别[客户机,新机,不在保,返修]
     */
    @ApiModelProperty(value = "类别[客户机,新机,不在保,返修]")
    private Integer type;
}
