package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.oa.afterservice.smallpro.vo.ShowPrintingEnumVO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户类型
 */
@Getter
@AllArgsConstructor
public enum SelectResistFilmOrderTypeEnum implements CodeMessageEnumInterface {
    AFTER_SALES_ORDER(1, "售后单"),
    CASH_ORDER(2, "现货单");

    private final Integer code;
    private final String message;

    /**
     * 将所有的枚举转换成list
     *
     * @return
     */
    public static List<ShowPrintingEnumVO> getAllPrintingEnum() {
        SelectResistFilmOrderTypeEnum[] array = SelectResistFilmOrderTypeEnum.values();
        List<ShowPrintingEnumVO> arrayList = new ArrayList<>();
        for (SelectResistFilmOrderTypeEnum t : array) {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO()
                    .setLabel(t.getMessage())
                    .setValue(t.getCode());
            arrayList.add(showPrintingEnumVO);
        }
        return arrayList;
    }
}
