package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.po.SubCollection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Mapper
public interface SubCollectionMapper extends BaseMapper<SubCollection> {
    /**
     * 获取订单的ch99id
     * @param subId
     * @return
     */
    List<Integer> getSubCollectionUserIds(@Param("subId") Integer subId);
}
