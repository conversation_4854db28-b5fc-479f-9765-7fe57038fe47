package com.jiuji.oa.afterservice.bigpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.afterservice.bigpro.enums.ServiceTypeEnum;
import com.jiuji.oa.nc.partjob.group.Update;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 售后服务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shouhou_service_config")
public class ShouhouServiceConfig extends Model<ShouhouServiceConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("自动编号")
    @NotNull(groups = {Update.class})
    private Long id;

    /**
     * ppid
     */
    @ApiModelProperty("商品ppid")
    @NotNull(message = "商品ppid不能为空")
    private Integer ppid;

    /**
     * 价格
     */
    @ApiModelProperty("配件价格")
    @NotNull(groups = {Default.class})
    private BigDecimal price;

    /**
     * 服务ppid
     */
    @ApiModelProperty("九机服务ppid")
    private Integer servicePpid;

    /**
     * 服务类型：9九机快修碎屏险,10九机快修电池保
     * @see ServiceTypeEnum
     */
    @ApiModelProperty("服务类型")
    @NotNull(message = "服务类型不能为空")
    private Integer serviceType;

    /**
     * 服务名称：9九机快修碎屏险,10九机快修电池保
     */
    @TableField(exist = false)
    @ApiModelProperty("服务名称")
    private String servicesName;

    /**
     * 租户
     */
    @ApiModelProperty("租户")
    private Integer xtenant;


    @ApiModelProperty("租户名称")
    @TableField(exist = false)
    private String xtenantName;

    /**
     * 是否删除：0否，1是
     */

    @ApiModelProperty("删除标识")
    private Boolean isDel;

    /**
     * 是否启用：0否，1是
     */
    @ApiModelProperty("是否启用")
    @TableField("[disable]")
    private Boolean disable;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "维修配件名称")
    @TableField("product_name")
    private String productName;

    @TableField(exist = false)
    @ApiModelProperty(value = "规格")
    private String productColor;

    @TableField(exist = false)
    @ApiModelProperty(value = "url")
    private String url;

    @TableField(exist = false)
    private String description;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
