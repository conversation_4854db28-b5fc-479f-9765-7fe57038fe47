package com.jiuji.oa.afterservice.lossReview.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.cloud.after.enums.JiujiServiceTypeEnum;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogNoticeBo;
import com.jiuji.oa.afterservice.bigpro.enums.BaoxiuStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.RecoverMarketsubinfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.lossReview.dao.LossReviewAuditMapper;
import com.jiuji.oa.afterservice.lossReview.enums.*;
import com.jiuji.oa.afterservice.lossReview.po.LossReviewAudit;
import com.jiuji.oa.afterservice.lossReview.po.LossReviewAuditDetail;
import com.jiuji.oa.afterservice.lossReview.service.ILossReviewAuditDetailService;
import com.jiuji.oa.afterservice.lossReview.service.ILossReviewAuditService;
import com.jiuji.oa.afterservice.lossReview.vo.req.*;
import com.jiuji.oa.afterservice.lossReview.vo.res.LossReviewAuditPageOutputRes;
import com.jiuji.oa.afterservice.lossReview.vo.res.LossReviewAuditPageRes;
import com.jiuji.oa.afterservice.lossReview.vo.res.LossReviewAuditRes;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.rabbitmq.bo.LossReviewShouhouMqMessageBo;
import com.jiuji.oa.afterservice.refund.enums.TradeTypeEnum;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-07-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
@DS("oanewWrite")
public class LossReviewAuditServiceImpl extends ServiceImpl<LossReviewAuditMapper, LossReviewAudit> implements ILossReviewAuditService {

    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    @Resource
    private SubService subService;

    @Resource
    private BasketService basketService;

    @Resource
    private AreainfoService areainfoService;

    @Resource
    private ShouhouLogsService shouhouLogsService;

    @Resource
    private ShouhouService shouhouService;

    @Resource
    private WxkcoutputService wxkcoutputService;

    @Resource
    private ProductinfoService productinfoService;

    @Resource
    private RecoverMarketinfoService recoverMarketinfoService;

    @Resource
    private IRecoverMarketsubinfoService recoverMarketsubinfoService;

    @Resource
    private ILossReviewAuditDetailService lossReviewAuditDetailService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private UserInfoClient userInfoClient;

    private static final String FINE_RANK = "shpf";



    @Override
    public Map<String, List<EnumVO>> getEnums() {
        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        List<EnumVO> baoxiuStatusEnum = EnumUtil.toEnumVOList(BaoxiuStatusEnum.class);
        List<EnumVO> tradeTypeEnum = EnumUtil.toEnumVOList(TradeTypeEnum.class);
        List<EnumVO> jiujiServiceTypeEnum = EnumUtil.toEnumVOList(JiujiServiceTypeEnum.class);
        List<EnumVO> errorKindEnum = EnumUtil.toEnumVOList(ErrorKindEnum.class);
        List<EnumVO> errorTypeEnum = EnumUtil.toEnumVOList(ErrorTypeEnum.class).stream()
                .filter(x-> !ErrorTypeEnum.HUAN_HUO_SHANG_PIN_KUI_SUN.getCode().equals(x.getValue())).collect(Collectors.toList());
        List<EnumVO> auditStatsEnum = EnumUtil.toEnumVOList(AuditStatsEnum.class);
        List<EnumVO> specialQualityAssuranceEnum = EnumUtil.toEnumVOList(SpecialQualityAssuranceEnum.class);
        List<EnumVO> lossListSearchTypeEnum = EnumUtil.toEnumVOList(LossListSearchTypeEnum.class);
        enumMap.put("baoxiuStatusEnum", baoxiuStatusEnum);
        enumMap.put("lossListSearchTypeEnum", lossListSearchTypeEnum);
        enumMap.put("tradeTypeEnum", tradeTypeEnum);
        enumMap.put("jiujiServiceTypeEnum", jiujiServiceTypeEnum);
        enumMap.put("errorTypeEnum", errorTypeEnum);
        enumMap.put("errorKindEnum", errorKindEnum);
        enumMap.put("auditStatsEnum", auditStatsEnum);
        enumMap.put("specialQualityAssuranceEnum", specialQualityAssuranceEnum);
        return enumMap;
    }

    @Override
    public R<Boolean> lossReviewAudit(LossReviewAuditReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String userName = oaUserBO.getUserName();
        Integer shouhouId = req.getShouhouId();
        String checkReason1 = req.getCheckReason1();
        String checkReason2 = req.getCheckReason2();
        LocalDateTime dtime1 = req.getDtime1();
        LocalDateTime dtime2 = req.getDtime2();

        List<LossReviewAudit> lossReviewAuditList = this.lambdaQuery().eq(LossReviewAudit::getShouhouId, shouhouId).list();
        if (CollectionUtils.isEmpty(lossReviewAuditList)) {
            return R.success(true);
        }
        LossReviewAudit lossReviewAudit = lossReviewAuditList.get(0);
        Integer auditStatus = lossReviewAudit.getAuditStatus();
        AuditStatsEnum auditStatsEnum = AuditStatsEnum.getEnumByCode(auditStatus);
        if (Objects.isNull(auditStatsEnum)) {
            return R.success(true);
        }
        ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
        shouhouLogNoticeBo.setNeedNotice(false);
        switch (auditStatsEnum) {
            case TWO:
                if (Objects.isNull(dtime1)) {
                    return R.success(true);
                }
                for (LossReviewAudit reviewAudit : lossReviewAuditList) {
                    if (!AuditStatsEnum.ONE.getMessage().equals(reviewAudit.getCheckUser1())) {
                        UpdateWrapper<LossReviewAudit> updateWrapper = new UpdateWrapper<>();
                        LambdaUpdateWrapper<LossReviewAudit> lambda = updateWrapper.lambda();
                        lambda.set(LossReviewAudit::getDtime1, dtime1);
                        lambda.set(LossReviewAudit::getCheckUser1, userName);
                        lambda.set(LossReviewAudit::getCheckReason1, checkReason1);

                        Integer auditStatusResult;
                        if (!AuditStatsEnum.ONE.getMessage().equals(reviewAudit.getCheckUser2())) {
                            auditStatusResult = AuditStatsEnum.THREE.getCode();
                        } else {
                            lambda.set(LossReviewAudit::getDtime2, dtime1);
                            auditStatusResult = AuditStatsEnum.FOUR.getCode();
                        }
                        lambda.set(LossReviewAudit::getAuditStatus, auditStatusResult);
                        lambda.eq(LossReviewAudit::getId, reviewAudit.getId());
                        this.update(updateWrapper);
                    }
                }
                shouhouLogsService.addShouhouLog(userName, shouhouId, ShouHouLogTypeEnum.CLXX.getCode(),
                        "亏损审核一操作，原因：" + checkReason1, shouhouLogNoticeBo, false, 0);
                break;
            case THREE:
                if (Objects.isNull(dtime2)) {
                    return R.success(true);
                }
                for (LossReviewAudit reviewAudit : lossReviewAuditList) {
                    if (!AuditStatsEnum.ONE.getMessage().equals(reviewAudit.getCheckUser2())) {
                        UpdateWrapper<LossReviewAudit> updateWrapper = new UpdateWrapper<>();
                        LambdaUpdateWrapper<LossReviewAudit> lambda = updateWrapper.lambda();
                        lambda.set(LossReviewAudit::getDtime2, dtime2);
                        lambda.set(LossReviewAudit::getCheckUser2, userName);
                        lambda.set(LossReviewAudit::getCheckReason2, checkReason2);
                        lambda.set(LossReviewAudit::getAuditStatus, AuditStatsEnum.FOUR.getCode());
                        lambda.eq(LossReviewAudit::getId, reviewAudit.getId());
                        this.update(updateWrapper);
                    }
                }
                shouhouLogsService.addShouhouLog(userName, shouhouId, ShouHouLogTypeEnum.CLXX.getCode(),
                        "亏损审核二操作，原因：" + checkReason2, shouhouLogNoticeBo, false, 0);
                break;
            default:
        }
        return R.success(true);
    }

    @Override
    public R<Boolean> specialQualityAssurance(LossReviewAuditReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String userName = oaUserBO.getUserName();
        Integer shouhouId = req.getShouhouId();
        if (Objects.isNull(shouhouId)) {
            throw new CustomizeException("维修单号不能为空");
        }
        Integer wxkcoutputId = req.getWxkcoutputId();
        if (Objects.isNull(wxkcoutputId)) {
            throw new CustomizeException("wxkcoutputId不能为空");
        }
        String specialQualityAssuranceReason = req.getSpecialQualityAssuranceReason();
        Integer specialQualityAssurance = req.getSpecialQualityAssurance();

        String specialQualityAssuranceName = SpecialQualityAssuranceEnum.getMessageByCode(specialQualityAssurance);
        Shouhou shouhou = shouhouService.getById(shouhouId);
        if (Objects.isNull(shouhou)) {
            throw new CustomizeException("找不到维修单:" + shouhouId);
        }

        if (SpecialQualityAssuranceEnum.ONE.getMessage().equals(specialQualityAssuranceName)) {
            int oldShouhouId;
            if (Objects.isNull(req.getOldShouhouId())) {
                throw new CustomizeException("老维修单不能为空");
            }
            try {
                oldShouhouId = Integer.parseInt(req.getOldShouhouId());
            } catch (NumberFormatException e) {
                return R.error("请输入正确的单号");
            }

            if (Objects.equals(shouhouId, oldShouhouId)) {
                throw new CustomizeException("返修单号与当前单号一致,不能提交。");
            }

            Shouhou oldShouhou = shouhouService.getById(oldShouhouId);
            if (Objects.isNull(oldShouhou)) {
                throw new CustomizeException("找不到老维修单:" + oldShouhouId);
            }
            if (!Objects.equals(shouhou.getImei(), oldShouhou.getImei())) {
                throw new CustomizeException("新老维修单串号不一致:" + oldShouhouId);
            }
        }
        ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
        shouhouLogNoticeBo.setNeedNotice(false);
        Wxkcoutput wxkcoutput = wxkcoutputService.getById(wxkcoutputId);
        if (Objects.nonNull(wxkcoutput.getSpecialQualityAssurance())) {
            throw new CustomizeException("特殊质保重复提交");
        }

        Integer ppriceid = wxkcoutput.getPpriceid();
        Productinfo productinfo = productinfoService.getProductinfoByPpid(ppriceid);
        String comment;
        if (Objects.nonNull(productinfo)) {
            comment = "特殊质保：" + productinfo.getProductName() + productinfo.getProductColor() + " ，"
                    + " 类型：" + specialQualityAssuranceName
                    + " 原因：" + specialQualityAssuranceReason;
        } else {
            comment = "特殊质保："
                    + " 类型：" + specialQualityAssuranceName
                    + " 原因：" + specialQualityAssuranceReason;
        }
        UpdateWrapper<Wxkcoutput> updateWrapper = new UpdateWrapper<>();
        LambdaUpdateWrapper<Wxkcoutput> lambda = updateWrapper.lambda();
        lambda.set(Wxkcoutput::getSpecialQualityAssurance, specialQualityAssurance);
        lambda.set(Wxkcoutput::getSpecialQualityAssuranceReason, specialQualityAssuranceReason);
        if (SpecialQualityAssuranceEnum.ONE.getMessage().equals(specialQualityAssuranceName)) {
            lambda.set(Wxkcoutput::getOldShouhouId, req.getOldShouhouId());
        }
        lambda.eq(Wxkcoutput::getId, wxkcoutputId);
        wxkcoutputService.update(updateWrapper);
        shouhouLogsService.addShouhouLog(userName, shouhouId, ShouHouLogTypeEnum.CLXX.getCode(),
                comment, shouhouLogNoticeBo, false, 0);
        return R.success(true);
    }

    @Override
    public R<Boolean> wxkcoutputPunishSub(LossReviewAuditReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String userName = oaUserBO.getUserName();
        Integer shouhouId = req.getShouhouId();
        if (Objects.isNull(shouhouId)) {
            throw new CustomizeException("维修单号不能为空");
        }
        Integer wxkcoutputId = req.getWxkcoutputId();
        if (Objects.isNull(wxkcoutputId)) {
            throw new CustomizeException("wxkcoutputId不能为空");
        }
        Integer punishSubId;
        if (Objects.isNull(req.getPunishSubId())) {
            throw new CustomizeException("乐捐单号不能为空");
        }
        try {
            punishSubId = Integer.parseInt(req.getPunishSubId());
        } catch (NumberFormatException e) {
            return R.error("请输入正确的单号");
        }

        Integer checkFlag = this.baseMapper.checkPunishSubExist(Convert.toStr(punishSubId));
        if (Objects.isNull(checkFlag)) {
            throw new CustomizeException("乐捐单号不存在或乐捐单号状态错误");
        }
        ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
        shouhouLogNoticeBo.setNeedNotice(false);
        Wxkcoutput wxkcoutput = wxkcoutputService.getById(wxkcoutputId);
        if (Objects.nonNull(wxkcoutput.getPunishSub())) {
            throw new CustomizeException("配件赔付重复提交");
        }
        //九机日志前面已经记录过一次了
        if(XtenantEnum.isSaasXtenant()){
            Integer ppriceid = wxkcoutput.getPpriceid();
            String comment;
            if (ObjectUtil.defaultIfNull(ppriceid, 0) > 0) {
                Productinfo productinfo = productinfoService.getProductinfoByPpid(ppriceid);
                comment = "配件赔付：" + productinfo.getProductName() + productinfo.getProductColor()
                        + " ，乐捐单号：" + punishSubId;
            } else {
                comment = StrUtil.format("配件赔付：{}，乐捐单号：{}", wxkcoutput.getName(), punishSubId);
            }
            shouhouLogsService.addShouhouLog(userName, shouhouId, ShouHouLogTypeEnum.CLXX.getCode(),
                    comment, shouhouLogNoticeBo, false, 0);
        }
        UpdateWrapper<Wxkcoutput> updateWrapper = new UpdateWrapper<>();
        LambdaUpdateWrapper<Wxkcoutput> lambda = updateWrapper.lambda();
        lambda.set(Wxkcoutput::getPunishSub, punishSubId);
        lambda.set(Wxkcoutput::getPunishSubTime, LocalDateTime.now());
        lambda.eq(Wxkcoutput::getId, wxkcoutputId);
        wxkcoutputService.update(updateWrapper);
        return R.success(true);
    }

    @Override
    public R<Boolean> wxkcoutputPunishSubV1(LossReviewAuditReq req) {
        //自动生成乐捐单
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData).filter(StrUtil::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String url = host+"/HROAApi/AddPunishList";
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息超时"));
        List<String> rankList = Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>());
        if(XtenantEnum.isJiujiXtenant() && !rankList.contains(FINE_RANK)){
            throw new CustomizeException("没有权限，不能生成乐捐单");
        }
        //封装参数
        CreateDonateVoluntarilyReq createDonateVoluntarilyReq = new CreateDonateVoluntarilyReq();
        Integer shouHouId = Optional.ofNullable(req.getShouhouId()).orElseThrow(() -> new CustomizeException("维修单号不能为空"));
        Integer wxkcoutputId = Optional.ofNullable(req.getWxkcoutputId()).orElseThrow(() -> new CustomizeException("维修配件id不能为空"));
        Shouhou shouhou = Optional.ofNullable(shouhouService.getById(shouHouId)).orElseThrow(() -> new CustomizeException("维修单查询为空"));
        String weixiuren = shouhou.getWeixiuren();
        if(StrUtil.isEmpty(weixiuren)){
            throw new CustomizeException("维修人为空不能生成乐捐单");
        }
        createDonateVoluntarilyReq.setTitle("维修单"+shouHouId+"赔付");
        createDonateVoluntarilyReq.setAddUser(userBO.getUserName())
                .setAddUserId(userBO.getUserId());
        //获取维修人的后台地区
        R<Ch999UserVo> ch999UserByUserName = userInfoClient.getCh999UserByUserName(weixiuren);
        if(ch999UserByUserName.isSuccess()){
            log.warn("获取维修人的后台地区传入参数：{}，返回结果：{}",weixiuren,JSONUtil.toJsonStr(ch999UserByUserName));
            Ch999UserVo ch999UserVo = Optional.ofNullable(ch999UserByUserName.getData()).orElseThrow(() -> new CustomizeException("调用办公获取维修人所属地地区返回数据为空"));
            createDonateVoluntarilyReq.setAreaId(Optional.ofNullable(ch999UserVo.getArea1id()).orElseThrow(()-> new CustomizeException("调用办公获取维修人所属地地区返回结果为空")));
        } else {
            throw new CustomizeException("调用办公获取维修人所属地区失败");
        }
        Wxkcoutput wxkcoutput = Optional.ofNullable(wxkcoutputService.getById(wxkcoutputId)).orElseThrow(()->new CustomizeException("维修配件查询为空"));
        createDonateVoluntarilyReq.setType(NumberConstant.ZERO)
                .setLevel(NumberConstant.ONE)
                .setReasion("维修单"+shouHouId+"赔付，配件（"+wxkcoutput.getName()+"）");
        //封装处罚依据
        PunishEvidence punishEvidence = new PunishEvidence();
        String evidenceUrl = host + "/cloudapi_nc/office/api/libraryAuditedDoc/donationDocSelect/v1?q=" + PunishEvidence.DEFAULT_FNAME;
        HttpResponse evidenceResult = HttpUtil.createGet(evidenceUrl)
                .header("xservicename", "oa-office")
                .header("authorization", userBO.getToken())
                .execute();
        if(evidenceResult.isOk()){
            log.warn("调用依据查询生成接口传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(result.isSuccess()){
                Object data = result.getData();
                if(ObjectUtil.isNull(data)){
                    throw new CustomizeException("调用依据查询生成返回数据为空");
                } else {
                    List<DonationDoc> donationDocs = JSONUtil.toList(JSONUtil.toJsonStr(data), DonationDoc.class);
                    DonationDoc donationDoc = donationDocs.get(0);
                    punishEvidence.setFname(donationDoc.getTitle())
                            .setFid(donationDoc.getId().toString());
                    createDonateVoluntarilyReq.setPunishEvidenceList(Collections.singletonList(punishEvidence));
                }
            } else {
                throw new CustomizeException("生成乐捐单失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("调用依据查询生成接口异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("调用依据查询生成接口异常");
        }
        //封装处罚对象
        PunishmentTargets punishmentTargets = new PunishmentTargets();
        punishmentTargets.setFine(wxkcoutput.getInprice())
                .setCh999Name(weixiuren)
                .setLeve(NumberConstant.ONE.toString())
                .setCh999Id(ch999UserByUserName.getData().getCh999Id());
        createDonateVoluntarilyReq.setDetailList(Collections.singletonList(punishmentTargets));

        //日志封装
        ExtraLogReq extraLogReq = new ExtraLogReq();
        extraLogReq.setContent("生成乐捐单，来源：维修单维修配件赔付，维修单:【<a href='/staticpc/#/after-service/order/edit/" + shouHouId + "' title='点击查看订单详细' >" + shouHouId + "</a>】");
        createDonateVoluntarilyReq.setExtraLogList(Collections.singletonList(extraLogReq));
        String parameter = JSONUtil.toJsonStr(Collections.singletonList(createDonateVoluntarilyReq));
        HttpResponse response = HttpUtil.createPost(url).header("Authorization", userBO.getToken())
                .body(parameter)
                .execute();
        if(response.isOk()){
            log.warn("调用乐捐生成接口传入参数：{}，返回结果：{}",parameter,response.body());
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(response.body()), R.class);
            if(result.isSuccess()){
                Object data = result.getData();
                if(ObjectUtil.isNull(data)){
                    throw new CustomizeException("生成乐捐返回数据为空");
                } else {
                    req.setPunishSubId(data.toString());
                    //进行维修单日志记录
                    ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
                    shouhouLogNoticeBo.setNeedNotice(false);
                    //生成乐捐单跳转日志
                    String comment = wxkcoutput.getName()+"，员工赔付操作，生成乐捐单:【<a href='/punish/PunishDetail?sub_id=" + data + "' title='点击查看订单详细' >" + data + "</a>】";
                    shouhouLogsService.addShouhouLog(userBO.getUserName(), shouHouId, ShouHouLogTypeEnum.CLXX.getCode(),
                            comment, shouhouLogNoticeBo, false, 0);
                }
            } else {
                throw new CustomizeException("生成乐捐单失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("调用乐捐生成接口异常传入参数：{}",parameter);
            throw new CustomizeException("调用乐捐生成接口异常");
        }
        return wxkcoutputPunishSub(req);
    }

    @Override
    public R<String> scanError(LossReviewShouhouMqMessageBo req) {
//        Integer shouhouId = req.getShouhouId();
//        Shouhou shouhou = shouhouService.getById(shouhouId);
//        LossReviewAuditReq v = new LossReviewAuditReq();
//        LossReviewAuditRes lossReviewAuditDetail = this.getLossReviewAuditDetail(v);
//        Long userId;
//        String mobile = shouhou.getMobile();
//        boolean isInnerUser = this.baseMapper.isInnerUserExistByMobile(mobile);
//
//        if (1 == shouhou.getIshuishou()) {
//            RecoverMarketinfo recoverMarketinfo = recoverMarketinfoService.getById(shouhou.getSubId());
//            userId = recoverMarketinfo.getUserid();
//        } else {
//            Sub sub = subService.getById(shouhou.getSubId());
//            userId = sub.getUserId();
//        }
//
//        Integer serviceType = shouhou.getServiceType();
//        List<ErrorTypeEnum> list = ErrorTypeEnum.getList();
//        for (ErrorTypeEnum errorTypeEnum : list) {
//            switch (errorTypeEnum) {
//                case ONE:
//                    JiujiServiceTypeEnum jiujiServiceTypeEnum = JiujiServiceTypeEnum.valueOfByCode(serviceType).orElse(null);
//                    if (Objects.isNull(jiujiServiceTypeEnum)) {
//                        break;
//                    }
//                    if (lossReviewAuditDetail.getLossSum().compareTo(BigDecimal.ZERO) >= 0) {
//                        break;
//                    }
//                    if (Objects.equals(shouhou.getUserid(), userId) || isInnerUser ) {
//                        userInfoClient.
//                    }
//
//
//                case TWO:
//                    break;
//                case THREE:
//                    break;
//                case FOUR:
//                    break;
//                case FIVE:
//                    break;
//                case SIX:
//                    break;
//                case SEVEN:
//                    break;
//                case EIGHT:
//                    break;
//                case NINE:
//                    break;
//                case TEN:
//                    break;
//                case ELEVEN:
//                    break;
//                case TWELVE:
//                    break;
//                default:
//            }
//
//        }

//        LossReviewAudit temp = new LossReviewAudit();
//        temp.setAuditStatus(AuditStatsEnum.ONE.getCode());
//        temp.setCheckUser1("待审核");
//        temp.setCheckUser2("待审核");
//        temp.setLossAmount();
//        temp.setErrorType();
//        temp.setKind();
//        temp.setShouhouId();
//        this.save(temp);
        return R.success("执行成功");
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<Page<LossReviewAuditPageRes>> getLossReviewAuditPage(LossReviewAuditPageReq req) {
        //角色数据查询
        if (CollUtil.isEmpty(req.getAuditStatus()) || req.getAuditStatus().contains(AuditStatsEnum.FOUR.getCode())) {
            R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.AFTER_SALES)
                    .getStartTimeFun(req::getStartTime)
                    .getEndTimeFun(req::getEndTime)
                    .setStartTimeFun(req::setStartTime)
                    .setEndTimeFun(req::setEndTime)
                    .build(), null);
            if (!dataViewRes.isSuccess()) {
                throw new CustomizeException(dataViewRes.getUserMsg());
            }
        }
        Page<LossReviewAuditPageRes> page = new Page<>(req.getCurrent(), req.getSize());
        List<Integer> auditStatus = req.getAuditStatus();
        if (auditStatus.contains(AuditStatsEnum.ONE.getCode())) {
            req.setExemptReviewFlag(AuditStatsEnum.ONE.getCode());
        }

        Page<LossReviewAuditPageRes> res = baseMapper.getLossReviewAuditPage(page, req);
        List<LossReviewAuditPageRes> records = res.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return R.success(res);
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<Integer> idList = records.stream().map(LossReviewAuditPageRes::getId).collect(Collectors.toList());
        List<LossReviewAuditDetail> list = CommenUtil.bigDataInQuery
                (idList,ids -> lossReviewAuditDetailService.lambdaQuery()
                        .in(LossReviewAuditDetail::getLossReviewAuditId,ids )
                        .list());
        Map<Integer, List<LossReviewAuditDetail>> detailMap =
                list.stream().collect(Collectors.groupingBy(LossReviewAuditDetail::getLossReviewAuditId));

        List<Integer> basketIdList = records.stream().map(LossReviewAuditPageRes::getBasketId).distinct().collect(Collectors.toList());
        List<RecoverMarketsubinfo> recoverMarketsubinfoList = CommenUtil.bigDataInQuery
                (basketIdList,ids -> recoverMarketsubinfoService.lambdaQuery().in(RecoverMarketsubinfo::getBasketId, ids)
                        .list());
        Map<Integer, RecoverMarketsubinfo> recoverMarketsubMap = recoverMarketsubinfoList.stream()
                .collect(Collectors.toMap(RecoverMarketsubinfo::getBasketId, Function.identity(), (k1, k2) -> k1));
        List<Basket> basketList = CommenUtil.bigDataInQuery
                (basketIdList,ids -> CommenUtil.autoQueryHist(() -> basketService.lambdaQuery().in(Basket::getBasketId, ids)
                        .list()));
        Map<Integer, Basket> basketMap = basketList.stream()
                .collect(Collectors.toMap(Basket::getBasketId, Function.identity(), (k1, k2) -> k1));

        List<Integer> areaIdList = records.stream().map(LossReviewAuditPageRes::getAreaId).distinct().collect(Collectors.toList());
        Map<Integer, Areainfo> areaMap = areainfoService.getAreaMap(areaIdList);

        for (LossReviewAuditPageRes x : records) {
            Long subId;
            String orderType;
            if (Objects.nonNull(x.getIshuishou()) && x.getIshuishou()) {
                RecoverMarketsubinfo one = recoverMarketsubMap.get(x.getBasketId());
                //npe 修改
                if (Objects.nonNull(one)) {
                    subId = one.getSubId();
                    orderType = TradeTypeEnum.LIANG_PIN_SUB.getMessage();
                    x.setSubId(Convert.toStr(subId));
                    x.setOrderType(Convert.toStr(orderType));
                }
            } else {

                Basket basket = basketMap.get(x.getBasketId());
                if (Objects.nonNull(basket)) {
                    subId = basket.getSubId();
                    //是否优品
                    if ("22".equals(Convert.toStr(basket.getType()))) {
                        orderType = TradeTypeEnum.YOU_PIN_SUB.getMessage();
                    } else {
                        orderType = TradeTypeEnum.NEW_SUB.getMessage();
                    }
                    x.setSubId(Convert.toStr(subId));
                    x.setOrderType(Convert.toStr(orderType));
                }
            }

            Areainfo areainfo = areaMap.get(x.getAreaId());
            x.setArea(areainfo.getArea());
            x.setBaoxiuName(BaoxiuStatusEnum.getMessageByCode(x.getBaoxiu()));
            x.setServiceTypeName(EnumUtil.getMessageByCode(BaoXiuTypeEnum.class, x.getServiceType()));
            x.setAuditStatusName(AuditStatsEnum.getMessageByCode(x.getAuditStatus()));

            Integer id = x.getId();
            List<LossReviewAuditDetail> lossReviewAuditDetails = detailMap.get(id);
            BigDecimal lossAmountSum = lossReviewAuditDetails.stream().map(LossReviewAuditDetail::getLossAmount)
                    .filter(Objects::nonNull).reduce(BigDecimal::add).map(BigDecimal::abs).orElse(BigDecimal.ZERO);
            StringBuilder errorType = new StringBuilder();
            StringBuilder errorTypeName = new StringBuilder();
            StringBuilder errorReason = new StringBuilder();
            StringBuilder errorKindeName = new StringBuilder();
            for (LossReviewAuditDetail lossReviewAuditDetail : lossReviewAuditDetails) {
                errorType.append(lossReviewAuditDetail.getErrorType()).append(StringPool.SEMICOLON);
                errorTypeName.append(ErrorTypeEnum.getMessageByCode(lossReviewAuditDetail.getErrorType())).append(StringPool.SEMICOLON);
                errorReason.append(lossReviewAuditDetail.getErrorReason()).append(StringPool.SEMICOLON);
                errorKindeName.append(ErrorKindEnum.getMessageByCode(lossReviewAuditDetail.getKind())).append(StringPool.SEMICOLON);
            }
            x.setErrorType(Convert.toStr(errorType));
            x.setErrorTypeName(Convert.toStr(errorTypeName));
            x.setErrorReason(Convert.toStr(errorReason));
            x.setErrorKindeName(Convert.toStr(errorKindeName));
            StringBuilder shenhe1 = new StringBuilder();
            StringBuilder shenhe2 = new StringBuilder();
            if (lossAmountSum.compareTo(BigDecimal.ZERO) != 0) {
                if (StrUtil.isNotEmpty(x.getCheckUser1()) && Objects.nonNull(x.getDtime1())) {
                    shenhe1.append("一审:").append(x.getCheckUser1()).append("(").append(x.getDtime1().format(dateTimeFormatter)).append(") ");

                    if (StrUtil.isNotEmpty(x.getCheckUser2()) && Objects.nonNull(x.getDtime2())) {
                        shenhe2.append("二审:").append(x.getCheckUser2()).append("(").append(x.getDtime2().format(dateTimeFormatter)).append(") ");
                    } else if ("免审".equals(x.getCheckUser2())) {
                        shenhe2.append("二审:").append(x.getCheckUser2()).append("(").append(x.getDtime1().format(dateTimeFormatter)).append(") ");
                    } else {
                        shenhe2.append("二审:待审核 ");
                    }

                } else {
                    shenhe1.append("一审:待审核 ");
                }
            }

            x.setShenhe1(shenhe1.toString());
            x.setShenhe2(shenhe2.toString());
        }
        return R.success(res);
    }

    @Override
    public void fixLossReviewAuditPage(){
        LossReviewAuditPageReq req = new LossReviewAuditPageReq();
        req.setAuditStatus(Arrays.asList(AuditStatsEnum.TWO.getCode()));
        req.setCurrent(1);
        req.setStartTime(LocalDateTime.now().minusDays(1));
        req.setEndTime(LocalDateTime.now());
        // 默认最大 50000 条
        req.setSize(50000);
        R<Page<LossReviewAuditPageRes>> lossReviewAuditPage = this.getLossReviewAuditPage(req);
        Page<LossReviewAuditPageRes> page = lossReviewAuditPage.getData();
        List<LossReviewAuditPageRes> records = page.getRecords();
        List<LossReviewAuditPageRes> zeroLossReviewAudit = records.stream()
                .filter(x -> x.getLossAmount().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        List<Integer> zeroLossReviewAuditIdList = zeroLossReviewAudit.stream().map(LossReviewAuditPageRes::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(zeroLossReviewAuditIdList)){
            this.lambdaUpdate().in(LossReviewAudit::getId,zeroLossReviewAuditIdList)
                    .set(LossReviewAudit::getCheckUser1,"免审")
                    .set(LossReviewAudit::getAuditStatus,AuditStatsEnum.ONE.getCode())
                    .update();
        }

    }

    @Override
    public void exportLossReviewAuditPage(LossReviewAuditPageReq req, HttpServletResponse response) {
        req.setCurrent(1);
        // 默认最大 50000 条
        req.setSize(50000);
        log.warn("售后亏损审核导出条件: {}", JSON.toJSONString(req));
        R<Page<LossReviewAuditPageRes>> lossReviewAuditPage = this.getLossReviewAuditPage(req);
        ExcelWriter excel = ExcelUtil.getWriter();
        excel.addHeaderAlias("shouhouId", "维修单号");
        excel.addHeaderAlias("subId", "销售单号");
        excel.addHeaderAlias("orderType", "销售类型");
        excel.addHeaderAlias("area", "地区");
        excel.addHeaderAlias("baoxiuName", "保修状态");
        excel.addHeaderAlias("serviceTypeName", "出险服务");
        excel.addHeaderAlias("productName", "商品名称");
        excel.addHeaderAlias("feiyong", "维修费用");
        excel.addHeaderAlias("costprice", "维修成本");
        excel.addHeaderAlias("lossAmount", "亏损金额");
        excel.addHeaderAlias("problem", "故障描述");
        excel.addHeaderAlias("errorTypeName", "异常类型");
        excel.addHeaderAlias("errorReason", "异常原因");
        excel.addHeaderAlias("offtime", "取机时间");
        excel.addHeaderAlias("shenhe", "审核");

        List<LossReviewAuditPageOutputRes> outPutRecords = new ArrayList<>();
        Page<LossReviewAuditPageRes> page = lossReviewAuditPage.getData();
        List<LossReviewAuditPageRes> records = page.getRecords();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (LossReviewAuditPageRes record : records) {
            LossReviewAuditPageOutputRes temp = new LossReviewAuditPageOutputRes();
            BeanUtils.copyProperties(record, temp);
            temp.setOfftime(dateTimeFormatter.format(record.getOfftime()));
            outPutRecords.add(temp);
        }
        excel.write(outPutRecords, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        ServletOutputStream out = null;
        try {
            String fileName = URLEncoder.encode("售后亏损审核导出", "UTF-8") + "-" + LocalDateTime.now().toLocalDate() + ".xls";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
        } catch (Exception e) {
            throw new CustomizeException("导出excel出错");
        } finally {
            excel.flush(out, true);
            excel.close();
            IoUtil.close(out);
        }
    }

    @Override
    public LossReviewAuditRes getLossReviewAuditDetail(LossReviewAuditReq req) {
        Integer shouhouId = req.getShouhouId();
        List<LossReviewAudit> lossReviewAuditList = this.lambdaQuery().eq(LossReviewAudit::getShouhouId, shouhouId).list();

        LossReviewAudit lossReviewAudit = lossReviewAuditList.stream().findFirst().orElse(null);
        if (Objects.isNull(lossReviewAudit)) {
            return null;
        }
        LossReviewAuditRes result = new LossReviewAuditRes();
        result.setShouhouId(shouhouId);
        result.setAuditStatus(lossReviewAudit.getAuditStatus());
        result.setAuditStatusName(AuditStatsEnum.getMessageByCode(lossReviewAudit.getAuditStatus()));
        result.setCheckUser1(lossReviewAudit.getCheckUser1());
        result.setDtime1(lossReviewAudit.getDtime1());
        result.setCheckUser2(lossReviewAudit.getCheckUser2());
        result.setDtime2(lossReviewAudit.getDtime2());

        List<Wxkcoutput> wxkcoutputList = wxkcoutputService.lambdaQuery()
                .eq(Wxkcoutput::getWxid, shouhouId).and(cnd -> cnd.ne(Wxkcoutput::getStats, Wxkcoutput.StatusEnum.CANCELED.getCode())
                .or().eq(Wxkcoutput::getTuiStatus, Wxkcoutput.TuiStatusEnum.REFUND_ONLY.getCode())).list();
        //以明细亏损维度来计算
        BigDecimal lossAmountSum = BigDecimal.ZERO;
        List<LossReviewAuditRes.LossReviewAuditDetailRes> detailList = new ArrayList<>();

        for (Wxkcoutput wxkcoutput : wxkcoutputList) {
            BigDecimal price = NumberUtil.null2Zero(wxkcoutput.getPrice()).subtract(NumberUtil.null2Zero(wxkcoutput.getRefundedPrice()));
            BigDecimal inprice = NumberUtil.null2Zero(wxkcoutput.getInprice());
            BigDecimal price1 = NumberUtil.null2Zero(wxkcoutput.getPrice1());
            Integer ppriceid = ObjectUtil.defaultIfNull(wxkcoutput.getPpriceid(),0);

            BigDecimal loss = inprice.subtract(price);
            if (loss.compareTo(BigDecimal.ZERO)<=0){
                continue;
            }

            if (ppriceid>0 && price1.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            Productinfo productinfo = productinfoService.getProductinfoByPpid(ppriceid);
            LossReviewAuditRes.LossReviewAuditDetailRes lossReviewAuditDetailRes = new LossReviewAuditRes.LossReviewAuditDetailRes();
            if (Objects.nonNull(productinfo)) {
                lossReviewAuditDetailRes.setProductName(productinfo.getProductName() + productinfo.getProductColor());
            } else {
                lossReviewAuditDetailRes.setProductName("手工费");
            }
            lossReviewAuditDetailRes.setCostPrice(price);
            lossReviewAuditDetailRes.setInPrice(inprice);
            lossAmountSum = lossAmountSum.add(loss);
            lossReviewAuditDetailRes.setLoss(loss);
            detailList.add(lossReviewAuditDetailRes);
        }
        result.setDetailList(detailList);
        List<LossReviewAuditRes.LossReviewAuditReason> reasonList = new ArrayList<>();
        List<Integer> lossReviewAuditIdList = lossReviewAuditList.stream().map(LossReviewAudit::getId).collect(Collectors.toList());

        List<LossReviewAuditDetail> lossReviewAuditDetailList = lossReviewAuditDetailService.lambdaQuery()
                .in(LossReviewAuditDetail::getLossReviewAuditId, lossReviewAuditIdList)
                .list();

        for (LossReviewAuditDetail reviewAuditDetail : lossReviewAuditDetailList) {
            LossReviewAuditRes.LossReviewAuditReason temp = new LossReviewAuditRes.LossReviewAuditReason();
            temp.setReason(reviewAuditDetail.getErrorReason());
            reasonList.add(temp);
        }
        result.setReasonList(reasonList);
        result.setLossSum(lossAmountSum);
        return result;
    }

}
