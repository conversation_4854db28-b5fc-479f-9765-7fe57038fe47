package com.jiuji.oa.afterservice.statistics.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.service.StaticsSaleShouYinService;
import com.jiuji.oa.afterservice.bigpro.statistics.service.impl.StatisticsCommonServiceImpl;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.StatisticsSaleShouYinQuery;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.StatisticsSaleShouYinVo;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.common.vo.res.CommonDataRes;
import com.jiuji.oa.afterservice.statistics.enums.PersonStatisticsTypeEnum;
import com.jiuji.oa.afterservice.statistics.service.PersonStatisticsService;
import com.jiuji.oa.afterservice.statistics.service.RepairProfitStatisticsService;
import com.jiuji.oa.afterservice.statistics.service.SmallproStatisticsService;
import com.jiuji.oa.afterservice.statistics.vo.req.PersonStatisticsReq;
import com.jiuji.oa.afterservice.statistics.vo.req.RepairProfitStatisticsReq;
import com.jiuji.oa.afterservice.statistics.vo.req.SmallproStatisticsFilterReq;
import com.jiuji.oa.afterservice.statistics.vo.res.*;
import com.jiuji.oa.nc.TrainClassAggregatedCloud;
import com.jiuji.oa.nc.train.TrainClassVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * description: <售后统计控制类>
 * translation: <After-sales statistical control>
 *
 * <AUTHOR>
 * @date 2020/4/28
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/after/statistics")
@Api(tags = "售后统计")
@Slf4j
public class AfterStatisticsController {

    @Autowired
    private SmallproStatisticsService smallproStatisticsService;
    @Autowired
    private PersonStatisticsService personStatisticsService;
    @Autowired
    private RepairProfitStatisticsService repairProfitStatisticsService;
    @Autowired
    private StatisticsCommonServiceImpl statisticsCommonService;
    @Autowired
    private TrainClassAggregatedCloud trainClassAggregatedCloud;
    @Autowired
    private StaticsSaleShouYinService staticsSaleShouYinService;

    // region 获取小件统计筛选项列表 get /smallpro/filter/selection

    /**
     * description: <获取小件统计筛选项列表>
     * translation: <Get a list of small items statistical filtering items>
     *
     * @return com.baomidou.mybatisplus.extension.api.R<com.jiuji.oa.afterservice.statistics.vo.res.SmallproStatisticsFilterRes>
     * <AUTHOR>
     * @date 20:54 2020/4/28
     * @since 1.0.0
     **/
    @GetMapping("/smallpro/filter/selection")
    @ApiOperation(value = "获取小件统计筛选项列表", httpMethod = "GET")
    public R<SmallproStatisticsFilterRes> getSmallproStatisticsFilterSelection() {
        return R.success(smallproStatisticsService.getSmallproStatisticsFilterSelection());
    }

    /**
     * description: <获取小件统计字段相关信息>
     * translation: <Get information about widget statistics fields>
     *
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.statistics.vo.res.SmallproStatisticsFieldRes>
     * <AUTHOR>
     * @date 15:35 2020/4/29
     * @since 1.0.0
     **/
    @GetMapping("/smallpro/field")
    @ApiOperation(value = "获取小件统计字段相关信息", httpMethod = "GET")
    public R<SmallproStatisticsFieldRes> getSmallproStatisticsField() {
        return R.success(smallproStatisticsService.getSmallproStatisticsField());
    }

    /**
     * description: <获取当前筛选情况下的统计数据>
     * translation: <Get statistics under current screening>
     *
     * @param req 筛选请求信息
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 21:13 2020/4/28
     * @since 1.0.0
     **/
    @PostMapping("/smallpro")
    @ApiOperation(value = "获取当前筛选情况下的统计数据", httpMethod = "POST")
    public R<SmallproStatisticsRes> getSmallproStatistics(@RequestBody SmallproStatisticsFilterReq req) {
        SmallproStatisticsRes result;
        result = smallproStatisticsService.getSmallproStatisticsRes(req);
        return R.success(result);
    }

    /**
     * 个人售后统计
     *
     * @param req req
     * @see .net oa999.oa999DAL.statisticsService.cs ShouhouGerenYeji
     */
    @PostMapping("/person")
    @ApiOperation(value = "获取个人售后统计数据", httpMethod = "POST", notes = "注意!areaCodes字段除了a开头的都传areaId")
    public R<List<PersonStatisticsVO>> personStatistics(@RequestBody @Validated PersonStatisticsReq req) {
        statisticsCommonService.dealDaQuReq(req);
        return R.success(personStatisticsService.personStatistics(req));
    }


    /**
     * 个人售后统计
     *
     * @param req req
     * @see .net oa999.oa999DAL.statisticsService.cs ShouhouGerenYeji
     */
    @PostMapping("/person/v2")
    @ApiOperation(value = "获取个人售后统计数据v2", httpMethod = "POST", notes = "注意!areaCodes字段除了a开头的都传areaId")
    public R<CommonDataRes<PersonStatisticsVO>> personStatisticsV2(@RequestBody @Validated PersonStatisticsReq req) {
        statisticsCommonService.dealDaQuReq(req);
        return personStatisticsService.personStatisticsV2(req);
    }

    /**
     * 个人售后统计
     *
     * @param req req
     * @see .net oa999.oa999DAL.statisticsService.cs ShouhouGerenYeji
     */
    @PostMapping("/person/app/v1")
    public R<PersonStatisticsAppVO> personStatisticsApp(@RequestBody @Validated PersonStatisticsReq req) {
        return personStatisticsService.personStatisticsApp(req);
    }

    @PostMapping("/person/export")
    @ApiOperation(value = "售后统计导出", httpMethod = "POST")
    public void personExport(@RequestBody PersonStatisticsReq req, HttpServletResponse response) {
        personStatisticsService.personExport(req,response);
    }

    /**
     * 个人售后统计查询类型枚举
     */
    @GetMapping("/person/enums")
    @ApiOperation(value = "获取个人售后统计枚举分类", httpMethod = "GET")
    public R<List<EnumVO>> personStatistics() {
        List<EnumVO> rtn = EnumUtil.toEnumVOList(PersonStatisticsTypeEnum.class);
        rtn.remove(0);
        return R.success(rtn);
    }


    /**
     * 个人售后统计查询类型枚举
     */
    @GetMapping("/person/enums/v2")
    @ApiOperation(value = "获取个人售后统计枚举分类v2", httpMethod = "GET")
    public R<Dict> personStatisticsEnumsV2() {
        return personStatisticsService.statisticsEnumsV2();
    }

    /**
     * 毛利分析统计
     *
     * @param req req
     */
    @PostMapping("/repair/profit")
    @ApiOperation(value = "毛利分析统计数据", httpMethod = "POST", notes = "注意!areaCodes字段除了a开头的都传areaId")
    public R<Map<String, List<RepairProfitStatisticsVO>>> repairProfitStatistics(@RequestBody @Validated RepairProfitStatisticsReq req) {
        statisticsCommonService.dealDaQuReq(req);
        return R.success(this.repairProfitStatisticsService.repairProfitStatistics(req));
    }


    /**
     * 毛利分析统计
     *
     * @param req req
     */
    @PostMapping("/repair/profit/v2")
    @ApiOperation(value = "毛利分析统计数据v2(后端计算)", httpMethod = "POST",notes = "注意!areaCodes字段除了a开头的都传areaId,后端做了计算,前端不需要在进行计算")
    public R<List<RepairProfitStatistics2VO>> repairProfitStatistics2(@RequestBody @Validated RepairProfitStatisticsReq req) {
        statisticsCommonService.dealDaQuReq(req);
        return R.success(this.repairProfitStatisticsService.repairProfitStatisticsV2(req));
    }


    @GetMapping("/tree")
    public R<List<TrainClassVO>> tree(){
        return trainClassAggregatedCloud.getTree();
    }

    @PostMapping("/saleShouYin")
    @ApiOperation(value = "销售收银统计(后端计算)", httpMethod = "POST",notes = "注意!时间范围必须传")
    public R<CommonDataRes<StatisticsSaleShouYinVo>> saleShouYin(@Valid @RequestBody StatisticsSaleShouYinQuery query) {
        //角色数据查询
        R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.FINANCES)
                .getStartTimeFun(query::getStartDate)
                .getEndTimeFun(query::getEndDate)
                .setStartTimeFun(query::setStartDate)
                .setEndTimeFun(query::setEndDate)
                .build(), null);
        if (!dataViewRes.isSuccess()) {
            throw new CustomizeException(dataViewRes.getUserMsg());
        }
        CommonDataRes<StatisticsSaleShouYinVo> commonDataRes = staticsSaleShouYinService.getSaleShouYinCommonDataRes();
        List<StatisticsSaleShouYinVo> ssList = staticsSaleShouYinService.saleShouYin(query);
        commonDataRes.setList(ssList);
        return R.success(commonDataRes);
    }

    /**导出excel*/
    @ApiOperation(value = "导出excel")
    @GetMapping("/exportSaleShouYin")
    public void exportSaleShouYin(@RequestParam("query") String query,HttpServletResponse response) throws IOException {
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", String.format("attachment;filename=%s_%s.xls", URLUtil.encode("销售收银统计报表")
                , LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"))));
        try(OutputStream out = response.getOutputStream()){
            staticsSaleShouYinService.exportSaleShouYin(JSON.parseObject(query,StatisticsSaleShouYinQuery.class), out);
        }
    }
}

