package com.jiuji.oa.afterservice.electronicTicket.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;

/**
 * 核实电子小票
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VerifyStatusVO {

    /**
     * 电子小票id
     */
    @NotNull(message = "电子小票id不能为空")
    private Long id;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    private Integer orderId;


    /**
     * 电子小票核实连接
     */
    @NotNull(message = "电子小票核实连接不能为空")
    private String verifyUrl;

    /**
     * 是否大小件
     */
    @NotNull(message = "大小件类型不能为空")
    private Boolean isMobile;
    /**
     * 订单类型
     * 1--接件单
     * 2--回执单
     */
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    /**
     * 用户id
     */
    @NotNull(message = "userId不能为空")
    private Integer userId;

    /**
     * 来源
     */
    private Integer sourceType;
}
