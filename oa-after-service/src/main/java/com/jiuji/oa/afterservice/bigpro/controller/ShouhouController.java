package com.jiuji.oa.afterservice.bigpro.controller;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.oa.afterservice.bigpro.bo.BaoxiuAndBuyBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouHuishou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouMsgconfig;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.UpdatePriceVO;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidBindService;
import com.jiuji.oa.afterservice.other.vo.req.ShouhouTuihuanReq;
import com.jiuji.oa.afterservice.stock.service.ProductKclogsService;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.req.MemberReq;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Api(tags = "大件：售后")
@RestController
@RequestMapping("/api/bigpro/shouhou")
public class ShouhouController {

    @Autowired
    private ShouhouService shouhouService;
    @Resource
    private ShouhouMapper shouhouMapper;
    @Autowired
    private ShouhouMsgconfigService shouhouMsgconfigService;
    @Autowired
    private ShouhouHuishouService shouhouHuishouService;
    @Resource
    private ShouhouInfoExtendService extendService;
    @Autowired
    private ProductKclogsService productKclogsService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Autowired
    private ShouhouImportantService shouhouImportantService;
    @Resource
    private MemberClient memberClient;
    @Autowired
    private ShouhouExService shouhouExService;
    @Autowired
    private WeixiuzuKindService weixiuzuKindService;
    @Autowired
    private ShouhouPpidBindService shouhouPpidBindService;


    /**
     * 获取到优品连接
     * @param req
     * @return
     */
    @PostMapping("/getSuperiorProductsLink")
    public R<SuperiorProductsLinkRes> getSuperiorProductsLink(@RequestBody  SuperiorProductsLinkReq req){
        return R.success(shouhouService.getSuperiorProductsLink(req));
    }

    @ApiOperation(value = "获取配件信息", notes = "param传相应的json数据", httpMethod = "POST",response = ShouhouYuyuePeijianInfoRes.class)
    @PostMapping("/getPeijian")
    public R<List<ShouhouYuyuePeijianInfoRes>> getPeijian(@RequestBody YuyuePeijianAddReq yuyuePeijianAddReq){
        return shouhouService.getPeijian(yuyuePeijianAddReq);
    }

    @ApiOperation(value = "获取库存数量")
    @GetMapping("/getWxkcNumber")
    public R<Integer> getWxkcNumber(@RequestParam(value = "ppid") int ppid,@RequestParam(value = "areaId") int areaId){
        return R.success("获取成功",shouhouService.getWxkcNumber(ppid,areaId));
    }

    @ApiOperation(value = "维修配件库存锁定和释放",notes = "type:1锁定，2解除锁定")
    @GetMapping("/lockWxPeijian")
    public R<Boolean> lockWxPeijian(Integer yyId,Integer ppid,Integer areaId,Integer type){
        return shouhouService.lockWxPeijian(yyId,ppid,areaId,type);
    }

    @ApiOperation(value = "维修查询",httpMethod = "POST",response = ShouhouListRes.class)
    @PostMapping("/getShouhouPage")
    public R<PageVo> getShouhouPage(@RequestBody ShouhouListReq param){
        return R.success(shouhouService.getShouhouPage(param));
    }

    @ApiOperation(value = "串号接件搜索",response = ImeiQueryRes.class)
    @GetMapping("/imeiQuery")
    public  R<List<ImeiQueryRes>> imeiQuery(String imei, Integer limit, Boolean saveSearchLog){
        return R.success(shouhouService.imeiQuery(imei,limit,saveSearchLog));
    }

    @ApiOperation(value = "获取售后维修保修信息",response = BaoxiuAndBuyBo.class)
    @GetMapping("/getServiceInfo")
    public R<BaoxiuAndBuyBo> getServiceInfo(@RequestParam(value = "imei",required = true) String imei){
        return R.success(shouhouService.getServiceInfo(imei,true));
    }

    @ApiOperation(value = "获取售后维修保修信息V2(保修信息查询结果直接返回，后端不作序列化处理)",response = BaoxiuAndBuyBo.class)
    @GetMapping("/getServiceInfo/v2")
    public R<String> getServiceInfoV2(@RequestParam(value = "imei",required = true) String imei){
        return shouhouService.getServiceInfoV2(imei,true);
    }

    @ApiOperation(value = "获取售后详情信息",response = ShouhouInfoRes.class)
    @GetMapping("/getShouhouInfo")
    public R<ShouhouInfoRes> getShouhouInfo(@RequestParam("shouhouId") String shouhouIdParam){
        Integer shouhouId = Convert.toInt(shouhouIdParam);
        if(CommenUtil.isNullOrZero(shouhouId)){
            return R.error("售后id错误");
        }
        return shouhouService.getShouhouInfo(shouhouId);
    }

    @ApiOperation(value = "获取售后详情信息扩展",response = ShouhouInfoRes.class)
    @PostMapping("/getShouhouInfoExtend")
    public R<ShouhouInfoExtendRes> getShouhouInfoExtend(@RequestBody @Valid ExtendCondition extendCondition){
        return extendService.selectShouhouInfoExtend(extendCondition);
    }

    @ApiOperation(value = "九机服务出险查询")
    @GetMapping("/getJiuJiFuWu")
    public R<JiuJiFuWuChuXianRes> getJiuJiFuWu(Integer shouhouId){
        return shouhouService.getJiuJiFuWu(shouhouId);
    }


    @ApiOperation(value = "九机服务出险办理",response = JiuJiFuWuChuXianReq.class)
    @PostMapping("/saveOrUpdateJiuJiFuWu")
    public R<Boolean> saveOrUpdateJiuJiFuWu(@RequestBody JiuJiFuWuChuXianReq req){
        return shouhouService.saveOrUpdateJiuJiFuWu(req);
    }

    @ApiOperation(value = "售后维修编辑提交",response = ShouhouInfoRes.class)
    @PostMapping("/saveOrUpdateShouhouInfo")
    @RepeatSubmitCheck(expression="#{classFullName}:updateShouhou:#{req.id != null && req.id>0 ? req.id : req.toMd5Hex()}")
    public R<Boolean> saveOrUpdateShouhouInfo(@RequestBody ShouhouInfoRes req){
        if(XtenantEnum.isJiujiXtenant()){
            List<FileReq> filesNew = new ArrayList<>();
            List<FileReq> filesFoldScreen = req.getFilesFoldScreen();
            List<FileReq> files = req.getFiles();
            if(CollectionUtils.isNotEmpty(files)){
                filesNew.addAll(files);
            }
            if(CollectionUtils.isNotEmpty(filesFoldScreen)){
                filesNew.addAll(filesFoldScreen);
            }
            req.setFiles(filesNew);
        }
        R<Boolean> result = shouhouService.saveOrUpdateShouhouInfo(req);
        if(result.isSuccess()){
            StringRedisTemplate stringRedisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
            if(XtenantEnum.isJiujiXtenant()){
                AttachmentsService attachmentsService = SpringUtil.getBean(AttachmentsService.class);
                //从主库查询数据并进行缓存
                List<Attachments> attachments = attachmentsService.getAttachmentsByLinkId(req.getId(), AttachmentsEnum.SHOUHOU.getCode(),req.getModidate());
                stringRedisTemplate.opsForValue().set(StrUtil.format(RedisKeys.SHOUHOU_ATTACHMENTS_ADD_CACHE, req.getId()),
                        JSON.toJSONString(attachmentsService.convertList(attachments)), Duration.ofMinutes(1));
            }
        }
        return result;
    }


    /**
     * 修改维修单串号
     * @param req
     * @return
     */
    @PostMapping("/updateShouHouImei")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{req.shouHouId}",seconds = 30,message = "修改串号太频繁，请30s后再重试")
    public R<UpdateShouHouImeiRes> updateShouHouImei(@RequestBody @Valid UpdateShouHouImeiReq req){
        return shouhouService.updateShouHouImei(req);
    }

    @ApiOperation(value = "更新倒计时")
    @PostMapping("/updateDaojishi")
    public R<Boolean> updateDaojishi(Integer shouhouId,Integer day){
        return shouhouService.updateDaojishi(shouhouId,day);
    }

    @ApiOperation(value = "取机队列")
    @GetMapping("/nahou")
    public R<Boolean> nahou(@RequestParam(value = "shouhouId",required = true) Integer shouhouId){
        return shouhouService.nahou(shouhouId);
    }

    @ApiOperation(value = "领取优惠码")
    @PostMapping("/sendNumberCard")
    public R<Boolean> sendNumberCard(@RequestParam(value = "shouhouId",required = true) Integer shouhouId){
        return shouhouService.sendNumberCard(shouhouId);
    }

    @ApiOperation(value = "邮寄预约地址发送")
    @PostMapping("/sendAddressInfo")
    public R<Boolean> sendAddressInfo(Integer yuyueId, String smsMobile, String mobile, String addr, String rUserName,
                                      @RequestParam(name = "addressCityId",required = false) Integer addressCityId){
        return shouhouService.sendAddressInfo(yuyueId,smsMobile,mobile,addr,rUserName, addressCityId);
    }

    @ApiOperation(value = "发送取机通知（获取消息列表）")
    @GetMapping("/getMessageTplList")
    public R<List<ShouhouMsgconfig>> getMessageTplList(@RequestParam(value = "shouhouId",required = true) String shouhouIdParam
            ,@RequestParam(value = "logType",required = false) String logType, @RequestParam(value = "key",required = false) String key){
        Integer shouhouId = Convert.toInt(shouhouIdParam);
        if(ObjectUtil.defaultIfNull(shouhouId,0) == 0){
            return R.error("售后id不能为空");
        }
        return R.success(shouhouMsgconfigService.getMessageTplList(shouhouId,Convert.toInt(logType), key));
    }

    @ApiOperation(value = "售后中心处理确认")
    @PostMapping("/setProcessConfirmLog")
    public R<Boolean> setProcessConfirmLog(@RequestParam(value = "shouhouId") Integer shouhouId, @RequestParam(value = "type") Integer type){
        return shouhouService.setProcessConfirmLog(shouhouId,type);
    }

    @ApiOperation(value = "更新电话跟进时间（电话通知备注）")
    @GetMapping("/updateTelTime")
    public R<Boolean> updateTelTime(@RequestParam(value = "shouhouId",required = true) Integer shouhouId){
        return shouhouService.updateTelTime(shouhouId);
    }

    @ApiOperation(value = "售后添加批签,批签校验已包含在里面")
    @GetMapping("/addPqDan")
    public R<Boolean> addPqDan(@RequestParam(value = "shouhouId") Integer shouhouId,@RequestParam(value = "orderId") String orderId){
        return shouhouService.addPqDan(shouhouId,orderId);
    }
    @ApiOperation(value = "添加维修费")
    @PostMapping("/addCostPrice")
    @RepeatSubmitCheck(expression="#{classFullName}:updateShouhou:#{wxFeeBo.shouhouId}")
    public R<ShouhouCostPriceRes> addCostPrice(@Valid @RequestBody WxFeeBo wxFeeBo, @RequestParam(value = "isKcLock",required = false,defaultValue = "true") Boolean isKcLock){
       OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        wxFeeBo.setAreaId(oaUserBO.getAreaId());
        wxFeeBo.setUser(oaUserBO.getUserName());
        wxFeeBo.setXtenant(oaUserBO.getXTenant());
        return shouhouService.addCostPriceWithAutoBind(wxFeeBo, false, isKcLock);
    }
    @ApiOperation(value = "售后维修配件：回收、换货、返还")
    @PostMapping("/addHuiShou")
    @RepeatSubmitCheck(argIndexs = {0},seconds = 30)
    public R<Boolean>addHuiShou(@Valid @RequestBody ShouhouHuiShouReq req){
        return shouhouHuishouService.addHuiShou(req);
    }

    @ApiOperation(value = "售后维修配件：回收列表查询")
    @PostMapping("/getHuishouList")
    public ShouHouHuishouListRes getHuishouList(ShouhouHuishouListReq req){
        return shouhouHuishouService.getHuishouList(req);
    }

    @ApiOperation(value = "售后维修配件：根据ppid获取最后一次入库的成本价")
    @GetMapping("/getLastCostPriceByPPid")
    public R<BigDecimal> getLastCostPriceByPPid(@RequestParam(value = "ppriceid",required = true) Integer ppriceid){
        return productKclogsService.getLastCostPriceByPPid(ppriceid);
    }

    @ApiOperation(value = "退换显示（详情）")
    @GetMapping("/huan")
    public R<ShouhouTuihuanRes> huan(@RequestParam(value = "id",required = true) Integer id){
        return R.success(shouhouService.huan(id));
    }
    @ApiOperation(value = "售后维修：增加处理日志")
    @PostMapping("/addShouHouLog")
    public R<Boolean> addShouhouLog(@RequestBody ShouhouLogReq req){
        return shouhouService.addShouHouLog(req);
    }

    @ApiOperation(value = "售后维修：获取售后日志")
    @GetMapping("/getShouhouLogs")
    public R<List<ShouhouLogBo>> getShouhouLogs(@RequestParam(value = "shouhouId",required = true) Integer shouhouId){
        List<ShouhouLogBo> shouhouLogs = Optional.ofNullable(shouhouService.getShouhouLogs(shouhouId)).orElseGet(Collections::emptyList);
        shouhouLogs.stream()
                .filter(shouhouLog -> Boolean.TRUE.equals(shouhouLog.getIsweb()))
                .forEach(shouhouLog -> shouhouLog.setComment(StrUtil.addPrefixIfNot(shouhouLog.getComment(), StringPool.ASTERISK)));
        return R.success(shouhouLogs);
    }

    @ApiOperation(value = "售后维修：批量获取售后日志信息")
    @GetMapping("/listShouhouLogs")
    public R<Map<Integer,List<ShouhouLogBo>>> listShouhouLogs(@RequestParam(value = "shouhouId",required = true) List<Integer> shouhouIds
            ,@RequestParam(value = "type",required = false)Integer type){
        return R.success(shouhouService.listShouhouLogs(shouhouIds,type));
    }

    @ApiOperation(value = "售后维修：维修周期查询")
    @GetMapping("/getShouHouWeiXiuZqByShId")
    public R<ShouhouWxzqRes> getShouHouWeiXiuZqByShId(@RequestParam(value = "shouhouId",required = true) Integer shouhouId){
        return R.success(shouhouExService.getShouHouWeiXiuZqByShId(shouhouId));
    }

    @GetMapping("/getEnums")
    @ApiOperation(value = "获取售后枚举选项")
    public R<Map<String, Object>> listAllEnum(@RequestParam(value = "issoft",required = false) Boolean issoft) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (issoft == null){
            issoft = Boolean.FALSE;
        }
        if (oaUserBO == null) {
            return R.error("请登陆");
        }
        Map<String, Object> enumMap = new HashMap<>();
        List<EnumVO> tuihuanKindEnum = EnumUtil.toEnumVOList(TuihuanKindEnum.class);
        //查询当前接件是否是软件接件
        List<EnumVO> wxStatusEnum;
        if (Boolean.TRUE.equals(issoft)) {
            wxStatusEnum = EnumUtil.toEnumVOList(WxStatusEnum.class);
            wxStatusEnum.removeIf(re -> Objects.equals(re.getValue(), WxStatusEnum.XBH.getCode()));
            wxStatusEnum.forEach(wx -> {
                if (Objects.equals(wx.getValue(), WxStatusEnum.YXH.getCode())) {
                    wx.setLabel("已完成");
                }
            });
        } else {
            wxStatusEnum = EnumUtil.toEnumVOList(WxStatusEnum.class);
        }
        List<EnumVO> bxStatusEnum = EnumUtil.toEnumVOList(BaoxiuStatusEnum.class);
        List<EnumVO> importantEnum = EnumUtil.toEnumVOList(ShouhouImportantStateEnum.class);
        List<EnumVO> bxTypeEnum = EnumUtil.toEnumVOList(BaoXiuTypeEnum.class);
        List<EnumVO> bankEnum = EnumUtil.toEnumVOList(BankTypeEnum.class);
        List<EnumVO> daiyongjiPayStatesEnum  = EnumUtil.toEnumVOList(DaiyongjiPayStatesEnum.class);
        List<EnumVO> riskNotificationTypeEnum  = ShouhouRiskNotificationTypeEnum.getEnumVOList();
        List<EnumVO> cantFixReasonEnum  = EnumUtil.toEnumVOList(CantFixReasonEnum.class);
        enumMap.put("tuihuanKind", tuihuanKindEnum);
        enumMap.put("wxStatusEnum", wxStatusEnum);
        enumMap.put("bxStatusEnum", bxStatusEnum);
        enumMap.put("importantEnum", importantEnum);
        enumMap.put("bxTypeEnum", bxTypeEnum);
        enumMap.put("bankEnum", bankEnum);
        enumMap.put("cantFixReasonEnum", cantFixReasonEnum);
        enumMap.put("daiyongjiPayStatesEnum", daiyongjiPayStatesEnum);
        //订购进程
        enumMap.put("orderFittingDes", Optional.of(ProcessDesEnum.ORDER_FITTING).map(nnb->new ListBean.OptionsBean(nnb.getCode(),nnb.getMessage())).orElse(null));
        enumMap.put("msgTemplateTab", Arrays.stream(MsgConfigLogTypeEnum.values()).filter(logType->Objects.nonNull(logType.getMsgTemplateTab()))
                .map(logType->new ListBean.OptionsBean(logType.getCode(), logType.getMsgTemplateTab())).collect(Collectors.toList()));
        //取机验证枚举类
        enumMap.put("validMemberType", ValidMemberType.toEnumVOList());
        enumMap.put("fromSourceEnum", ShouhouFromSourceEnum.getEnumVoList(1));
        //风险告知书枚举
        enumMap.put("riskNotificationType", riskNotificationTypeEnum);
        return R.success(enumMap);
    }

    @PostMapping("/sendSms7")
    @ApiOperation(value = "售后维修进程发送sms")
    public R<Boolean> sendSms7(@RequestParam(value = "wxId",required = true)Integer wxId,
                               @RequestParam(value = "mobile",required = true)String mobile,
                               @RequestParam(value = "smsContent",required = true)String smsContent,
                               Integer already, Integer type){
        return shouhouMsgService.sendMsms7(wxId,mobile,smsContent,already,type);
    }

    @PostMapping("/changeShouhouImportantStats")
    @ApiOperation(value = "修改重大办状态")
    public R<Boolean> changeShouhouImportantStats(@RequestParam(value = "shouhouId",required = true)Integer shouhouId,
                               @RequestParam(value = "stats",required = true)Integer stats,
                               @RequestParam(value = "comment",required = false)String comment){
        return shouhouImportantService.changeShouhouImportantStats(shouhouId,stats,comment);
    }

    @PostMapping("/addShouhouImportantStats")
    @ApiOperation(value = "添加售后重大办")
    public R<Boolean> addShouhouImportantStats(@RequestParam(value = "shouhouId",required = true)Integer shouhouId,
                                                  @RequestParam(value = "comment",required = false)String comment){
        return shouhouImportantService.addShouhouImportantStats(shouhouId,comment,0);
    }

    @GetMapping("/checkReturnType")
    @ApiOperation(value = "检测退款方式默认")
    public R<String> checkReturnType(@RequestParam(value = "id",required = true) long id,
                                     @RequestParam(value = "type",required = true)int type){
        return shouhouService.checkReturnType(id,type);
    }

    @GetMapping("/subCollectDeal")
    @ApiOperation(value = "订单 关注、取消操作")
    public R<String> subCollectDeal(@RequestParam(value = "subId",required = true) Integer subId,
                                     @RequestParam(value = "type",required = true)Integer type){
        return shouhouService.subCollectDeal(subId,type, SubCollectTypeEnum.WX.getCode());
    }

    @GetMapping("/pandianCancel")
    @ApiOperation(value = "取消盘点")
    public R<Boolean> pandianCancel(@RequestParam(value = "wxId",required = true) Integer wxId){
        return shouhouService.pandianCancel(wxId);
    }

    @GetMapping("/yuyueCheck")
    @ApiOperation(value = "预约维修单，门店大件确认接件")
    public R<String> yuyueCheck(@RequestParam(value = "shouhouId") Integer shouhouId){
        return shouhouService.yuyueCheck(shouhouId);
    }

    @ApiOperation(value = "发送电子凭证")
    @PostMapping("/sendWxPz")
    public R<String> sendWxPz(@RequestParam(value = "shouhouId") Integer shouhouId,
                        @RequestParam(value = "userId",required = true) Integer userId,
                        @RequestParam(value = "mobile",required = true) String mobile){
        return shouhouService.sendWxPz(shouhouId,userId,mobile);
    }

    @ApiOperation(value = "取机通知发送")
    @PostMapping("/qujiSendmsg")
    public R<String> qujiSendmsg(@RequestParam(value = "wxId",required = true) Integer wxId,
                        @RequestParam(value = "msg",required = true) String msg){
        return shouhouService.qujiSendmsg(wxId,msg);
    }

    @ApiOperation(value = "售后接件，自动注册为会员")
    @PostMapping("/shouhouReg")
    public R<Integer> shouhouReg(MemberReq req){
        req.setRegClient("OA");
        //设置用户名默认值
        req.setUserName(ObjectUtil.defaultIfBlank(req.getUserName(), req.getMobile()));
        return memberClient.checkAndRegisterUser(req);
    }

    @ApiOperation(value = "售后退换提交")
    @PostMapping("/shouhouTuihuan")
    public R<String> shouhouTuihuan(@RequestBody ShouhouTuihuanReq tuihuan){
        return shouhouExService.shouhouTuihuan(tuihuan);
    }

    @ApiOperation(value = "保存送修客户信息")
    @PostMapping("/saveZySxBusinessInfo")
    public R<String> saveZySxBusinessInfo(@RequestParam(value = "shouhouId",required = true) Integer shouhouId,
                          @RequestParam(value = "mobile",required = true) String mobile,
                          @RequestParam(value = "username",required = true) String username){
        return shouhouService.saveZySxBusinessInfo(shouhouId,mobile,username);
    }
    @ApiOperation(value = "根据单号获取进水保信息")
    @GetMapping("/getJinshuibaoInfo")
    public R<JinshuibaoInfoRes> getJinshuibaoInfo(@RequestParam(value = "shouhouId",required = true) Integer shouhouId,
                                   @RequestParam(value = "mobile",required = false) Integer serviceType){
        return shouhouExService.getJinshuibaoInfo(shouhouId,serviceType);
    }

    @ApiOperation(value = "获取维修人列表")
    @GetMapping("/getWeixiuren")
    public R<List<String>> getWeixiuren(@RequestParam(value = "weixiuzuId",required = true) Integer weixiuzuId){
        return weixiuzuKindService.getWeiXiuRenByWxzId(weixiuzuId);
    }

    @ApiOperation(value = "客户数据备份相关操作")
    @GetMapping("/bakDataOp")
    public R<String> bakDataOp(@RequestParam(value = "stats") Integer stats,
                               @RequestParam(value = "shouhouId") Integer shouhouId,
                               @RequestParam(value = "reason",required = false) String reason){
        return shouhouExService.bakDataOp(stats,shouhouId,reason);
    }

    @ApiOperation(value = "取指定售后ID回收配件")
    @GetMapping("/getHuishouListBy")
    public R<List<ShouhouHuishou>> getHuishouListBy(@RequestParam(value = "shouhouId",required = true) Integer shouhouId){
        List<ShouhouHuishou> res =  shouhouHuishouService.getHuishouListBy(shouhouId);
        if (CollectionUtils.isEmpty(res)){
            return R.success(res);
        }else{
            return R.error("无相关记录");
        }
    }

    @ApiOperation(value = "配件查询接口合并（含回收配件、维修配件）")
    @GetMapping("/getShouhouPjInfoById")
    public R<ShouhouPjInfoRes> getShouhouPjInfoById(@RequestParam(value = "shouhouId",required = true) Integer shouhouId){
        ShouhouPjInfoRes res =  shouhouHuishouService.getShouhouPjInfoById(shouhouId);
        if (res != null){
            return R.success(res);
        }else{
            return R.error("无相关记录");
        }
    }

    @ApiOperation(value = "旧件审核")
    @GetMapping("/huiShouCheck")
    public R<String> huiShouCheck(@RequestParam(value = "id",required = true) Integer id){
        return shouhouHuishouService.huiShouCheck(id);
    }

    @ApiOperation(value = "回收配件撤销")
    @GetMapping("/huishouPjDel")
    public R<String> huishouPjDel(@RequestParam(value = "id",required = true) Integer id){
        return shouhouHuishouService.huishouPjDel(id);
    }

    @ApiOperation(value = "添加维修绑定费")
    @PostMapping("/addBindCostPrice")
    @RepeatSubmitCheck(expression="#{classFullName}:updateShouhou:#{wxFeeBo.shouhouId}")
    public R<List<ShouhouCostPriceRes>> addBindCostPrice(@RequestBody WxFeeBo wxFeeBo
            , @RequestParam(value = "isKcLock",required = false,defaultValue = "true") Boolean isKcLock){
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        wxFeeBo.setAreaId(oaUserBO.getAreaId());
        wxFeeBo.setUser(oaUserBO.getUserName());
        wxFeeBo.setXtenant(oaUserBO.getXTenant());
        return shouhouService.addBindCostPrice(wxFeeBo, false, isKcLock,true);
    }


    @ApiOperation(value = "添加维修绑定费")
    @PostMapping("/updatePrice")
    public R<Boolean> updatePrice(@RequestBody @Valid UpdatePriceVO updatePrice){
        return shouhouService.updatePrice(updatePrice);
    }

    @ApiOperation(value = "无理由退货检测")
    @GetMapping("/checkNoReasonReturn")
    public R checkNoReasonReturn(@ApiParam("产品id,多个以,分隔") @Valid @NotBlank(message = "产品id不能为空") @RequestParam("pids") String pids){
        return shouhouService.checkNoReasonReturn(StrUtil.splitTrim(pids,",").stream().map(Convert::toInt).filter(Objects::nonNull)
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "保存售后服务记录")
    @PostMapping("/saveShouhouServiceRecord/v1")
    public R saveShouhouServiceRecord(@RequestBody QujiReq qujiReq){
        Shouhou shouhou = shouhouMapper.getByIdSqlServer(qujiReq.getId());
        shouhouService.saveShouhouServiceRecord(qujiReq,shouhou);
        return R.success("操作成功");
    }



    @ApiOperation(value = "取机")
    @PostMapping("quji")
    @RepeatSubmitCheck(expression="#{classFullName}:updateShouhou:#{qujiReq.id}")
    public R quji(@Valid @RequestBody QujiReq qujiReq){
        return shouhouService.quji(qujiReq);
    }


    @GetMapping("/sendCouponsParts")
    public R sendCouponsParts( @RequestParam(value = "shouHouId") Integer shouHouId){
        Shouhou shouhou = shouhouService.getById(shouHouId);
        shouhouService.sendCouponsParts(shouhou);
        return R.success("发券成功");
    }


    /**
     * 手动出现
     * @param shouHouId
     * @return
     */
    @GetMapping("/qujiUseServiceRecord")
    public R<String> qujiUseServiceRecord(@RequestParam(value = "shouHouId") Integer shouHouId){
       shouhouService.qujiUseServiceRecord(shouHouId);
        return R.success("操作成功");
    }



}

