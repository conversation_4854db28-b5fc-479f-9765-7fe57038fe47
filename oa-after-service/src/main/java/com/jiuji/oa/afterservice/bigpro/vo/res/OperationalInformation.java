package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OperationalInformation {
    /**
     * 操作名称
      */
    private String operationalName;
    /**
     * 操作code
     */
    private Integer operationalCode;
    /**
     * 操作人
     */
    private String operationalUser;
    /**
     * 操作时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationalTime;
    /**
     * 操作状态
     */
    private Boolean operationalState;


}
