package com.jiuji.oa.afterservice.common.config.interceptor;

import com.alibaba.fastjson.JSON;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OaTokenInterceptor implements HandlerInterceptor {

    @Autowired
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    static final String REQUEST_HEADER_TOKEN = "Authorization";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.debug("开始进入OA地址拦截");
        R result = new R(ResultCode.UNLOGIN, "invalid ticket", "请登录后再操作哦~!");
        String resultJson = JSON.toJSONString(result);
        String token = AbstractCurrentRequestComponent.getToken(request);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        log.debug("拦截到的token为:" + token);
        try {
            OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
            if (currentStaffId != null) {
                return true;
            }
        } catch (Exception e) {
            log.warn("拦截器异常：" + e.getMessage());
            response.getWriter().write(resultJson);
            return false;
        }
        response.getWriter().write(resultJson);
        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {

    }
}
