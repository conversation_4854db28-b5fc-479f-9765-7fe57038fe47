package com.jiuji.oa.afterservice.sys.vo.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jiuji.oa.afterservice.sys.enums.ConfigEnum;
import com.jiuji.oa.afterservice.sys.po.SysConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * @description: BaseConfigVO
 * </p>
 * @author: David
 * @create: 2021-06-29 16:01
 */
@Data
@NoArgsConstructor
public class BaseConfigVO {

    /**
     * 配置项
     */
    private String title;

    /**
     * 层级唯一码
     */
    private Integer type;

    /**
     * 是否允许启用
     */
    private Boolean disable;

    /**
     * 是否是开关
     */
    @JsonProperty("isSwitch")
    private Boolean switchFlag;

    /**
     * 是否是全局配置
     */
    private Boolean settingsFlag;

    /**
     * 属于哪个大类
     */
    private Integer titleType;

    /**
     * 字段校验长度 0是不限制长度  限制的长度是按照英文逗号间隔的数量
     */
    private Integer checkSize;

    /**
     * 是否隐藏【checkSize控制】给的提示文案，为true隐藏，其他都不隐藏
     */
    private Boolean hideTip;

    /**
     * 具体内容
     */
    private List<SysConfig> sysConfigs;

    /**
     * 配置项说明
     */
    private String desc;

    public BaseConfigVO(ConfigEnum configEnum, Boolean disable,
                        List<SysConfig> sysConfigs) {
        this.title = configEnum.getMessage();
        this.type = configEnum.getCode();
        this.hideTip = configEnum.getHideTip();
        this.disable = disable;
        this.switchFlag = sysConfigs.get(0).getSwitchFlag();
        this.settingsFlag = sysConfigs.get(0).getSettingsFlag();
        this.titleType = sysConfigs.get(0).getTitleType();
        this.checkSize = sysConfigs.get(0).getCheckSize();
        if (CollectionUtils.isEmpty(sysConfigs)) {
            this.desc = "";
        } else {
            this.sysConfigs = Collections.unmodifiableList(sysConfigs);
            this.desc = sysConfigs.get(0).getDsc();
        }
    }
}