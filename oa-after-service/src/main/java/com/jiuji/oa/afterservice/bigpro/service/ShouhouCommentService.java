package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouComment;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouCommentReq;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * 售后进程服务类
 *
 * <AUTHOR> quan
 * @date 2020-05-06 09:18:46
 */
public interface ShouhouCommentService extends IService<ShouhouComment> {

    /**
     * 查询售后进程
     * @param shouhouId
     * @return
     */
    R<List<ShouhouComment>> getShouhouComment(Integer shouhouId);

    /**
     * 售后留言回复
     * @param req
     * @return
     */
    R<Boolean>reply(ShouhouCommentReq req);

    /**
     * 转地区3天未接收转地区的提交人做oa消息推送
     */
    void pushMsgOa();
}

