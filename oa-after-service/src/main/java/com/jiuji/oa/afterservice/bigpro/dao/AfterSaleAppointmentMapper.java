package com.jiuji.oa.afterservice.bigpro.dao;

import com.jiuji.oa.afterservice.bigpro.bo.yuyue.NoticePushBeforeCustomerArriveStoreBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> quan
 * @description: TODO
 * @date 2021/9/1 20:04
 */
@Mapper
public interface AfterSaleAppointmentMapper {

    /**
     * 查询正在上班的员工信息
     *
     * @param roleIds
     * @param areaIds
     * @return
     */
    List<NoticePushBeforeCustomerArriveStoreBo> getWorkingCh999IdByAreaIdsAndRoleIds(@Param("roleIds") List<Integer> roleIds, @Param("areaIds") List<Integer> areaIds);

    /**
     * 查询需要推送的预约信息
     *
     * @return
     */
    List<NoticePushBeforeCustomerArriveStoreBo> getAppointmentInfoBeforeCustomerArriveStore();
}
