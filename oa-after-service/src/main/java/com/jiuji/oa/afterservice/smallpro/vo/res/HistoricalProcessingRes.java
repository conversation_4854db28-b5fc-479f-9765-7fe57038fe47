package com.jiuji.oa.afterservice.smallpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class HistoricalProcessingRes {

    /**
     *小件单号
     */
    private Integer smallproId;
    /**
     * 原单号
     */
    private Integer subId;
    /**
     * 门店
     */
    private String areaName;
    /**
     * 处理方式
     */
    private Integer kind;
    private String kindValue;
    /**
     * 状态
     */
    private Integer stats;
    private String statsValue;
    /**
     * 使用服务
     */
    private Integer serviceType;
    private String serviceTypeValue;
    /**
     * 接件人
     */
    private String inuer;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime indate;
    /**
     * 维修费
     */
    private BigDecimal feiyong;
    /**
     * 置换商品
     */
    private Integer changePpriceid;
    private String changePpriceidName;
    /**
     * 换货商品
     */
    private Integer ppriceid;
    private String ppriceidName;
}

