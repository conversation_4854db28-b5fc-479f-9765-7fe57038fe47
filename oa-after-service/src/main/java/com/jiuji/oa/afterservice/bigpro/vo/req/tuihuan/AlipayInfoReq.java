package com.jiuji.oa.afterservice.bigpro.vo.req.tuihuan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class AlipayInfoReq {

    @ApiModelProperty(value = "订单号")
    @NotNull(message = "订单号不能为空")
    private Integer subId;

    @ApiModelProperty(value = "退款方式")
    private String tuiWay;

    @ApiModelProperty(value = "退换方式")
    @NotNull(message = "退换类别不能为空")
    private Integer tuiHuanKind;

    @ApiModelProperty(value = "退换ID")
    private Integer tuiHuanId;

}
