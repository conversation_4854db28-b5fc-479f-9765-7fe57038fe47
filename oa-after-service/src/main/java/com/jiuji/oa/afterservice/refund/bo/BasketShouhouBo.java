package com.jiuji.oa.afterservice.refund.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 订单商品售后信息
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Data
@Accessors(chain = true)
@ApiModel("订单商品售后信息Bo")
public class BasketShouhouBo {

    /**
     * sku id
     */
    @ApiModelProperty(value = "sku id")
    private Integer ppriceid;
    /**
     * 售后门店id
     */
    @ApiModelProperty(value = "库存门店id")
    private Integer areaId;
    /**
     * 转地区id
     */
    @ApiModelProperty(value = "库存状态")
    private Integer newAreaId;
}
