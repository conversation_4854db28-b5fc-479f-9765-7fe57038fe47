package com.jiuji.oa.afterservice.refund.vo.res.way;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jiuji.oa.afterservice.mapstruct.CommonStructMapper;
import com.jiuji.oa.afterservice.refund.enums.RefundBusinessEnum;
import com.jiuji.oa.afterservice.refund.enums.ThirdRefundTypeEnum;
import com.jiuji.oa.afterservice.refund.enums.TuiGroupEnum;
import com.jiuji.oa.afterservice.refund.po.PaymentConfigPo;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 原路径退款vo对象
 * <AUTHOR>
 * @since 2022/7/27 8:43
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
@ApiModel("原路径退款实体类")
public class ThirdOriginRefundVo implements Refund<ThirdOriginRefundVo> {
    /**
     * 退款明细的主键
     */
    @ApiModelProperty("退款明细的主键")
    private Integer id;
    /**
     * 分组代码
     * @see TuiGroupEnum
     */
    @ApiModelProperty("分组代码")
    @NotNull(message = "分组代码不能为空")
    private Integer groupCode;
    /**
     * 支付单号
     */
    @ApiModelProperty("单号")
    private Integer subId;
    /**
     * 退款方式名称
     */
    @ApiModelProperty("退款方式名称")
    @NotBlank(message = "退款方式不能为空")
    private String returnWayName;
    /**
     * 本次退款金额
     */
    @ApiModelProperty("本次退款金额")
    @NotNull(message = "本次退款金额不能为空")
    @Min(value = 0,message = "本次退款金额必须大于等于0")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal refundPrice;
    /**
     * 优惠码金额
     */
    @ApiModelProperty("优惠码金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal couponPrice;
    /**
     * 实付金额
     */
    @ApiModelProperty("实付金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal actualPayPrice;
    /**
     * 已退金额
     */
    @ApiModelProperty("已退金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal refundedPrice;
    /**
     * 未退款办理的已退金额
     */
    @ApiModelProperty("未退款办理的已退金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal noCheck3RefundedPrice;
    /**
     * 流水号
     */
    @ApiModelProperty("流水号")
    @NotBlank(message = "流水号不能为空")
    private String tradeNo;
    /**
     * 收银other记录id
     */
    @ApiModelProperty("收银other记录id")
    @NotNull(message = "收银other记录id不能为空")
    @Min(value = 1,message = "收银other记录id必须大于等于1")
    private Integer otherRecordId;
    /**
     * 收银记录id
     */
    @ApiModelProperty("收银记录id")
    private Integer shouyingId;
    /**
     * 收款方式配置id
     */
    @ApiModelProperty("收款方式配置id")
    private Integer sysConfigId;
    /**
     * 收款方式配置code
     */
    @ApiModelProperty("收银配置code")
    private Integer sysConfigCode;

    /**
     * 退款方式
     * @see ThirdRefundTypeEnum
     */
    @ApiModelProperty("退款方式")
    private Integer refundType;

    /**
     * 退款方式
     * @see ThirdRefundTypeEnum
     */
    @ApiModelProperty("退款方式")
    private String refundTypeName;

    @ApiModelProperty(value = "shouying_other type_",hidden = true)
    private Integer otherType;

    /**
     * @see PaymentConfigPo.ActivityTypeEnum
     */
    @TableField("activity_type")
    private String activityType;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "软删除标识",hidden = true)
    private Boolean isDel;
    /**
     * 退款的业务类型 0和null是普通退款 1 运营商返现
     * @see RefundBusinessEnum
     */
    @ApiModelProperty("退款的业务类型")
    private Integer refundBusinessType;

    /**
     * 是否必须退款字段
     * 1=必须
     */
    private Boolean returnFlag;

    /**
     *  对应 basketId
     */
    private String basketId;

    /**
     * @see com.jiuji.oa.afterservice.refund.enums.TransportNewSubEnum
     * 转到新订单
     */
    private String transportNewSub;

    /**
     * 收银时间
     */
    @JSONField(format = TimeFormatConstant.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime dtime;

    private String outordernum;

    /**
     * 注意事项
     */
    private String heed;


    public ThirdOriginRefundVo copy(){
        return SpringUtil.getBean(CommonStructMapper.class).copyThirdOriginRefund(this);
    }
}
