package com.jiuji.oa.afterservice.webaddreservation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.webaddreservation.entity.InquiryInformationFault;
import com.jiuji.oa.afterservice.webaddreservation.mapper.InquiryInformationFaultMapper;
import com.jiuji.oa.afterservice.webaddreservation.service.InquiryInformationFaultService;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@DS(DataSourceConstants.OA_NC)
public class InquiryInformationFaultServiceImpl extends ServiceImpl<InquiryInformationFaultMapper, InquiryInformationFault> implements InquiryInformationFaultService {
}
