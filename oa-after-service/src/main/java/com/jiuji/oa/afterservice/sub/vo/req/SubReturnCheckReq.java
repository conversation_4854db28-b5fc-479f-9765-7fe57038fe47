package com.jiuji.oa.afterservice.sub.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * <AUTHOR> quan
 * @description: 退款审核请求参数
 * @date 2021/6/30 13:34
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class SubReturnCheckReq implements Serializable {

    private static final long serialVersionUID = -8470265316195175846L;

    @ApiModelProperty(value = "列表数据单号")
    @NotNull(groups = Default.class,message = "ID不能为空")
    private Integer id;

    @ApiModelProperty(value = "业务单号")
    @NotNull(groups = Default.class,message = "业务ID不能为空")
    private Long subId;

    @ApiModelProperty(value = "业务类型",notes = "1、销售单，2、良品单，3、预约单，4、售后维修单，5、小件接件单，6、回收订单，7、租机单")
    @NotNull(groups = Default.class,message = "业务类型不能为空")
    private Integer subType;

    @ApiModelProperty(value = "审核状态",notes = "2 通过 3 拒绝")
    @Min(value = 2)
    @Max(value = 3)
    private Integer status;

    @ApiModelProperty(value = "审核备注信息")
    private String checkRemark;
}
