package com.jiuji.oa.afterservice.sys.vo.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-7-1
 */
@Data
public class SysConfigVO {

    private Integer id;

    /**
     * 描述
     */
    private String dsc;

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value;

    /**
     * 类型
     */
    private Integer code;

    private Long xtenant;

    private Integer authId;

    private String areaids;

    private String kemu;

    /**
     * 排序
     */
    private Integer rank;

    /**
     * 删除标识
     */
    private Boolean isdel;

    private Integer fzhsType;
}
