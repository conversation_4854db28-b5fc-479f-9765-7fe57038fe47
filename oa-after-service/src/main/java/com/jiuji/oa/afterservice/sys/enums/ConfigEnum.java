package com.jiuji.oa.afterservice.sys.enums;

import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 系统配置项枚举
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ConfigEnum implements CodeMessageEnumInterface {
    /**
     * 优惠码配置
     */
    PROMO_CODE_1(231, "购买Care+服务发券","用户购买了care+服务,订单完成之后，系统每月自动发放3张面值为30元的优惠券首次发券为购买次日，优惠券使用条件手机配件满100元可用，有效期为1个月，发放时间为1年.",
            true,true,ConfigTitleEnum.PROMO_CODE,0,Boolean.FALSE),
//    PROMO_CODE_2(97, "购买屏背保服务发券",true,false,ConfigTitleEnum.PROMO_CODE),
//    PROMO_CODE_3(96, "购买碎屏保服务发券",true,false,ConfigTitleEnum.PROMO_CODE),
    PROMO_CODE_4(227, "购买延长保服务不发券","用户购买了延长保，订单完成之后，系统自动发放1张面值为50元延保回馈优惠券，优惠券使用条件大件商品满500可用，不可累加；有效期为1年。"
        ,true,true,ConfigTitleEnum.PROMO_CODE,0,Boolean.FALSE),
//    PROMO_CODE_5(0, "购买意外保服务发券",true,false,ConfigTitleEnum.PROMO_CODE),
    PROMO_CODE_6(93, "换新补贴优惠券配置","换新补贴优惠券配置",
        false,true,ConfigTitleEnum.PROMO_CODE,0,Boolean.FALSE),
    PROMO_CODE_7(282, "学生认证优惠券配置","在此配置中设置了的优惠券需要完成学生认证才能使用",
            false,true,ConfigTitleEnum.PROMO_CODE,0,Boolean.FALSE),

    /**
     * 售后/售前配置R
     */
    AFTER_SALES_OR_PRE_SALES_1(24, "考核成本控制","启用则考核成本使用单独的设置的成本作为商品的考核成本，关闭则使用真实成本作为考核成本。"
            ,true,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_2(121, "订单价格改价使用权限","启用则使用改价使用权限进行控制，关闭则使用考核成本里面配置的销售底价进行改价限制。"
            ,true,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_3(234, "商场订单启用控制","商场订单启用则代表该商户配置了商场门店之后，该门店的订单在配置金额的区间内会自动标记为商场订单。"
            ,true,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_4(228, "网络订单自动确认的ppid","配置了ppid的商品，在网络下单时系统会自动进行订单的确认。"
            ,false,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    NUMBER_OF_TOUCHPOINTS(349, "团购订单定义配置","用于更改团购订单的定义，一个用户（个人，单位或者集团）单次购买x台以上大件触发团购"
            ,false,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.TRUE),
    AFTER_SALES_OR_PRE_SALES_5(54, "客服热线配置","配置的客服热线是用于oa端消息推送里面的客服电话。"
            ,false,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_6(SysConfigConstant.MY_MASK_HIDES_ZERO_YUAN_FILM, "我的贴膜隐藏0元膜","开启后我的贴膜将隐藏0元出库和赠送的贴膜信息。"
            ,true,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_7(SysConfigConstant.ORDER_SHOPPING_BAG, "订单详情页购物袋PPID配置","订单详情页购物袋PPID配置。"
            ,false,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,6,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_8(266, "三星订单推送排除门店","被排除的门店卖出三星产品后将不会生成三星订单交易完成的推送。"
            ,false,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_9(121000, "收银是否默认交易配置","用于收银模块中交易功能是否默认勾选配置，配置为开启状态则默认勾选，反之为不勾选。"
            ,true,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),
    AFTER_SALES_OR_PRE_SALES_10(123000, "订单商品备货配置","配置订单商品添加时是否需要自动备货"
            ,true,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.FALSE),

    RECYCLE_OPT_DIFF_SYNC(121003, "是否同步拍机堂复检回收差异（OPT）","开启后代拍拍机堂的回收商品差异以首次代拍质检确认节点进行标记及推送。将以拍机堂复检的差异作为回收差异。\n" +
            "关闭后代拍拍机堂的回收商品差异以首次提交代拍节点进行标记及推送。将以OA复检的差异作为回收差异。\n" +
            "* 若没有OA复检记录则依然以拍机堂复检首次代拍质检确认节点标记及推送"
                                       ,true,true,ConfigTitleEnum.RECYCLE,0,Boolean.FALSE),

    RECYCLE_LOW_PRICE_MACHINE(122004, "低价值机标准配置","低价值机(或环保机)判断标准为: 回收金额小于 (<)配置数值"
            ,false,true,ConfigTitleEnum.RECYCLE,1,Boolean.TRUE),

    RECYCLE_UNLOCK_CONFIG(131000, " 回收加单免锁单账号配置","配置用于大批量回收加单避免锁单限制的特殊加单账号"
            ,false,true,ConfigTitleEnum.RECYCLE,0,Boolean.TRUE),

    ORDER_PAYMENT_COMPLETES_PPID(324, "订单支付完成自动交易打印小票","配置需要自动完成和打印小票的端口名称，订单支付完成系统将会自动完成订单和打印销售小票"
            ,false,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.TRUE),
    ORDER_PAYMENT_COMPLETES_PRINT(325, "订单支付完成自动交易打印客户端编号","配置需要自动完成和打印小票的端口名称，订单支付完成系统将会自动完成订单和打印销售小票"
            ,false,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.TRUE),
    RECOVER_PRICE_REVIEW(140000, " 回收加价审核权限/条件配置","配置用于回收单加价金额不同范围/额度的审核权限，以及审核超时时间条件"
            ,false,true,ConfigTitleEnum.RECYCLE,0,Boolean.TRUE),
    RECOVER_LOCK_REVIEW(140100, " 回收单审核校验激活锁/监管锁","用于回收单在审核操作时校验苹果手机激活锁/监管锁必须为“关”，开启时则强校验"
            ,true,true,ConfigTitleEnum.RECYCLE,0,Boolean.TRUE),
    RECOVER_SHOP_CHECK_REVIEW(140200, " 回收单收货检测校验导师复检","用于回收单在收货检测操作时校验回收价＞10000或回收售出良品当前特征优于售出前特征时必须导师复检，开启时则强校验"
            ,true,true,ConfigTitleEnum.RECYCLE,0,Boolean.TRUE),
    RECOVER_OPT_AREA_CONFIG(154110, "OPT代拍门店配置","用于配置支持代拍的门店"
            ,false,true,ConfigTitleEnum.RECYCLE,0,Boolean.TRUE),
    SUB_SOURCE(120008, " 订单来源配置","【销售单、回收单、良品单】订单来源配置（不同租户后台下配置不同租户数据）"
            ,false,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.TRUE),
    REPORT_EXCLUDES_CROSS_AUTHORIZED_TRANSFER_ORDERS(120304, " 报表剔除跨授权调拨订单","开启后，【销售财务报表、销售明细报表、赛马业绩统计、销售报表、大件品牌销售统计、销售人员业绩统计、增值业务分析、热销机型分析1、热销配件分析、热销机型分析2、配件统计、配件搭售比统计、我的业绩、业绩看板】报表会剔除跨授权调拨生成的订单数据"
            ,true,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.TRUE),
    RECYCLE_FOLLOW_NEW_CULL_SUB_CONFIG(143000, " 回收跟机率大件销量剔除类型","配置用于回收统计、回收人员统计、赛马业绩统计报表、销售人员业绩统计、我的业绩、业绩看板中回收跟机率字段关于大件销量剔除类型的配置"
            ,false,true,ConfigTitleEnum.RECYCLE,0,Boolean.TRUE),
    RECOVERY_RETURN_CID(143100, "回收，返还的旧件分类配置","对哪些分类的旧件需要进行返还和回收操作，进行配置"
            ,false,true,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.TRUE),
    NATIONAL_USER_ADDRESS(143201, "国补获取用户地址","开启后，“到店自取”的“国补订单”可以录入 【用户地址信息】"
            ,true,false,ConfigTitleEnum.AFTER_SALES_OR_PRE_SALES,0,Boolean.TRUE),
            ;

    /**
     * 编码
     */
    private Integer code;


    /**
     * 名称
     */
    private String message;

    /**
     * 描述
     */
    private String dsc;
    /**
     * 是否是开关
     */
    private Boolean switchFlag;

    /**
     * 是否是全局配置
     */
    private Boolean settingsFlag;

    /**
     * 属于哪类标题
     */
    private ConfigTitleEnum configTitleEnum;

    /**
     * 字段校验长度 0是不限制长度  限制的长度是按照英文逗号间隔的数量
     */
    private Integer checkSize;

    /**
     * 是否隐藏【checkSize控制】给的提示文案，为true隐藏，其他都不隐藏
     */
    private Boolean hideTip;

    public static String getMessage(Integer code) {
        CodeMessageEnumInterface[] value = values();
        for (CodeMessageEnumInterface enumi : value) {
            if (enumi.getCode().equals(code)) {
                return enumi.getMessage();
            }
        }
        return null;
    }

    public static ConfigEnum getEnumByCode(Integer code){
        ConfigEnum[] value = values();
        for (ConfigEnum e : value) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

}
