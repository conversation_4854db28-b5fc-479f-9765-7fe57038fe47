package com.jiuji.oa.afterservice.smallpro.vo.req;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SelectResistFilmReq {
    /**
     * 每页显示条数
     */
    @NotNull(message = "每页显示条数不能为空")
    private Long size;
    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Long current;

    /**
     * 是否为导出
     */
    private Boolean isExport;

    /**
     * @see com.jiuji.oa.afterservice.smallpro.enums.SelectResistFilmEnum
     * 查询类型
     */
    private Integer selectKeyType;
    /**
     * 查询内容
     */
    private String selectKeyValue;

    /**
     * 分类
     */
    private List<Integer> cidList;

    /**
     * 接件地区
     */
    private List<String> areaIdList;
    /**
     * 状态
     */
    private List<Integer> status;

    /**
     * 服务类型
     */
    private List<Integer> serviceTypeList;
    /**
     * @see com.jiuji.oa.afterservice.smallpro.enums.SelectResistFilmOrderTypeEnum
     * 订单类型
     */
    private Integer orderType;

    /**
     * 补差金额最小值
     */
    private BigDecimal supplementAmountMin;
    /**
     * 补差金额最大值
     */
    private BigDecimal supplementAmountMax;
    /**
     * 损耗数量最小值
     */
    private Integer lossCountMin;
    /**
     * 损耗数量最大值
     */
    private Integer lossCountMax;
    /**
     * 下单开始时间
     */
    private LocalDateTime orderTimeStart;
    /**
     * 下单结束时间
     */
    private LocalDateTime orderTimeEnd;
}
