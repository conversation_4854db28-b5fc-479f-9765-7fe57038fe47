package com.jiuji.oa.afterservice.sys.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.sys.bo.UserTokenCode;
import com.jiuji.oa.afterservice.sys.service.UserTokenService;
import com.jiuji.tc.common.vo.R;
import jdk.nashorn.internal.ir.annotations.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户信息服务类
 * <AUTHOR>
 * @since 2021/11/30 14:33
 */
@Service
@Slf4j
public class UserTokenServiceImpl implements UserTokenService {
    /**用户识别码缓存信息*/
    private static String USER_TOKEN_REDIS_KEY = "userTokenRedisKey";
    private static String USER_TOKEN_REDIS_EXPIRE_KEY = "userTokenRedisExpireKey";
    @Autowired
    private RedisTemplate<String,Object> redisTemplate;
    @Resource
    private SmsService smsService;

    public static final Integer SEPARATING_VALUES = 6;

    /**
     * 根据识别码 获取 userId
     * @param code
     * @return
     */
    @Override
    public Integer getUserIdByToken(String code)
    {
        Integer userId;
        if(StringUtils.isNotBlank(code) && code.length() > SEPARATING_VALUES && XtenantEnum.isJiujiXtenant()){
            Object value = redisTemplate.opsForValue().get(code);
            if(ObjectUtil.isNotNull(value) && StringUtils.isNumeric(value.toString())){
                userId = Integer.parseInt(value.toString());
            } else {
                String comment = String.format("（新） 根据识别码 获取 userId 获取异常 传入参数：%s,获取结果:%s", code, JSONUtil.toJsonStr(value));
                RRExceptionHandler.logError(comment,code,null,smsService::sendOaMsgTo9JiMan);
                return null;
            }
        } else {
            userId = getUserTokenList().stream().filter(ut->Objects.equals(ut.getCode(),code)).findFirst()
                    .map(UserTokenCode::getUserId)
                    .orElse(null);
        }
        return userId;
    }

    /**
     * 清除会员识别码
     * @param code
     * @return
     */
    @Override
    public R<Boolean> delUserIdByToken(String code) {
        if(StringUtils.isNotBlank(code) && code.length() > SEPARATING_VALUES && XtenantEnum.isJiujiXtenant()){
             //redisTemplate 删除 code
            Boolean delete = redisTemplate.delete(code);
            if(!delete){
                RRExceptionHandler.logError("(新)清除会员识别码失败",code,null,smsService::sendOaMsgTo9JiMan);
            }
        } else {
            getUserTokenList().stream().filter(ut -> Objects.equals(ut.getCode(), code)).findFirst()
                    .ifPresent(userTokenCode -> {
                        redisTemplate.opsForHash().delete(USER_TOKEN_REDIS_KEY, userTokenCode.getKey());
                        UserTokenCode.UserTokenRedisExpireItem userTokenRedisExpireItem = new UserTokenCode.UserTokenRedisExpireItem();
                        userTokenRedisExpireItem.setCode(userTokenCode.getCode());
                        userTokenRedisExpireItem.setExpireDate(toDateByToken());
                        redisTemplate.opsForHash().put(USER_TOKEN_REDIS_EXPIRE_KEY,userTokenRedisExpireItem.getCode(),JSON.toJSONString(userTokenRedisExpireItem));
                    });
        }
        return R.success("已刷新会员识别码!", true);
    }

    /**
     * 时间戳格式
     * @return
     */
    private static String toDateByToken(){
        long l = LocalDateTime.now().plusMinutes(20).toEpochSecond(ZoneOffset.of("+8"));
        return "\\/Data(\""+l+"\"+0800)\\/";
    }


    /**
     * 获取所有列表
     * @return
     */
    @Override
    public List<UserTokenCode> getUserTokenList()
    {
        HashOperations<String, String, String> opsForHash = redisTemplate.opsForHash();
        Map<String, String> netSubHash = opsForHash.entries(USER_TOKEN_REDIS_KEY);
        LocalDateTime now = LocalDateTime.now();
        return netSubHash.entrySet().stream().map(Map.Entry::getValue).map(JSON::parseObject).map(jsonObj->new UserTokenCode().setCode(jsonObj.getString("code"))
                .setUserId(jsonObj.getInteger("userId")).setKey(jsonObj.getString("key")).setCreateDate(parseLocalDateTime(jsonObj.getString("createDate")))
                .setExpireDate(parseLocalDateTime(jsonObj.getString("expireDate"))))
                .filter(utc-> Objects.nonNull(utc.getExpireDate()) && utc.getExpireDate().isAfter(now))
                .collect(Collectors.toList());
    }

    private LocalDateTime parseLocalDateTime(String dateStr){
        if(StrUtil.isBlank(dateStr)){
            return null;
        }
        String timestamp = ReUtil.get("^/Date\\((\\d+)\\+\\d+\\)/$", dateStr, 1);
        return Optional.ofNullable(timestamp).map(Convert::toLong)
                .map(tt->LocalDateTime.ofInstant(Instant.ofEpochMilli(tt), ZoneId.systemDefault()))
                .orElse(null);
    }

}
