package com.jiuji.oa.afterservice.patchsearch.service;


import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes;

import java.util.List;

/**
 * @Auther: qiweiqing
 * @Date: 2020/02/06
 * @Description:
 */
public interface PatchSearchService{


    /**
     * 根据basket查询我的贴膜列表
     *
     * @param personChangesReq
     */
    List<PatchSearchRes> listPageById(PatchSearchReq personChangesReq);

}
