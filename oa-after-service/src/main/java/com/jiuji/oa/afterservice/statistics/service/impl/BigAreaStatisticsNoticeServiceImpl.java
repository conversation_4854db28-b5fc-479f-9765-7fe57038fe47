package com.jiuji.oa.afterservice.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.Header;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.afterservice.batchreturn.vo.req.ShouHouStatisticsHttpReq;
import com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.WXSmsReceiver;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.WXSmsReceiverService;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.ShouHouStatisticsEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.service.ShouhouStatisticsService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.SmsReceiverClassfyEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.AreaDepartBo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.rabbitmq.SmallproRabblitMq;
import com.jiuji.oa.afterservice.statistics.bo.shouhou.BigAreaStatisticsNoticeBo;
import com.jiuji.oa.afterservice.statistics.bo.shouhou.NotifyParamBo;
import com.jiuji.oa.afterservice.statistics.service.BigAreaStatisticsNoticeService;
import com.jiuji.oa.nc.RacePerformanceCloud;
import com.jiuji.oa.nc.race.req.RacePerformanceCloudReq;
import com.jiuji.oa.nc.race.res.RacePerformanceCloudRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/** 售后中心统计通知
 * <AUTHOR>
 * @since 2023/8/4 15:51
 */
@Service
@Slf4j
public class BigAreaStatisticsNoticeServiceImpl implements BigAreaStatisticsNoticeService {
    @Resource
    private AreainfoService areainfoService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private SmsService smsService;

    @Override
    public R notice(NotifyParamBo notifyParamBo){
        String departIds = notifyParamBo.getDepartIds();
        List<Integer> departIdList = StrUtil.splitTrim(departIds, StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList());
        // 获取所有通知人员和大区消息,其他租户要推送 可以改这里 listInfo里面的实现
        List<BigAreaStatisticsNoticeBo> noticeBos = BigAreaStatisticsNoticeBo.listInfo().stream()
                .filter(basn -> departIdList.isEmpty() || departIdList.contains(basn.getDepartId()))
                .collect(Collectors.toList());
        // 查询大区的统计数据
        List<Integer> bigDepartIds = noticeBos.stream().map(BigAreaStatisticsNoticeBo::getDepartId).distinct().collect(Collectors.toList());
        // 获取大区下的所有门店信息
        List<AreaDepartBo> areaDepartBos = areainfoService.listAreaIdByDepartId(bigDepartIds, null);
        if(areaDepartBos.isEmpty()){
            return R.error("门店信息为空,无法进行通知推送");
        }
        List<Integer> areaIds = areaDepartBos.stream().map(AreaDepartBo::getAreaId).distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        //模拟登录用户信息
        simulateUser(areaIds, SpringContextUtil.getRequest().map(req -> req.getHeader(Header.AUTHORIZATION.getValue()))
                .orElseGet(() -> {
                    throw new CustomizeException("必须提供请求头Authorization");
                }));
        //统计时间计算
        LocalDate now = LocalDate.now();
        LocalDateTime startTime = now.plus(notifyParamBo.getDaysFromNowStart(), ChronoUnit.DAYS).atStartOfDay();
        LocalDateTime endTime = now.plus(notifyParamBo.getDaysFromNowEnd(), ChronoUnit.DAYS).atTime(LocalTime.MAX);
        // 添加赛马统计的msg信息
        addRacePerformanceMsg(noticeBos, areaIds, startTime, endTime);

        // 添加售后统计的msg信息
        addShouhouStatisticsMsg(noticeBos, areaIds, startTime, endTime);
        // 获取推送的用户id
        addCh999Ids(noticeBos, areaDepartBos, areaIds);
        // 推送oa消息
        sendOaMsg(noticeBos);
        // 打印通知消息
        if(log.isDebugEnabled()){
            log.debug("通知内容: {}", JSON.toJSONString(noticeBos, SerializerFeature.PrettyFormat));
        }
        return R.success(Dict.create().set(TraceIdUtil.TRACE_ID_KEY, MDC.get(TraceIdUtil.TRACE_ID_KEY)))
                .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    private void addRacePerformanceMsg(List<BigAreaStatisticsNoticeBo> noticeBos, List<Integer> areaIds, LocalDateTime startTime, LocalDateTime endTime) {
        // 添加赛马统计信息
        RacePerformanceCloudReq param = LambdaBuild.create(new RacePerformanceCloudReq())
                .set(RacePerformanceCloudReq::setAreaIds, Convert.toList(Long.class, areaIds))
                .set(RacePerformanceCloudReq::setStartTime, startTime)
                .set(RacePerformanceCloudReq::setEndTime, endTime)
                // 对比时间, 取开始往前移动一天
                .set(RacePerformanceCloudReq::setComparisonStartTime, startTime.minusDays(1))
                .set(RacePerformanceCloudReq::setComparisonEndTime, startTime.minusDays(1).toLocalDate().atTime(LocalTime.MAX))
                .set(RacePerformanceCloudReq::setDiscountCode, Boolean.FALSE)
                .set(RacePerformanceCloudReq::setGift, Boolean.FALSE)
                .set(RacePerformanceCloudReq::setGift, Boolean.FALSE)
                .set(RacePerformanceCloudReq::setItems, Arrays.asList("area", "hardwareRepairProfit", "hardwareRepairSingleProfit", "hardwareRepairProfitRatio"))
                .set(RacePerformanceCloudReq::setLargeCustomers, Boolean.FALSE)
                .set(RacePerformanceCloudReq::setLargePrice, 0)
                .set(RacePerformanceCloudReq::setOrderStatus, 0)
                .set(RacePerformanceCloudReq::setRegionOnly, Boolean.TRUE)
                .set(RacePerformanceCloudReq::setStalledClearance, Boolean.FALSE)
                .set(RacePerformanceCloudReq::setVirtualGoods, Boolean.TRUE)
                .set(RacePerformanceCloudReq::setZeroOrder, Boolean.FALSE)
                .build();
        log.warn("获取赛马数据参数: {}", JSON.toJSONString(param));
        RacePerformanceCloudRes racePerformanceData = CommonUtils.getResultData(SpringUtil.getBean(RacePerformanceCloud.class).getRacePerformanceData(param),
                userMsg -> {
                    smsService.sendOaMsgTo9JiMan("获取赛马数据异常: {}", userMsg);
                    return LambdaBuild.create(new RacePerformanceCloudRes()).set(RacePerformanceCloudRes::setData, Collections.emptyList()).build();
                });
        // 根据结果生成对应的推送消息
        noticeBos.forEach(notice -> racePerformanceData.getData().stream()
                .filter(rpf -> Objects.equals(Convert.toInt(rpf.getAreaId()), notice.getDepartId()) && !"其他".equals(rpf.getArea()))
                .findFirst()
                .ifPresent(rpf -> {
                    //维修毛利
                    notice.getMsgJoiner().add(StrUtil.format("WXML{}", rpf.getHardwareRepairProfit()));
                    //维修单毛
                    notice.getMsgJoiner().add(StrUtil.format("DM{}", rpf.getHardwareRepairSingleProfit()));
                    //维修配比
                    notice.getMsgJoiner().add(StrUtil.format("PB{}", rpf.getHardwareRepairProfitRatio()));
                    notice.getStatisticsMap()
                            .set("WXML", rpf.getHardwareRepairProfit())
                            .set("DM", rpf.getHardwareRepairSingleProfit())
                            .set("PB", rpf.getHardwareRepairProfitRatio())
                    ;
                }));

    }

    /**
     * 发送oa通知消息
     * @param noticeBos
     */
    private void sendOaMsg(List<BigAreaStatisticsNoticeBo> noticeBos) {
        // 按员工id分组合并发送
        Map<Integer, List<BigAreaStatisticsNoticeBo>> employeeIdMap = noticeBos.stream().filter(nt -> nt.getMsgJoiner().length() > 0 && nt.getEmployeeIds().size() > 0)
                .flatMap(nt -> nt.getEmployeeIds().stream().map(eId -> new Object[]{eId, nt}))
                .collect(Collectors.groupingBy(arr -> (Integer) arr[0], Collectors.mapping(arr-> (BigAreaStatisticsNoticeBo)arr[1], Collectors.toList())));
        // 遍历并推送单人多条的消息
        String[] keys = new String[]{"WXML", "DM", "PB","JJL", "WXL", "FBL"};
        employeeIdMap.entrySet().stream().filter(entry -> entry.getValue().size()>1)
                .forEach(entry -> {
                    //合计量
                    Dict total = Dict.create();
                    String content = entry.getValue().stream()
                            .peek(nt -> {
                                // WXML bigd DM bigd PB bigd  JJL long  WXL long FBL string
                                // 按以下顺序进行合计
                                Dict statisticsMap = nt.getStatisticsMap();
                                for (String key : keys) {
                                    Object value = statisticsMap.get(key);
                                    if(value != null){
                                        if ("FBL".equals(key)){
                                            value = StrUtil.removeSuffix(value.toString(), "%");
                                            value = Convert.toBigDecimal(value);
                                        }
                                        if(total.get(key) != null){
                                            total.set(key, NumberUtil.add(total.getNumber(key),(Number) value));
                                        }else{
                                            total.set(key, value);
                                        }

                                    }
                                }
                            })
                            .map(nt -> StrUtil.format("{}：{}", nt.getDepartName(), nt.getMsgJoiner().toString()))
                            .collect(Collectors.joining(StringPool.NEWLINE));
                    //增加合计行
                    StringJoiner totalJoiner = new StringJoiner(StringPool.PIPE);
                    for (String key : keys) {
                        Object value = total.get(key);
                        if(value != null){
                            if("FBL".equals(key)){
                                BigDecimal bigValue = Convert.toBigDecimal(value);
                                totalJoiner.add(StrUtil.format("{}{}{}", key,
                                        bigValue.divide(BigDecimal.valueOf(entry.getValue().size()), RoundingMode.HALF_UP), "%"));
                            } else if("DM".equals(key) || "PB".equals(key)){
                                BigDecimal bigValue = Convert.toBigDecimal(value);
                                totalJoiner.add(StrUtil.format("{}{}", key,
                                        bigValue.divide(BigDecimal.valueOf(entry.getValue().size()), RoundingMode.HALF_UP)));
                            }else{
                                totalJoiner.add(StrUtil.format("{}{}", key, value));
                            }

                        }
                    }
                    content = StrUtil.format("{}{}合计：{}",content, StringPool.NEWLINE, totalJoiner.toString());
                    smsService.sendOaMsg(content, "", Convert.toStr(entry.getKey()), OaMesTypeEnum.XLTZ);

                });
        Predicate<Integer> notHasSendFun = eId -> employeeIdMap.get(eId) == null || employeeIdMap.get(eId).size() <= 1;
        // 推送单条的消息
        noticeBos.stream()
                .filter(nt -> nt.getMsgJoiner().length()>0 && nt.getEmployeeIds().stream().filter(notHasSendFun).findAny().isPresent())
                .forEach(nt -> smsService.sendOaMsg(StrUtil.format("{}：{}", nt.getDepartName(), nt.getMsgJoiner().toString()), "",
                        nt.getEmployeeIds().stream()
                                //排除已经发送了的
                                .filter(notHasSendFun)
                                .map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA)), OaMesTypeEnum.XLTZ));
    }

    private void addCh999Ids(List<BigAreaStatisticsNoticeBo> noticeBos, List<AreaDepartBo> areaDepartBos, List<Integer> areaIds) {
        // 获取全区推送人
        SpringUtil.getBean(WXSmsReceiverService.class).lambdaQuery()
                .eq(WXSmsReceiver::getClassify, SmsReceiverClassfyEnum.AFTER_SALES_REPORT_ALL_DEPART.getCode())
                .orderByDesc(WXSmsReceiver::getId).list().stream().findFirst()
                .ifPresent(sr -> {
                    List<Integer> ch999Ids = StrUtil.splitTrim(sr.getCh999ids(), StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList());
                    noticeBos.forEach(notice -> notice.getEmployeeIds().addAll(ch999Ids));
                });
        // 获取大区指定角色的推送人
        List<Integer> roleIds = noticeBos.stream().map(BigAreaStatisticsNoticeBo::getRoleIds).flatMap(Collection::stream)
                .distinct().collect(Collectors.toList());
        // 添加推送角色门店id
        List<Integer> roleAreaIds = Stream.concat(noticeBos.stream().map(BigAreaStatisticsNoticeBo::getRoleAreaIds)
                        .flatMap(Collection::stream).filter(Objects::nonNull)
                , areaIds.stream()).distinct().collect(Collectors.toList());
        List<AreaDepartBo> mainCh999Ids = CommonUtils.bigDataInQuery(roleAreaIds, ids -> areainfoService.listCh999IdByAreaIdAndMainRole(ids, roleIds));
        noticeBos.forEach(notice -> mainCh999Ids.stream()
                .filter(mc -> areaDepartBos.stream()
                // 部门匹配信息
                .anyMatch(ad -> Objects.equals(ad.getDepartId(),notice.getDepartId()) && Objects.equals(mc.getAreaId(), ad.getAreaId()))
                 || notice.getRoleAreaIds().contains(mc.getAreaId()))
                // 转为用户id
                .map(AreaDepartBo::getMainRoleCh999Id)
                .filter(Objects::nonNull)
                .forEach(notice.getEmployeeIds()::add));
    }

    /**
     * 添加售后统计的通知消息
     * @param noticeBos
     * @param areaIds
     * @param tartTime
     * @param endTime
     */
    private void addShouhouStatisticsMsg(List<BigAreaStatisticsNoticeBo> noticeBos, List<Integer> areaIds, LocalDateTime tartTime, LocalDateTime endTime) {
        ShouhouStatisticsService shouhouStatisticsService = SpringUtil.getBean(ShouhouStatisticsService.class);
        ShouHouStatisticsHttpReq req = ShouHouStatisticsHttpReq.builder().areaCodes(Convert.toList(String.class,areaIds))
                .start(tartTime).end(endTime).filterAreaType(1)
                .shouHouStatisticsCodes(CollUtil.newHashSet(ShouHouStatisticsEnum.JIE_JIAN_LIANG.getCode()))
                .build();
        log.warn("获取售后统计参数: {}", JSON.toJSONString(req));
        List<ShouHouStatisticsRes> shouhouStatistics = CommonUtils.getResultData(shouhouStatisticsService.getAndBuildStatisticsList(req), userMsg -> {
            smsService.sendOaMsgTo9JiMan("获取售后统计异常: {}", userMsg);
            return Collections.emptyList();
        });
        // 根据结果生成对应的推送消息
        noticeBos.forEach(notice -> shouhouStatistics.stream().filter(ss -> Objects.equals(ss.getAreaid(), notice.getDepartId()))
                .findFirst()
                .ifPresent(ss -> {
                    //总接件量
                    notice.getMsgJoiner().add(StrUtil.format("JJL{}", ss.getTotalJieJian()));
                    // 外修量
                    notice.getMsgJoiner().add(StrUtil.format("WXL{}", ss.getWaiXiuJieJianLiang()));
                    // 非保率
                    notice.getMsgJoiner().add(StrUtil.format("FBL{}", ss.getFeiBaoRate()));
                    notice.getStatisticsMap()
                            .set("JJL", ss.getTotalJieJian())
                            .set("WXL", ss.getWaiXiuJieJianLiang())
                            .set("FBL", ss.getFeiBaoRate())
                    ;
                }));
    }

    /**
     * 模拟用户信息
     * @param areaIds
     * @return
     */
    private void simulateUser(List<Integer> areaIds, String token) {
        if(currentRequestComponent.getCurrentStaffId() != null){
            // 存在登录用户信息,不需要模拟
            return;
        }
        // 模拟查询用户信息, 获取总部门店任选一个, 设定应该拥有的权限, 并缓存到redis,以便调用nc的接口
        AreaInfo areaInfo = areaIds.stream().findFirst().map(areaId -> areainfoService.getAreaBelongsDcHqD1AreaId(areaId))
                .map(abd -> abd.getDcAreaId()).map(areaId -> SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(areaId))
                .filter(r -> {
                    if (!r.isSuccess()) {
                        throw new CustomizeException(r.getUserMsg());
                    }
                    return r.isSuccess();
                }).map(R::getData)
                .orElseThrow(() -> new CustomizeException(StrUtil.format("查询不到对应的大仓门店信息")));
        OaUserBO user = SpringUtil.getBean(SmallproRabblitMq.class).simulateUser(areaInfo, "售后消息推送");
        // 设置权限信息
        SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(RequestAttrKeys.REQUEST_ATTR_OA_USER, user));
        user.setUserId(-1);
        //用户登录,设置到redis中
        currentRequestComponent.login(user, token);
    }

}
