/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有, 侵权必究！
 */

package com.jiuji.oa.afterservice.common.exception;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.filter.BodyReaderHttpServletRequestWrapper;
import com.jiuji.oa.afterservice.common.robot.QQRobot;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.logging.LogLevel;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import redis.clients.jedis.exceptions.JedisConnectionException;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.rmi.RemoteException;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.IntFunction;

/**
 * 异常处理器
 *
 * @<NAME_EMAIL>
 */
@RestControllerAdvice
@ResponseStatus(HttpStatus.ACCEPTED)
@Slf4j
@RequiredArgsConstructor
public class RRExceptionHandler {
    private final QQRobot qqRobot;
    private final ApplicationContext context;
    private static final int QQ_GROUP_ID = 850_031_065;
    /**前端提示*/
    private static final String SYSTEM_ERROR_MSG = "服务器开小差了,错误编码:AE{}";
    /**日志内容*/
    private static final String PARAM_ERROR = "参数异常,错误编码:AE{}: {}\n{}";


    /**
     * 处理自定义异常
     */
    @ExceptionHandler(CustomizeException.class)
    public R handleRRException(CustomizeException e) {
        R r = Optional.ofNullable(e.getR()).orElseGet(() -> {
            R r1 = new R();
            r1.setCode(e.getCode());
            r1.setUserMsg(e.getMsg());
            r1.setMsg("error");
            return r1;
        });

        if(e.getCause() != null){
            invokeWithErrorCode(errorCode -> {
                String content = getRequest().map(request -> makeContent(request, e)).orElse("");
                log.error(StrUtil.format("{},错误编码:AE{}:{}\n{}",e.getMsg(),errorCode, content, Exceptions.getStackTraceAsString(e)));
                r.setUserMsg(StrUtil.format("{},错误编码:AE{}",e.getMsg(),errorCode));
                r.setMsg(getOutMsg(e));
                return resultWithStackTrace(r, e);
            });
        }

        return r;
    }

    @ExceptionHandler(BindException.class)
    public R exception(BindException e) {
        return invokeWithErrorCode(errorCode-> {
            String content = getRequest().map(request -> makeContent(request, e)).orElse("");
            log.error(StrUtil.format(PARAM_ERROR,errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.PARAM_ERROR, getOutMsg(e), Optional.ofNullable(e).map(BindException::getFieldError)
                    .map(FieldError::getDefaultMessage).map(msg->StrUtil.format("{},错误编码:AE{}",msg,errorCode))
                    .orElseGet(()->StrUtil.format("参数异常,错误编码:AE{}", errorCode))),e);
        });
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R exception(MissingServletRequestParameterException e) {
        return R.error(StrUtil.format("类型[{}]的参数[{}]不能为空",e.getParameterType(),e.getParameterName()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R exception(MethodArgumentNotValidException e) {
        Optional<String> msgOpt = Optional.ofNullable(e).map(MethodArgumentNotValidException::getBindingResult)
                .map(BindingResult::getFieldError).map(FieldError::getDefaultMessage);
        if(e.getCause() == null && msgOpt.isPresent()){
            return R.error(msgOpt.get());
        }
        return invokeWithErrorCode(errorCode-> {
            String content = getRequest().map(request -> makeContent(request, e)).orElse("");
            log.error(StrUtil.format("数据验证未通过,错误编码:AE{}: {}\n{}",errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.PARAM_ERROR, getOutMsg(e), msgOpt.map(msg->StrUtil.format("{},错误编码:AE{}",msg,errorCode))
                    .orElse(StrUtil.format("数据验证未通过,错误编码:AE{}", errorCode))),e);
        });
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R exception(HttpMessageNotReadableException e) {
        return invokeWithErrorCode(errorCode-> {
            String content = getRequest().map(request -> makeContent(request, e)).orElse("");
            log.error(StrUtil.format(PARAM_ERROR,errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.PARAM_ERROR, getOutMsg(e), StrUtil.format("参数错误,错误编码:AE{}", errorCode)),e);
        });
    }

    @ExceptionHandler(JedisConnectionException.class)
    public R exception(JedisConnectionException e) {
        return invokeWithErrorCode(errorCode-> {
            String content = getRequest().map(request -> makeContent(request, e)).orElse("");
            log.error(StrUtil.format("redis链接异常,错误编码:AE{}: {}\n{}",errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.SERVER_ERROR, getOutMsg(e), StrUtil.format("服务器繁忙,错误编码:AE{}", errorCode)),e);
        });
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public R exception(IllegalArgumentException e) {
        return invokeWithErrorCode(errorCode-> {
            String content = getRequest().map(request -> makeContent(request, e)).orElse("");
            log.error(StrUtil.format(PARAM_ERROR,errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.PARAM_ERROR, getOutMsg(e), StrUtil.format("参数异常,{},错误编码:AE{}",e.getMessage(), errorCode)),e);
        });
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public R exception(HttpMediaTypeNotSupportedException e) {
        String s = getRequest().map(request->request.getRequestURL().toString()).orElse("");
        return invokeWithErrorCode(errorCode->{
            StringBuilder logMessage = new StringBuilder(NumberConstant.THIRTY_ONE)
                    .append("错误编码:AE").append(errorCode).append(",入参异常,参数: ");
            for (Map.Entry<String, String> entry : e.getContentType().getParameters().entrySet()) {
                logMessage.append(entry.getKey()).append(':').append(entry.getValue()).append(',');
            }
            logMessage.append("url: ").append(s);
            log.error("{}\n{}",logMessage.toString(), Exceptions.getStackTraceAsString(e));
            return resultWithStackTrace(new R(ResultCode.SERVER_ERROR, getOutMsg(e), StrUtil.format(SYSTEM_ERROR_MSG,errorCode)),e);
        });

    }


    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R exception(MethodArgumentTypeMismatchException e) {
        Optional<HttpServletRequest> requestOpt = getRequest();
        String s = requestOpt.map(request->request.getRequestURL().toString()).orElse("");
        final String plateform = requestOpt.map(request->request.getHeader("Platform")).orElse("");
        return invokeWithErrorCode(errorCode->{
            log.error(StrUtil.format("错误编码:AE{},url:{},客户端:{},参数名: {},参数值: {}\n{}",errorCode, s, plateform, e.getName(), e.getValue(), Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.SERVER_ERROR, getOutMsg(e), StrUtil.format(SYSTEM_ERROR_MSG, errorCode)),e);
        });

    }

    @ExceptionHandler(RemoteException.class)
    public R exception(RemoteException e) {
        String content = getRequest().map(request->makeContent(request, e)).orElse("") ;
        return invokeWithErrorCode(errorCode->{
            log.error(StrUtil.format("错误编码:AE{},调用远程服务器异常: {}\n{}",errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.SERVER_ERROR, getOutMsg(e), StrUtil.format(SYSTEM_ERROR_MSG, errorCode)),e);
        });

    }

    @ExceptionHandler(IllegalStateException.class)
    public R exception(IllegalStateException e) {
        return invokeWithErrorCode(errorCode-> {
            String content = getRequest().map(request -> makeContent(request, e)).orElse("");
            log.error(StrUtil.format(PARAM_ERROR,errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.PARAM_ERROR, getOutMsg(e), StrUtil.format("请求参数不正确,错误编码:AE{}", errorCode)),e);
        });
    }

    @ExceptionHandler(Exception.class)
    public R exception(Exception e) {
        Throwable rootCause = ExceptionUtil.getRootCause(e);
        if (rootCause instanceof CustomizeException){
            //客户异常进行友好提示
           return handleRRException((CustomizeException) rootCause);
        }
        String content = getRequest().map(request->makeContent(request, e)).orElse("") ;
        return invokeWithErrorCode(errorCode->{
            log.error(StrUtil.format("错误编码:AE{},调用服务器异常: {}\n{}",errorCode, content, Exceptions.getStackTraceAsString(e)));
            return resultWithStackTrace(new R(ResultCode.SERVER_ERROR, getOutMsg(e), StrUtil.format(SYSTEM_ERROR_MSG, errorCode)),e);
        });

    }

    private static String getOutMsg(final Throwable e) {
        return ExceptionUtil.getRootCauseMessage(e);
    }

    /**
     * 根据请求来获取日志内容
     * @param request
     * @param e
     * @return
     */
    public static String makeContent(HttpServletRequest request, Throwable e) {
        String profile = SpringContextUtil.getActiveProfileList().stream().filter(s -> !(
                s.startsWith("prod") ||
                        s.startsWith("consul")
        )).findFirst().orElse("");
        String url = request.getScheme() == null ? request.getRequestURI() : request.getRequestURL().toString();
        Map<String, String[]> parameterMap = request.getParameterMap();
        final String platform = request.getHeader("Platform");
        final String authorization = request.getHeader("authorization");
        String host = null;
        try {
            host = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException unknownHostException) {
            if(log.isErrorEnabled()){
                log.error("Unknown host {}\n{}",unknownHostException.getMessage(), Exceptions.getStackTraceAsString(unknownHostException));
            }
        }
        if (request instanceof BodyReaderHttpServletRequestWrapper) {
            return MessageFormat.format("Profile: {0},\nError: {1},\nHost: {2}" +
                            ",\nPlatform: {3},\nParams: {4},\nUrl:{5},\nToken:{6},\nBody:{7}",
                    profile,
                    getOutMsg(e),
                    host,
                    platform,
                    JSON.toJSONString(parameterMap),
                    url,
                    authorization,
                    BodyReaderHttpServletRequestWrapper.getBodyString(request));
        } else {
            return MessageFormat.format("Profile: {0},\nError: {1},\nHost: {2}" +
                            ",\nPlatform: {3},\nParams: {4},\nUrl:{5},\nToken:{6}",
                    profile,
                    getOutMsg(e),
                    host,
                    platform,
                    JSON.toJSONString(parameterMap),
                    url,
                    authorization);

        }
    }

    public static <T> R<T> invokeWithErrorCode(IntFunction<R<T>> function){
        int errorCode = CommonUtils.getRandom4Code();
        return  function.apply(errorCode);
    }

    public static void logError(String workName, Object param, Throwable e, Consumer<String> showMsgCallBack){
        log(LogLevel.ERROR,workName,param,e,showMsgCallBack);
    }

    public static void log(LogLevel logLevel, String workName, Object param, Throwable e, Consumer<String> showMsgCallBack){
        int errorCode = CommonUtils.getRandom4Code();
        String msg = StrUtil.format("{}【{}{}】{}{},错误编码:AE{}",
                Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId()).map(OaUserBO::getAreaId)
                        .map(areaId -> SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(areaId)).filter(R::isSuccess).map(R::getData)
                        .map(AreaInfo::getPrintName).orElse(""), XtenantEnum.getTenantName(), SpringContextUtil.isProduce() ? "" : "测试",
                workName, Optional.ofNullable(e).filter(ee -> ee instanceof CustomizeException).map(ee -> (CustomizeException) ee)
                        .map(CustomizeException::getMsg).orElse("系统异常"), errorCode);
        Optional<HttpServletRequest> reqOpt = SpringContextUtil.getRequest();
        String logMsg;
        if(reqOpt.isPresent()){
            logMsg = StrUtil.format("{}\n{}\n方法参数: {}", reqOpt.map(req -> makeContent(req, e)).orElse(null), msg, JSON.toJSONString(param, SerializerFeature.PrettyFormat));
        }else{
            logMsg = StrUtil.format("{}\n方法参数: {}",msg,JSON.toJSONString(param, SerializerFeature.PrettyFormat));
        }
        switch (logLevel){
            case ERROR:
                log.error(logMsg,e);
                break;
            case INFO:
                log.info(logMsg,e);
                break;
            case WARN:
                log.warn(logMsg,e);
                break;
            case DEBUG:
            default:
                log.debug(logMsg,e);
                break;
        }
        Optional.ofNullable(msg).filter(m-> Objects.nonNull(showMsgCallBack)).ifPresent(m->showMsgCallBack.accept(m));
    }

    /**
     * 通过日志的简易消息获取日志查询连接地址
     * @param simpleLogMsg
     */
    public static String getLogUrl(String serviceName, String simpleLogMsg){
        String domainUrl = getLogHost();
        //构建参数
        //偏离时间差
        long offsetTime = Duration.ofHours(1).toMillis();
        Dict leftParam = Dict.create().set("queries", Arrays.asList(Dict.create().set("editorMode","builder").set("expr",
                StrUtil.format("{ServiceName=\"{}\"} |= `{}`",serviceName,simpleLogMsg)).set("queryType","range")))
                .set("range",Dict.create().set("from", Convert.toStr(System.currentTimeMillis()- offsetTime))
                        .set("to",Convert.toStr(System.currentTimeMillis()+offsetTime)));
        String leftEncode = StrUtil.replace(JSON.toJSONString(leftParam), "[\\{\\}\\[\\]=\\s\\\\|`]", matcher -> {
            switch (matcher.group()) {
                case "{":
                    return "%7B";
                case "}":
                    return "%7D";
                case "[":
                    return "%5B";
                case "]":
                    return "%5D";
                case "=":
                    return "%3D";
                case "\\":
                    return "%5C";
                case " ":
                    return "%20";
                case "|":
                    return "%7C";
                case "`":
                    return "%60";
                default:
                    return matcher.group();
            }
        });
        return StrUtil.format("{}/explore?left={}",domainUrl, leftEncode);
    }

    public static String getLogHost() {
        String domainUrl;
        Environment environment = SpringUtil.getApplicationContext().getEnvironment();
        String zone = SpringContextUtil.getContext().getEnvironment().getProperty("instance-zone","");
        switch (zone){
            case "9ji":
                domainUrl = SpringContextUtil.isProduce() ? "https://log.9ji.com" : "http://************:3000";
                break;
            case "zlf":
                //智乐方
                domainUrl = "https://zlflog.9xun.com";
                break;
            case "10002":
            case "10003":
            case "10004":
                //深圳易腾
                domainUrl = "https://itenglog.9xun.com";
                break;
            default:
                //sqlserver.ch999oanew.host
                String ch999oanewHost = environment.getProperty("sqlserver.ch999oanew.host");
                String bigArea = ReUtil.get("\\.([^.]+)\\.saas\\.ch999\\.cn", ch999oanewHost, 1);
                domainUrl = StrUtil.format("https://{}log.9xun.com", bigArea);
                break;
        }
        return domainUrl;
    }

    private static Optional<HttpServletRequest> getRequest() {
        return Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .map(ServletRequestAttributes::getRequest);
    }

    /**
     * 错误结果带上堆栈信息 只有cookie 带有showError且为true返回结果显示错误堆栈信息
     * @param r
     * @param e
     * @param <T>
     */
    private static <T> R<T> resultWithStackTrace(R<T> r,Throwable e){
        getRequest().ifPresent(req-> Optional.ofNullable(ServletUtil.getCookie(req,"showError")).map(Cookie::getValue)
                .map(Boolean::valueOf).filter(Boolean.TRUE::equals).map(showError->e)
                .ifPresent(ee->{
                    Map<Character, String> replaceCharToStrMap = new HashMap<>();
                    replaceCharToStrMap.put(StrUtil.C_CR, StrUtil.SPACE);
                    replaceCharToStrMap.put(StrUtil.C_TAB, StrUtil.SPACE);
                    String stat = ExceptionUtil.stacktraceToString(ee, -1, replaceCharToStrMap);
                    r.put("exceptionStackTrace", StrUtil.split(stat,StrUtil.C_LF));
                }));
        return r;
    }

}
