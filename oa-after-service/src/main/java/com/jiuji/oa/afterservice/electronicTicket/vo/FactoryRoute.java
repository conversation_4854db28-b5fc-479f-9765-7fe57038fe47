package com.jiuji.oa.afterservice.electronicTicket.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 工厂路由
 * <AUTHOR>
 */
@Data
public class FactoryRoute {

    /**
     * 是否大小件
     */
    @NotNull(message = "大小件类型不能为空")
    private Boolean isMobile;
    /**
     * 订单类型
     * 1--接件单
     * 2--回执单
     */
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    private Integer orderId;

    /**
     * 用户id
     *
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

}
