package com.jiuji.oa.afterservice.refund.service.way.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.common.enums.ShortXtenantEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.refund.bo.AccountCodePo;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.dao.way.BankPayTransferAccountsMapper;
import com.jiuji.oa.afterservice.refund.enums.TuiGroupEnum;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.AccountCodeService;
import com.jiuji.oa.afterservice.refund.service.BaseRefundService;
import com.jiuji.oa.afterservice.refund.service.impl.BaseRefundServiceImpl;
import com.jiuji.oa.afterservice.refund.service.way.BankPayTransferAccountsService;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundWayDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.BankTransferRefundVo;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 银行转账服务
 * <AUTHOR>
 * @since 2022/7/19 15:31
 */
@Service
@Slf4j
public  class BankPayTransferAccountsServiceImpl extends BaseRefundServiceImpl<BankTransferRefundVo> implements BankPayTransferAccountsService {
    @Resource
    @Lazy
    private BankPayTransferAccountsService bankPayTransferAccountsService;
    @Resource
    private BankPayTransferAccountsMapper transferAccountsMapper;

    @Override
    protected void setGroupDataByRefundWays(DetailParamBo detailParamBo, RefundWayDetailVo<BankTransferRefundVo> refundWayDetailVo, R<RefundMoneyDetailVo> result) {
        TuiGroupEnum myGroup = getMyGroup();
        refundWayDetailVo.setDataTemplate(new BankTransferRefundVo().setGroupCode(myGroup.getCode()).setReturnWayName(ShouhouRefundService.REFUND_WAY_BANK));
    }

    @Override
    protected List<BankTransferRefundVo> toGroupDataByDetail(DetailParamBo detailParamBo, List<ShouhouTuihuanDetailPo> myTuihuanDetailPos) {
        return myTuihuanDetailPos.stream()
                .map(
                        thd -> {
                            LambdaBuild<BankTransferRefundVo> refundBulid = LambdaBuild.create(new BankTransferRefundVo()).set(BankTransferRefundVo::setGroupCode, thd.getTuiGroup())
                                    .set(BankTransferRefundVo::setRefundPrice, thd.getRefundPrice()).set(BankTransferRefundVo::setId, thd.getId())
                                    .set(BankTransferRefundVo::setReturnWayName, thd.getTuiWay()).set(BankTransferRefundVo::setBankFuming,thd.getBankFuming())
                                    .set(BankTransferRefundVo::setBankNumber,thd.getBankNumber()).set(BankTransferRefundVo::setBankName,thd.getBankName())
                                    .set(BankTransferRefundVo::setRefundBusinessType,thd.getRefundBusinessType())
                                    .set(BankTransferRefundVo::setKemuTui,thd.getKemuTui())
                                    .set(BankTransferRefundVo::setPayOpenType,thd.getPayOpenType())
                                    ;
                            return refundBulid.build();
                        }
                )
                .collect(Collectors.toList());
    }

    @Override
    protected RefundWayDetailVo<BankTransferRefundVo> initRefundWayDetailVo(DetailParamBo detailParamBo) {
        boolean isEdit = detailParamBo.getTuiHuanPo() == null;
        String inputOrText = DecideUtil.iif(isEdit, RefundWayDetailVo.GroupColumnTypeEnum.INPUT.getCode(), RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode());
        String selectOrText = DecideUtil.iif(isEdit, RefundWayDetailVo.GroupColumnTypeEnum.SELECT.getCode(), RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode());
        LambdaBuild<LinkedList<RefundWayDetailVo.GroupColumns>> groupColumns = LambdaBuild.create(new LinkedList<RefundWayDetailVo.GroupColumns>())
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("returnWayName").setTitle(TuiGroupEnum.BANK_PAY_TRANSFER_ACCOUNTS.getMessage())
                        .setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("bankFuming").setTitle("收款人姓名").setType(inputOrText))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("bankNumber").setTitle("银行卡号").setType(inputOrText))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("bankName").setTitle("开户行").setType(inputOrText))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("refundPrice").setTitle("本次退款金额").setType(inputOrText));

        if(XtenantEnum.isSaasXtenant()
                //智乐方不要科目,他家自行做账
                && CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()) != ShortXtenantEnum.ZLF.getCode()
                && !isEdit && Boolean.TRUE.equals(detailParamBo.getTuiHuanPo().getCheck2())){
            //非编辑增加科目列
            groupColumns.set(List::add,new RefundWayDetailVo.GroupColumns().setDataIndex("kemuTui").setTitle("退款科目")
                    .setType(DecideUtil.iif(ObjectUtil.notEqual(detailParamBo.getTuiHuanPo().getCheck3(),Boolean.TRUE),
                            RefundWayDetailVo.GroupColumnTypeEnum.SELECT.getCode(), RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode())));
        }

        return new RefundWayDetailVo<BankTransferRefundVo>().setGroupColumns(groupColumns.build());
    }

    @Override
    public boolean isMyGroup(Integer tuiGroup) {
        return Objects.equals(tuiGroup,getMyGroup().getCode());
    }

    @Override
    public TuiGroupEnum getMyGroup(){
        return TuiGroupEnum.BANK_PAY_TRANSFER_ACCOUNTS;
    }

    @Override
    protected BaseRefundService<BankTransferRefundVo> getMyService() {
        return bankPayTransferAccountsService;
    }

    @Override
    protected void doAssertCheckSave(GroupTuihuanFormVo tuihuanForm, List<BankTransferRefundVo> tuiWayDetails) {
        //todo 九机 银行转账 可退金额为 现金+刷卡 收银总和
        if(XtenantEnum.isJiujiXtenant()){
            //如果名字小于等于4个字，并且金额>=50000的时候不允许保存，提示，“公转私不支持5万以上的金额，请使用公户或进行拆单处理”
            if(tuiWayDetails.stream()
                    .anyMatch(twd -> StrUtil.length(twd.getBankFuming()) < 4 && twd.getRefundPrice().compareTo(MAX_PERSON_REFUND_MONEY) > 0)){
                throw new CustomizeException("公转私不支持5万以上的金额，请使用公户或进行拆单处理");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSave(GroupTuihuanFormVo tuihuanForm, List<BankTransferRefundVo> myTuiWayDetails) {
        transferAccountsMapper.batchInsert(tuihuanForm, myTuiWayDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCheck(TuiHuanCheckVo tuiHuanCheckVo) {
        List<ShouhouTuihuanDetailPo> myTuihuanDetails = getMyTuihuanDetails(tuiHuanCheckVo.getTuihuanDetailPos());
        if (myTuihuanDetails.isEmpty()) {
            return;
        }
        List<BankTransferRefundVo> myClassDetailList = getMyClassDetailList(tuiHuanCheckVo.getRefundWayDetails());
        if (XtenantEnum.isJiujiXtenant()) {
            //todo 九机科目写死,输出科目必填
        } else if (CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()) == ShortXtenantEnum.ZLF.getCode()) {
            //智乐方不要科目,他家自行做账
            ;
        } else {
            //输出科目
            //设置并更新匹配的科目
            //正常情况下,银行转账只有一条
            myTuihuanDetails.stream()
                    .forEach(mtd -> myClassDetailList.stream().filter(mcd -> ObjectUtil.equal(mcd.getId(), mtd.getId()))
                            .findFirst().map(BankTransferRefundVo::getKemuTui).filter(StrUtil::isNotBlank)
                            .filter(kemu -> ObjectUtil.notEqual(kemu,mtd.getKemuTui()))
                            .ifPresent(kemu -> {
                                mtd.setKemuTui(kemu);
                                assertCheckKemu(kemu,tuiHuanCheckVo.getCurrUser().getAuthorizeId());
                                getTuihuanDetailService().lambdaUpdate().set(ShouhouTuihuanDetailPo::getKemuTui, kemu)
                                        .eq(ShouhouTuihuanDetailPo::getId, mtd.getId()).update();
                            }));
            //在退款办理的时候进行校验 oa999DAL\Returns.cs:630
            switch (tuiHuanCheckVo.getProcessStatusEnum()) {
                case CHECK3:
                    List<ShouhouTuihuanDetailPo> blankKemuDetail = myTuihuanDetails.stream()
                            .filter(mtd -> StrUtil.isBlank(mtd.getKemuTui())).collect(Collectors.toList());
                    if (!blankKemuDetail.isEmpty()) {
                        throw new CustomizeException(blankKemuDetail.stream().map(ShouhouTuihuanDetailPo::getTuiWay)
                                .collect(Collectors.joining(StringPool.SLASH)) + "退款科目不能为空");
                    }
                    break;
            }
        }

    }

    /**
     * 校验科目 不通过抛出客户异常
     * @param kemu
     * @param authorizeId
     */
    private void assertCheckKemu(String kemu, Integer authorizeId) {
        try {
            Assert.isFalse(StrUtil.isBlank(kemu),"科目不能为空");
            if (SpringUtil.getBean(SysConfigService.class).isAuxiliaryVerify()){
                Integer ztId = SpringUtil.getBean(AuthConfigService.class).getZtIdByAuId(authorizeId);
                if(ztId>0){
                    Optional<AccountCodePo> auxiliaryOpt = SpringUtil.getBean(AccountCodeService.class).lambdaQuery()
                            .select(AccountCodePo::getAuxiliary)
                            .eq(AccountCodePo::getCode, kemu).eq(AccountCodePo::getAccountSetId, ztId).list().stream()
                            .findFirst();
                    Assert.isTrue(auxiliaryOpt.isPresent(),StrUtil.format("科目{}不存在！",kemu));
                    Assert.isFalse(auxiliaryOpt.map(AccountCodePo::getAuxiliary).filter(auxi -> auxi>0).isPresent(),"只能填写辅助核算为无的科目！");
                }
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
    }


}
