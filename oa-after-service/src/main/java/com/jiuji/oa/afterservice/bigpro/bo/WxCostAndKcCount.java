package com.jiuji.oa.afterservice.bigpro.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:成本和价格和库存量
 * @author: <PERSON>
 * @date: 2020/4/16 11:04
 */
@Data
public class WxCostAndKcCount {
    private Integer leftCount;

    private BigDecimal inPrice;
    private Integer cid;
    private String productColor;
    private String productName;
    /**会员价格*/
    @ApiModelProperty("会员价格")
    private BigDecimal memberPrice;
}
