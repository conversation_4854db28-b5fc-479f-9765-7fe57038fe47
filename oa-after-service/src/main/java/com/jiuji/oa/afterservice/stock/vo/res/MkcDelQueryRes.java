package com.jiuji.oa.afterservice.stock.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel
public class MkcDelQueryRes {

    private Boolean isRsT1eof;

    private Integer id;

    private BigDecimal inPriceNow;

    private String comment;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dTime;

    private String inUser;

    private Boolean check1 = Boolean.FALSE;

    private Boolean check2 = Boolean.FALSE;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime check1Time;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime check2Time;

    private String check1User;

    private String check2User;

    private String productName;

    private String productName1;

    private String imei;

    private Integer kcCheck;

    private BigDecimal inBeiHuoPrice;

    private BigDecimal price1;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inBeiHuoDate;

    private Integer mkcId;

    private Integer kmcDealId;

    private Integer areaId;

    private String area;

    private String kinds;

    private Integer ppid;

    private Integer newPpid;

    private BigDecimal price2;

    private Boolean caiGouLock;

    private String frAreaId;

    @ApiModelProperty(value = "归属门店全名")
    private String areaText;

    private BigDecimal youHuiPrice;

    private String checkCommentUser;

    private Integer mkcDealId;

    private List<FileReq> files;

}
