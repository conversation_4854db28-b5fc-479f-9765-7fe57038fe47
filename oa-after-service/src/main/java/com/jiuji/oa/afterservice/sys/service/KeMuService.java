package com.jiuji.oa.afterservice.sys.service;

import com.alicp.jetcache.anno.Cached;
import com.jiuji.oa.afterservice.sys.bo.KemuFzhsItem;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-15
 */
public interface KeMuService {

    KemuFzhsItem getKemuFzhsInfoByEKemu(Integer code,Integer ztId,Integer nowAreaId);


    KemuModel getKemuItemByCode(Integer ztId,Integer code);


    @Cached(name = "com.jiuji.oa.afterservice.sys.service.getKemuConfig", expire = 10, timeUnit = TimeUnit.MINUTES)
    List<KemuModel> getKemuConfig();

    /**
     * 根据类别 小件分类（sjpj_income等）  显示 科目、辅助核算
     * @param type
     * @param ztId
     * @param areaId
     * @return
     */
    KemuFzhsItem getKemuFzhsInfoByType(String type, Integer ztId, Integer areaId);

}
