package com.jiuji.oa.afterservice.other.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.bo.YuyuePpidsInfo;
import com.jiuji.oa.afterservice.bigpro.dao.ProductinfoMapper;
import com.jiuji.oa.afterservice.bigpro.enums.DealWayEnum;
import com.jiuji.oa.afterservice.bigpro.po.ProductinfowithBrand;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.res.YixiugeOrderVo;
import com.jiuji.oa.afterservice.bigpro.vo.res.YixiugeShouhouPingjiaVo;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.YuYueDetailVO;
import com.jiuji.oa.afterservice.common.enums.ShouhouOrderTypeEnum;
import com.jiuji.oa.afterservice.common.enums.ShouhouPushTypeEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.evaluate.dao.EvaluateScoreDao;
import com.jiuji.oa.afterservice.other.enums.YixiugeOrderStatusEnum;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.service.YixiugeOrderService;
import com.jiuji.oa.afterservice.other.vo.res.YiXiuGeRes;
import com.jiuji.oa.afterservice.rabbitmq.bo.ThirdShouhouStatusSyncMqMessageBo;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.ConnectException;
import java.net.SocketException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 移修哥订单推送接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class YixiugeOrderServiceImpl implements YixiugeOrderService {

    @Resource
    private ShouhouService shouhouService;

    @Resource
    private ShouhouYuyueService shouhouYuyueService;

    @Resource
    private SmsService smsService;

    @Resource
    private AreainfoService areainfoService;

    @Resource
    private WebCloud webCloud;

    @Resource
    private ProductinfoMapper productinfoMapper;

    @Resource
    private EvaluateScoreDao evaluateScoreDao;

    /**
     * 推送移修哥
     * @param thirdShouhouStatusSyncMqMessageBo
     */
    @Override
    @Retryable(value = {IORuntimeException.class,SocketException.class, ConnectException.class},maxAttempts = 3,backoff = @Backoff(delay = 1000,multiplier = 1.5))
    public void pushDatatoYixiuge(ThirdShouhouStatusSyncMqMessageBo thirdShouhouStatusSyncMqMessageBo) {

        switch (EnumUtil.getEnumByCode(ShouhouPushTypeEnum.class, thirdShouhouStatusSyncMqMessageBo.getPushType())) {
            case INFO:
                pushShouhouDatatoYixiuge(thirdShouhouStatusSyncMqMessageBo);
                break;
            case STATUS:
                break;
            case PINGJIA:
                pushPingjiaDatatoYixiuge(thirdShouhouStatusSyncMqMessageBo);
                break;
            default:
                break;
        }
    }

    @Override
    public void pushShouhouDatatoYixiuge(ThirdShouhouStatusSyncMqMessageBo thirdShouhouStatusSyncMqMessageBo){
        String jsonData = "";
        Integer shouhouId = null;
        Integer yuyueId = null;
        ShouhouYuyue shouhouYuyue = null;
        YixiugeOrderVo yixiugeOrderVo = new YixiugeOrderVo();
        switch (Objects.requireNonNull(ShouhouOrderTypeEnum.valueOfByCode(thirdShouhouStatusSyncMqMessageBo.getType()))){
            case YUYUE:
                yuyueId = thirdShouhouStatusSyncMqMessageBo.getId();
                shouhouYuyue = shouhouYuyueService.getById(yuyueId);
                shouhouId = shouhouYuyue.getShouhouId();
                break;
            case SHOUHOU:
                shouhouId = thirdShouhouStatusSyncMqMessageBo.getId();
                yuyueId = shouhouService.getById(shouhouId).getYuyueid();
                shouhouYuyue = shouhouYuyueService.getById(yuyueId);
                break;
            default:
                break;
        }
        if (yuyueId == null || shouhouYuyue == null){
            log.error("{} 为空,{}", yuyueId, shouhouYuyue);
            return;
        }
        Integer userId = shouhouYuyue.getUserid();
        Result<YuYueDetailVO> yuYueDetailVOResult = webCloud.getYuYueDetailByUserId(userId, yuyueId, XtenantEnum.getXtenant());
        if (!yuYueDetailVOResult.isSuccess()){
            return;
        }
        YuYueDetailVO yuYueDetailVO = yuYueDetailVOResult.getData();
        if (yuYueDetailVO == null){
            return;
        }
        log.warn("移修哥主站查询预约单信息传入参数：{}，返回结果：{}",userId+"-"+yuyueId,JSON.toJSONString(yuYueDetailVOResult));
        yixiugeOrderVo.setOrderNo(yuyueId);
        //移修哥只能到店方式
        yixiugeOrderVo.setServiceType(100);
        //移修哥订单状态
        Optional.of(EnumUtil.getEnumByCode(YixiugeOrderStatusEnum.class, shouhouYuyue.getStats())).ifPresent(e -> {
            yixiugeOrderVo.setOrderStatus(e.getYixiugeOrderStatus());
        });
        //取消的状态同步(因为预约单通过isdel来标识)
        if(Boolean.TRUE.equals(shouhouYuyue.getIsdel())){
            yixiugeOrderVo.setOrderStatus(YixiugeOrderStatusEnum.YQX.getYixiugeOrderStatus());
        }
        //移修哥订单状态
        yixiugeOrderVo.setCustomerName(yuYueDetailVO.getUserInfo().getUserName());
        yixiugeOrderVo.setCustomerAddress(yuYueDetailVO.getUserInfo().getAddress());
        String userMobile = Optional.ofNullable(yuYueDetailVO.getUserInfo().getUserMobile()).orElse(shouhouYuyue.getMobile());
        yixiugeOrderVo.setCustomerMobile(userMobile);
        yixiugeOrderVo.setOrderingMobile(userMobile);
        yixiugeOrderVo.setContractMobile(userMobile);
        //预约时间（yyyy-MM-dd HH:mm:ss）
        if (StringUtils.isNotBlank(yuYueDetailVO.getServiceType().getGoStoreTime())){
            yixiugeOrderVo.setPredictTime(yuYueDetailVO.getServiceType().getGoStoreTime().substring(0,16) + ":00");
        }
        yixiugeOrderVo.setSerialNo(shouhouYuyue.getImei());
        //下单时间（yyyy-MM-dd HH:mm:ss）
        yixiugeOrderVo.setOrderingTime(shouhouYuyue.getDtime().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM_SS)));
        yixiugeOrderVo.setProductName(shouhouYuyue.getName());
        yixiugeOrderVo.setProductTypeName(shouhouYuyue.getColor());
        yixiugeOrderVo.setOrderType(100);
        yixiugeOrderVo.setOrderPrice(yuYueDetailVO.getWxInfo().getWxPrice());
        yixiugeOrderVo.setProductImgUrl(yuYueDetailVO.getWxInfo().getImage());
        yixiugeOrderVo.setStoreName(yuYueDetailVO.getStoreInfo().getSelectStore());
        yixiugeOrderVo.setStoreMobile(yuYueDetailVO.getStoreInfo().getStoreTel());
        yixiugeOrderVo.setStoreAddress(yuYueDetailVO.getStoreInfo().getStoreAddress());
        yixiugeOrderVo.setActualServiceContent(shouhouYuyue.getProblem());
        String yuyuePPids = shouhouYuyue.getYuyuePPids();
        List<YuyuePpidsInfo> yuyuePpidsInfoList = Collections.emptyList();
        if (StringUtils.isNotBlank(yuyuePPids)){
            yuyuePpidsInfoList = JSON.parseArray(yuyuePPids, YuyuePpidsInfo.class);
        }
        List<ProductinfowithBrand> productinfos = Collections.emptyList();
        if (CollUtil.isNotEmpty(yuyuePpidsInfoList)) {
            productinfos = productinfoMapper.getProductwithBrandListByPpids(yuyuePpidsInfoList.stream().map(YuyuePpidsInfo::getPpid).collect(Collectors.toList()));
        }
//        Map<Integer,ProductinfowithBrand> productinfowithBrandMap = productinfos.stream().collect(Collectors.toMap(ProductinfowithBrand::getPpriceid, Function.identity(), (key1, key2) -> key2));
        //商品品牌信息（华为、苹果等）
        yixiugeOrderVo.setGoodsBrandCode(productinfos.stream().map(ProductinfowithBrand::getBrandId).collect(Collectors.toList()).toString());
        yixiugeOrderVo.setGoodsBrandName(productinfos.stream().map(ProductinfowithBrand::getBrandName).collect(Collectors.toList()).toString());
        //商品品类信息(声音故障、品牌故障等)
        yixiugeOrderVo.setGoodsCategoryName(yuyuePpidsInfoList.stream().map(YuyuePpidsInfo::getRepParentTitle).filter(StrUtil::isNotBlank).collect(Collectors.toList()).toString());
        yixiugeOrderVo.setGoodsCategoryCode(yuyuePpidsInfoList.stream().map(YuyuePpidsInfo::getTroubleId).collect(Collectors.toList()).toString());
        //商品信息(苹果 iPhone XS Max 扬声器等)
        yixiugeOrderVo.setGoodsCode(yuyuePpidsInfoList.stream().map(YuyuePpidsInfo::getPpid).collect(Collectors.toList()).toString());
        yixiugeOrderVo.setGoodsName(productinfos.stream().map(ProductinfowithBrand::getProductName).collect(Collectors.toList()).toString());


        YixiugeOrderVo.ExtraParams extraParams = new YixiugeOrderVo.ExtraParams();
        extraParams.setShouhouId(shouhouId);
        extraParams.setDelivery(shouhouYuyue.getDelivery());
        extraParams.setEnterUser(shouhouYuyue.getEnterUser());
        extraParams.setFchecktime(shouhouYuyue.getFchecktime());
        extraParams.setYchecktime(shouhouYuyue.getYchecktime());
        extraParams.setCheckTime(shouhouYuyue.getCheckTime());
        extraParams.setCancelKind(shouhouYuyue.getCancelKind());
        extraParams.setCancelRemark(shouhouYuyue.getCancelRemark());
        extraParams.setName(shouhouYuyue.getName());
        extraParams.setPeizhi(shouhouYuyue.getColor());
        extraParams.setComment(shouhouYuyue.getComment());
        extraParams.setUsername(shouhouYuyue.getUsername());
        extraParams.setMobile(shouhouYuyue.getMobile());

        Optional.of(EnumUtil.getEnumByCode(DealWayEnum.class, shouhouYuyue.getKind())).ifPresent(e -> extraParams.setDealWay(e.getMessage()));
        if (shouhouId != null){
            Shouhou shouhou = shouhouService.getById(shouhouId);
            yixiugeOrderVo.setSerialNo(shouhou.getImei());
            extraParams.setTel(shouhou.getTel());
            extraParams.setStats(shouhou.getStats());
            extraParams.setBaoxiu(shouhou.getBaoxiu());
            extraParams.setInuser(shouhou.getInuser());
            extraParams.setImei(shouhou.getImei());
            extraParams.setIsdel(!shouhou.getXianshi());
            extraParams.setContentcsdate(shouhou.getContentcsdate());
            extraParams.setModidate(shouhou.getModidate());
            extraParams.setFeiyong(shouhou.getFeiyong());
            extraParams.setCostprice(shouhou.getCostprice());
            extraParams.setWeixiuren(shouhou.getWeixiuren());
            extraParams.setOfftime(shouhou.getOfftime());
            extraParams.setAreaid(shouhou.getAreaid());
            extraParams.setWaiguan(shouhou.getWaiguan());
            extraParams.setModidtime(shouhou.getModidtime());
            extraParams.setProductId(shouhou.getProductId());
            extraParams.setProductColor(shouhou.getProductColor());
            extraParams.setWeixiuzuid(shouhou.getWeixiuzuid());
            extraParams.setWeixiuzuidJl(shouhou.getWeixiuzuidJl());
            extraParams.setWeixiudtime(shouhou.getWeixiudtime());
            extraParams.setWeixiuStartdtime(shouhou.getWeixiuStartdtime());
            extraParams.setIsquji(shouhou.getIsquji());
            extraParams.setPingjia(shouhou.getPingjia());
            extraParams.setPingjia1(shouhou.getPingjia1());
            extraParams.setSubId(shouhou.getSubId());
            extraParams.setServiceType(shouhou.getServiceType());
            extraParams.setBasketId(shouhou.getBasketId());
            extraParams.setYuyueid(yuyueId);
            extraParams.setWeixiurentime(shouhou.getWeixiurentime());
            extraParams.setReweixiuren(shouhou.getReweixiuren());
            extraParams.setSxname(shouhou.getSxname());
            extraParams.setSxmobile(shouhou.getSxmobile());
            extraParams.setSxsex(shouhou.getSxsex());
            extraParams.setTestuser(shouhou.getTestuser());
            extraParams.setTesttime(shouhou.getTesttime());
            extraParams.setWxTestStats(shouhou.getWxTestStats());
            extraParams.setIsBakData(shouhou.getIsBakData());
            extraParams.setQujitongzhitime(shouhou.getQujitongzhitime());
            extraParams.setServersOutDtime(shouhou.getServersOutDtime());
            extraParams.setYouhuifeiyong(shouhou.getYouhuifeiyong());
            extraParams.setWxAreaid(shouhou.getWxAreaid());
            extraParams.setImeifid(shouhou.getImeifid());
            extraParams.setYifum(shouhou.getYifum());
        }

        yixiugeOrderVo.setExtraParams(JSON.toJSONString(extraParams));
        jsonData = JSON.toJSONString(yixiugeOrderVo);
        String method = "yun_fixer_createOrModifyOrder";
        pushRequestToYixiuge(jsonData, method, yixiugeOrderVo.getOrderNo());
    }

    @Override
    public void pushPingjiaDatatoYixiuge(ThirdShouhouStatusSyncMqMessageBo thirdShouhouStatusSyncMqMessageBo) {
        Integer shouhouId = null;
        Integer yuyueId = null;
        ShouhouYuyue shouhouYuyue = null;
        switch (Objects.requireNonNull(ShouhouOrderTypeEnum.valueOfByCode(thirdShouhouStatusSyncMqMessageBo.getType()))){
            case YUYUE:
                yuyueId = thirdShouhouStatusSyncMqMessageBo.getId();
                shouhouYuyue = shouhouYuyueService.getById(yuyueId);
                shouhouId = shouhouYuyue.getShouhouId();
                break;
            case SHOUHOU:
                shouhouId = thirdShouhouStatusSyncMqMessageBo.getId();
                yuyueId = shouhouService.getById(shouhouId).getYuyueid();
                break;
            default:
                break;
        }
        if (yuyueId == null || shouhouId == null){
            log.error("{} 为空,{}", yuyueId, shouhouId);
            return;
        }

        List<YixiugeShouhouPingjiaVo> yixiugeShouhouPingjiaVos = evaluateScoreDao.getPingjiaVoByOrderId(shouhouId);
        if (CollectionUtils.isNotEmpty(yixiugeShouhouPingjiaVos)){
            YixiugeShouhouPingjiaVo yixiugeShouhouPingjiaVo = yixiugeShouhouPingjiaVos.get(0);
            Optional.of(yixiugeShouhouPingjiaVo.getOrderRate()).ifPresent(n -> yixiugeShouhouPingjiaVo.setOrderRate(n.divide(BigDecimal.valueOf(2), NumberConstant.FOUR, RoundingMode.HALF_UP)));
            yixiugeShouhouPingjiaVo.setOrderNo(yuyueId);
            String jsonData = JSON.toJSONString(yixiugeShouhouPingjiaVos.get(0));
            String method = "yun_fixer_notifyOrderComment";
            pushRequestToYixiuge(jsonData, method, shouhouId);
        }
    }

    public void pushRequestToYixiuge(String jsonData, String method, Integer orderId){
        if (StrUtil.isNotBlank(jsonData)){
            log.warn("三方售后订单状态同步数据{}",jsonData);
            //body 时间参数
            LocalDateTime localDateTime = LocalDateTime.now();
            String time = localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssS"));

            //body sign参数
            Environment environment = SpringUtil.getApplicationContext().getEnvironment();
            String appId = environment.getProperty("yi.dong.yi_xiu_ge.app_id");
            String secret = environment.getProperty("yi.dong.yi_xiu_ge.secret");
            String signStr = "appId=" + appId + "&method=" + method + "&time=" + time + secret;
            String sign = getMd5Hex(signStr.getBytes(StandardCharsets.UTF_8));
            String content = encrypt(secret, jsonData);
            String url = environment.getProperty("yi.dong.yi_xiu_ge.url");

            Map<String, String> param = new HashMap<>();
            param.put("time", time);
            param.put("sign", sign);
            param.put("appId", appId);
            param.put("method", method);
            param.put("content", content);
            Map<String, String> hearder = new HashMap<>();
            hearder.put("Content-Type", "application/json");

            if(StrUtil.isBlank(url)){
                throw new CustomizeException("移修哥的回调地址不能为空");
            }
            String httpResult  = HttpRequest.post(url).body(JSON.toJSONString(param)).execute().body();
            log.warn("移动返回结果: httpResult: {}",httpResult);
            YiXiuGeRes yiXiuGeRes = JSON.parseObject(httpResult, YiXiuGeRes.class);
            if(ObjectUtil.defaultIfNull(yiXiuGeRes.getStatus(),0) > 0){
                RRExceptionHandler.logError(StrUtil.format("同步维修单状态到移修哥, 单号: {}", orderId), Dict.create().set("param",param).set("url",url).set("signStr",signStr)
                                .set("jsonData",jsonData).set("httpResult",httpResult), new CustomizeException(yiXiuGeRes.getMessage()),
                        SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }

        }
    }


    public static String encrypt(String secret, String value) {
        try {
            String ivStr = secret.substring(secret.length() - 16);
            String key = secret.substring(0, 16);
            IvParameterSpec iv = new IvParameterSpec(ivStr.getBytes("UTF-8"));
            SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            byte[] encrypted = cipher.doFinal(value.getBytes());
            return Base64.encodeBase64String(encrypted);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static String getMd5Hex(byte[] source) {
        String s = null;
        // 用来将字节转换成 16 进制表示的字符
        char[] hexDigits = {
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A',
                'B', 'C', 'D',
                'E', 'F'};
        try {
            java.security.MessageDigest md =
                    java.security.MessageDigest
                            .getInstance("MD5");
            md.update(source);
            byte[] tmp = md.digest();
            // 用字节表示就是 16 个字节
            char[] str = new char[16 * 2];
            // 所以表示成 16 进制需要 32 个字符
            int k = 0;
            for (int i = 0; i < 16; i++) {
                // 转换成 16 进制字符的转换
                byte byte0 = tmp[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                // >>> 为逻辑右移，将符号位一起右移
                str[k++] = hexDigits[byte0 & 0xf];
            }
            s = new String(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return s;
    }
}
