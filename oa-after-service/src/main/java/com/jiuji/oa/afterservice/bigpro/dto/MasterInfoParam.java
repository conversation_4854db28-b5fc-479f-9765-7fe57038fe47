package com.jiuji.oa.afterservice.bigpro.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 处理主表信息的参数类
 */
@Data
public class MasterInfoParam {
    private String currentUser;
    private LocalDateTime now;
    private Integer shouHouId;
    private boolean isNew;

    public MasterInfoParam(String currentUser, LocalDateTime now, Integer shouHouId, boolean isNew) {
        this.currentUser = currentUser;
        this.now = now;
        this.shouHouId = shouHouId;
        this.isNew = isNew;
    }

} 