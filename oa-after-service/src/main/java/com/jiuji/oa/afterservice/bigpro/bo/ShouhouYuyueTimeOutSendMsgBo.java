package com.jiuji.oa.afterservice.bigpro.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 售后预约超时预约单推送
 * <AUTHOR>
 * @since 2020-04-23
 */
@Data
public class ShouhouYuyueTimeOutSendMsgBo  {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 服务方式
     */
    private Integer stype;

    /**
     * 处理状态
     */
    private Integer stats;

    private String checkUser;

    private Integer areaid;

    private String enterUser;

    private LocalDateTime enterTime;

    private LocalDateTime fCheckTime;

    private Integer fDay;

    private Integer mins;

    private String curAdmin;

    private String ch999Name;

    private Integer ch999Id;

    private Integer code;

}
