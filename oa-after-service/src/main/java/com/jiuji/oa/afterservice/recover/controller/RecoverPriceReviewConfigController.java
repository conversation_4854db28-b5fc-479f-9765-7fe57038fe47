package com.jiuji.oa.afterservice.recover.controller;

import com.jiuji.oa.afterservice.recover.service.IRecoverPriceReviewConfigService;
import com.jiuji.oa.afterservice.recover.vo.req.PriceReviewConfigVO;
import com.jiuji.oa.afterservice.recover.vo.res.ReviewConfigWeightVO;
import com.jiuji.tc.common.vo.R;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-12-14
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/recover-price-config")
public class RecoverPriceReviewConfigController {

    @Resource
    private IRecoverPriceReviewConfigService recoverPriceReviewConfigService;

    @PostMapping("/saveConfig/v1")
    public R<String> saveConfig(@RequestBody PriceReviewConfigVO req) {
        recoverPriceReviewConfigService.saveConfig(req);
        return R.success("操作成功");
    }

    @GetMapping("/getConfigList/v1")
    public R<PriceReviewConfigVO> getConfigList(@RequestParam(value = "xtenant", required = false) Integer xtenant) {
        PriceReviewConfigVO res = recoverPriceReviewConfigService.getConfigList(xtenant);
        return R.success(res);
    }

    @GetMapping("/getReviewConfigWeight/v1")
    public R<ReviewConfigWeightVO> getReviewConfigWeight(@RequestParam(value = "xtenant", required = false) Integer xtenant,
                                                         @RequestParam(value = "basketId") Integer basketId,
                                                         @RequestParam(value = "changeprice", required = false) BigDecimal changeprice) {
        ReviewConfigWeightVO res = recoverPriceReviewConfigService.getReviewConfigWeight(xtenant,basketId,changeprice);
        return R.success(res);
    }
}

