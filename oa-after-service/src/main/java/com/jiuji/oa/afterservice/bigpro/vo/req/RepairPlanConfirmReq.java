package com.jiuji.oa.afterservice.bigpro.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 确认维修方案请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "确认维修方案请求DTO")
public class RepairPlanConfirmReq {

    @ApiModelProperty(value = "维修方案ID列表")
    private List<Integer> planIds;

    @ApiModelProperty(value = "确认人")
    @NotNull(message = "确认人不能为空")
    private String confirmUser;


    @NotNull(message = "用户不能为空")
    private Integer userId;



    @NotNull(message = "方案类型不能为空")
    private Integer repairPlanMasterTypeId;


    @NotNull(message = "维修单id不能为空")
    private Integer shouHouId;
} 