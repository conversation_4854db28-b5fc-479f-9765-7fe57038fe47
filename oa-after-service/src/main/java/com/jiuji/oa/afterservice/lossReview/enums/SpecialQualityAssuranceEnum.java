package com.jiuji.oa.afterservice.lossReview.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/2/24
 * @description 门店类型
 */
@Getter
@AllArgsConstructor
public enum SpecialQualityAssuranceEnum implements CodeMessageEnumInterface {

    /**
     * 门店类型
     */
    ONE(1, "返修"),
    TWO(2, "本次维修")
    ;

    /**
     * key
     */
    private final Integer code;
    /**
     * value
     */
    private final String message;

    public static String getMessageByCode(Integer code){
        for (SpecialQualityAssuranceEnum temp : SpecialQualityAssuranceEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp.getMessage();
            }
        }
        return "";
    }

    public static SpecialQualityAssuranceEnum getEnumByCode(Integer code){
        for (SpecialQualityAssuranceEnum temp : SpecialQualityAssuranceEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp;
            }
        }
        return null;
    }
}
