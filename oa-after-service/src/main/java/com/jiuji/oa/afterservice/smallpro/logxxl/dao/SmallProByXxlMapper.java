package com.jiuji.oa.afterservice.smallpro.logxxl.dao;

import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 10:55
 * @Description
 */
@Mapper
public interface SmallProByXxlMapper {
    /**
     * 查询小件转现重复问题
     * @return
     */
    Integer getSmallProByXxl();

    /**
     * 查询租户存在超库存绑定
     * @return
     */
    Integer getProductKc();
    
    /**
     * 查询售后退换数据
     * @return 售后退换数据列表
     */
    List<ShouhouTuiHuanPo> getShouhouTuiHuanList();
}
