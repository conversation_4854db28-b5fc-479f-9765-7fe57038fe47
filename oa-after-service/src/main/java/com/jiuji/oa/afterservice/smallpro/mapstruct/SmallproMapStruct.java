package com.jiuji.oa.afterservice.smallpro.mapstruct;

import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproBasketReq;
import org.mapstruct.*;

import java.util.List;

/**
 * 这个配置会忽略空值
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE,nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SmallproMapStruct {

    /**
     * 更新小件换货配置选项
     * @param source
     * @param target
     */
    @Mapping(target = "id", ignore = true)
    void updateSmallExchangeConfig(SmallExchangeConfigPo source, @MappingTarget SmallExchangeConfigPo target);

    List<SmallproBill> toSmallproBillList(List<SmallproBasketReq.SmallproBasket> smallproBaskets);

    SmallproBill toSmallproBillList(SmallproBasketReq.SmallproBasket smallproBaskets);

}
