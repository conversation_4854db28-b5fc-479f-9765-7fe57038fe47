package com.jiuji.oa.afterservice.bigpro.statistics.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 代用机批量转地区提交
 * @author: Li quan
 * @date: 2020/10/23 15:55
 */

@Data
@ApiModel
public class DaiYongToAreaReq implements Serializable {

    private static final long serialVersionUID = 7247200135404833299L;

    @ApiModelProperty(value = "代用机ID列表")
    @NotNull(groups = Default.class,message = "代用机ID不能为空")
    private List<Integer> ids;

    @ApiModelProperty(value = "转地区ID")
    @NotNull(groups = Default.class,message = "转地区ID不能为空")
    @Min(value = 1,message = "转地区ID不能为空")
    private Integer toAreaId;
}
