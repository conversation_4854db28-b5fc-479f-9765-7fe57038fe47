package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouTroubleMapper;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouTrouble;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouTroubleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Service
public class ShouhouTroubleServiceImpl extends ServiceImpl<ShouhouTroubleMapper, ShouhouTrouble> implements ShouhouTroubleService {

    @Override
    public List<ShouhouTrouble> listAll() {
        return super.list();
    }
}
