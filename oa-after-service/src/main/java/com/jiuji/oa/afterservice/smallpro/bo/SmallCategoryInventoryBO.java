package com.jiuji.oa.afterservice.smallpro.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Auther: qiweiqing
 * @Date: 2021/01/12/16:26
 * @Description:
 */
@Data
public class SmallCategoryInventoryBO implements Serializable {

    private Integer lCount;
    private Integer cid;
    private String categoryName;
    private BigDecimal cost;

    private Integer pid;
}
