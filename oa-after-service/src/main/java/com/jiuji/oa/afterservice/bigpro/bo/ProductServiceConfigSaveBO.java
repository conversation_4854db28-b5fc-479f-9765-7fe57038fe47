package com.jiuji.oa.afterservice.bigpro.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description 商品服务配置
 * @Author: Wang<PERSON>ai
 * @Date: 2021-8-19 15:45:17
 */
@Data
@Accessors(chain = true)
public class ProductServiceConfigSaveBO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 分类id
     */
    @NotNull(message = "请选择商品分类")
    private Integer categoryId;

    /**
     * 类型
     */
    @NotNull(message = "请选择服务类型")
    private Integer type;

    /**
     * 服务明细
     */
    private List<ProductServiceBO> serviceList;

    /**
     * 员工
     */
    @JsonIgnore
    private String staffName;

}
