package com.jiuji.oa.afterservice.bigpro.statistics.controller;

import com.jiuji.oa.afterservice.bigpro.enums.DaiYongJiStatsEnum;
import com.jiuji.oa.afterservice.bigpro.enums.DaiyongjiPayStatesEnum;
import com.jiuji.oa.afterservice.bigpro.service.DaiyongjiService;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.DaiYongJiListQueryKindEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.DaiYongJiListQuerySortEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.DaiYongJiListQueryReq;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.DaiYongJiPanDianReq;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.DaiYongJiListResVo;
import com.jiuji.oa.afterservice.common.util.ValidatorUtil;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 售后备用机管理相关
 * @author: Li quan
 * @date: 2020/10/23 10:01
 */

@RestController
@RequestMapping("/api/bigpro/shouhou/dyj")
@Api(tags = "售后: 备用机管理")
@RequiredArgsConstructor
public class ShouhouDaiYongJiController {

    private final DaiyongjiService daiyongjiService;

    @GetMapping("/getEnums")
    @ApiOperation(value = "获取售后枚举选项")
    public R<Map<String, List<EnumVO>>> listAllEnum() {

        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        List<EnumVO> daiYongJiStatusEnum = EnumUtil.toEnumVOList(DaiYongJiStatsEnum.class);
        List<EnumVO> daiyongjiPayStatusEnum  = EnumUtil.toEnumVOList(DaiyongjiPayStatesEnum.class);
        List<EnumVO> queryKindEnum  = EnumUtil.toEnumVOList(DaiYongJiListQueryKindEnum.class);
        List<EnumVO> sortEnum  = EnumUtil.toEnumVOList(DaiYongJiListQuerySortEnum.class);

        enumMap.put("statusEnum", daiYongJiStatusEnum);
        enumMap.put("payStatusEnum", daiyongjiPayStatusEnum);
        enumMap.put("queryKindEnum", queryKindEnum);
        enumMap.put("sortEnum", sortEnum);
        return R.success(enumMap);
    }

    @PostMapping("/getList")
    @ApiOperation(value = "代用机列表查询")
    public R<List<DaiYongJiListResVo>> getList(@RequestBody DaiYongJiListQueryReq req){
        return R.success(daiyongjiService.getList(req));
    }

    @PostMapping("/daiYongJiPanDian")
    @ApiOperation(value = "代用机盘点")
    public R<String> daiYongJiPanDian(@RequestBody DaiYongJiPanDianReq req){
        ValidatorUtil.validateEntity(req, Default.class);
        if (req.getOperateType().equals(1)){
            return daiyongjiService.pandianStart();
        }else{
            return daiyongjiService.daiYongJiPanDian(req.getDyjId());
        }
    }

}
