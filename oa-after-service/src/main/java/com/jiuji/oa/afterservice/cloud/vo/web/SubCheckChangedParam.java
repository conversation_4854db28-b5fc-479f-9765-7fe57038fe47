package com.jiuji.oa.afterservice.cloud.vo.web;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class SubCheckChangedParam {

    private Integer payType;
    private Integer  sub_id;
    private Integer sub_check ;
    private Integer userId;

    private String areaId;

    /**
     * 订单类型，详见 OA_CLIENT_PLAT
     */
    private Integer subType;
    /**
     * 订单类型名
     */
    private String subTypeName;

    private List<Integer> basketTypes;

    /**
     * 商品类型 用于买即赠券的时候使用
     * 与java-utils中的UserMedalCustomConditionEnum对应
     * web的买即赠券只处理新机的 所以 productType强制写死为新机即可
     */
    private Integer productType;

    /**
     * 0-默认 1-回收权益卡 2-维修配件
     */
    private Integer extendType;

    /**
     * 维修单使用
     */
    private Integer cityId;

    /**
     * 维修配件信息
     */
    private List<RepairInfo> repairInfos;

    @Data
    public static class RepairInfo{
        private Integer repairId;
        private Integer ppid;
    }
}
