package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.WxconfigMapper;
import com.jiuji.oa.afterservice.bigpro.po.Wxconfig;
import com.jiuji.oa.afterservice.bigpro.service.WxconfigService;
import com.jiuji.oa.afterservice.bigpro.service.WxconfigoptionService;
import com.jiuji.oa.afterservice.bigpro.vo.res.WxConfigRes;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("wxconfigService")
public class WxconfigServiceImpl extends ServiceImpl<WxconfigMapper, Wxconfig> implements WxconfigService {
    @Autowired
    @Lazy
    private WxconfigoptionService wxconfigoptionService;

    @Override
    public List<Wxconfig> listAll() {
        return super.list();
    }

    @Override
    public WxConfigRes getByKeys(String key) {
        Wxconfig result = this.listAll().parallelStream().filter(p -> p.getKeys().contains(key)).findFirst().orElse(null);
        if (result != null) {
            return wxconfigoptionService.getWxConfig(result.getId());
        }
        return null;
    }
}