package com.jiuji.oa.afterservice.smallpro.po.exchange;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 预设实体类,后面会走表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("small_exchange_config")
public class SmallExchangeConfigPo {
    /**
     * 自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 配置名称
     */
    private String title;
    /**
     * 服务类型
     *
     * @see ExchangeServiceTypeEnum
     */
    private Integer serviceType;
    /**
     * 是否补差价
     */
    private Boolean isDifferentPrice;
    /**
     * 使用商品服务金额补差价
     */
    private Boolean isServiceDifferentPrice;
    /**
     * 补差价方式(1 按差价金额补 2 按照换货商品金额补)
     * @see DifferentPriceTypeEnum
     */
    private Integer differentPriceType;
    /**
     * 补差价百分比
     */
    private BigDecimal differentPricePercent;
    /**
     * 可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
     */
    private Integer productConfigType;
    /**
     * 置换政策
     */
    private String policyContent;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 同机型
     */
    private Boolean isSameMachine;

    /**
     * 附加项 可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
     */
    private Integer additionProductConfigType;
    /**
     * 补差价方式(1 按差价金额补 2 按照换货商品金额补)
     */
    @Getter
    @AllArgsConstructor
    public enum DifferentPriceTypeEnum implements CodeMessageEnumInterface {
        DIFFERENT_PRICE(1,"按差价金额补"),
        EXCHANGE_PRICE(2,"按照换货商品金额补")
        ;

        /**
         * 编码
         */
        private Integer code;

        /**
         * 编码对应信息
         */
        private String message;
    }

    /**
     * 可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
     */
    @Getter
    @AllArgsConstructor
    public enum ProductConfigTypeEnum implements CodeMessageEnumInterface {
        BRAND(1,"品牌",5,"brandID"),
        CATEGORY(2,"分类",4,"cid"),
        BRAND_CATEGORY(3,"品牌+分类",3,null),
        SPU(4,"SPU",2,"productid"),
        SKU(5,"SKU",1,"ppriceid")
        ;

        /**
         * 编码
         */
        private Integer code;

        /**
         * 编码对应信息
         */
        private String message;

        /**
         * 排序
         */
        private Integer rank;
        /**
         * 查询配置的列字段名
         */
        private String columnName;
    }


    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class ConfigTypeAndValue{

        /**
         * 配置类型
         */
        @ApiModelProperty("配置类型")
        private ProductConfigTypeEnum configType;
        /**
         * 配置值
         */
        @ApiModelProperty("配置值")
        private Integer configValue;
    }

    @Getter
    @AllArgsConstructor
    public enum ExchangeServiceTypeEnum implements CodeMessageEnumInterface {
        /**
         * 小件商品接件九机服务 - 编码-编码信息
         */
        SMALL_PRO_KIND_NULL(SmallProServiceTypeEnum.SMALL_PRO_KIND_NULL),
        SMALL_PRO_KIND_UNEXPECTED_REPLACEMENT(SmallProServiceTypeEnum.SMALL_PRO_KIND_UNEXPECTED_REPLACEMENT),
        SMALL_PRO_KIND_EXTENDED_WARRANTY(SmallProServiceTypeEnum.SMALL_PRO_KIND_EXTENDED_WARRANTY),
        SMALL_PRO_KIND_ONLY_CHANGE(SmallProServiceTypeEnum.SMALL_PRO_KIND_ONLY_CHANGE),
        SMALL_PRO_KIND_YEAR_CARD(SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD),
        SMALL_PRO_KIND_WARRAN_TYRE_PLACEMENT(SmallProServiceTypeEnum.SMALL_PRO_KIND_WARRAN_TYRE_PLACEMENT),
        MOBILE_ACCESSORIES_EXCHANGE(6, "大件附件换货"),
        ;

        /**
         * 编码
         */
        private Integer code;
        /**
         * 编码对应信息
         */
        private String message;

        ExchangeServiceTypeEnum(SmallProServiceTypeEnum smallProServiceTypeEnum) {
            this.code = smallProServiceTypeEnum.getCode();
            this.message = smallProServiceTypeEnum.getMessage();
        }
    }
}
