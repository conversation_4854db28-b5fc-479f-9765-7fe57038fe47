package com.jiuji.oa.afterservice.bigpro.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: liuwenwu
 * @date: 2020/3/24
 */
@ApiModel
@Data
@Accessors(chain = true)
public class ShouhouReceiveFastReq  {
    @ApiModelProperty(value = "维修单生成")
    private ShouhouReceiveReq shouhouReceive;

    @ApiModelProperty(value = "维修单日志")
    private List<ShouhouLogReq> logs;

}
