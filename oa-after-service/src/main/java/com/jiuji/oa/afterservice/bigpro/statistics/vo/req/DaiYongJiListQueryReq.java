package com.jiuji.oa.afterservice.bigpro.statistics.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 代用机列表查询
 * @author: Li quan
 * @date: 2020/10/23 13:55
 */

@Data
@ApiModel
public class DaiYongJiListQueryReq implements Serializable {

    private static final long serialVersionUID = 7247200135404833299L;

    @ApiModelProperty(value = "查询类别")
    private Integer queryKind;

    @ApiModelProperty(value = "查询状态(代用机状态)")
    private Integer status;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    @ApiModelProperty(value = "当前地区")
    private Integer curAreaId;

    @ApiModelProperty(value = "选择区域")
    private List<Integer> areaIds;

    @ApiModelProperty(value = "门店类型")
    private Integer areaKind;

    @ApiModelProperty(value = "盘点状态")
    private Integer panStatus;

    @ApiModelProperty(value = "押金情况")
    private Integer yaJinkind;

    @ApiModelProperty(value = "地区-新")
    private String areaCodeNew;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;

    @ApiModelProperty(value = "一个月未维护现值")
    private Integer price30day;

    @ApiModelProperty(value = "排序字段")
    private Integer sortKind;

    @ApiModelProperty(value = "1为升序，2为逆序")
    @Max(value = 2,message = "排序参数有误")
    private Integer sortDes;

}
