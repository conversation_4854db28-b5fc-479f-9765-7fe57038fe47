package com.jiuji.oa.afterservice.bigpro.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.po.Ok3wQudao;
import com.jiuji.oa.afterservice.bigpro.vo.res.QudaoUserRes;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.tc.common.vo.R;

import java.util.List;


/**
 * ${comments}
 *
 * <AUTHOR> quan
 * @email ${email}
 * @date 2020-07-07 11:26:44
 */
public interface Ok3wQudaoService extends IService<Ok3wQudao> {

    /**
     * 渠道用户信息
     *
     * @param queryWords
     * @param kinds
     * @param insourceId
     * @param authorizeId
     * @return
     */
    R<List<QudaoUserRes>> autoQudaoUser(String queryWords, String kinds, String insourceId, Integer authorizeId);

    /**
     * 获取渠道用户列表
     *
     * @return
     */
    @Cached(name = RedisKeys.QUDAO_USER, expire = 1440)
    List<QudaoUserRes> getQudaoUser();

}

