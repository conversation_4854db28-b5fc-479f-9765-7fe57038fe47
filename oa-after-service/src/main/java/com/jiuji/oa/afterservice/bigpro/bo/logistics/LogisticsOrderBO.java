package com.jiuji.oa.afterservice.bigpro.bo.logistics;

import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/6/4 10:30
 */
@Data
@ApiModel("物流单")
public class LogisticsOrderBO {
    /**分类*/
    @ApiModelProperty("分类")
    private Integer wCateId;
    /**类别*/
    @ApiModelProperty("类别")
    private Integer wutype;
    /**寄件人*/
    @ApiModelProperty("寄件人")
    private String sname;
    /**寄件电话*/
    @ApiModelProperty("寄件电话")
    private String smobile;
    /**寄件详细地址*/
    @ApiModelProperty("寄件详细地址")
    private String saddress;
    /**寄件城市id*/
    @ApiModelProperty("寄件城市id")
    private Integer scityid;
    /**收件人*/
    @ApiModelProperty("收件人")
    private String rname;
    /**收件电话*/
    @ApiModelProperty("收件电话")
    private String rmobile;
    /**寄件门店*/
    @ApiModelProperty("寄件门店")
    private Integer sareaid;
    /**收件门店*/
    @ApiModelProperty("收件门店")
    private Integer rareaid;
    /**收件详细地址*/
    @ApiModelProperty("收件详细地址")
    private String raddress;
    /**收件城市id*/
    @ApiModelProperty("收件城市id")
    private Integer rcityid;
    /**单号*/
    @ApiModelProperty("单号")
    private Integer danhaobind;
    /**备注*/
    @ApiModelProperty("备注")
    private String comment;
    /**无*/
    @ApiModelProperty("无")
    private String shoujianren;
    /**无*/
    @ApiModelProperty("无")
    private String paijianren;

    /**
     * 构建物流单
     * @param oaUserBO
     * @param ch999UserVo
     * @return
     */
    public static LogisticsOrderBO buildLogisticsOrderBO(OaUserBO oaUserBO, Ch999UserVo ch999UserVo) {
        //生成物流单
        LogisticsOrderBO logisticsOrderBO = new LogisticsOrderBO();
        int wCateId = 8;
        logisticsOrderBO.setWCateId(wCateId);
        int wutype = 1;
        logisticsOrderBO.setWutype(wutype);
        logisticsOrderBO.setSname(oaUserBO.getUserName());
        logisticsOrderBO.setSmobile(ch999UserVo.getMobile());
        logisticsOrderBO.setSareaid(oaUserBO.getAreaId());
        logisticsOrderBO.setRname("严婷");
        logisticsOrderBO.setRmobile("15911730161");
        int rareaid = 13;
        logisticsOrderBO.setRareaid(rareaid);
        logisticsOrderBO.setComment("维修报账单据发回总部");
        return logisticsOrderBO;
    }
}
