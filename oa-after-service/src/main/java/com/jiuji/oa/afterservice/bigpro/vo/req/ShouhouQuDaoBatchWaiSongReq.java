package com.jiuji.oa.afterservice.bigpro.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 售后渠道批量外送
 */

@Data
@Accessors(chain = true)
public class ShouhouQuDaoBatchWaiSongReq {

    @NotNull(message = "渠道ID不能为空")
    @ApiModelProperty(value = "渠道ID")
    private Integer qdId;

    @NotNull(message = "渠道名称不能为空")
    @ApiModelProperty(value = "渠道名称")
    private String qdName;

    @NotNull(message = "请填写售后单号")
    @ApiModelProperty(value = "售后ID")
    private List<Integer> shouhouIdList;

}
