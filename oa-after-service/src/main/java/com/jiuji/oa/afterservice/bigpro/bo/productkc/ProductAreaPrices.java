package com.jiuji.oa.afterservice.bigpro.bo.productkc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> quan
 * 商品区域价格
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProductAreaPrices {

    /**
     * PPID
     */
    private Integer ppid;

    /**
     * 区域价格
     */
    private BigDecimal price;

}
