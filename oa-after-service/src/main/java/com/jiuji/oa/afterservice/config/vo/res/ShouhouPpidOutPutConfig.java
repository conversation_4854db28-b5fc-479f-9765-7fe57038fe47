package com.jiuji.oa.afterservice.config.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidBind;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class ShouhouPpidOutPutConfig {

    @ApiModelProperty(value = "配置主键")
    private Integer id;

    @ApiModelProperty("ppid")
    private Integer ppid;

    /**
     * 配件名称
     */
    @ApiModelProperty("配件名称")
    private String name;

    /**
     * 租户
     */
    @ApiModelProperty(value = "租户")
    private Integer xtenant;

    private String xtenantName;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用 1启用")
    private Integer status;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "是否负库存出库")
    private Boolean negative;

    private Boolean isDel;

    private List<ShouhouPpidBind> bindList;

}
