package com.jiuji.oa.afterservice.smallpro.recover.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class SmallProRecoverInfo {
    /**
     * 回收单ID
     */
    private Integer id;

    /**
     * 售后单id
     */
    private Integer shouhouId;

    /**
     * 旧件价格
     */
    private BigDecimal price;

    /**
     * 出售价格
     */
    private BigDecimal salePrice;

    /**
     * 换回价格
     */
    private BigDecimal huanHuiPrice;

    /**
     * 出售类型
     */
    private Integer saleType;

    /**
     * 当前所在地区
     */
    private Integer nowAreaId;

    /**
     * 销售渠道
     */
    private Integer companyId;

}
