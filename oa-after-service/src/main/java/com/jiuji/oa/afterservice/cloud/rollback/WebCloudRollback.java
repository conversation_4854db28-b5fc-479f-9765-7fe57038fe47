package com.jiuji.oa.afterservice.cloud.rollback;

import cn.hutool.json.JSONUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.*;
import com.jiuji.oa.afterservice.cloud.vo.web.*;
import com.jiuji.oa.afterservice.cloud.vo.web.req.FilmAccessoriesReq;
import com.jiuji.oa.afterservice.cloud.vo.web.res.WebFilmAccessoriesRes;
import com.jiuji.oa.afterservice.machine.vo.RecomentProductVO;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WebCloudRollback implements WebCloud {

    @Override
    public Result<List<RepairBuyCouponVO>> sendRepairBuyCoupon(@RequestBody SubCheckChangedParam param) {
        log.error("取机调用主站发优惠券异常 传入参数param:{}",JSONUtil.toJsonStr(param));
        return Result.error();
    }

    @Override
    public Result<RepairTuiFeeCheckVO> repairTuiFeeCheck(Integer userId, Integer orderId) {
        log.error("优惠码校验：传入参数userId：{}，orderId：{}",userId,orderId);
        return Result.error();
    }

    @Override
    public Result repairTuiFeeDel(Integer userId, Integer orderId,List<Integer> repairIds) {
        log.error("优惠券删除传入参数userId：{}，orderId：{}，repairIds：{}",userId,orderId,repairIds);
        return Result.error();
    }

    @Override
    public Result<MemberClubBackground> getOneByUserClass(Integer userClass) {
        log.error("WebCloud.getOneByUserClass 调用失败进入降级服务");
        return Result.error();
    }
    @Override
    public Result<List<HuishouYouHuiMa>> getProductCouponList(Integer ppid, Integer cateId) {
        log.error("调用web接口失败 /api/youhuima/getProductCouponList/v1 参数{},{}", ppid, cateId);
        return Result.error("调用web接口失败！");
    }


    @Override
    public Result<String> removeSignatureByDataId(Integer dataId, String imei) {
        log.error("WebCloud.removeSignatureByDataId 调用失败进入降级服务");
        return Result.error();
    }


    @Override
    public Result<List<Integer>> getProductRepirParts(Integer productId) {
        log.error("web.getProductRepirParts 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<Map<Integer, List<Integer>>> getMapPIdsByPpids(List<Integer> productIds) {
        log.error("web.getProductRepirParts 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<List<RecomentProductVO>> getRecomentParts(Integer ppid, Integer cityId, String cids,Long xtenant) {
        log.error("web.getRecomentParts 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<AfterServiceTimeCfg> getShouhouTimeConfig(Integer ppid, Long xtenant) {
        log.error("web/api/afterService/time/config/v1 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<List<AfterServiceTimeCfg>> listShouhouTimeConfig(List<Integer> ppids, Long xtenant) {
        log.error("/api/afterService/time/listConfig/v1 调用失败进入降级服务");
        return Result.error();
    }

    @Override
    public Result<List<ProductServiceOpeningVO>> openingGetServices(String ppids, Integer xtenant) {
        log.error("web.openingGetServices 调用失败进入降级服务,参数:{},{}",ppids,xtenant);
        return Result.error();
    }

    @Override
    public Result<JiuJiServiceVO> get9jiShieldServiceList(Integer productId, Integer xtenant) {
        log.error("web.get9jiShieldServiceList 调用失败进入降级服务,参数:{},{}",productId,xtenant);
        return Result.error();
    }

    @Override
    public Result<List<ProRelateInfoService>> productsGetServicesV1(String ppriceid, Integer xtenant) {
        log.error("web.productsGetServicesV1 调用失败进入降级服务,参数:{},{}",ppriceid,xtenant);
        return Result.error();
    }

    @Override
    public Result<Map<Integer, List<ProRelateInfoService>>> listProductServicesV1(List<Integer> ppriceids, Integer xtenant) {
        log.error("web.listProductServicesV1 调用失败进入降级服务,参数:{},{}", JSON.toJSONString(ppriceids),xtenant);
        return Result.error();
    }

    @Override
    public Result<List<SelectsVo.SelectVo>> getAfterServiceShopTime(Integer shopId, String date, Integer xtenant) {
        log.error("web.getAfterServiceShopTime 调用失败进入降级服务,参数:{},{},{}",shopId,date,xtenant);
        return Result.error();
    }

    @Override
    public Result<Map<Integer, List<ProRelateInfoService>>> listProductServicesV2(Integer productId, List<Integer> ppriceids, Integer xtenant) {
        log.error("web.listProductServicesV2 调用失败进入降级服务,参数:{},{},{}", productId,JSON.toJSONString(ppriceids),xtenant);
        return Result.error();
    }

    @Override
    public Result<List<WarrantyMainInfoVO>> getWarrantyMainInfoList(List<Integer> ppriceids, Integer xtenant) {
        log.error("web.WarrantyMainInfoVO 调用失败进入降级服务,参数:{},{} ", JSON.toJSONString(ppriceids),xtenant);
        return Result.error();
    }

    @Override
    public Result<YuYueDetailVO> getYuYueDetailByUserId(Integer userId, Integer id, Integer xtenant) {
        log.error("web.getYuYueDetailByUserId 调用失败进入降级服务,参数:{},{} ", userId, id);
        return Result.error();
    }

    @Override
    public Result<String> generateShortUrl(ShortUrlParam param, @RequestHeader("xtenant") Integer xtenant) {
        log.error("web.generateShortUrlV2 []调用失败进入降级服务,参数:{},{}",xtenant, JSON.toJSONString(param));
        return Result.error();
    }

    @Override
    public Result<YouHuiMaList3VO> getRepairMaList(Integer userId) {
        log.error("web.getRepairMaList []调用失败进入降级服务,参数:{}",userId);
        return Result.error();
    }

    /**
     * 提交故障订单前,查询需要检测和维修的项目.例如"屏幕故障"-true,"外壳故障"-false,"其他故障"-true
     *
     * @param productId
     * @param ppid
     * @return
     */
    @Override
    public Result<AccessoriesVO> getAccessories(Integer productId, Integer ppid) {
        log.error("web.getAccessories []调用失败进入降级服务,参数:{}-{}",productId, ppid);
        return Result.error();
    }

    /**
     * 查询售后服务
     *
     * @param productId 主商品id
     * @param ppids     配件ppid
     * @return
     */
    @Override
    public Result<Map<Integer, List<ProRelateInfoServiceVO>>> getServiceList(Integer productId, List<Integer> ppids) {
        log.error("web.getServiceList []调用失败进入降级服务,参数:{}-{}",productId, ppids);
        return Result.error();
    }

    /**
     * 获取文档详情
     *
     * @param id
     * @return
     */
    @Override
    public Result<DocumentVO> getDocumentDetail(Integer id) {
        log.error("web.DocumentDetail []调用失败进入降级服务,参数:{}",id);
        return Result.error();
    }

    /**
     * 根据电子签章类型获取对应的文档id
     *
     * @param typeId
     * @param xtenant
     * @return
     */
    @Override
    public Result<ElectronicTypeDocumentVO> getDocumentIdByTypeIdAndXtenant(Integer typeId, Integer xtenant) {
        log.error("web.getDocumentIdByTypeIdAndXtenant []调用失败进入降级服务,参数:{}-{}",typeId, xtenant);
        return Result.error();
    }

    @Override
    public R<List<WebFilmAccessoriesRes>> getFilmAccessories(FilmAccessoriesReq req) {
        log.error("/api/oa/searchExchangeConfProduct/v1 调用失败进入降级服务,参数:{}",req);
        return R.error(StrUtil.format("/api/oa/searchExchangeConfProduct/v1 调用失败进入降级服务,参数:{}", req));
    }
}
