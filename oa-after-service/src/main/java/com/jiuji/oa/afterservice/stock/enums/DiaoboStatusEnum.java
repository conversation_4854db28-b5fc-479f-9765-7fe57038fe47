package com.jiuji.oa.afterservice.stock.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 调拨单状态diaobo_sub.stats取值范围
 */
@Getter
@AllArgsConstructor
public enum DiaoboStatusEnum implements CodeMessageEnumInterface {
    DELETE(0, "删除"),
    COMMITTED(1, "已提交"),
    CHECKED(2, "已审核"),
    ON_THE_WAY(3, "已发货"),
    COMPLETED(4, "已完成 "),
    SEND_GOODS(5, "准备发货 "),
    STOCK_OUT(6, "出库中");
    /**
     * 状态
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String message;
}

