package com.jiuji.oa.afterservice.refund.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.ch999.common.util.utils.DateTimeUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.after.vo.refund.RecoverNoReasonReduceVo;
import com.jiuji.oa.afterservice.bigpro.bo.HuanMkcSecondBo;
import com.jiuji.oa.afterservice.bigpro.bo.RecoverMkc;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouSubDetailBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import com.jiuji.oa.afterservice.bigpro.enums.FaultTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.IshuishouEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.tuihuan.TuiHuanService;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouCostPriceRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.web.ShortUrlParam;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.oa.afterservice.csharp.returns.service.CsharpReturnService;
import com.jiuji.oa.afterservice.other.bo.GiftBasketBo;
import com.jiuji.oa.afterservice.other.bo.NewVoucherBo;
import com.jiuji.oa.afterservice.other.bo.TuihuanConfigReqBo;
import com.jiuji.oa.afterservice.other.dao.ShouhouTuihuanMapper;
import com.jiuji.oa.afterservice.other.enums.AreaKindEnum;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.po.TuihuanConfig;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.other.service.TuihuanConfigService;
import com.jiuji.oa.afterservice.other.service.VoucherService;
import com.jiuji.oa.afterservice.refund.bo.*;
import com.jiuji.oa.afterservice.refund.dao.RefundMachineMapper;
import com.jiuji.oa.afterservice.refund.dao.RefundValidMemberMapper;
import com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper;
import com.jiuji.oa.afterservice.refund.enums.*;
import com.jiuji.oa.afterservice.refund.po.OperatorBasket;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanGiftRelation;
import com.jiuji.oa.afterservice.refund.service.*;
import com.jiuji.oa.afterservice.refund.service.kind.BaseTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.kind.RefundMachineKindService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.OperatorByBasketIdReq;
import com.jiuji.oa.afterservice.refund.vo.req.RefundValidVo;
import com.jiuji.oa.afterservice.refund.vo.req.SelectTuiGiftBasketListVo;
import com.jiuji.oa.afterservice.refund.vo.req.machine.HuanInfoReq;
import com.jiuji.oa.afterservice.refund.vo.req.machine.RefundMachineFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.machine.ZheJiaMachineFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.*;
import com.jiuji.oa.afterservice.refund.vo.res.machine.*;
import com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.service.NetpayRecordService;
import com.jiuji.oa.afterservice.stock.dao.ProductMkcMapper;
import com.jiuji.oa.afterservice.stock.po.MkcDellogs;
import com.jiuji.oa.afterservice.stock.po.ProductMkc;
import com.jiuji.oa.afterservice.stock.service.IRecoverMkcService;
import com.jiuji.oa.afterservice.stock.service.MkcDellogsService;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.sub.po.Category;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.BaseValidMemberService;
import com.jiuji.oa.afterservice.sys.service.RetryService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.sys.vo.req.ValidReq;
import com.jiuji.oa.loginfo.common.pojo.AddLogReq;
import com.jiuji.oa.loginfo.mkc.client.MkcLogClient;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/11/14 14:19
 */
@Service
@Slf4j
public class RefundMachineServiceImpl implements RefundMachineService {
    @Resource
    private ShouhouTuihuanService shouhouTuihuanService;
    @Resource
    private RefundMachineMapper refundMachineMapper;
    @Resource
    @Lazy
    private RefundMachineService refundMachineService;
    @Resource
    private ShouhouService shouhouService;

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Resource
    private BasketService basketService;

    @Resource
    private ShouhouExService shouhouExService;

    @Resource
    private MkcDellogsService mkcDellogsService;

    @Resource
    private AuthConfigService authConfigService;

    @Resource
    private VoucherService voucherService;

    @Resource
    private ProductMkcMapper productMkcMapper;

    @Resource
    private NetpayRecordService netpayRecordService;

    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private IRecoverMarketsubinfoService recoverMarketsubinfoService;

    @Resource
    private SmsService smsService;
    @Resource
    private ShouhouTuihuanGiftRelationService giftRelationService;

    @Resource
    private ZheJiaPayService zheJiaPayService;
    @Resource
    private OperatorBasketService operatorBasketService;

    @Override
    public OperatorByBasketIdRes getOperatorByBasketId(OperatorByBasketIdReq req) {
        OperatorByBasketIdRes operatorByBasketIdRes = new OperatorByBasketIdRes();
        List<OperatorBasket> operatorBasketList = operatorBasketService.lambdaQuery()
                .eq(OperatorBasket::getBindBasketId, req.getBasketId())
                .eq(OperatorBasket::getStatus, OperatorBasketStatusEnum.HANDLED.getCode())
                .select(OperatorBasket::getId, OperatorBasket::getOffsetMoney, OperatorBasket::getStatus,OperatorBasket::getBasketId,OperatorBasket::getIsChecked)
                .list();
        //判断是否存在未返销的 运营商业务
        if(CollUtil.isNotEmpty(operatorBasketList)){
            List<Integer> basketIdList = operatorBasketList.stream().map(OperatorBasket::getBasketId).collect(Collectors.toList());
            Map<Integer, Basket> basketMap = CommenUtil.autoQueryHist(() -> basketService.lambdaQuery().in(Basket::getBasketId, basketIdList)
                    .and(bo -> bo.eq(Basket::getIsdel, NumberConstant.ZERO).or().isNull(Basket::getIsdel))
                    .list().stream()
                    .collect(Collectors.toMap(Basket::getBasketId, Function.identity(), (n1, n2) -> n2)));
            List<Long> ppidList = new ArrayList<>();
            String ppids = operatorBasketList.stream().map(item -> basketMap.getOrDefault(item.getBasketId(), new Basket()).getPpriceid())
                    .filter(Objects::nonNull)
                    .peek(ppidList::add)
                    .map(Convert::toStr).collect(Collectors.joining(","));
            String text = String.format("订单里ppid %s运营商业务没有进行返销，请先进行返销运营商业务再进行提交退款申请，并确认已在运营商系统中完成相关操作", ppids);
            operatorByBasketIdRes.setOffsetMoney(operatorBasketList.stream().filter(item->Boolean.TRUE.equals(item.getIsChecked())).map(OperatorBasket::getOffsetMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
            operatorByBasketIdRes.setText(text);
            operatorByBasketIdRes.setPpidList(ppidList);
        }
        return operatorByBasketIdRes;
    }

@Override
public R<RefundMachineDetailVo> detail(Integer shouhouId, Integer basketId) {
        // 计算器模拟售后数据
        mockShouhouInfo(shouhouId, basketId);
        //查询售后单信息
        Shouhou shouhou = refundMachineService.getShouhouById(shouhouId);
        //都查询不到,单号错误
        if(shouhou == null){
            return R.error(StrUtil.format("售后单号{}错误",shouhouId));
        }
        //查询退换申请信息shouhou_tuihua
        ShouhouTuiHuanPo tuihuan = refundMachineService.getShouhouTuihuanByShouhouId(shouhouId);
        RefundMachineDetailVo refundDetail = new RefundMachineDetailVo().setShouhou(shouhou).setTuihuan(tuihuan).setNotes(new LinkedList<>());
        AreaInfo areaInfo = refundMachineService.getShouhouAreaInfo(shouhou);
        refundDetail.setShouhouAreaInfo(areaInfo);
        //详情页面信息设置
        convertAndSetDetailInfo(refundDetail);

        if (tuihuan == null || StrUtil.isNotBlank(refundDetail.getFaultType())){
            //查询赠品信息 oa999DAL\ShouhouTuihuan.cs::GetGiftBasketList
            List<TuiGiftBasket> giftBasketList = getGiftBasketList(refundDetail.getBasketId());
            refundDetail.setGiftBasketList(giftBasketList);
        }
        //判断如果是九机并且是良品
        if(XtenantEnum.isJiujiXtenant() && Optional.ofNullable(shouhou.getIshuishou()).orElse(NumberConstant.ZERO).equals(NumberConstant.ONE)){
            jiujiLpDetail(shouhou, tuihuan, refundDetail);
        }
        //设置微信红包信息
        setWeiXinHongBao(refundDetail);
        return R.success(refundDetail).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    /**
     * 模拟构建售后单数据
     * @param shouhouId
     * @param basketId
     */
    private void mockShouhouInfo(Integer shouhouId, Integer basketId) {
        if(ObjectUtil.defaultIfNull(shouhouId, 0) != -1 || ObjectUtil.defaultIfNull(basketId, 0) < 0){
            return;
        }
        Integer ishuishou = SpringContextUtil.getRequest().map(req -> req.getHeader("ishuishou"))
                .map(Convert::toInt).orElse(0);
        Shouhou shouhou;
        if(ishuishou == 0){
            shouhou = mockNewSubShouhouInfo(shouhouId, basketId);
        }else{
            // 良品
            shouhou = mockRecoverSubShouhouInfo(shouhouId, basketId);
        }


        // 设置shouhou属性
        SpringContextUtil.reqCache(() -> shouhou, RequestCacheKeys.REFUND_MACHINE_SERVICE_GET_SHOUHOU_BY_ID, shouhouId);


    }

    private Shouhou mockRecoverSubShouhouInfo(Integer shouhouId, Integer basketId) {
        Shouhou shouhou;
        // 查询订单信息通过basketId
        RecoverMarketsubinfo basket = recoverMarketsubinfoService.getById(basketId);

        // 如果查询到的basket为空，则返回错误信息
        if (basket == null) {
            throw new CustomizeException(StrUtil.format("未找到basketId为{}的商品信息", basketId));
        }
        RecoverMarketinfo sub = SpringUtil.getBean(RecoverMarketinfoService.class).getById(Convert.toInt(basket.getSubId()));
        if(sub == null){
            throw new CustomizeException(StrUtil.format("未找到subId为{}的订单信息", basket.getSubId()));
        }
        // 获取库存信息
        RecoverMkc productMkc = SpringUtil.getBean(IRecoverMkcService.class).lambdaQuery().eq(RecoverMkc::getToBasketId, basketId)
                .list().stream().findFirst()
                .orElseThrow(() -> new CustomizeException(StrUtil.format("basketId{}库存信息为空", basketId)));
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        shouhou = new Shouhou();
        shouhou.setId(shouhouId);
        shouhou.setBasketId(basket.getBasketId()); // 设置basketId
        shouhou.setPpriceid(Convert.toInt(basket.getPpriceid()));
        shouhou.setSubId(Convert.toInt(sub.getSubId())); // 设置subId
        shouhou.setUserid(sub.getUserid()); // 假设basket中包含userId信息
        shouhou.setImei(productMkc.getImei());
        shouhou.setAreaid(oaUser.getAreaId());
        shouhou.setTradedate(sub.getTradedate1()); // 假设sub中包含交易日期

        shouhou.setIshuishou(1); // 假设sub中包含是否售后的信息
        shouhou.setModidate(LocalDateTime.now()); // 设置当前时间为接件时间
        return shouhou;
    }

    private @NonNull Shouhou mockNewSubShouhouInfo(Integer shouhouId, Integer basketId) {
        Shouhou shouhou;
        // 查询订单信息通过basketId
        Basket basket = basketService.getByIdSqlServer(basketId);

        // 如果查询到的basket为空，则返回错误信息
        if (basket == null) {
            throw new CustomizeException(StrUtil.format("未找到basketId为{}的商品信息", basketId));
        }
        Sub sub = SpringUtil.getBean(SubService.class).getByIdSqlServer(Convert.toInt(basket.getSubId()));
        if(sub == null){
            throw new CustomizeException(StrUtil.format("未找到subId为{}的订单信息", basket.getSubId()));
        }
        // 获取库存信息
        ProductMkc productMkc = SpringUtil.getBean(ProductMkcService.class).lambdaQuery().eq(ProductMkc::getBasketId, basketId)
                .list().stream().findFirst()
                .orElseThrow(() -> new CustomizeException(StrUtil.format("basketId{}库存信息为空", basketId)));
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        shouhou = new Shouhou();
        shouhou.setId(shouhouId);
        shouhou.setBasketId(basket.getBasketId()); // 设置basketId
        shouhou.setPpriceid(Convert.toInt(basket.getPpriceid()));
        shouhou.setSubId(sub.getSubId()); // 设置subId
        shouhou.setUserid(sub.getUserId()); // 假设basket中包含userId信息
        shouhou.setImei(productMkc.getImei());
        shouhou.setAreaid(oaUser.getAreaId());
        shouhou.setTradedate(sub.getTradeDate1()); // 假设sub中包含交易日期

        shouhou.setIshuishou(0); // 假设sub中包含是否售后的信息
        shouhou.setModidate(LocalDateTime.now()); // 设置当前时间为修改时间
        return shouhou;
    }

    /**
     * 九机良品明细
     * @param shouhou
     * @param tuihuan
     * @param refundDetail
     */
    private void jiujiLpDetail(Shouhou shouhou, ShouhouTuiHuanPo tuihuan, RefundMachineDetailVo refundDetail) {
        //获取良品的tips
        int year = Optional.ofNullable(tuihuan).map(ShouhouTuiHuanPo::getDtime).map(LocalDateTime::getYear).orElseGet(()-> LocalDateTime.now().getYear());
        List<Integer> discountSubIds = shouhouTuihuanService.getDiscountSubIds(refundDetail.getUserId(), year);
       String noReasonCnName = Convert.numberToChinese(RefundMachineService.LIANG_PIN_NO_REASON_ALL_COUNT, false);

        if (discountSubIds.size() > 0) {
            refundDetail.getNotes().add(StrUtil.format("该用户当年已享受良品无折价退款{}次（单号：{}），每位用户一年仅享受{}次无折价退款请注意按照规范给用户进行折价。",
                    discountSubIds.size(), discountSubIds.stream().sorted(Comparator.reverseOrder()).map(Convert::toStr)
                            .limit(10).collect(Collectors.joining(StringPool.COMMA)), noReasonCnName));
        } else {
            refundDetail.getNotes().add(StrUtil.format("该用户当年已享受良品无折价退款0次，每位用户一年仅享受{}次无折价退款请注意按照规范给用户进行折价。", noReasonCnName));
        }
        refundDetail.setGoodProductAccessoriesList(getLpAccessories(shouhou, tuihuan));
    }

    /**
     * 获取到良品附件
     * @param shouhou
     * @param tuihuan
     * @return
     */
    @Override
    public List<TuiGiftBasketListVo> getLpAccessories(Shouhou shouhou, ShouhouTuiHuanPo tuihuan){
        SelectTuiGiftBasketListVo req = new SelectTuiGiftBasketListVo();
        req.setSubId(shouhou.getSubId());
        if(Optional.ofNullable(tuihuan).isPresent()){
            req.setTuihuanId(tuihuan.getId());
        } else {
            Integer subId = shouhou.getSubId();
            List<RecoverMarketsubinfo> list = CommenUtil.autoQueryHist(()-> recoverMarketsubinfoService.lambdaQuery()
                    .eq(RecoverMarketsubinfo::getSubId, subId)
                    .eq(ObjectUtil.defaultIfNull(shouhou.getBasketId(), 0) > 0, RecoverMarketsubinfo::getBasketId, shouhou.getBasketId())
                    .list(), MTableInfoEnum.RECOVER_MARKET_INFO, subId);
            List<Integer> collect = list.stream()
                    .map(RecoverMarketsubinfo::getBasketId)
                    .collect(Collectors.toList());
            req.setLpBasketIdList(collect);

        }
        return giftRelationService.getLpGiftBasketList(req);
    }

    /**
     * 设置关联列表数据
     * @param tuihuanConfigs
     * @param refundDetail
     */
    private void setRelateList(List<TuihuanConfig> tuihuanConfigs, RefundMachineDetailVo refundDetail) {
        if(CollUtil.isNotEmpty(tuihuanConfigs)){
            ListBean faultTypeList = new ListBean(RefundMachineDetailVo.FAULT_TYPE_KEY, "故障类型",
                    ListBean.ListType.SELECT, false,new LinkedList<>());
            //级联关系模板
            ListBean[] listBeanTemps = new ListBean[]{new ListBean(RefundMachineDetailVo.CHECK_TYPE_KEY, "审核类型",
                    ListBean.ListType.SELECT, false, new LinkedList<>()), new ListBean(RefundMachineDetailVo.TUIHUAN_KIND_KEY, "退款类型",
                    ListBean.ListType.SELECT, false, new LinkedList<>())};
            Shouhou shouhou = refundDetail.getShouhou();
            // 获取真实退款类型
            TuihuanKindEnum realThEnum = getRealKindEnum(TuihuanKindEnum.TK, shouhou.getIshuishou());
            ZheJiaPayEnum zheJiaPayEnum = zheJiaPayService.getZheJiaPayEnum(shouhou.getSubId(), realThEnum);
            for (TuihuanConfig tuihuanConfig : tuihuanConfigs) {
                buildRelateList(0,faultTypeList,listBeanTemps,new List[]{
                        //故障类型
                        StrUtil.splitTrim(tuihuanConfig.getFaultOption(), StringPool.COMMA).stream().distinct()
                                .map(fo -> new ListBean.OptionsBean(fo,fo)).collect(Collectors.toList()),
                        //审核类型
                        StrUtil.splitTrim(tuihuanConfig.getCheckOption(),StringPool.COMMA).stream().distinct()
                                .filter(ck -> refundDetail.getAfterServicesDisCount()<NumberConstant.THREE || Objects.equals("特殊退换",ck))
                                .map(ck -> new ListBean.OptionsBean(ck,ck)).collect(Collectors.toList()),
                        //退款类型
                        StrUtil.splitTrim(tuihuanConfig.getDealOption(),StringPool.COMMA).stream().distinct().map(Convert::toInt)
                                // 国补特殊过滤
                                .filter(d -> !(zheJiaPayEnum != null && ZheJiaPayEnum.GUO_JIA_BUTIE.equals(zheJiaPayEnum))
                                        || Stream.of(TuihuanKindEnum.HQTXH, TuihuanKindEnum.HJT).noneMatch(tke -> Objects.equals(tke.getCode(), d)))
                                .map(d -> new ListBean.OptionsBean(d,EnumUtil.getMessageByCode(TuihuanKindEnum.class,d)))
                                .collect(Collectors.toList())
                });
            }
            refundDetail.setFaultTypeList(faultTypeList);
        }
        ShouhouTuiHuanPo tuihuan = refundDetail.getTuihuan();
        if (tuihuan != null){
            ListBean tklx = new ListBean(RefundMachineDetailVo.TUIHUAN_KIND_KEY, "退款类型",
                    ListBean.ListType.SELECT, false,
                    Collections.singletonList(
                            new ListBean.OptionsBean(tuihuan.getTuihuanKind(), EnumUtil.getMessageByCode(TuihuanKindEnum.class, tuihuan.getTuihuanKind()))
                    )).setIsShow(Objects.nonNull(tuihuan.getTuihuanKind())).setDisabled(Boolean.TRUE);
            ListBean shlx = new ListBean(RefundMachineDetailVo.CHECK_TYPE_KEY, "审核类型",
                    ListBean.ListType.SELECT, false,
                    Collections.singletonList(new ListBean.OptionsBean(tuihuan.getCheckType(), tuihuan.getCheckType())
                            .setRelateList(tklx)))
                    .setIsShow(StrUtil.isNotBlank(tuihuan.getCheckType())).setDisabled(Boolean.TRUE);
            ListBean faultTypeList = new ListBean(RefundMachineDetailVo.FAULT_TYPE_KEY, "故障类型",
                    ListBean.ListType.SELECT, false,
                    Collections.singletonList(
                            new ListBean.OptionsBean(tuihuan.getFaultType(), tuihuan.getFaultType())
                                    .setRelateList(shlx)))
                    .setIsShow(StrUtil.isNotBlank(tuihuan.getFaultType())).setDisabled(Boolean.TRUE);
            refundDetail.setFaultTypeList(faultTypeList);
        }
    }

    private void buildRelateList(int level,ListBean listBean,ListBean[] listBeanTempList,List<ListBean.OptionsBean>[] relateList){
        if (level >= relateList.length || level<0){
            //结束级联关系
            return;
        }
        relateList[level].stream()
                .filter(fo -> {
                    Optional<ListBean.OptionsBean> obOpt = listBean.getOptions().stream()
                            .filter(fto -> Objects.equals(fto.getValue(), fo.getValue())).findFirst();

                    if(level < listBeanTempList.length){
                        ListBean.OptionsBean ob = obOpt.orElse(fo);
                        if(ob.getRelateList() == null){
                            ob.setRelateList(listBeanTempList[level].copy().setOptions(new LinkedList<>()));
                        }
                        buildRelateList(level + 1, ob.getRelateList(), listBeanTempList, relateList);
                    }
                    return !obOpt.isPresent();
                })
                .forEach(listBean.getOptions()::add);
    }

    /**
     * 获取退货配置信息
     *
     * @param tradeType
     * @param tradeDay
     * @param xtenant
     * @param ppriceid
     * @return
     */
    public List<TuihuanConfig> getTuihuanConfigs(Integer tradeType, Integer tradeDay, Integer xtenant, Integer ppriceid) {
        Productinfo productinfo = Optional.ofNullable(SpringUtil.getBean(ProductinfoService.class).getProductinfoByPpid(ppriceid))
                .orElseThrow(() -> new CustomizeException(StrUtil.format("ppid[{}]找不到对应的商品", ppriceid)));
        //相等排在最前面
        Comparator<Category> categoryComparator = Comparator.comparing(c -> Objects.equals(c.getId(), productinfo.getCid()), Comparator.reverseOrder());
        //按级别倒序排
        categoryComparator = categoryComparator.thenComparing(c -> c.getLevel(), Comparator.reverseOrder());
        List<Category> categories = SpringUtil.getBean(CategoryService.class).listWithAllParent(productinfo.getCid()).stream()
                .sorted(categoryComparator).collect(Collectors.toList());
        List<String> productCidList = categories.stream().map(Category::getId).map(Convert::toStr).collect(Collectors.toList());
        //查询数据
        TuihuanConfigService tuihuanConfigService = SpringUtil.getBean(TuihuanConfigService.class);
        TuihuanConfigReqBo req = TuihuanConfigReqBo.builder().xtenant(xtenant).tradeType(tradeType).tradeDay(tradeDay)
                .productId(Convert.toStr(productinfo.getProductId()))
                .productCids(productCidList.stream().collect(Collectors.joining(StringPool.COMMA)))
                .build();
        List<TuihuanConfig> tuihuanConfigs = tuihuanConfigService.listTuihuanConfig(req);
        //商品相等 优先
        Comparator<TuihuanConfig> tuihuanConfigComparator = Comparator.comparing(tc -> TuihuanConfig.ProductTypeEnum.PRODUCT_ID.getCode().equals(tc.getProductType())
                && StrUtil.splitTrim(tc.getProductValues(), StringPool.COMMA).contains(req.getProductId()), Comparator.reverseOrder());
        //分类相等 优先, 且分类级别越高优先级越高
        tuihuanConfigComparator = tuihuanConfigComparator.thenComparing(tc -> {
            if(TuihuanConfig.ProductTypeEnum.CATEGORY.getCode().equals(tc.getProductType())){
                List<String> tcCids = StrUtil.splitTrim(tc.getProductValues(), StringPool.COMMA);
                for (int i = 0; i < productCidList.size(); i++) {
                    if (tcCids.contains(productCidList.get(i))){
                        return i;
                    }
                }
            }
            //否则排到最后
            return categories.size();
        });
        //最后按时间倒序排
        tuihuanConfigComparator = tuihuanConfigComparator.thenComparing(tc -> tc.getId(), Comparator.reverseOrder());
        tuihuanConfigs.sort(tuihuanConfigComparator);
        return tuihuanConfigs;
    }

    @Override
    public R<ZheJiaMachineVo> zheJia(ZheJiaMachineFormVo zheJiaFormVo) {
        mockShouhouInfo(zheJiaFormVo.getShouhouId(), zheJiaFormVo.getBasketId());
        R<ZheJiaMachineVo> cR = checkAndSetZheJia(zheJiaFormVo);
        if (!cR.isSuccess()){
            return cR;
        }
        ZheJiaMachineVo zheJiaMachineVo = new ZheJiaMachineVo().setPiaoPrice(BigDecimal.ZERO).setTuikuanM(zheJiaFormVo.getTuikuanM())
                .setZhejiaM(BigDecimal.ZERO).setSubIdM(BigDecimal.ZERO).setRefundPrice(zheJiaFormVo.getTotalPrice());
        R<ZheJiaMachineVo> r = R.success(zheJiaMachineVo);
        //计算折价信息
        calcZheJiaPrice(zheJiaFormVo, zheJiaMachineVo, r);
        // 获取真实退款类型
        TuihuanKindEnum realThEnum = getRealKindEnum(zheJiaFormVo.getTuihuanKindEnum(), zheJiaFormVo.getShouhou().getIshuishou());
        if(isGroupRefund(zheJiaFormVo.getTuihuanKind())){
            //折价后的金额 给获取最大可退金额
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE,zheJiaMachineVo.getRefundPrice()));
            Optional<RefundSubInfoBo> subInfoOpt = BaseTuiHuanKindService.getBean(realThEnum)
                    .getSuInfoWithMaxRefundPrice(zheJiaFormVo.getShouhouId(), realThEnum.getCode());
            handleZheJiaPay(zheJiaFormVo, subInfoOpt, zheJiaMachineVo, realThEnum, r);
            handleSmallRefund(zheJiaFormVo, subInfoOpt, zheJiaMachineVo, realThEnum, r);
        }

        //获取换货信息
        getAndSetHuanInfo(zheJiaFormVo, zheJiaMachineVo, r);
        if (!r.isSuccess()){
            // 换货信息获取失败
            return r;
        }
        zheJiaMachineVo.setIsShowGroupDetail(isShowGroupDetail(zheJiaFormVo.getTuihuanKind(), zheJiaMachineVo.getRefundPrice()));
        switch (zheJiaFormVo.getTuihuanKindEnum()){
            case TK:
            case HQTXH:
                //折价后的金额 给获取最大可退金额
                SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE,zheJiaMachineVo.getRefundPrice()));
                DetailParamBo detailParamBo = new DetailParamBo()
                        .setOrderId(zheJiaFormVo.getShouhouId()).setSupportSeconds(NumberConstant.ONE)
                        .setTuihuanId(null).setTuihuanKindEnum(zheJiaFormVo.getTuihuanKindEnum());
                R<RefundMoneyDetailVo> groupR = SpringUtil.getBean(RefundMoneyService.class).detail(detailParamBo,
                        detail -> BaseTuiHuanKindService.getBean(realThEnum)
                                .getSuInfoWithMaxRefundPrice(zheJiaFormVo.getShouhouId(), realThEnum.getCode()));
                r.addAllBusinessLog(groupR.businessLogs());
                if(!groupR.isSuccess()){
                    r.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
                    r.addBusinessLog(StrUtil.format("组合退获取结果: {}", groupR.getUserMsg()));
                }
                RefundMoneyDetailVo refundMoneyDetailVo = groupR.getData();
                if(XtenantEnum.isJiujiXtenant()){
                    Optional.ofNullable(refundMoneyDetailVo).ifPresent(item->{
                        List<String> notes = Optional.ofNullable(item.getNotes()).orElse(new ArrayList<>());
                        notes.addAll(getHeedInfo(refundMoneyDetailVo));
                        notes.addAll(zheJiaFormVo.getNodes());
                        item.setNotes(notes);
                    });
                }

                zheJiaMachineVo.setRefundMoneyDetailVo(refundMoneyDetailVo);
                break;
            default:
                break;
        }

        return r;
    }

    private void handleSmallRefund(ZheJiaMachineFormVo zheJiaFormVo, Optional<RefundSubInfoBo> subInfoOpt,
                                   ZheJiaMachineVo zheJiaMachineVo, TuihuanKindEnum realThEnum, R<ZheJiaMachineVo> r) {
        if(!TuihuanKindEnum.TK.getCode().equals(zheJiaFormVo.getTuihuanKind())){
            // 非退款,直接返回
            return;
        }
        if(!subInfoOpt.isPresent()){
            return;
        }

        Integer smallSubId = refundMachineMapper.getYouHuiSmallSubId(zheJiaFormVo.getShouhou().getBasketId());
        zheJiaMachineVo.setIsShowSmallRefund(smallSubId != null);
        zheJiaMachineVo.setSmallRefundSubId(smallSubId);
        PrioritySmallRefundEnum smallRefundEnum = PrioritySmallRefundEnum.PEI_JIAN_JUAN;
        zheJiaMachineVo.setSmallRefundCode(smallRefundEnum.getCode());
        zheJiaMachineVo.setSmallRefundReason(smallRefundEnum.getReason());

    }

    /**
     * 处理折价支付信息
     * @param zheJiaFormVo
     * @param subInfoOpt
     * @param zheJiaMachineVo
     * @param realThEnum
     * @param r
     */
    private void handleZheJiaPay(ZheJiaMachineFormVo zheJiaFormVo, Optional<RefundSubInfoBo> subInfoOpt,
                                 ZheJiaMachineVo zheJiaMachineVo, TuihuanKindEnum realThEnum, R<ZheJiaMachineVo> r) {
        // 是否显示折价支付信息
        Shouhou shouhou = Optional.ofNullable(zheJiaFormVo.getShouhou()).orElse(new Shouhou());
        BigDecimal huiShouPrice = Optional.ofNullable(shouhou.getBasketId()).map(refundMachineMapper::selectHuiShouPriceByBasketId)
                .map(RecoverSub::getPrices).orElse(BigDecimal.ZERO);
        BigDecimal totalZhejiaM = zheJiaMachineVo.getZhejiaM().add(zheJiaMachineVo.getPiaoPrice()).add(zheJiaFormVo.getPeizhiPrice());
        if(subInfoOpt.isPresent() && (totalZhejiaM.compareTo(BigDecimal.ZERO) > 0 || huiShouPrice.compareTo(BigDecimal.ZERO) > 0) ){
            ZheJiaPayEnum zheJiaPayEnum = zheJiaPayService.getZheJiaPayEnum(subInfoOpt.get().getOrderId(), realThEnum);
            boolean isShowZheJiaPay = zheJiaPayEnum != null || huiShouPrice.compareTo(BigDecimal.ZERO) >0;
            // 折价支付费用信息
            List<Integer> allPayCode = Wxkcoutput.PartTypeEnum.getAllPayCode(zheJiaPayEnum, huiShouPrice);
            List<Wxkcoutput> wxkcoutputs = zheJiaPayService.listPayWxkcoutput(zheJiaFormVo.getShouhouId(), allPayCode);
            BigDecimal zheJiaPayPrice = wxkcoutputs.stream()
                    .map(wxk -> ObjectUtil.defaultIfNull(wxk.getPrice(), BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            isShowZheJiaPay = isShowZheJiaPay && wxkcoutputs.size() < allPayCode.size();
            if(zheJiaPayPrice.compareTo(BigDecimal.ZERO) >0){
                zheJiaFormVo.getNodes().add(StrUtil.format("原折价金额{}元, 维修单内收取折价金额{}元",
                        zheJiaMachineVo.getZhejiaM(), zheJiaPayPrice));
                BigDecimal zhejiaM = zheJiaMachineVo.getZhejiaM();
                // 发票和商品折价进行扣减
                zheJiaMachineVo.setPiaoPrice(NumberUtil.max(BigDecimal.ZERO, zheJiaMachineVo.getPiaoPrice().subtract(zheJiaPayPrice)));
                zheJiaMachineVo.setZhejiaM(NumberUtil.max(BigDecimal.ZERO, zhejiaM.subtract(
                        NumberUtil.max(BigDecimal.ZERO, zheJiaPayPrice.subtract(zheJiaMachineVo.getPiaoPrice()))
                    )));


                zheJiaMachineVo.setRefundPrice(zheJiaMachineVo.getRefundPrice().add(NumberUtil.min(totalZhejiaM, zheJiaPayPrice)));
                zheJiaMachineVo.setTuikuanM(zheJiaMachineVo.getTuikuanM().add(NumberUtil.max(BigDecimal.ZERO, zhejiaM.subtract(zheJiaMachineVo.getZhejiaM()))));
                r.addBusinessLog(StrUtil.format("折价扣减维修单收取折价金额{}元", zheJiaPayPrice));
            }
            zheJiaMachineVo.setIsShowZheJiaPay(isShowZheJiaPay);
            if(zheJiaPayEnum != null){
                zheJiaMachineVo.setZheJiaPayCode(zheJiaPayEnum.getCode());
            }
            if(isShowZheJiaPay){
                Dict formatParam = Dict.create().set("totalZhejiaM", totalZhejiaM).set("huanNewBuTie", huiShouPrice);
                String zheJiaPayReason = zheJiaPayService.buildZheJiaPayReason(zheJiaPayEnum, formatParam);
                zheJiaMachineVo.setZheJiaPayReason(zheJiaPayReason);
            }
        }
    }

    private void getAndSetHuanInfo(ZheJiaMachineFormVo zheJiaFormVo, ZheJiaMachineVo zheJiaMachineVo,R<ZheJiaMachineVo> r) {
        if ((zheJiaFormVo.getNewMkcId() != null || zheJiaFormVo.getNewSubId() != null)
                && Stream.of(TuihuanKindEnum.HQTXH,TuihuanKindEnum.HJT).anyMatch(tk -> Objects.equals(zheJiaFormVo.getTuihuanKindEnum(),tk))){
            R<HuanInfoVo> hiR = huanInfo(new HuanInfoReq().setId(zheJiaFormVo.getShouhouId()).setKind(zheJiaFormVo.getTuihuanKind())
                    .setMkcid(zheJiaFormVo.getNewMkcId()).setSubid(zheJiaFormVo.getNewSubId()));
            r.addBusinessLog(StrUtil.format("换货信息获取结果:{}",hiR.getUserMsg()));
            if(hiR.isSuccess()){
                zheJiaMachineVo.setHuanInfo(hiR.getData());
            }else{
                r.setCode(ResultCode.SERVER_ERROR);
                r.setUserMsg(hiR.getUserMsg());
                return;
            }

        }
        if (zheJiaMachineVo.getHuanInfo() != null && TuihuanKindEnum.HQTXH == zheJiaFormVo.getTuihuanKindEnum()){
            BigDecimal money = zheJiaMachineVo.getHuanInfo().getMoney();
            //可充值金额需要扣减三方只支持原路径退的 min(可退金额,max(已付金额-总仅支持原路径可退,0))
            TuihuanKindEnum tuihuanKindEnum = zheJiaFormVo.getTuihuanKindEnum();
            BigDecimal refundPrice = zheJiaMachineVo.getRefundPrice();
            Optional<Boolean> isJiuJiOpt = Optional.ofNullable(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue);
            BigDecimal canPayPrice = getCanChangePriceOpt(zheJiaFormVo.getShouhou(), tuihuanKindEnum, refundPrice).orElse(refundPrice);
            if (canPayPrice.compareTo(money)>=0) {
                zheJiaMachineVo.setSubIdM(money);
            }else{
                zheJiaMachineVo.setSubIdM(canPayPrice);
            }
            isJiuJiOpt
                    .ifPresent(isJiuji -> {
                        //当前商品的绑定的只支持原路径的收银
                        List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class)
                                .listAll(zheJiaFormVo.getSubId(), zheJiaFormVo.getTuihuanKindEnum());
                        List<ThirdOriginRefundVo> vnmaooList = RefundMoneyUtil.filterChangeThirdOrigin(thirdOriginRefundVos,
                                zheJiaFormVo.getTuihuanKindEnum(), Collections.singletonList(zheJiaFormVo.getBasketId()))
                                .collect(Collectors.toList());
                        String basketIdStr = Convert.toStr(zheJiaFormVo.getBasketId());
                        BigDecimal basketOnlyOriginPrice = thirdOriginRefundVos.stream()
                                // 排除参与充值的原路径
                                .filter(tor -> !vnmaooList.contains(tor))
                                //只支持原路径的收银
                                .filter(tor -> ThirdRefundTypeEnum.onlyOriginRefund(tor.getRefundType(), null))
                                .filter(tor -> StrUtil.splitTrim(tor.getBasketId(), StringPool.COMMA).contains(basketIdStr))
                                .map(ThirdOriginRefundVo::getRefundPrice).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        BigDecimal onlyOriginSubIdM = basketOnlyOriginPrice.add(zheJiaMachineVo.getSubIdM()).add(zheJiaFormVo.getPeizhiPrice()).add(zheJiaFormVo.getPiaoPrice())
                                .subtract(zheJiaFormVo.getTuikuanM());
                        if(onlyOriginSubIdM.compareTo(BigDecimal.ZERO) > 0){
                            //当前商品绑定收银只支持原路径收银+冲抵+折价>商品金额 说明已经占用了其他方式的收银,需要再次进行扣减
                            r.addBusinessLog(StrUtil.format("客户实付冲抵金额:{}, 需要扣减当前商品仅支持原路径部分收银金额: {}", zheJiaMachineVo.getSubIdM(), onlyOriginSubIdM));
                            zheJiaMachineVo.setSubIdM(NumberUtil.max(zheJiaMachineVo.getSubIdM().subtract(onlyOriginSubIdM), BigDecimal.ZERO));
                        }
                    });

            zheJiaMachineVo.setRefundPrice(zheJiaMachineVo.getRefundPrice().subtract(zheJiaMachineVo.getSubIdM()));
        }
    }

    private Optional<BigDecimal> getCanChangePriceOpt(Shouhou shouhou, TuihuanKindEnum tuihuanKindEnum, BigDecimal refundPrice) {
        Optional<Boolean> isJiuJiOpt = Optional.ofNullable(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue);
        return isJiuJiOpt
                .map(isJiuji -> {
                    BigDecimal customerMaxRefundPrice = getCustomerMaxRefundPrice(shouhou.getId(), tuihuanKindEnum, refundPrice);
                    //当前商品的绑定的只支持原路径的收银
                    List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class)
                            .listAll(shouhou.getSubId(), tuihuanKindEnum);

                    // （1）三方收银方式为：代金券 （2）代金券活动类型为：非商场活动（3）代金券退款方式配置了：原路径退款 排除非当前商品的收银
                    BigDecimal onlyOriginRefundPrice = RefundMoneyUtil.filterChangeThirdOrigin(thirdOriginRefundVos,
                                    tuihuanKindEnum, Collections.singletonList(shouhou.getBasketId()))
                            .map(tor -> tor.getRefundPrice())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    return NumberUtil.max(NumberUtil.add(customerMaxRefundPrice, onlyOriginRefundPrice), refundPrice);
                });
    }

    /**
     * 获取其他型号可退金额
     * @param tuihuanKindEnum
     * @param refundPrice
     * @return
     */
    private BigDecimal getCustomerMaxRefundPrice(Integer shouhouId, TuihuanKindEnum tuihuanKindEnum, BigDecimal refundPrice) {
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE, refundPrice));
        Optional<RefundSubInfoBo> refundSubInfo = SpringUtil.getBean(RefundMachineKindService.class).getSuInfoWithMaxRefundPrice(shouhouId, tuihuanKindEnum.getCode());
        return refundSubInfo.map(rsi -> rsi.getCustomerMaxRefundPrice()).orElse(BigDecimal.ZERO);
    }

    /**
     * 计算折价金额
     * @param zheJiaFormVo
     * @param zheJiaMachineVo
     * @param r
     */
    private void calcZheJiaPrice(ZheJiaMachineFormVo zheJiaFormVo, ZheJiaMachineVo zheJiaMachineVo, R<ZheJiaMachineVo> r) {
        TuihuanKindEnum tuihuanKindEnum = zheJiaFormVo.getTuihuanKindEnum();
        if(tuihuanKindEnum == TuihuanKindEnum.HJT || tuihuanKindEnum == TuihuanKindEnum.HZB){
            zheJiaMachineVo.setRefundPrice(BigDecimal.ZERO);
            return;
        }
        Optional<TuihuanConfig> tuiHuanCfgOpt =  Optional.empty();
        //计算发票折价金额
        calcPiaoPrice(zheJiaFormVo).ifPresent(zheJiaMachineVo::setPiaoPrice);
        Shouhou shouhou = zheJiaFormVo.getShouhou();
        BigDecimal customerMaxRefundPrice = Optional.ofNullable(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue)
                .map(isJiuji -> {
                    TuihuanKindEnum thkEnum = getRealKindEnum(tuihuanKindEnum, shouhou.getIshuishou());
                    return getCustomerMaxRefundPrice(shouhou.getId(), thkEnum, zheJiaFormVo.getTotalPrice());
                })
                .orElse(zheJiaFormVo.getTotalPrice());
        //计算折价金额
        if(Objects.nonNull(zheJiaFormVo.getTradeType()) && Objects.nonNull(zheJiaFormVo.getTradeDay())
                && StrUtil.isNotBlank(zheJiaFormVo.getFaultType()) && StrUtil.isNotBlank(zheJiaFormVo.getCheckType())){
            tuiHuanCfgOpt = refundMachineService.getTuihuanConfigs(zheJiaFormVo.getTradeType(), zheJiaFormVo.getTradeDay(),
                            zheJiaFormVo.getXtenant(), shouhou.getPpriceid())
                    .stream()
                    .filter(thc -> StrUtil.splitTrim(thc.getFaultOption(), StringPool.COMMA).contains(zheJiaFormVo.getFaultType())
                            && StrUtil.splitTrim(thc.getCheckOption(), StringPool.COMMA).contains(zheJiaFormVo.getCheckType())
                            && StrUtil.splitTrim(thc.getDealOption(), StringPool.COMMA).stream().map(Convert::toInt).anyMatch(tk -> Objects.equals(tk, zheJiaFormVo.getTuihuanKind()))
                    )
                    // 金额匹配
                    .filter(thc -> (thc.getSPrice() == null || thc.getSPrice().compareTo(customerMaxRefundPrice)<=0)
                            && (thc.getEPrice() == null || thc.getEPrice().compareTo(customerMaxRefundPrice)>0))
                    .findFirst();
            tuiHuanCfgOpt.ifPresent(thc -> r.addBusinessLog(StrUtil.format("匹配到的折价规则id: {} 正常退换的折价率: {}, 补贴折价率: {}",
                    thc.getId(),thc.getPrices(), thc.getBuTiePrices())));
        }
        // 获取真实退款类型
        TuihuanKindEnum realThEnum = getRealKindEnum(zheJiaFormVo.getTuihuanKindEnum(), zheJiaFormVo.getShouhou().getIshuishou());
        //正常退换折价计算
        if(Objects.equals(zheJiaFormVo.getCheckType(), ZHENG_CHANG_TUI_HUAN)){
            BigDecimal cfZheJiaM = customerMaxRefundPrice
                    .multiply(tuiHuanCfgOpt.map(tcf -> {
                                BigDecimal zheJiaScale = Convert.toBigDecimal(tcf.getPrices(), BigDecimal.ZERO);
                                ZheJiaPayEnum zheJiaPayEnum = zheJiaPayService.getZheJiaPayEnum(shouhou.getSubId(), realThEnum);
                                if(zheJiaPayEnum != null && ZheJiaPayEnum.GUO_JIA_BUTIE.equals(zheJiaPayEnum)){
                                    r.addBusinessLog(StrUtil.format("增加{}折价上浮比例{}", zheJiaPayEnum.getMessage(), tcf.getBuTiePrices()));
                                    zheJiaScale = zheJiaScale.add(NumberUtil.null2Zero(tcf.getBuTiePrices()));
                                }
                                return zheJiaScale;
                            })
                            .orElse(BigDecimal.ZERO))
                    // 折价四舍五入,保证后续计算的一直性
                    .setScale(2,RoundingMode.HALF_UP);
            //当按照比例计算出来的折价金额小于配置“保底折价金额”时按照“保底折价金额”来进行折价），大于则按照比例计算出来的折价金额来进行折价
            if(tuiHuanCfgOpt.isPresent()){
                BigDecimal minPrice = Optional.ofNullable(tuiHuanCfgOpt.get().getMinPrice()).orElse(BigDecimal.ZERO);
                if(minPrice.compareTo(cfZheJiaM) >0){
                    r.addBusinessLog(StrUtil.format("计算折价: {}小于保底折价: {}, 取保底折价", cfZheJiaM, minPrice));
                    cfZheJiaM = minPrice;
                    if (cfZheJiaM.compareTo(customerMaxRefundPrice)>0){
                        // 降低对应的折价金额
                        cfZheJiaM = customerMaxRefundPrice;
                        r.addBusinessLog(StrUtil.format("折价金额大于实付, 调整保底折价金额为实付: {}", customerMaxRefundPrice));
                    }
                }
            }
            if(XtenantEnum.isJiujiXtenant() && NumberConstant.ONE.equals(shouhou.getIshuishou()) && TuihuanKindEnum.TK.equals(tuihuanKindEnum)){
                LocalDateTime modidate = shouhou.getModidate();
                if(modidate!=null){
                    LocalDate tradedate = Optional.ofNullable(shouhou.getTradedate()).orElse(LocalDateTime.MIN).toLocalDate();
                    String faultType = Optional.ofNullable(zheJiaFormVo.getFaultType()).orElse("");
                    String checkType = Optional.ofNullable(zheJiaFormVo.getCheckType()).orElse("");
                    //必须为良品退款且故障类别为“无故障”以及审核类别为“正常退换”
                    if(ChronoUnit.DAYS.between(tradedate, modidate) <= ShouhouExService.getWuLiYouDays()
                            && FaultTypeEnum.FAULTY.getMessage().equals(faultType)
                            && RefundMachineService.ZHENG_CHANG_TUI_HUAN.equals(checkType) ){
                        BigDecimal discount = getDiscount(Optional.ofNullable(zheJiaFormVo.getUserId()).map(Convert::toLong).orElse(shouhou.getUserid()));
                        cfZheJiaM = cfZheJiaM.add(discount);
                        r.addBusinessLog(StrUtil.format("良品大件退款增加折价金额:{}",discount));
                        if (cfZheJiaM.compareTo(customerMaxRefundPrice)>0){
                            // 降低对应的折价金额
                            cfZheJiaM = customerMaxRefundPrice;
                            r.addBusinessLog(StrUtil.format("折价金额大于实付, 调整良品折价金额为实付: {}", customerMaxRefundPrice));
                        }
                    }
                }
            }
            zheJiaMachineVo.setTuikuanM(zheJiaFormVo.getTotalPrice().subtract(cfZheJiaM));
            zheJiaMachineVo.setRefundPrice(zheJiaMachineVo.getRefundPrice().subtract(cfZheJiaM));
        }else{
            zheJiaMachineVo.setRefundPrice(zheJiaFormVo.getTuikuanM());
        }
        //扣减其他折价金额
        BigDecimal refundPrice = zheJiaMachineVo.getRefundPrice().subtract(zheJiaMachineVo.getPiaoPrice()).subtract(zheJiaFormVo.getPeizhiPrice());
        zheJiaMachineVo.setRefundPrice(NumberUtil.max(refundPrice, BigDecimal.ZERO));
        //计算折价金额
        zheJiaMachineVo.setZhejiaM(NumberUtil.max(zheJiaFormVo.getTotalPrice().subtract(zheJiaMachineVo.getTuikuanM()),BigDecimal.ZERO));

    }

    /**
     * 获取良品附件折价价格
     * @param zheJiaFormVo
     * @return
     */
    private BigDecimal getGoodAccessoriesPrice(ZheJiaMachineFormVo zheJiaFormVo){
        List<TuiGiftBasketListVo> goodProductAccessoriesList = zheJiaFormVo.getGoodProductAccessoriesList();
        if(CollectionUtils.isNotEmpty(goodProductAccessoriesList)){
            return goodProductAccessoriesList.stream()
                    .filter(item -> !Boolean.TRUE.equals(item.getIsBack()))
                    .map(TuiGiftBasketListVo::getDiscountAmount)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return BigDecimal.ZERO;
    }


    /**
     * 获取良品进行折价100逻辑优化
     * @param userid
     * @return
     */
    public BigDecimal getDiscount(Long userid){
        ShouhouRefundMoneyMapper shouhouRefundMoneyMapper = SpringUtil.getBean(ShouhouRefundMoneyMapper.class);
        RecoverNoReasonReduceVo recoverReduce = shouhouRefundMoneyMapper.getRecoverReduce(Convert.toInt(userid));
        int leftCount = Optional.ofNullable(recoverReduce).map(RecoverNoReasonReduceVo::getLeftCount).orElse(1);
        if(leftCount > 0 && recoverReduce != null){
            List<NoReasonTuiHuanInfoVO> goodtuihuanInfoLit = shouhouRefundMoneyMapper.getGoodNoReasonTuihuanInfo(recoverReduce);
            long onWayTuihuanCount = goodtuihuanInfoLit.stream()
                    .filter(gti -> ObjectUtil.defaultIfNull(gti.getCheck3(), 0) == 0)
                    .count();
            leftCount = leftCount - Convert.toInt(onWayTuihuanCount);
        }
        return leftCount <= 0? ShouhouExService.NO_REASON_REDUCE_MONEY : BigDecimal.ZERO;
    }

    private Optional<BigDecimal> calcPiaoPrice(ZheJiaMachineFormVo zheJiaFormVo) {
        if(ObjectUtil.defaultIfNull(zheJiaFormVo.getTuihuanId(),0)<=0
                && NumberConstant.ONE.equals(zheJiaFormVo.getCalculateType())
                && ObjectUtil.defaultIfNull(zheJiaFormVo.getPiaoInfo(),0) >=0){
            if(ObjectUtil.equal(zheJiaFormVo.getPiaoInfo(),PiaoInfoEnum.WEISHOUHUI.getCode())
                    && Objects.equals(zheJiaFormVo.getShouhouAreaInfo().getKind1(), AreaKindEnum.SELF.getCode())){
                return Optional.of(zheJiaFormVo.getTotalPrice().multiply(new BigDecimal("0.03")).setScale(0,RoundingMode.UP));
            }else if (ObjectUtil.equal(zheJiaFormVo.getPiaoInfo(),PiaoInfoEnum.YISHOUHUI.getCode())){
                return Optional.of(BigDecimal.ZERO);
            }
        }
        return Optional.ofNullable(zheJiaFormVo.getPiaoPrice());
    }

    private R<ZheJiaMachineVo> checkAndSetZheJia(ZheJiaMachineFormVo zheJiaFormVo) {
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, zheJiaFormVo.getTuihuanKind());
        try {
            Assert.notNull(tuihuanKindEnum, "退款类型不能为空");
            Assert.notNull(zheJiaFormVo.getXtenant(), "租户标识码不能为空");
            zheJiaFormVo.setTuihuanKindEnum(tuihuanKindEnum);
            //查询售后单信息
            Shouhou shouhou = refundMachineService.getShouhouById(zheJiaFormVo.getShouhouId());
            //初始化默认值
            zheJiaFormVo.setTotalPrice(ObjectUtil.defaultIfNull(zheJiaFormVo.getTotalPrice(),BigDecimal.ZERO));
            zheJiaFormVo.setCoinM(ObjectUtil.defaultIfNull(zheJiaFormVo.getCoinM(),BigDecimal.ZERO));
            zheJiaFormVo.setGiftPrice(ObjectUtil.defaultIfNull(zheJiaFormVo.getGiftPrice(),BigDecimal.ZERO));
            zheJiaFormVo.setBaitiaoPrice(ObjectUtil.defaultIfNull(zheJiaFormVo.getBaitiaoPrice(),BigDecimal.ZERO));
            zheJiaFormVo.setKuBaitiaoPrice(ObjectUtil.defaultIfNull(zheJiaFormVo.getKuBaitiaoPrice(),BigDecimal.ZERO));
            zheJiaFormVo.setWxHongBao(ObjectUtil.defaultIfNull(zheJiaFormVo.getWxHongBao(),BigDecimal.ZERO));
            zheJiaFormVo.setPeizhiPrice(ObjectUtil.defaultIfNull(zheJiaFormVo.getPeizhiPrice(),BigDecimal.ZERO));
            zheJiaFormVo.setPiaoPrice(ObjectUtil.defaultIfNull(zheJiaFormVo.getPiaoPrice(),BigDecimal.ZERO));
            zheJiaFormVo.setTuikuanM(ObjectUtil.defaultIfNull(zheJiaFormVo.getTuikuanM(),BigDecimal.ZERO));
            zheJiaFormVo.setShouhou(shouhou);
            AreaInfo areaInfo = getShouhouAreaInfo(shouhou);
            zheJiaFormVo.setShouhouAreaInfo(areaInfo);
        } catch (IllegalArgumentException e) {
           return R.error(e.getMessage());
        }
        return R.success(null);
    }

    @Override
    public R<HuanInfoVo> huanInfo(HuanInfoReq huanInfoReq) {
        if (Objects.isNull(huanInfoReq)) {

            return R.paramError("param null");
        }
        OaUserBO oaUserBo = abstractCurrentRequestComponent.getCurrentStaffId();
        if (ObjectUtil.defaultIfNull(huanInfoReq.getSubid(),0)<=0) {
            // r
            HuanInfoWithShouhouBo huan1 = getHuanInfo1(huanInfoReq.getId(), oaUserBo.getAreaId());
            if(Objects.isNull(huan1)){
                return R.error(StrUtil.format("售后id[{}]错误",huanInfoReq.getId()));
            }
            // k
            HuanInfoWithShouhouBo huan2 = getHuanInfo2(huanInfoReq.getMkcid(), oaUserBo.getAreaId(),huanInfoReq.getIsIgnoreStatus());
            if(Objects.isNull(huan2)){
                return R.error(StrUtil.format("mkcId[{}]必须为当前门店现货且是[库存]或[售后]状态",huanInfoReq.getMkcid()));
            }
            if (Objects.isNull(huan1.getPpriceid())) {

                return R.error("售后机商品id为空，不可通过此系统更换！");
            }
            if (huan1.getPpriceid().equals(huan2.getPpriceid())) {
                HuanInfoVo huanInfoVo = new HuanInfoVo();
                huanInfoVo.setImei(huan2.getImei());
                huanInfoVo.setName(huan2.getProductName() + " " + huan2.getProductName());
                return R.success(huanInfoVo);
            }
            String msg = StrUtil.format("商品不匹配原ppid[{}]新ppid[{}]",huan1.getPpriceid(),huan2.getPpriceid());
            if (Objects.nonNull(huanInfoReq.getKind())) {
                msg = Objects.equals(huanInfoReq.getKind(),1) ? msg+",机型或颜色不相同！" : Objects.equals(huanInfoReq.getKind(),2) ? msg+",颜色不相同" : msg;
            }
            return R.error(msg);
        }
        // r
        HuanInfoWithShouhouBo huanBySub1 = getHuanBySub1(huanInfoReq.getId(), oaUserBo.getAreaId());
        if (Objects.isNull(huanBySub1)) {

            return R.error("地区错误，请确认机器是否已经转出");
        }
        Integer userId = huanBySub1.getUserId();
        // k
        HuanInfoWithSubBo huanBySub2 = getHuanBySub2(huanInfoReq.getSubid(), userId, oaUserBo.getAreaId(),huanInfoReq.getIsIgnoreStatus());
        if (Objects.isNull(huanBySub2)) {

            return R.error("订单状态必须为[已确认]或[欠款]状态且必须是当前门店的同会员订单");
        }
        HuanInfoVo huanInfoVo = new HuanInfoVo();
        BigDecimal money = huanBySub2.getNeedPayMoney();
        if (Objects.isNull(money)
                || money.compareTo(BigDecimal.ZERO) <= 0) {
            money = huanBySub2.getYingfuMoney();
        }
        huanInfoVo.setMoney(money);
        return R.success(huanInfoVo);
    }

    /**
     * 退换需要的查询
     *
     * @param shouhouId 售后单id
     * @param areaId 门店id
     * @return
     */
    private HuanInfoWithShouhouBo getHuanInfo1(Integer shouhouId, Integer areaId) {

        return refundMachineMapper.getHuanInfo1(shouhouId, areaId);
    }

    /**
     *
     * @param shouhouId
     * @param areaId
     * @return
     */
    private HuanInfoWithShouhouBo getHuanBySub1(Integer shouhouId, Integer areaId) {

        return refundMachineMapper.getHuanBySub1(shouhouId, areaId);
    }

    /**
     * 退换需要的查询
     *
     * @param shouhouId
     * @param areaId
     * @param isIgnoreStatus
     * @return
     */
    private HuanInfoWithShouhouBo getHuanInfo2(Integer shouhouId, Integer areaId, Boolean isIgnoreStatus) {

        return refundMachineMapper.getHuanInfo2(shouhouId, areaId,isIgnoreStatus);
    }

    /**
     * 退换需要的查询
     *
     * @param subId
     * @param userId 用户id
     * @param areaId
     * @param isIgnoreStatus
     * @return
     */
    private HuanInfoWithSubBo getHuanBySub2(Integer subId, Integer userId, Integer areaId, Boolean isIgnoreStatus) {

        return refundMachineMapper.getHuanBySub2(subId, userId, areaId,isIgnoreStatus);
    }

    private HuanInfoVo getHuanByShouhouId(Integer mkcid, Integer areaId) {
        return refundMachineMapper.getHuanByShouhouId(mkcid, areaId);
    }

    /**
     * 设置微信红包金额  oa999DALEx\ShouHouService.cs::getWeiXinHongbao
     * @param refundDetail
     */
    private void setWeiXinHongBao(RefundMachineDetailVo refundDetail) {
        BigDecimal wxHongBao = refundMachineMapper.getWeiXinHongBao(refundDetail.getBasketId());
        refundDetail.setWxHongBao(wxHongBao);
    }

    /**
     * 内部流转的字段设置到详情上
     * @param refundDetail
     */
    private void convertAndSetDetailInfo(RefundMachineDetailVo refundDetail) {
        //tuihuan属性设置到页面
        Shouhou shouhou = refundDetail.getShouhou();
        ShouhouTuiHuanPo tuihuan = refundDetail.getTuihuan();
        //设置售后信息
        refundDetail.setShouhouId(shouhou.getId());
        AreaInfo shouhouAreaInfo = refundDetail.getShouhouAreaInfo();
        refundDetail.setArea(shouhouAreaInfo.getArea());
        refundDetail.setAreaId(shouhouAreaInfo.getId());
        refundDetail.setXtenant(shouhouAreaInfo.getXtenant());
        //默认为售后单的用户id, 查询订单信息后, 赋值为订单的用户id
        refundDetail.setUserId(Convert.toInt(shouhou.getUserid()));

        if(tuihuan != null){
            //设置售后折退次数
            setAfterServicesDisCount(refundDetail);
            setTuihuanDetail(refundDetail, shouhou);
            //获取组合退的详情
            if(Boolean.TRUE.equals(refundDetail.getIsShowGroupDetail())){
                TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuihuan.getTuihuanKind());
                //折价后的金额 给组合退
                BigDecimal refundPrice = ObjectUtil.defaultIfNull(tuihuan.getTuikuanM(), BigDecimal.ZERO)
                        .subtract(ObjectUtil.defaultIfNull(tuihuan.getPeizhiPrice(), BigDecimal.ZERO))
                        .subtract(ObjectUtil.defaultIfNull(tuihuan.getPiaoPrice(), BigDecimal.ZERO));
                SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE,refundPrice));
                refundDetail.setRefundMoneyDetailVo(SpringUtil.getBean(RefundMoneyService.class).detail(new DetailParamBo()
                                .setOrderId(tuihuan.getShouhouId()).setSupportSeconds(NumberConstant.ONE)
                                .setTuihuanId(tuihuan.getId()).setTuihuanKindEnum(tuihuanKindEnum),
                                detail -> {
                                    TuihuanKindEnum thEnum = getRealKindEnum(tuihuanKindEnum, shouhou.getIshuishou());
                                    return BaseTuiHuanKindService.getBean(thEnum).getSuInfoWithMaxRefundPrice(tuihuan.getShouhouId(), thEnum.getCode());
                                })
                                .getData());
            }
            if(Objects.equals(TuihuanKindEnum.HJT.getCode(),tuihuan.getTuihuanKind())){
                //设置换货详情信息
                HuanInfoReq huanInfoReq = new HuanInfoReq().setId(refundDetail.getShouhouId()).setKind(refundDetail.getTuihuanKind()).setIsIgnoreStatus(Boolean.TRUE);
                huanInfoReq.setMkcid(refundDetail.getNewBasketId());
                R<HuanInfoVo> hiR = huanInfo(huanInfoReq);
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,StrUtil.format("换货信息获取结果:{}",hiR.getUserMsg()));
                HuanInfoVo huanInfoVo = hiR.getData();
                refundDetail.setHuanInfo(huanInfoVo);
            }else if(TuihuanKindEnum.HQTXH.getCode().equals(tuihuan.getTuihuanKind())){
                refundDetail.setHuanInfo(LambdaBuild.create(new HuanInfoVo()).set(HuanInfoVo::setMoney,tuihuan.getSubIdM()).build());
            }

            //组装详情的级联数据 下拉只有一项数据
            setRelateList(null,refundDetail);
            //详情且是退款或换其他
            if(Stream.of(TuihuanKindEnum.TK,TuihuanKindEnum.HQTXH).map(TuihuanKindEnum::getCode)
                    .anyMatch(kind -> Objects.equals(kind,refundDetail.getTuihuanKind()))){
                Optional.ofNullable(tuihuan.getPiaoType()).ifPresent(piaoType -> {
                    //查询发票信息 string.IsNullOrEmpty(piaoType) ? "未开票" : "已开票(" + Fun.getPiaoType(piaoType) + ")"
                    refundDetail.setPiaoType(piaoType);
                    Optional.ofNullable(PiaoEnum.getEnumByCode(piaoType)).ifPresent(a -> refundDetail.setPiaoTypeDesc(a.getMessage()));
                    //需要增加字典枚举项 已开票才进行显示
                    refundDetail.setPiaoInfoList(new ListBean(RefundMachineDetailVo.PIAOINFO_TYPE_KEY,"发票收回", ListBean.ListType.SELECT,false
                            ,Arrays.stream(PiaoInfoEnum.values())
                            .map(e->new ListBean.OptionsBean(Convert.toStr(e.getCode()),e.getMessage()))
                            .collect(Collectors.toList())).setIsShow(Objects.nonNull(tuihuan.getPiaoType())));
                });
            }
        } else{
            //设置流程状态
            if (Boolean.TRUE.equals(refundDetail.getShouhou().getIsquji())) {
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,StrUtil.format("售后单[{}]已取机不允许提交申请",shouhou.getId()));
                refundDetail.setProcessName(ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK.getMessage());
                refundDetail.setProcessStatus(ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK.getCode());
                refundDetail.setCurrProcessStatus(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode());
            } else {
                refundDetail.setProcessName(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getMessage());
                refundDetail.setProcessStatus(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode());
                refundDetail.setCurrProcessStatus(ShouhouRefundDetailVo.ProcessStatus.SUBMIT.getCode());
            }
            //申请页面
            //获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice wjh
            setBuyPriceAndSubInfo(refundDetail);
            //设置售后折退次数
            setAfterServicesDisCount(refundDetail);
            //按匹配条件设置到缓存中
            List<TuihuanConfig> tuihuanConfigs = refundMachineService.getTuihuanConfigs(refundDetail.getTradeType(),
                    refundDetail.getTradeDay(),refundDetail.getShouhouAreaInfo().getXtenant(), shouhou.getPpriceid());
            //组装配置的级联数据
            setRelateList(tuihuanConfigs,refundDetail);
        }

        //设置所有的收银方式
        if(XtenantEnum.isJiujiXtenant()){
            TuihuanKindEnum tuiKindEnum = ObjectUtil.defaultIfNull(shouhou.getIshuishou(), 0) == 0 ? TuihuanKindEnum.TK : TuihuanKindEnum.TK_LP;
            refundDetail.getNotes().add(SpringUtil.getBean(RefundMoneyService.class).getAllShouYingDescription(shouhou.getSubId(), tuiKindEnum));
            refundDetail.getNotes().addAll(getHeedInfo(refundDetail.getRefundMoneyDetailVo()));
        }
    }

    private static TuihuanKindEnum getRealKindEnum(TuihuanKindEnum tuihuanKindEnum, Integer isHuiShou) {
        TuihuanKindEnum thEnum = tuihuanKindEnum;
        if(tuihuanKindEnum == TuihuanKindEnum.TK && ObjectUtil.defaultIfNull(isHuiShou, 0) == 1){
            thEnum = TuihuanKindEnum.TK_LP;
        }
        return thEnum;
    }


    /**
     * 获取注意事项
     * @param refundMoneyDetailVo
     * @return
     */
    @Override
    public Set<String> getHeedInfo(RefundMoneyDetailVo refundMoneyDetailVo) {
        Set<String> set = new HashSet<>();
        if(ObjectUtil.isNull(refundMoneyDetailVo)){
            return set;
        }
        List<RefundWayDetailVo> refundWayDetails = refundMoneyDetailVo.getRefundWayDetails();
        if(CollectionUtils.isNotEmpty(refundWayDetails)){
            //过滤三方支付原路退款 相关信息
            List<RefundWayDetailVo> collectThird = refundWayDetails.stream().filter(obj -> TuiGroupEnum.THIRD_PAY_ORIGIN_WAY.getCode().equals(obj.getGroupCode())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collectThird)){
                for(RefundWayDetailVo third : collectThird){
                    List groupData = third.getGroupData();
                    if(CollectionUtils.isEmpty(groupData)){
                        continue;
                    }
                    groupData.stream().forEach(data -> {
                        if (data instanceof ThirdOriginRefundVo) {
                            ThirdOriginRefundVo refundVo = (ThirdOriginRefundVo) data;
                            String heed = refundVo.getHeed();
                            if(StringUtils.isNotBlank(heed)){
                                set.add(heed);
                            }
                        }
                    });
                }
            }
        }
        return set;
    }

    @Override
    public R<RecoverNoReasonReduceVo> getRecoverNoReasonReduce(Integer userId) {
        ShouhouRefundMoneyMapper shouhouRefundMoneyMapper = SpringUtil.getBean(ShouhouRefundMoneyMapper.class);
        RecoverNoReasonReduceVo.RecoverNoReasonReduceVoBuilder builder = RecoverNoReasonReduceVo.builder();
        RecoverNoReasonReduceVo recoverReduce = shouhouRefundMoneyMapper.getRecoverReduce(userId);
        builder.days(ShouhouExService.getWuLiYouDays()).isReduceServiceFee(false).allCount(1).leftCount(1)
                .reducePrice(ShouhouExService.NO_REASON_REDUCE_MONEY).tuiCount(0);
        List<NoReasonTuiHuanInfoVO> goodtuihuanInfoLit = Optional.ofNullable(recoverReduce)
                .map(shouhouRefundMoneyMapper::getGoodNoReasonTuihuanInfo)
                .orElse(Collections.emptyList());
        int onWayTuihuanCount = (int)goodtuihuanInfoLit.stream()
                .filter(gti -> ObjectUtil.defaultIfNull(gti.getCheck3(), 0) == 0)
                .count();
        builder.onWayTuiCount(onWayTuihuanCount);
        builder.freeTuiSubIds(goodtuihuanInfoLit.stream().map(NoReasonTuiHuanInfoVO::getSubId).distinct().collect(Collectors.toList()));
        if(recoverReduce != null){
            builder.allCount(recoverReduce.getAllCount());
            builder.leftCount(recoverReduce.getLeftCount());
            builder.configId(recoverReduce.getConfigId());
            builder.tuiCount(recoverReduce.getTuiCount());
        }
        RecoverNoReasonReduceVo reduceVo = builder.build();
        reduceVo.setCnAllCount(Convert.numberToChinese(reduceVo.getAllCount(), false));
        reduceVo.setLeftCount(NumberUtil.max(reduceVo.getLeftCount() - onWayTuihuanCount, 0));
        //没有免费次数
        reduceVo.setIsReduceServiceFee(reduceVo.getLeftCount() <=0);
        return R.success(reduceVo);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public R<Integer> saveZheJiaPay(ZheJiaPayFormVo form) {
        ZheJiaPayEnum payEnum= EnumUtil.getEnumByCode(ZheJiaPayEnum.class, form.getZheJiaPayCode());
        //国补回收价格查询
        Shouhou shouhou = shouhouService.getById(form.getShouhouId());
        Optional<RecoverSub> huanNewBuTieOpt = Optional.ofNullable(shouhou.getBasketId())
                .map(refundMachineMapper::selectHuiShouPriceByBasketId)
                .filter(rs -> NumberUtil.null2Zero(rs.getPrices()).compareTo(BigDecimal.ZERO)>0);

        List<Integer> allPayCode = Wxkcoutput.PartTypeEnum.getAllPayCode(payEnum,
                huanNewBuTieOpt.map(RecoverSub::getPrices).orElse(BigDecimal.ZERO));
        List<Wxkcoutput> wxkcoutputs = zheJiaPayService.listPayWxkcoutput(form.getShouhouId(), allPayCode);
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        // 校验售后单是否已经有折价支付信息
        if(wxkcoutputs.size() == allPayCode.size()){
            return R.error("已生成折价费用, 请去维修单上收银");
        }

        if(wxkcoutputs.stream().noneMatch(wk -> Wxkcoutput.PartTypeEnum.HUI_SHOU_ZHE_JIA_PAY.getCode().equals(wk.getPartType()))
                && huanNewBuTieOpt.isPresent()){
            RecoverSub huanNewSub = huanNewBuTieOpt.get();
            WxFeeBo wxFeeBo = new WxFeeBo().setKinds(1).setInprice(BigDecimal.ZERO).setIshexiao(false).setAreaId(oaUser.getAreaId())
                    .setPrice(huanNewSub.getPrices()).setPrice1(huanNewSub.getPrices()).setPriceGs(BigDecimal.ZERO)
                    .setProductName("回收补贴费用退回(禁止改价或删除)").setShouhouId(form.getShouhouId()).setUser(oaUser.getUserName())
                    .setPrevLog(StrUtil.format(" 回收单[{}]换新补贴", huanNewSub.getSubId()));
            R<ShouhouCostPriceRes> addR = shouhouService.addCostPrice(wxFeeBo, false, false);
            if(!addR.isSuccess()){
                throw new CustomizeException(addR);
            }
            //更新为配件
            SpringUtil.getBean(WxkcoutputService.class).lambdaUpdate().eq(Wxkcoutput::getId, wxFeeBo.getId())
                    .set(Wxkcoutput::getPartType, Wxkcoutput.PartTypeEnum.HUI_SHOU_ZHE_JIA_PAY.getCode()).update();
            //更新为已审核
            SpringUtil.getBean(ShouHouPjService.class).acept(wxFeeBo.getId());
        }
        BigDecimal zheJiaPayPrice = form.getZheJiaPayPrice();
        if(wxkcoutputs.stream().noneMatch(wk -> Wxkcoutput.PartTypeEnum.ZHE_JIA_PAY.getCode().equals(wk.getPartType()))
                && zheJiaPayPrice.compareTo(BigDecimal.ZERO) > 0){
            WxFeeBo wxFeeBo = new WxFeeBo().setKinds(1).setInprice(BigDecimal.ZERO).setIshexiao(false).setAreaId(oaUser.getAreaId())
                    .setPrice(zheJiaPayPrice).setPrice1(zheJiaPayPrice).setPriceGs(BigDecimal.ZERO)
                    .setProductName("保值退换折价金额").setShouhouId(form.getShouhouId()).setUser(oaUser.getUserName())
                    .setPrevLog(StrUtil.format("{} ", payEnum.getMessage()));
            R<ShouhouCostPriceRes> addR = shouhouService.addCostPrice(wxFeeBo, false, false);
            if(!addR.isSuccess()){
                throw new CustomizeException(addR);
            }
            //更新为配件
            SpringUtil.getBean(WxkcoutputService.class).lambdaUpdate().eq(Wxkcoutput::getId, wxFeeBo.getId())
                    .set(Wxkcoutput::getPartType, Wxkcoutput.PartTypeEnum.ZHE_JIA_PAY.getCode()).update();
            //更新为已审核
            SpringUtil.getBean(ShouHouPjService.class).acept(wxFeeBo.getId());
        }

        return R.success(null);
    }

    private void setAfterServicesDisCount(RefundMachineDetailVo refundDetail) {
        //该用户近一年折价退换次数为@(ViewBag.afterServicesDisCount)次，请注意核查该用户之前的退换情况
        //oa999DAL\ShouhouTuihuan.cs::getAfterServicesDiscount 追加到notes
        Integer count = shouhouTuihuanService.countAfterServicesDiscount(refundDetail.getUserId());
        if(count>0){
            refundDetail.getNotes().add(StrUtil.format("该用户近一年折价退换次数为{}次，请注意核查该用户之前的退换情况",count));
        }
        refundDetail.setAfterServicesDisCount(count);
        if(count >= NumberConstant.THREE){
            refundDetail.getNotes().add("* 客户1年内折价退换已超过3次，只能特殊退换！");
        }
    }

    private void setTuihuanDetail(RefundMachineDetailVo refundDetail, Shouhou shouhou) {
        ShouhouTuiHuanPo shouhouTuihuan = refundDetail.getTuihuan();
        refundDetail.setTuihuanId(shouhouTuihuan.getId());
        refundDetail.setCheckType(shouhouTuihuan.getCheckType());
        refundDetail.setTuihuanKind(shouhouTuihuan.getTuihuanKind());
        refundDetail.setCType(shouhouTuihuan.getCtype());
        refundDetail.setTradeType(shouhouTuihuan.getTradeType());
        refundDetail.setTradeTypeDesc(EnumUtil.getMessageByCode(TradeTypeEnum.class,shouhouTuihuan.getTradeType()));
        refundDetail.setZhejiaM(shouhouTuihuan.getZhejiaM());
        refundDetail.setCoinM(shouhouTuihuan.getCoinM());
        refundDetail.setBaitiaoPrice(shouhouTuihuan.getBaitiaoM());
        refundDetail.setKuBaitiaoPrice(shouhouTuihuan.getKuBaiTiaoM());
        if (Objects.equals(refundDetail.getTuihuanKind(),TuihuanKindEnum.HQTXH.getCode())){
            refundDetail.setSubId(shouhou.getSubId());
            refundDetail.setNewBasketId(shouhouTuihuan.getSubId());
        }else{
            refundDetail.setSubId(shouhouTuihuan.getSubId());
            refundDetail.setNewBasketId(shouhouTuihuan.getBasketId());
        }
        refundDetail.setSubIdM(shouhouTuihuan.getSubIdM());

        refundDetail.setPeizhi(shouhouTuihuan.getPeizhi());
        refundDetail.setPeizhiPrice(shouhouTuihuan.getPeizhiPrice());
        refundDetail.setPiaoPrice(shouhouTuihuan.getPiaoPrice());
        refundDetail.setTotalRefundPrice(shouhouTuihuan.getBuypriceM());
        refundDetail.setTradeDate(shouhouTuihuan.getTradeDate());
        refundDetail.setTuikuanM(shouhouTuihuan.getTuikuanM());
        refundDetail.setInprice(shouhouTuihuan.getInprice());
        refundDetail.setComment(shouhouTuihuan.getComment());
        //按退款类型显示实际退款金额
        if(ObjectUtil.equal(shouhouTuihuan.getTuihuanKind(),TuihuanKindEnum.HQTXH.getCode())){
            refundDetail.setRefundPrice(shouhouTuihuan.getTuikuanM1());
        }else{
            refundDetail.setRefundPrice(ObjectUtil.defaultIfNull(shouhouTuihuan.getTuikuanM(),BigDecimal.ZERO)
                    .subtract(ObjectUtil.defaultIfNull(shouhouTuihuan.getPeizhiPrice(),BigDecimal.ZERO))
                    .subtract(ObjectUtil.defaultIfNull(shouhouTuihuan.getPiaoPrice(),BigDecimal.ZERO)));
        }
        refundDetail.setIsShowGroupDetail(isShowGroupDetail(shouhouTuihuan.getTuihuanKind(), refundDetail.getRefundPrice()));
        refundDetail.setFaultType(shouhouTuihuan.getFaultType());
        refundDetail.setPiaoInfo(shouhouTuihuan.getPiaoInfo());
        if (shouhou.getModidate() != null && shouhouTuihuan.getTradeDate() != null) {
            refundDetail.setTradeDay(Convert.toInt(Duration.between(shouhouTuihuan.getTradeDate().truncatedTo(ChronoUnit.DAYS), shouhou.getModidate().truncatedTo(ChronoUnit.DAYS)).toDays()));
        }
        refundDetail.setCheckHistorys(new LinkedList<>());
        ShouhouRefundDetailVo.ProcessStatus processStatus = ShouhouRefundService.addProcessInfo(shouhouTuihuan, ShouhouRefundDetailVo.ProcessStatus.SUBMIT, refundDetail.getCheckHistorys());
        ShouhouRefundDetailVo.ProcessStatus nextStatus = ShouhouRefundDetailVo.ProcessStatus.nextProcess(processStatus);
        refundDetail.setCurrProcessStatus(processStatus.getCode());
        refundDetail.setProcessName(nextStatus.getMessage());
        refundDetail.setProcessStatus(nextStatus.getCode());
        refundDetail.setIsCanCancel(Stream.of(ShouhouRefundDetailVo.ProcessStatus.SUBMIT,ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK)
                .noneMatch(ps -> ObjectUtil.equal(ps,nextStatus)));
        //设置发票相关信息
        refundDetail.setFpOpenid(shouhouTuihuan.getFpOpenid());
        refundDetail.setFpPayState(shouhouTuihuan.getFpPayState());
        refundDetail.setFpPayTime(shouhouTuihuan.getFpPayTime());
    }


    /**
     * /获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice
     * @param shouhouId 订单号
     *
     * @param tuihuanKindEnum
     * @return BuyPriceSubInfoBo
     */
    @Override
    public BuyPriceSubInfoBo getBuyPriceAndSubInfo(Integer shouhouId, TuihuanKindEnum tuihuanKindEnum) {
        return SpringContextUtil.reqCache(()-> invokeGetBuyPriceAndSubInfo(shouhouId, tuihuanKindEnum), RequestCacheKeys.GET_BUY_PRICE_AND_SUB_INFO, shouhouId, tuihuanKindEnum);
    }

    /**
     * /获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice
     * @param shouhouId 订单号
     *
     * @param tuihuanKindEnum
     * @return BuyPriceSubInfoBo
     */
    private BuyPriceSubInfoBo invokeGetBuyPriceAndSubInfo(Integer shouhouId, TuihuanKindEnum tuihuanKindEnum) {
        Shouhou shouhou = refundMachineService.getShouhouById(shouhouId);
        if(shouhou == null){
            return null;
        }
        return refundMachineService.getBuyPriceAndSubInfo(shouhou.getSubId(), shouhou, tuihuanKindEnum);
    }

    /**
     * 良品附件表保存
     * @param tuihuanForm
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLpAccessories(RefundMachineFormVo tuihuanForm){
        List<TuiGiftBasketListVo> lpAccessories = getLpAccessories(tuihuanForm.getShouhou(), null);
        List<TuiGiftBasketListVo> goodProductAccessoriesList = tuihuanForm.getGoodProductAccessoriesList();
        if(lpAccessories.size()!=goodProductAccessoriesList.size()){
            throw new CustomizeException("良品附件数量异常");
        }
        if(CollectionUtils.isNotEmpty(goodProductAccessoriesList)){
            Integer tuihuanId = tuihuanForm.getTuihuanId();
            goodProductAccessoriesList.forEach(item->{
                ShouhouTuihuanGiftRelation shouhouTuihuanGiftRelation = new ShouhouTuihuanGiftRelation();
                shouhouTuihuanGiftRelation.setFkTuihuanId(tuihuanId)
                        .setBasketId(item.getBasketId())
                        .setIsBack(item.getIsBack())
                        .setCreateTime(LocalDateTime.now())
                        .setIsDel(Boolean.FALSE)
                        .setDiscountAmount(item.getDiscountAmount());
                SpringUtil.getBean(ShouhouTuihuanGiftRelationService.class).save(shouhouTuihuanGiftRelation);
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmitCheck(expression = "#{classFullName}:#{machineFormVo.subId}",message = "存在未完成的退款")
    public R<Integer> save(RefundMachineFormVo machineFormVo) {

        try {
            R<Integer> checkR = assertCheckSaveAndSet(machineFormVo);
            if (!checkR.isSuccess()) {
                return checkR;
            }

            R<Integer> checkResult2 = assertCheckSaveAndSet2(machineFormVo);
            if (!checkResult2.isSuccess()) {
                return checkResult2;
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        if(isGroupRefund(machineFormVo.getTuihuanKind())){
            GroupTuihuanFormVo tuihuanFormVo = machineFormVo.getTuihuanFormVo();
            if(machineFormVo.getTuihuanKindEnum() == TuihuanKindEnum.HQTXH){
                tuihuanFormVo.setTuikuanM1(tuihuanFormVo.getRefundPrice());
                tuihuanFormVo.setRefundPrice(tuihuanFormVo.getRefundPrice().add(machineFormVo.getSubIdm()));
                machineFormVo.setRefundPrice(tuihuanFormVo.getRefundPrice());
                tuihuanFormVo.getRefundWayDetails().add(JSON.parseObject(JSON.toJSONString(new OtherRefundVo().setGroupCode(TuiGroupEnum.DEDUCTION_NOT_SAVE.getCode())
                        .setRefundPrice(machineFormVo.getSubIdm()).setReturnWayName(TuihuanKindEnum.HQTXH.getMessage()))));
            }

            RefundMoneyService refundMoneyService = SpringUtil.getBean(RefundMoneyService.class);
            //折价后的金额 给组合退
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE,machineFormVo.getRefundPrice()));
            if(XtenantEnum.isJiujiXtenant()){
                SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.FAULT_TYPE,machineFormVo.getFaultType()));
                SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.CHECK_TYPE,machineFormVo.getCheckType()));
            }
            //前面已经校验过用户信息, 后续不再需要校验
            //组合退信息
            R<Integer> grouSaveR = refundMoneyService.save(tuihuanFormVo, tuihuanForm -> {
                TuihuanKindEnum thEnum = getRealKindEnum(machineFormVo.getTuihuanKindEnum(), machineFormVo.getShouhou().getIshuishou());
                return BaseTuiHuanKindService.getBean(thEnum)
                        .getSuInfoWithMaxRefundPrice(ObjectUtil.defaultIfNull(machineFormVo.getShouhouId(), machineFormVo.getSubId()), thEnum.getCode());
            });
            if(!grouSaveR.isSuccess()){
                throw new CustomizeException(grouSaveR.getUserMsg());
            }
            if(RefundShouhouService.CHECK_DATA_VALUE.equals(tuihuanFormVo.getCheckData())){
                //只校验直接返回
                return grouSaveR;
            }
            machineFormVo.setTuihuanId(grouSaveR.getData());
            if(XtenantEnum.isJiujiXtenant() && NumberConstant.ONE.equals(machineFormVo.getShouhou().getIshuishou())){
                //良品附件保存
                refundMachineService.saveLpAccessories(machineFormVo);
            }
            TuiHuanService tuiHuanService = SpringUtil.getBean(TuiHuanService.class);
            //更新大件退款才有的字段信息
            LambdaUpdateChainWrapper<ShouhouTuiHuanPo> tuiHuanLambdaUpdate = tuiHuanService.lambdaUpdate().eq(ShouhouTuiHuanPo::getId,grouSaveR.getData());
            tuiHuanLambdaUpdate
                    .set(ShouhouTuiHuanPo::getFaultType,machineFormVo.getFaultType()).set(ShouhouTuiHuanPo::getCheckType,machineFormVo.getCheckType())
                    .set(ShouhouTuiHuanPo::getZhejiaM,machineFormVo.getZhejiaM())
                    .set(ShouhouTuiHuanPo::getCoinM,machineFormVo.getCoinM()).set(ShouhouTuiHuanPo::getPeizhi,machineFormVo.getPeizhi())
                    .set(ShouhouTuiHuanPo::getPeizhiPrice,machineFormVo.getPeizhiPrice()).set(ShouhouTuiHuanPo::getTradeDate,machineFormVo.getTradeDate())
                    .set(ShouhouTuiHuanPo::getPuhuim,machineFormVo.getPuhuiM()).set(ShouhouTuiHuanPo::getSubIdM,machineFormVo.getSubIdm())
                    .set(ShouhouTuiHuanPo::getBuypriceM,machineFormVo.getBuypriceM()).set(ShouhouTuiHuanPo::getKuBaiTiaoM,machineFormVo.getKuBaitiaoPrice())
                    .set(ShouhouTuiHuanPo::getBaitiaoM,machineFormVo.getBaitiaoPrice()).set(ShouhouTuiHuanPo::getCtype,machineFormVo.getCType())
                    .set(ShouhouTuiHuanPo::getTradeType,machineFormVo.getTradeType()).set(ShouhouTuiHuanPo::getPiaoPrice,machineFormVo.getPiaoPrice())
                    .set(ShouhouTuiHuanPo::getPiaoInfo,machineFormVo.getPiaoInfo()).set(ShouhouTuiHuanPo::getPiaoType,machineFormVo.getPiaoType())
                    .set(ShouhouTuiHuanPo::getTuikuanM,machineFormVo.getTuikuanM()).set(ShouhouTuiHuanPo::getTuikuanM1,machineFormVo.getTuikuanM1())
                    .set(ShouhouTuiHuanPo::getInprice,machineFormVo.getInprice());
            if (TuihuanKindEnum.HQTXH == machineFormVo.getTuihuanKindEnum()){
                tuiHuanLambdaUpdate.set(ShouhouTuiHuanPo::getSubId,machineFormVo.getNewBasketId());
            }else{
                tuiHuanLambdaUpdate.set(ShouhouTuiHuanPo::getBasketId,ObjectUtil.defaultIfNull(machineFormVo.getNewBasketId(),
                        machineFormVo.getBuyPriceSubInfo().getBasketId()));
            }
            if(!tuiHuanLambdaUpdate.update()){
                throw new CustomizeException("组合退款,换机信息更新失败");
            }
        }
        //如果是传1那就是单纯校验
        if(RefundShouhouService.CHECK_DATA_VALUE.equals(Optional.ofNullable(machineFormVo.getTuihuanFormVo()).orElse(new GroupTuihuanFormVo()).getCheckData())){
            return R.success(machineFormVo.getTuihuanId());
        }
        //校验验证
        R<Integer> validR = validCode(machineFormVo);
        if (!validR.isSuccess()){
            return validR;
        }
        // 状态 错误消息
        Tuple2<Boolean, String> tuple2 = refundMachineService.beginTuihuan(machineFormVo.getCurrUser(), machineFormVo, machineFormVo.getNewBasketId());
        if (!Boolean.TRUE.equals(tuple2.getT1())) {
            throw  new CustomizeException(StringUtils.isEmpty(tuple2.getT2()) ? "备注内容过长，尝试内容短一些再提交！" : tuple2.getT2());
        }

        return R.success(machineFormVo.getTuihuanId()).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @Override
    public void autoMobileHttpTuikuan(RefundMachineFormVo machineFormVo) {
        ShouhouTuiHuanPo shouhouTuiHuanPo = SpringUtil.getBean(ShouhouTuihuanMapper.class).getPoByIdSqlServer(machineFormVo.getTuihuanId());
        Boolean lpAutomaticRefund = shouhouTuihuanService.lpAutomaticRefund(shouhouTuiHuanPo);
        //如果是良品那就行自动退换办理
        if(lpAutomaticRefund){
            OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息不能为空"));
            SpringUtil.getBean(CsharpReturnService.class).refundMachineTransact(machineFormVo.getTuihuanId(), machineFormVo.getNewBasketId());
        }
    }

    private R<Integer> validCode(RefundMachineFormVo machineFormVo) {
        TuihuanKindEnum tuihuanKindEnum = machineFormVo.getTuihuanKindEnum();
        Shouhou shouhou = machineFormVo.getShouhou();
        TuihuanKindEnum validTuiKindEnum = tuihuanKindEnum;
        if(NumberConstant.ONE.equals(shouhou.getIshuishou()) && TuihuanKindEnum.TK == tuihuanKindEnum){
            validTuiKindEnum = TuihuanKindEnum.TK_LP;
        }
        boolean isNeedValid = SpringUtil.getBean(RefundCommonService.class).isNeedValid(machineFormVo.getSubId(), machineFormVo.getShouhouId(), machineFormVo.getValidtWay(), validTuiKindEnum);
        boolean isNotNeedValid = !isNeedValid;
        Assert.isTrue(isNotNeedValid || Objects.nonNull(machineFormVo.getValidtWay()),"验证类型不能为空");
        Assert.isTrue(isNotNeedValid || Objects.nonNull(machineFormVo.getValidtWayEnum()),"不支持验证类型{}", machineFormVo.getValidtWay());
        if(isNeedValid){
            R<Boolean>  validtR = SpringUtil.getBean(BaseValidMemberService.class).valid(LambdaBuild.create(new ValidReq<RefundValidVo>())
                    .set(ValidReq::setValidCode, machineFormVo.getValidtCode()).set(ValidReq::setValidType, machineFormVo.getValidtWay())
                    .set(ValidReq::setBusinessType, BusinessTypeV1Enum.REFUND_MONEY.getCode()).set(ValidReq::setOrderId, Convert.toLong(machineFormVo.getSubId()))
                    .set(ValidReq::setT,new RefundValidVo().setShouhouId(machineFormVo.getShouhouId()).setTuihuanKind(validTuiKindEnum.getCode())).build());
            if(!validtR.isSuccess()){
                return R.error(ResultCode.NEED_VERIFY,validtR.getUserMsg());
            }
        }
        return R.success(null);
    }

    private R<Integer> assertCheckSaveAndSet(RefundMachineFormVo machineFormVo) {
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, machineFormVo.getTuihuanKind());
        setSaveDefault(machineFormVo);
        //设置退款类型
        machineFormVo.setTuihuanKindEnum(tuihuanKindEnum);
        Assert.isTrue(Objects.nonNull(tuihuanKindEnum),"不支持{}退款类型", machineFormVo.getTuihuanKind());
        Assert.isFalse(StrUtil.isBlank(machineFormVo.getFaultType()),"故障类型不能为空");
        Assert.isFalse(StrUtil.isBlank(machineFormVo.getCheckType()),"审核类型不能为空");
        Assert.isFalse(TuihuanKindEnum.HQTXH == tuihuanKindEnum && ObjectUtil.defaultIfNull(machineFormVo.getNewBasketId(),0)<=0,"{}所换订单号不能为空",tuihuanKindEnum.getMessage());
        Assert.isFalse(TuihuanKindEnum.HJT == tuihuanKindEnum && ObjectUtil.defaultIfNull(machineFormVo.getNewBasketId(),0)<=0,"{}所换机型mkc_id不能为空",tuihuanKindEnum.getMessage());
        //设置tuikuanM1的值
        if (TuihuanKindEnum.HQTXH == machineFormVo.getTuihuanKindEnum()){
            machineFormVo.setTuikuanM1(machineFormVo.getRefundPrice());
        }else{
            machineFormVo.setTuikuanM1(machineFormVo.getTuikuanM());
        }

        Shouhou shouhou = refundMachineService.getShouhouById(machineFormVo.getShouhouId());
        Assert.notNull(shouhou,"售后单号[{}]错误",machineFormVo.getShouhouId());
        Assert.isTrue(Boolean.TRUE.equals(shouhou.getXianshi()),"维修单已删除");
        machineFormVo.setShouhou(shouhou);
        AreaInfo shouhouAreaInfo = getShouhouAreaInfo(shouhou);
        machineFormVo.setShouhouAreaInfo(shouhouAreaInfo);
        machineFormVo.setCurrUser(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId());
        //校验退款地区
        if (ObjectUtil.notEqual(machineFormVo.getCurrUser().getAreaId(),machineFormVo.getShouhouAreaInfo().getId())){
            return R.error(StrUtil.format("地区不符，请切换至{}再操作！",shouhouAreaInfo.getArea()));
        }
        BuyPriceSubInfoBo buyPriceSubInfo = refundMachineService.getBuyPriceAndSubInfo(machineFormVo.getSubId(), shouhou, tuihuanKindEnum);
        machineFormVo.setBuyPriceSubInfo(buyPriceSubInfo);
        Assert.isFalse(buyPriceSubInfo == null,"订单信息不能为空,订单或者库存状态不对");
        Assert.notNull(machineFormVo.getTuikuanM(),"tuikuanM不能为空");
        //设置购买门店信息
        machineFormVo.setBuyAreaInfo(getAreaInfo(buyPriceSubInfo.getAreaId()).orElseThrow(()-> new CustomizeException("购买门店信息不能为空")));
        Assert.isFalse(machineFormVo.getRefundPrice().compareTo(BigDecimal.ZERO)<0,"退款金额必须大于等于0");
        Assert.isFalse(Optional.ofNullable(machineFormVo.getPeizhiPrice()).orElse(BigDecimal.ZERO).compareTo(new BigDecimal("200.00"))>0,"附件折价金额不能大于200");
        Assert.isTrue(machineFormVo.getRefundPrice().compareTo(buyPriceSubInfo.getTotalPrice())<=0,"退款金额不能大于{}", buyPriceSubInfo.getTotalPrice().setScale(2, RoundingMode.HALF_DOWN));
        ShouhouTuiHuanPo shouhouTuiHuanPo = getShouhouTuihuanByShouhouId(machineFormVo.getShouhouId());
        Assert.isNull(shouhouTuiHuanPo,"已经存在退款记录!");
        //存在其他退款记录
        shouhouTuiHuanPo = refundMachineService.getShouhouTuihuanBySubId(machineFormVo.getSubId());
        Assert.isNull(shouhouTuiHuanPo,"已经存在其他退款记录,批量/售后单号: {}",Optional.ofNullable(shouhouTuiHuanPo)
                .map(ShouhouTuiHuanPo::getShouhouId).orElse(0));
        OaUserBO oaUserBO = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        machineFormVo.setCurrUser(oaUserBO);
        AreaInfoClient areaInfoClient = SpringUtil.getBean(AreaInfoClient.class);
        machineFormVo.setCurrAreaInfo(Optional.ofNullable(areaInfoClient.getAreaInfoById(oaUserBO.getAreaId()))
                .map(R::getData).orElseThrow(()->new CustomizeException("获取当前门店信息异常")));
        machineFormVo.setValidtWayEnum(EnumUtil.getEnumByCode(ValidMemberType.class,machineFormVo.getValidtWay()));
        boolean isTkOrHqtxh = Stream.of(TuihuanKindEnum.TK, TuihuanKindEnum.HQTXH).anyMatch(tk -> tk == tuihuanKindEnum);
        boolean isNotHuiShou = Objects.equals(NumberConstant.ZERO, shouhou.getIshuishou());
        Assert.isFalse(
                isTkOrHqtxh
                // 加盟店
                && Stream.of(AreaKindEnum.SELF,AreaKindEnum.SMALL).map(AreaKindEnum::getCode).noneMatch(kind -> Objects.equals(kind,machineFormVo.getBuyAreaInfo().getKind1()))
                // 购买门店不相等
                && !Objects.equals(machineFormVo.getBuyAreaInfo().getId(),machineFormVo.getShouhouAreaInfo().getId())
                // 新机单
                && isNotHuiShou,"加盟店购机退款或换其它型号操作只可在购买地区进行!");
        Assert.isFalse(!isNotHuiShou && isTkOrHqtxh && !Objects.equals(machineFormVo.getShouhouAreaInfo().getKind1(),NumberConstant.ONE),
                "良品退款需发回总部检测并由总部进行退款操作，请做好客户解释工作，如有特殊情况及时联系良品组负责人为客户处理问题!");
        boolean isGroupRefund = isGroupRefund(machineFormVo.getTuihuanKind());
        Assert.isFalse(isGroupRefund && machineFormVo.getTuihuanFormVo() == null, "当前退款类型组合退信息不能为空");
        Assert.isFalse(isTkOrHqtxh && ObjectUtil.defaultIfNull(machineFormVo.getPiaoType(),0)>0
                && ObjectUtil.defaultIfNull(machineFormVo.getPiaoInfo(),0) == 0, "请选择发票是否收回");
        Assert.isFalse(isTkOrHqtxh && machineFormVo.getPiaoInfo() == PiaoInfoEnum.WEISHOUHUI.getCode()
                && ObjectUtil.defaultIfNull(machineFormVo.getPiaoPrice(),new BigDecimal("-1")).compareTo(BigDecimal.ZERO)<=0, "请输入发票折价金额");
        //设置组合退必须要的值
        if(isGroupRefund){
            GroupTuihuanFormVo tuihuanFormVo = machineFormVo.getTuihuanFormVo();
            tuihuanFormVo.setRefundPrice(machineFormVo.getRefundPrice());
            tuihuanFormVo.setComment(machineFormVo.getComment());
            tuihuanFormVo.setTuikuanM1(machineFormVo.getTuikuanM1());
            tuihuanFormVo.setTuihuanKind(machineFormVo.getTuihuanKind());
            tuihuanFormVo.setValidtCode(machineFormVo.getValidtCode());
            tuihuanFormVo.setValidtWay(machineFormVo.getValidtWay());
            tuihuanFormVo.setSubId(machineFormVo.getSubId());
            tuihuanFormVo.setShouhouId(machineFormVo.getShouhouId());
        }

        return R.success(null);
    }

    /**
     * 设置保存默认值
     * @param machineFormVo
     */
    private void setSaveDefault(RefundMachineFormVo machineFormVo) {
        //设置保存默认值
        if (ObjectUtil.defaultIfNull(machineFormVo.getNewBasketId(), 0) <= 0) {
            machineFormVo.setNewBasketId(null);
        }

    }

    private R<Integer> assertCheckSaveAndSet2(RefundMachineFormVo machineFormVo) {
        OaUserBO currUser = machineFormVo.getCurrUser();
        Shouhou shouhou = machineFormVo.getShouhou();
        HuanMkcBo huanMkc = refundMachineMapper.getHuanMkc(currUser.getUserId(), shouhou.getMkcId(), shouhou.getImei(),
                shouhou.getIshuishou(), shouhou.getSubId());
        //默认买家等于totalPrice
        machineFormVo.setBuypriceM(machineFormVo.getTotalPrice());
        if (Objects.nonNull(huanMkc)) {
            machineFormVo.setBasketId(huanMkc.getBasketId());
            machineFormVo.setBuypriceM(huanMkc.getBuyPrice());
            machineFormVo.setInprice(huanMkc.getInPrice());
        }
        // 退换类别
        TuihuanKindEnum tuihuanKindEnum = machineFormVo.getTuihuanKindEnum();
        if (Stream.of(TuihuanKindEnum.HQTXH,TuihuanKindEnum.TK).anyMatch(tk -> Objects.equals(tuihuanKindEnum,tk))) {
            // 购买金额
            BigDecimal tuihuanBuyPrice = machineFormVo.getBuypriceM();
            // 退款金额
            BigDecimal tuikuanPrice = machineFormVo.getRefundPrice()
                    .add(machineFormVo.getPeizhiPrice())
                    .add(machineFormVo.getPiaoPrice());
            if (tuihuanBuyPrice.compareTo(BigDecimal.ZERO) > 0
                    && tuikuanPrice.compareTo(tuihuanBuyPrice) > 0) {

                return R.error(StrUtil.format("退款金额大于全款!{}", tuihuanBuyPrice));
            }
            if(tuikuanPrice.compareTo(machineFormVo.getTuikuanM())>0){
                return R.error(StrUtil.format("退款金额不能大于可退金额{}!", machineFormVo.getTuikuanM()));
            }
            if(TuihuanKindEnum.TK.getCode().equals(machineFormVo.getTuihuanKind())
                    && refundMachineMapper.getYouHuiSmallSubId(shouhou.getBasketId()) != null){
                // 先校验配件是否已退
                return R.error(PrioritySmallRefundEnum.PEI_JIAN_JUAN.getReason());
            }
            TuihuanKindEnum thRealEnum = getRealKindEnum(machineFormVo.getTuihuanKindEnum(), machineFormVo.getShouhou().getIshuishou());
            ZheJiaPayEnum zheJiaPayEnum = zheJiaPayService.getZheJiaPayEnum(machineFormVo.getShouhou().getSubId(), thRealEnum);
            Optional<RecoverSub> huiShouPriceOpt = Optional.ofNullable(shouhou.getBasketId())
                    .map(refundMachineMapper::selectHuiShouPriceByBasketId)
                    .filter(rs -> NumberUtil.null2Zero(rs.getPrices()).compareTo(BigDecimal.ZERO) >0);
            List<Wxkcoutput> payWxkcoutputs;
            if(zheJiaPayEnum != null || huiShouPriceOpt.isPresent()){
                payWxkcoutputs = zheJiaPayService.listPayWxkcoutput(machineFormVo.getShouhouId(),
                        Wxkcoutput.PartTypeEnum.getAllPayCode());
            }else{
                payWxkcoutputs = Collections.emptyList();
            }
            if(zheJiaPayEnum != null){
                BigDecimal totalZhejiaM = machineFormVo.getZhejiaM()
                        .add(NumberUtil.null2Zero(machineFormVo.getPeizhiPrice()))
                        .add(NumberUtil.null2Zero(machineFormVo.getPiaoPrice()));
                if(totalZhejiaM.compareTo(BigDecimal.ZERO) >0 && CollUtil.isEmpty(payWxkcoutputs)){
                    return R.error(StrUtil.format("{}折价金额{}元需要先生成折价费并收银!",
                            zheJiaPayEnum.getMessage(), totalZhejiaM));
                }else if(totalZhejiaM.compareTo(BigDecimal.ZERO) >0){
                    return R.error(StrUtil.format("{}需要增加保值退换折价费用{}元!",
                            zheJiaPayEnum.getMessage(), totalZhejiaM));
                }
            }
            if(huiShouPriceOpt.isPresent()){
                Optional<Wxkcoutput> huanNewBuTiePjOpt = payWxkcoutputs.stream()
                        .filter(item -> Wxkcoutput.PartTypeEnum.HUI_SHOU_ZHE_JIA_PAY.getCode().equals(item.getPartType())).findFirst();
                if(!huanNewBuTiePjOpt.isPresent()){
                    RecoverSub recoverSub = huiShouPriceOpt.get();
                    return R.error(StrUtil.format("回收单{}已使用换新补贴加价券{}元，退款需要全额收回", recoverSub.getSubId(), recoverSub.getPrices()));
                }
            }
            //支付金额校验
            if(CollUtil.isNotEmpty(payWxkcoutputs)){
                BigDecimal yiFum = NumberUtil.null2Zero(machineFormVo.getShouhou().getYifum());
                if (CollUtil.isNotEmpty(payWxkcoutputs)
                        && NumberUtil.null2Zero(machineFormVo.getShouhou().getFeiyong())
                        .compareTo(yiFum) > 0) {
                    BigDecimal needPayPrice = NumberUtil.null2Zero(machineFormVo.getShouhou().getFeiyong())
                            .subtract(NumberUtil.null2Zero(machineFormVo.getShouhou().getYifum()));
                    return R.error(zheJiaPayEnum.format(needPayPrice));
                }
            }
        }
        // 购买门店信息
        AreaInfo buyAreaInfo = machineFormVo.getBuyAreaInfo();
        if (Objects.isNull(buyAreaInfo)) {
            return R.error("购买地区查询失败!");
        }

        if (Objects.nonNull(tuihuanKindEnum)) {
            switch (tuihuanKindEnum) {
                case HJT:
                    if (ObjectUtil.defaultIfNull(machineFormVo.getNewBasketId(),0) != 0) {
                        String mkc2 = huanMKC2kind1(Convert.toLong(machineFormVo.getNewBasketId()), machineFormVo.getShouhouId());
                        if (StringUtils.isNotEmpty(mkc2)) {

                            return R.error(mkc2 + "[" + currUser.getArea() + "]");
                        }
                    }
                    break;
                case HZB:
                    if (ObjectUtil.defaultIfNull(machineFormVo.getNewBasketId(),0) != 0) {
                        HuanMkcSecondBo huanMkc2Bo = basketService.huanMKC2(currUser.getAreaId(), Math.toIntExact(machineFormVo.getNewBasketId()));
                        if (Objects.isNull(huanMkc2Bo)) {
                            return R.error(String.format("mkc_id错误,请检查是否为售后、售后2状态! {}" + currUser.getArea()));
                        }
                    }
                    break;
                case HQTXH:
                    if (!(ObjectUtil.defaultIfNull(machineFormVo.getNewBasketId(),0) >0 &&
                            (machineFormVo.getTuikuanM().compareTo(BigDecimal.ZERO) > 0 || ObjectUtil.defaultIfNull(machineFormVo.getPuhui(),0) == 1))) {

                        return R.error("所换订单号不能为空！");
                    }
                    if (machineFormVo.getTuikuanM1() == null) {
                        machineFormVo.setTuikuanM1(BigDecimal.ZERO);
                    }
                    if (machineFormVo.getTuikuanM().compareTo(machineFormVo.getSubIdm()) < 0) {
                        machineFormVo.setSubIdm(machineFormVo.getTuikuanM());
                    }
                    BigDecimal hqtxhCanPayPrice = getCanChangePriceOpt(shouhou, tuihuanKindEnum,
                            machineFormVo.getTuikuanM1().add(machineFormVo.getSubIdm())).orElse(machineFormVo.getSubIdm());
                    if(machineFormVo.getSubIdm().compareTo(hqtxhCanPayPrice)>0){
                        return R.error(StrUtil.format("金额错误！冲抵金额最大不能超过:{}", hqtxhCanPayPrice));
                    }
                    if (machineFormVo.getTuikuanM().compareTo(machineFormVo.getSubIdm().add(machineFormVo.getTuikuanM1())
                            .add(machineFormVo.getPeizhiPrice()).add(machineFormVo.getPiaoPrice())) != 0) {
                        return R.error(StrUtil.format("金额错误！退款金额: {},冲抵金额:{},门店[{}]",
                                machineFormVo.getTuikuanM1(),machineFormVo.getSubIdm(),currUser.getArea()));
                    }
                    break;
                default:
                    break;
            }
        }
        return R.success(null);
    }

    /**
     * 重构自：oa999DAL\ShouhouTuihuan.cs#HuanMKC2kind1
     *
     * @param basketId
     * @param shouhouId
     * @return
     */
    private String huanMKC2kind1(Long basketId, Integer shouhouId) {
        // dtMkc
        List<BasketMkcBo> basketMkcBos = refundMachineMapper.listBasketMkcByBasketId(basketId);
        if (CollectionUtils.isEmpty(basketMkcBos)) {

            return "查不到该mkc_id为" + basketId + "的数据";
        }
        // dtShou
        List<BasketShouhouBo> basketShouhouBos = refundMachineMapper.listBasketShouhouByShouhouId(shouhouId);
        if (CollectionUtils.isEmpty(basketShouhouBos)) {

            return "查不到该售后id为" + shouhouId + "的数据";
        }
        BasketMkcBo basketMkcBo = basketMkcBos.get(0);
        BasketShouhouBo basketShouhouBo = basketShouhouBos.get(0);
        Integer ppidNew = basketMkcBo.getPpriceid();
        Integer ppidOld = basketShouhouBo.getPpriceid();
        // msg
        String errorMsg = StringUtils.EMPTY;
        if (ObjectUtil.notEqual(ppidNew, ppidOld)) {
            errorMsg += StrUtil.format("ppid[{}]和所换机型ppid[{}]不一致；",ppidOld,ppidNew);
        }
        // kc_check
        Integer kcCheck = basketMkcBo.getKcCheck();
        if (kcCheck != 3 && kcCheck != 6) {
            errorMsg += "所换机型库存状态必须为[库存]或[售后]；";
        }
        // area
        Integer shouhouAreaId = basketShouhouBo.getAreaId();
        Integer newAreaId = basketShouhouBo.getNewAreaId();
        Integer mkcAreaId = basketMkcBo.getAreaId();
        if (!(shouhouAreaId.equals(newAreaId) && newAreaId.equals(mkcAreaId) && shouhouAreaId.equals(mkcAreaId))) {
            errorMsg += "库存当前所在地区必须与接件地、库存地当前所在地区三个地区一致；";
        }
        return errorMsg;
    }

    /**
     * 重构自：oa999DAL\ShouhouTuihuan.cs#BeginTuihuan
     *
     * @param currUser
     * @param tuihuan
     * @param basketId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Tuple2<Boolean, String> beginTuihuan(OaUserBO currUser, RefundMachineFormVo tuihuan, Integer basketId) {
        String tuihuanErrorMessage = StringUtils.EMPTY;
        Boolean stats = false;
        Integer tuihuanKind = tuihuan.getTuihuanKind();
        /*List<Integer> youhuiPpriceids = shouhouTuihuanService.aliPayYouhuiPpriceids();
        // 退款 支付宝优惠码效验处理
        if (tuihuanKind == 3 && CollectionUtils.isNotEmpty(youhuiPpriceids)) {
            // 支付宝优惠码的必须原路径退款
            Integer subId = shouhouExService.queryAlipayYouhuiSubId(tuihuan.getShouhouId(), youhuiPpriceids);
            if (Objects.nonNull(subId) && !Objects.equals(tuihuan.getTuiWay(), "支付宝(pay1)返回")) {
                tuihuanErrorMessage = "此订单只能以【支付宝(pay1)返回】退款！";
                return Tuples.of(false, tuihuanErrorMessage);
            }
        }*/
        // ishuishou
        Shouhou shouhou = tuihuan.getShouhou();
        Integer isHuishou = IntConstant.ZERO;
        Integer subId = null;
        if (Objects.nonNull(shouhou)) {
            isHuishou = shouhou.getIshuishou();
            subId = shouhou.getSubId();
        }
        // 这里是校验交易号的
        //List<NetPayModelBo> netList = new ArrayList<>();
        /*if (TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind)) {
            boolean isPayOnline = PayConstants.checkOnlinePay(tuihuan.getTuiWay());
            if (isPayOnline && StringUtils.isBlank(tuihuan.getNetPay())) {
                tuihuanErrorMessage = "原路径退款请先确定交易号！";
                return Tuples.of(false, tuihuanErrorMessage);
            }
            if (StringUtils.isNotBlank(tuihuan.getNetPay()) && isPayOnline) {
                netList = JSON.parseArray(tuihuan.getNetPay(), NetPayModelBo.class);
            }
            if (CollectionUtils.isEmpty(netList)) {
                tuihuanErrorMessage = "交易号查找失败！";
                return Tuples.of(false, tuihuanErrorMessage);
            }
            for (int i = 0; i < netList.size(); i++) {
                NetPayModelBo netPayModel = netList.get(i);
                if (i == 0) {
                    BigDecimal addPrice = tuihuan.getPeizhiPrice()
                            .add(tuihuan.getPiaoPrice());
                    if (addPrice.compareTo(BigDecimal.ZERO) > 0) {
                        netPayModel.setPrice(netPayModel.getPrice().subtract(addPrice));
                    }
                }
                NetpayRecord netpayRecord = netpayRecordService.getNetpayRecordByTypeAndId(netPayModel.getId(), isHuishou == 0 ? 1 : 3);
                if (Objects.isNull(netpayRecord)) {
                    tuihuanErrorMessage = "交易号查找失败！";
                    return Tuples.of(false, tuihuanErrorMessage);
                }
                if (netPayModel.getPrice()
                        .compareTo(netpayRecord.getMoney().subtract(netpayRecord.getRefundPrice())) > 0) {
                    tuihuanErrorMessage = "交易号[" + netpayRecord.getTradeNo() + "]退款金额不能大于" + (netpayRecord.getMoney().subtract(netpayRecord.getRefundPrice())) + "！";
                    return Tuples.of(false, tuihuanErrorMessage);
                }
            }*/
            /*BigDecimal tuikuanMoney = netList.stream()
                    .map(netPay -> netPay.getPrice()
                            .add(tuihuan.getPeizhiPrice())
                            .add(tuihuan.getPiaoPrice()))
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            if (tuikuanMoney.compareTo(tuihuan.getRefundPrice()) != 0) {
                tuihuanErrorMessage = "退款金额与原路径退款金额不一致，请重试！";
                return Tuples.of(false, tuihuanErrorMessage);
            }
            BigDecimal tuikuanMoney1 = tuihuan.getTuikuanM1()
                    .add(tuihuan.getPeizhiPrice())
                    .add(tuihuan.getPiaoPrice());
            tuihuan.setTuikuanM1(tuikuanMoney1);
        }*/

        Integer thId = 0;
        List<AddLogReq> mkcLogs = new ArrayList<>();
        if (TuihuanKindEnum.HJT.getCode().equals(tuihuanKind)
                || TuihuanKindEnum.HZB.getCode().equals(tuihuanKind)) {
            ShouhouTuihuan addtTuihuan = new ShouhouTuihuan();
            addtTuihuan.setShouhouId(tuihuan.getShouhouId());
            addtTuihuan.setTuihuanKind(tuihuanKind);
            addtTuihuan.setInuser(currUser.getUserName());
            addtTuihuan.setAreaid(currUser.getAreaId());
            addtTuihuan.setComment(tuihuan.getComment());
            addtTuihuan.setPiaoInfo(tuihuan.getPiaoInfo());
            addtTuihuan.setCtype(tuihuan.getCType());
            addtTuihuan.setIncludeChecklist(tuihuan.getIncludeChecklist());
            addtTuihuan.setFaultType(tuihuan.getFaultType());
            addtTuihuan.setCheckType(tuihuan.getCheckType());
            addtTuihuan.setTradeType(tuihuan.getTradeType());
            addtTuihuan.setTradeDate(tuihuan.getTradeDate());
            addtTuihuan.setBuypriceM(tuihuan.getBuypriceM());
            addtTuihuan.setBasketId(Convert.toLong(tuihuan.getNewBasketId()));
            addtTuihuan.setTuiKinds(ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode());
            insertTuihuan(addtTuihuan);
            tuihuan.setTuihuanId(addtTuihuan.getId());
            thId = tuihuan.getTuihuanId();
            if (TuihuanKindEnum.HJT.getCode().equals(tuihuanKind)) {
                refundMachineService.mkcDellogsh1(currUser, Convert.toLong(tuihuan.getNewBasketId()), tuihuan.getShouhouId());
            }
            // 记录操作日志
            AddLogReq addLogReq = new AddLogReq();
            addLogReq.setInUser(currUser.getUserName());
            addLogReq.setId(Math.toIntExact(tuihuan.getNewBasketId()));
            addLogReq.setShowAll(false);
            addLogReq.setComment("售后换机头转入售后操作(" + tuihuan.getShouhouId() + ")");
            mkcLogs.add(addLogReq);
            stats = true;
        } else if (Stream.of(TuihuanKindEnum.TK,TuihuanKindEnum.HQTXH).anyMatch(tk -> ObjectUtil.equal(tuihuan.getTuihuanKindEnum(),tk))) {
            // 如果是退款或 换其他 检测手机原订单是否有赠品 如果有赠品 原始订单赠品做提交退款操作
            String result = refundMachineService.submitTuiEx(tuihuan.getShouhouId(), currUser.getUserName(), tuihuanKind,currUser.getAreaId(), tuihuan.getGiftBasketList());
            if (!Objects.equals(result, "1")) {
                throw new CustomizeException(result);
            }
            stats = true;
        }

        /*if (TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind) && !isGroupRefund(tuihuan.getTuihuanKind(),tuihuan.getRefundPrice())) {
            ShouhouTuihuan addtTuihuan = new ShouhouTuihuan();
            addtTuihuan.setShouhouId(tuihuan.getShouhouId());
            addtTuihuan.setTuihuanKind(tuihuanKind);
            addtTuihuan.setInuser(currUser.getUserName());
            addtTuihuan.setAreaid(currUser.getAreaId());
            addtTuihuan.setComment(tuihuan.getComment());
            //换其他型号 subId 为所换的订单号
            addtTuihuan.setSubId(tuihuan.getNewBasketId());
            addtTuihuan.setTuikuanM(tuihuan.getTuikuanM());
            addtTuihuan.setTuikuanM1(tuihuan.getTuikuanM1());
            addtTuihuan.setSubIdm(tuihuan.getSubIdm());
            addtTuihuan.setZhejiaM(tuihuan.getZhejiaM());
            addtTuihuan.setBuypriceM(tuihuan.getBuypriceM());
            addtTuihuan.setInprice(tuihuan.getInprice());
            addtTuihuan.setSalenm(null);
            addtTuihuan.setTuiWay(null);
            addtTuihuan.setBankname(null);
            addtTuihuan.setBankfuming(null);
            addtTuihuan.setBanknumber(null);
            addtTuihuan.setPiaoInfo(tuihuan.getPiaoInfo());
            addtTuihuan.setCtype(tuihuan.getCType());
            addtTuihuan.setCoinM(tuihuan.getCoinM());
            addtTuihuan.setIncludeChecklist(tuihuan.getIncludeChecklist());
            addtTuihuan.setBaitiaoM(tuihuan.getBaitiaoPrice());
            addtTuihuan.setKuBaiTiaoM(tuihuan.getKuBaitiaoPrice());
            addtTuihuan.setIsValidt(null);
            addtTuihuan.setPeizhi(tuihuan.getPeizhi());
            addtTuihuan.setPeizhiPrice(tuihuan.getPeizhiPrice());
            addtTuihuan.setPiaoPrice(tuihuan.getPiaoPrice());
            addtTuihuan.setPiaoType(tuihuan.getPiaoType());
            addtTuihuan.setFaultType(tuihuan.getFaultType());
            addtTuihuan.setCheckType(tuihuan.getCheckType());
            addtTuihuan.setTradeType(tuihuan.getTradeType());
            addtTuihuan.setTradeDate(tuihuan.getTradeDate());
            addtTuihuan.setTuiKinds(ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode());
            insertTuihuan(addtTuihuan);
            thId = addtTuihuan.getId();
            stats = true;
        }*/

        /*
        else if (TuihuanKindEnum.TK.getCode().equals(tuihuanKind)) {
            ShouhouTuihuan addtTuihuan = new ShouhouTuihuan();
            if (Objects.nonNull(tuihuan.getPuhui())
                    && tuihuan.getPuhui() == 1) {
                addtTuihuan.setPuhuim(tuihuan.getPuhuim());
            }
            addtTuihuan.setShouhouId(tuihuan.getShouhouId());
            addtTuihuan.setTuihuanKind(tuihuanKind);
            addtTuihuan.setInuser(currUser.getUserName());
            addtTuihuan.setAreaid(currUser.getAreaId());
            addtTuihuan.setComment(tuihuan.getComment());
            addtTuihuan.setTuiWay(tuihuan.getTuiWay());
            addtTuihuan.setBankname(tuihuan.getBankname());
            addtTuihuan.setBankfuming(tuihuan.getBankfuming());
            addtTuihuan.setBanknumber(tuihuan.getBanknumber());
            addtTuihuan.setTuikuanM(tuihuan.getTuikuanM());
            addtTuihuan.setTuikuanM1(tuihuan.getTuikuanM1());
            addtTuihuan.setZhejiaM(tuihuan.getZhejiaM());
            addtTuihuan.setBuypriceM(tuihuan.getBuypriceM());
            addtTuihuan.setInprice(tuihuan.getInprice());
            addtTuihuan.setSalenm(tuihuan.getSalenm());
            addtTuihuan.setPiaoInfo(tuihuan.getPiaoInfo());
            addtTuihuan.setCtype(tuihuan.getCtype());
            addtTuihuan.setCoinM(tuihuan.getCoinM());
            addtTuihuan.setIncludeChecklist(tuihuan.getIncludeChecklist());
            addtTuihuan.setBaitiaoM(tuihuan.getBaitiaoM());
            addtTuihuan.setKuBaiTiaoM(tuihuan.getKuBaiTiaoM());
            addtTuihuan.setIsValidt(tuihuan.getIsValidt());
            addtTuihuan.setPeizhi(tuihuan.getPeizhi());
            addtTuihuan.setPeizhiPrice(tuihuan.getPeizhiPrice());
            addtTuihuan.setFaultType(tuihuan.getFaultType());
            addtTuihuan.setCheckType(tuihuan.getCheckType());
            addtTuihuan.setTradeType(tuihuan.getTradeType());
            addtTuihuan.setTradeDate(tuihuan.getTradeDate());
            addtTuihuan.setBasketId(Long.valueOf(basketId));

            insertTuihuan(addtTuihuan);
            thId = addtTuihuan.getId();
            if (Objects.nonNull(thId) && thId > 0) {
                if (CollectionUtils.isNotEmpty(netList)) {
                    netList.forEach(netPay -> {
                        insertNetPayRefundInfo(netPay.getId(), netPay.getPrice(), currUser.getUserName(), thId);

                        Integer count = netpayRecordService.updateRefundPrice(netPay.getPrice(), netPay.getId());
                        if (count <= 0) {
                            throw new CustomizeException("支付金额退款错误");
                        }
                    });
                }
            }
            try {
                // 如果是退款或 换其他 检测手机原订单是否有赠品 如果有赠品 原始订单赠品做提交退款操作
                String result = new shouhouEx().submitTuiEx(thmodel.shouhou_id, myUser, thmodel.tuihuan_kind, cmd, myAreaid, thmodel.giftBasketid);
                if (!Objects.equals(result, "1")) {

                    throw new CustomizeException(result);
                }
                stats = true;
            } catch (Exception ex) {
                tuihuanErrorMessage = ex.getMessage();
            }
        }*/
        // 如果是外包仓退款
        boolean isOutDepot = sysConfigService.isOutDepot(currUser.getAreaId(), currUser.getXTenant());
        if (stats && tuihuanKind == 3 && isOutDepot) {
            Shouhou shouhouDetail = shouhouService.getById(tuihuan.getShouhouId());

            OutDepotRefundPostBo outDepotRefundBo = new OutDepotRefundPostBo();
            outDepotRefundBo.setShouhouSubId(tuihuan.getShouhouId().toString());
            outDepotRefundBo.setAreaid(currUser.getAreaId());
            outDepotRefundBo.setWarehouseCode(currUser.getArea());
            outDepotRefundBo.setIsmobile(true);

            OutDepotRefundPostBo.ProductItem productItem = new OutDepotRefundPostBo.ProductItem();
            productItem.setPpid(shouhouDetail.getPpriceid());
            productItem.setQuantity(1);

            List<OutDepotRefundPostBo.ProductItem> items = new ArrayList<>();
            items.add(productItem);
            outDepotRefundBo.setItems(items);

            // data
            Map<String, Object> map = new HashMap<>();
            map.put("act", "OutDepotRefundAdd");
            map.put("data", outDepotRefundBo);
            String jsonData = JSONUtil.toJsonStr(map);
            // 发送mq
            smsService.oaRabbitMQWorkQueue(jsonData, RabbitMqConfig.QUEUE_TOPIC_OAASYNC);
        }
        if (CollectionUtils.isNotEmpty(mkcLogs)) {
            SpringUtil.getBean(MkcLogClient.class).batchAdd(mkcLogs);
        }
        if (Objects.nonNull(thId) && thId > 0) {
            // 自动审核
            shouhouTuihuanService.autoTuihuanCheck(thId);
            tuihuan.setTuihuanId(thId);
        }
        return Tuples.of(stats, StringUtils.EMPTY);
    }

    /**
     * 提交售后退换（赠品退货） 扩展 带事务
     *
     * @param shouhouId 售后ID
     * @param inUser 操作人
     * @param tuihuanKind 退款类别 3、退款 4、换其它型号
     * @param areaId 门店
     * @param giftBasketList 赠品basketId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submitTuiEx(Integer shouhouId, String inUser, Integer tuihuanKind,
                              Integer areaId, List<TuiGiftBasket> giftBasketList) {
        if (CollUtil.isEmpty(giftBasketList)) {
            return "1";
        }
        List<Integer> giftBasketIds= giftBasketList.stream().map(TuiGiftBasket::getBasketId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(giftBasketIds)) {
            return "1";
        }
        if (TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind)
                || TuihuanKindEnum.TK.getCode().equals(tuihuanKind)) {
            ShouhouSubDetailBo shouhouDetailBo = shouhouExService.loadSubInfoByShouhouid(shouhouId);
            if (shouhouDetailBo != null) {
                // 判断原始订单是否有赠品
                List<GiftBasketBo> giftBasketBos = basketService.listGiftBasketByBasketIds(shouhouDetailBo.getBasketId(), giftBasketIds);
                List<GiftBasketBo> smallGiftBasketBos = giftBasketBos.stream()
                        .filter(gb -> !Boolean.TRUE.equals(gb.getIsMobile())).collect(Collectors.toList());
                //提交赠品退货申请
                if (CollectionUtils.isNotEmpty(smallGiftBasketBos)) {
                    // group by subId
                    Map<Integer, List<GiftBasketBo>> groupBySubId = smallGiftBasketBos.stream()
                            .collect(Collectors.groupingBy(GiftBasketBo::getSubId));
                    for (Map.Entry<Integer, List<GiftBasketBo>> entry : groupBySubId.entrySet()) {
                        Integer subId = entry.getKey();
                        List<GiftBasketBo> baskets = entry.getValue();
                        // 判断原始订单是否已提交并且未处理的
                        Integer shthId = shouhouTuihuanService.getShThBySubId(subId);
                        if (Objects.nonNull(shthId)) {
                            return "订单[" + subId + "]所关联赠品已提交退货申请，请先取消！";
                        }
                        // 名称集合
                        String pNames = baskets.stream()
                                .map(p -> p.getProductName() + " ")
                                .reduce((a, b)-> a + b)
                                .orElse(StringUtils.EMPTY);
                        Smallpro smallpro = new Smallpro();
                        smallpro.setUserId(shouhouDetailBo.getUserId());
                        smallpro.setSubId(subId);
                        smallpro.setName(pNames);
                        smallpro.setAreaId(areaId);
                        smallpro.setGroupId(1);
                        smallpro.setIsBaoxiu(true);
                        smallpro.setUserName(shouhouDetailBo.getSubTo());
                        smallpro.setMobile(shouhouDetailBo.getSubMobile());
                        smallpro.setKind(3);
                        smallpro.setProblem("大件退款，相应赠品自动退款操作，售后单:" + shouhouId);
                        //设置购买日期
                        baskets.stream().findFirst().ifPresent(first -> smallpro.setBuyDate(first.getTradeDate()));
                        // 新增小件单
                        insertSmallPro(smallpro);
                        Integer spId = smallpro.getId();
                        if (Objects.isNull(spId)) {
                            return "0";
                        }
                        //插入小件详情信息
                        for (GiftBasketBo basket : baskets) {
                            refundMachineService.insertSmallProBill(spId, basket.getBasketId(), basket.getPpriceid(), basket.getBasketCount());
                        }

                        // 插入售后退款记录
                        ShouhouTuihuan addTuihuan = new ShouhouTuihuan();
                        addTuihuan.setShouhouId(shouhouId);
                        addTuihuan.setTuihuanKind(7);
                        addTuihuan.setTuikuanM(BigDecimal.ZERO);
                        addTuihuan.setTuikuanM1(BigDecimal.ZERO);
                        addTuihuan.setSubId(subId);
                        //addTuihuan.setTuiWay("余额");
                        addTuihuan.setComment("大件退款，相应赠品退款操作，售后单：" + shouhouId);
                        addTuihuan.setInuser(inUser);
                        addTuihuan.setIsdel(false);
                        addTuihuan.setZhejiaM(BigDecimal.ZERO);
                        addTuihuan.setInprice(BigDecimal.ZERO);
                        addTuihuan.setAreaid(shouhouDetailBo.getAreaId());
                        addTuihuan.setSmallproid(spId);
                        addTuihuan.setCheck2(true);
                        addTuihuan.setCheck2user("系统");
                        addTuihuan.setIszengpin(true);
                        addTuihuan.setTuiKinds(ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode());
                       insertTuihuan(addTuihuan);
                        Integer thId = addTuihuan.getId();
                        //插入组合退的详情信息
                        SpringUtil.getBean(ShouhouTuihuanDetailService.class).save(LambdaBuild.create(new ShouhouTuihuanDetailPo())
                        .set(ShouhouTuihuanDetailPo::setFkTuihuanId,thId).set(ShouhouTuihuanDetailPo::setRefundPrice,BigDecimal.ZERO)
                                .set(ShouhouTuihuanDetailPo::setTuiWay,ShouhouRefundService.YU_E).set(ShouhouTuihuanDetailPo::setTuiGroup,TuiGroupEnum.OTHER_REFUND.getCode())
                                .set(ShouhouTuihuanDetailPo::setCreateTime,LocalDateTime.now()).set(ShouhouTuihuanDetailPo::setCreateUser,inUser)
                                .set(ShouhouTuihuanDetailPo::setUpdateTime,LocalDateTime.now()).set(ShouhouTuihuanDetailPo::setIsDel,Boolean.FALSE)
                                .build());
                        if (Objects.nonNull(thId)) {
                            for (GiftBasketBo gift : baskets) {
                                insertReturnsDetail(thId, gift.getBasketId(), gift.getBasketCount());
                            }
                        }
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"相应赠品退款操作,小件id[{}]",spId);
                    }
                }
            }
        }
        return "1";
    }

    /**
     * 新增退换记录
     *
     * @param tuihuan 退换数据
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertTuihuan(ShouhouTuihuan tuihuan) {

        return refundMachineMapper.insertTuihuan(tuihuan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> cancelRefund(Integer tuihuanId, String mark) {
        if (ObjectUtil.defaultIfNull(tuihuanId,0) <= 0){
            return R.error("退换id不能为空");
        }
        ShouhouTuiHuanPo tuiHuanPo = SpringUtil.getBean(TuiHuanService.class).getById(tuihuanId);
        R<Boolean> tuihuanDelR = SpringUtil.getBean(ShouhouBigProTuihuanService.class).groupTuihuanDel(tuihuanId, mark);
        if(tuihuanDelR.isSuccess() && Stream.of(TuihuanKindEnum.TK,TuihuanKindEnum.HQTXH)
                .anyMatch(tk -> Objects.equals(tk.getCode(),tuiHuanPo.getTuihuanKind()))){
            //组合退的数据处理
            List<ShouhouTuihuanDetailPo> tuihuanDetailPos = SpringUtil.getBean(RefundCommonService.class)
                    .listTuihuanDetail(tuihuanId, null,true);
            //按分组撤销各分组的数据
            RefundMoneyService.forEachRefundInvoke(refundService -> refundService.cancelRefund(tuihuanId,tuihuanDetailPos));
            TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuiHuanPo.getTuihuanKind());
            //撤销详情信息
            int ctdUpdateNum = SpringUtil.getBean(ShouhouRefundMoneyMapper.class).cancelTuihuanDetail(tuihuanId);
            if (ctdUpdateNum < tuihuanDetailPos.stream().filter(thd -> ObjectUtil.notEqual(Boolean.TRUE,thd.getIsDel())).count()) {
                SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan("组合退款退款id[{}]撤销失败,原因: 组合退详情更新异常",tuihuanId);
            }
        }
        return tuihuanDelR;
    }

    @Override
    public R<Integer> submitCheck(TuiHuanCheckVo tuiHuanCheckVo) {
        OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();

        try {
            R<Integer> checkR = checkSubmitCheckAndSet(tuiHuanCheckVo,oaUser);
            if (!checkR.isSuccess()){
                return checkR;
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        ShouhouRefundDetailVo.ProcessStatus processStatus = tuiHuanCheckVo.getProcessStatusEnum();
        if(processStatus == ShouhouRefundDetailVo.ProcessStatus.CHECK3){
            if(Stream.of(TuihuanKindEnum.TK,TuihuanKindEnum.HQTXH).anyMatch(tk -> tk == tuiHuanCheckVo.getTuihuanKindEnum())){
                //退款办理操作 单独事务进行提交
                MultipleTransaction.build().execute(DataSourceConstants.DEFAULT,()->{
                    //根据退款类型不同来处理
                    R<Integer> checkR = BaseTuiHuanKindService.getBean(tuiHuanCheckVo.getTuihuanKindEnum()).checkSubmitCheckAndSet(tuiHuanCheckVo);
                    if(!checkR.isSuccess()){
                        //校验未通过,中断操作
                        throw new CustomizeException(checkR);
                    }
                    ShouhouRefundDetailVo.ProcessStatus rankProcessStatus = BaseTuiHuanKindService.getBean(tuiHuanCheckVo.getTuihuanKindEnum()).processStatusRank(processStatus,
                            tuiHuanCheckVo.getTuiHuanPo(), tuiHuanCheckVo.getTuihuanDetailPos());
                    if(rankProcessStatus == ShouhouRefundDetailVo.ProcessStatus.NOT_ALLOW_WORK){
                        throw new CustomizeException(StrUtil.format("你没有{}的权限(提示: {})", processStatus.getMessage(),
                                SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.NOT_WORK_RANK).stream().collect(Collectors.joining(StringPool.SPACE))));
                    }
                    RefundMoneyService.forEachRefundInvoke(refundService -> refundService.updateCheck(tuiHuanCheckVo));
                }).commit();
            }
            R<Integer> check3R = SpringUtil.getBean(CsharpReturnService.class).refundMachineTransact(tuiHuanCheckVo.getTuihuanId(), tuiHuanCheckVo.getNewBasketId());
            try {
                refundMachineService.sendNoticeToUser(check3R, tuiHuanCheckVo);
            } catch (Exception e) {
                // 发送通知,不阻塞业务
                RRExceptionHandler.logError("退款发送用户短信通知", tuiHuanCheckVo, e, smsService::sendOaMsgTo9JiMan);
            }
            return check3R;
        }
        switch (tuiHuanCheckVo.getTuihuanKindEnum()){
            case TK:
            case HQTXH:
                return SpringUtil.getBean(RefundMoneyService.class).submitCheck(tuiHuanCheckVo);
            default:
                switch (processStatus){
                    case CHECK1:
                        return SpringUtil.getBean(CsharpReturnService.class).refundMachineCheck1(tuiHuanCheckVo.getTuihuanId());
                    case CHECK2:
                        return SpringUtil.getBean(CsharpReturnService.class).refundMachineCheck2(tuiHuanCheckVo.getTuihuanId(), tuiHuanCheckVo.getTuihuanKindEnum().getCode());

                }
                break;
        }

        return R.success(tuiHuanCheckVo.getTuihuanId());
    }

    /**
     * 发送通知消息给用户
     * @param check3R
     * @param tuiHuanCheckVo
     */
    @Override
    public void sendNoticeToUser(R<Integer> check3R, TuiHuanCheckVo tuiHuanCheckVo) {
        Shouhou shouhou = tuiHuanCheckVo.getShouhou();
        ShouhouTuiHuanPo tuiHuanPo = tuiHuanCheckVo.getTuiHuanPo();
        if (XtenantEnum.isJiujiXtenant() && check3R.isSuccess() && TuihuanKindEnum.TK == tuiHuanCheckVo.getTuihuanKindEnum()
                && ObjectUtil.defaultIfNull(shouhou.getIshuishou(), 0).equals(IshuishouEnum.GOOD_PRODUCT.getCode())
                && ObjectUtil.defaultIfNull(shouhou.getSubId(), 0) > 0
                && FaultTypeEnum.FAULTY.getMessage().equals(tuiHuanPo.getFaultType())
                && NumberUtil.null2Zero(tuiHuanPo.getZhejiaM()).compareTo(BigDecimal.ZERO) == 0
        ) {
            //获取推送号码
            RefundValidBo refundValidByLp = SpringUtil.getBean(RefundValidMemberMapper.class).getRefundValidByLp(Convert.toLong(shouhou.getSubId()));
            if(refundValidByLp == null || ObjectUtil.defaultIfNull(refundValidByLp.getUserid(), 0L) == 0L){
                RRExceptionHandler.logError(StrUtil.format("良品订单[{}]短信推送数据获取", shouhou.getSubId()), tuiHuanCheckVo, null, smsService::sendOaMsgTo9JiMan);
                return;
            }
            R<RecoverNoReasonReduceVo> recoverNoReasonReduce = refundMachineService.getRecoverNoReasonReduce(Convert
                    .toInt(ObjectUtil.defaultIfNull(refundValidByLp.getUserid(), shouhou.getUserid())));
            if(!recoverNoReasonReduce.isSuccess() ){
                RRExceptionHandler.logError(StrUtil.format("良品订单[{}]短信推送获取退款次数", shouhou.getSubId()), Dict.create()
                        .set("tuiHuanCheckVo", tuiHuanCheckVo).set("recoverNoReasonReduce", recoverNoReasonReduce), null, smsService::sendOaMsgTo9JiMan);
                return;
            }
            RecoverNoReasonReduceVo reduceVo = recoverNoReasonReduce.getData();
            if(reduceVo.getTuiCount() != reduceVo.getAllCount()){
                log.warn("良品单[{}, 客户退款次数[{}], 无需通知客户", shouhou.getSubId(), reduceVo.getTuiCount());
                return;
            }
            // 第三次无理由退款进行通知
            String contentFormat = "尊敬的会员用户您好，很抱歉您购买的二手良品未能达到您的预期。因二手良品无理由退货后需重新进行质检、" +
                    "包装等才能再次销售，期间会有产品价值贬损，每位用户每个自然年可享受{}台{}天内免费无理由退货，因您免费次数已使用完，" +
                    "今年内再次购买的良品商品如需无理由退货，将收取{}元/台的售后服务费。若出现单月内无理由退货三次以上，单年内无理由退货五次以上，" +
                    "可能会限制您后续良品下单（详情请点击：{}），感谢您对二手良品的支持与理解。（免费退货单号：{}）";
            String mHost = Optional.ofNullable(SpringUtil.getBean(SysConfigClient.class).getValueByCode(SysConfigConstant.M_URL))
                    .map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取网站m域名出错"));
            String url = StrUtil.format("{}/doc/after-service?type=3", mHost);
            ShortUrlParam shortUrlParam = LambdaBuild.create(new ShortUrlParam())
                    .set(ShortUrlParam::setUrl, url).set(ShortUrlParam::setDescription, "良品售后质保短链").build();
            Result<String> urlR = SpringUtil.getBean(RetryService.class).retryByFeignRetryableException(()->
                    SpringUtil.getBean(WebCloud.class).generateShortUrl(shortUrlParam, XtenantEnum.getXtenant()));
            String shortUrl = urlR.getData();
            log.warn("良品售后质保生成成短链 传入参数：{}，返回结果：{}",url,shortUrl);
            //良品退款推送消息
            smsService.sendSms(refundValidByLp.getMobile(), StrUtil.format(contentFormat,reduceVo.getCnAllCount(),
                            reduceVo.getDays(), reduceVo.getReducePrice().setScale(0, RoundingMode.HALF_UP),
                            shortUrl, reduceVo.getFreeTuiSubIds().stream().map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA))),
                    DateUtil.localDateTimeToString(LocalDateTime.now()), "系统",
                    smsService.getSmsChannelByTenant(CommenUtil.currAreaId(shouhou.getToareaid(), shouhou.getAreaid()), ESmsChannelTypeEnum.YXTD));
        }
    }

    @Override
    public R<Boolean> updatePiaoPrice(UpdatePiaoPriceFormVo formVo) {
        ShouhouTuiHuanPo tuiHuanPo = SpringUtil.getBean(TuiHuanService.class).getById(formVo.getTuiHuanId());
        OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        if(!CollUtil.contains(oaUser.getRank(), RankEnum.TAX.getCode())){
            return R.error("您无权限修改此发票价格");
        }

        if(tuiHuanPo.getPiaoPrice().compareTo(formVo.getPiaoPrice()) == 0){
            return R.error("发票价格相等，请核对");
        }
        //退款方式修改的总金额必须与发票金额相等

        //校验组合退金额

        //修改金额

        return R.success(Boolean.TRUE);
    }

    private R<Integer> checkSubmitCheckAndSet(TuiHuanCheckVo tuiHuanCheckVo, OaUserBO oaUser) {
        ShouhouRefundDetailVo.ProcessStatus status = EnumUtil.getEnumByCode(ShouhouRefundDetailVo.ProcessStatus.class, tuiHuanCheckVo.getProcessStatus());
        Assert.isFalse(status == null,"流程状态错误");
        ShouhouTuiHuanPo tuiHuanPo = SpringUtil.getBean(ShouhouRefundMoneyMapper.class).getRefund(tuiHuanCheckVo.getTuihuanId());
        Assert.isFalse(tuiHuanPo == null,"退款审核id错误");
        TuihuanKindEnum tuihuanKindEnum = EnumUtil.getEnumByCode(TuihuanKindEnum.class, tuiHuanPo.getTuihuanKind());
        Assert.isFalse(tuihuanKindEnum == null,"退款类型[{}]不支持",tuiHuanPo.getTuihuanKind());
        //校验库的状态是否与当前状态一致
        ShouhouRefundDetailVo.ProcessStatus currStatus = ShouhouRefundService.addProcessInfo(tuiHuanPo, ShouhouRefundDetailVo.ProcessStatus.SUBMIT, new LinkedList<>());
        Assert.isTrue(ShouhouRefundDetailVo.ProcessStatus.nextProcess(currStatus) == status,"审批失败,已经审核");
        Assert.isFalse(status == ShouhouRefundDetailVo.ProcessStatus.CHECK3
                && Stream.of(TuihuanKindEnum.HJT,TuihuanKindEnum.HZB).anyMatch(tk -> Objects.equals(tk,tuihuanKindEnum))
                && ObjectUtil.defaultIfNull(tuiHuanCheckVo.getNewBasketId(),0) == 0 ,"所换的设备编号不能为空");
        //设置值
        tuiHuanCheckVo.setProcessStatusEnum(status);
        tuiHuanCheckVo.setTuiHuanPo(tuiHuanPo);
        //查询售后单信息
        Shouhou shouhou = refundMachineService.getShouhouById(tuiHuanPo.getShouhouId());
        Assert.isFalse(shouhou == null,StrUtil.format("售后id[{}]不存在", tuiHuanPo.getShouhouId()));
        tuiHuanCheckVo.setShouhou(shouhou);
        List<ShouhouTuihuanDetailPo> allTuihuanDetailPos = SpringUtil.getBean(RefundCommonService.class).listTuihuanDetail(tuiHuanPo.getId(), null, true);
        tuiHuanCheckVo.setTuihuanDetailPos(allTuihuanDetailPos.stream().filter(td -> ObjectUtil.notEqual(Boolean.TRUE,td.getIsDel())).collect(Collectors.toList()));
        tuiHuanCheckVo.setOtherTuihuanDetailPos(allTuihuanDetailPos.stream().filter(td -> ObjectUtil.equal(Boolean.TRUE,td.getIsDel())).collect(Collectors.toList()));
        tuiHuanCheckVo.setCurrUser(oaUser);
        tuiHuanCheckVo.setSubLogs(new LinkedList<>());
        tuiHuanCheckVo.setTuihuanKindEnum(tuihuanKindEnum);
        return R.success(null);
    }

    /**
     * 新增
     *
     * @param netRecordId
     * @param price
     * @param userName
     * @param returnId
     */
    private void insertNetPayRefundInfo(Integer netRecordId, BigDecimal price, String userName, Integer returnId) {

        refundMachineMapper.insertNetPayRefundInfo(netRecordId, price, userName, returnId);
    }

    private Integer insertSmallPro(Smallpro sp) {

        return refundMachineMapper.insertSmallPro(sp);
    }

    /**
     * 插入小件详情信息
     * @param spId
     * @param basketId
     * @param ppid
     * @param count
     */
    @Override
    public void insertSmallProBill(Integer spId, Integer basketId, Integer ppid, Integer count) {

        refundMachineMapper.insertSmallproBill(spId, basketId, ppid, count);
    }

    private void insertReturnsDetail(Integer thId, Integer basketId, Integer basketCount) {

        refundMachineMapper.insertReturnsDetail(thId, basketId, basketCount);
    }

    /**
     * 售后换机头转入
     *
     * @param currUser 当前登录用户
     * @param basketId 退换basketId
     * @param shouhouId 售后单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mkcDellogsh1(OaUserBO currUser, Long basketId, Integer shouhouId) {
        List<BasketMkcBo> basketMkcBos = refundMachineMapper.listBasketMkcByBasketId(basketId);
        if (CollectionUtils.isEmpty(basketMkcBos)) {

            throw new CustomizeException("mkc错误或地区不符或仅现货才可作废;");
        }
        BasketMkcBo basketMkcBo = basketMkcBos.get(0);
        Integer kcCheck = basketMkcBo.getKcCheck();
        // 兼容老逻辑
        if (kcCheck == 3) {
            // 系统自动对填写的mkcid 库存进行转售后流程提交以及审核操作
            MkcDellogs mkcDellogs = new MkcDellogs();
            mkcDellogs.setMkcId(Math.toIntExact(basketId));
            mkcDellogs.setInuser(currUser.getUserName());
            mkcDellogs.setAreaid(currUser.getAreaId());
            mkcDellogs.setComment("售后换机头转入售后操作(" + shouhouId + ")");
            mkcDellogs.setKinds("h1");
            mkcDellogs.setPrice1(basketMkcBo.getInBeihuoPrice());
            mkcDellogs.setPpriceid(basketMkcBo.getPpriceid());
            mkcDellogs.setFrareaid(Convert.toStr(basketMkcBo.getFrAreaId()));
            // 新增大件库存日志
            mkcDellogsService.insertMkcDellogs(mkcDellogs, currUser.getUserName());
            Integer logId = mkcDellogs.getId();
            if (Objects.nonNull(logId) && logId > 0) {
                Optional<AreaInfo> areaOpt = getAreaInfo(currUser.getAreaId());
                Integer ztid = null;
                if (areaOpt.isPresent()) {
                    ztid = authConfigService.getZtIdByAuId(areaOpt.get().getAuthorizeId());
                }
                if (Objects.nonNull(ztid) && ztid > 0) {
                    NewVoucherBo voucher = new NewVoucherBo();
                    voucher.setAct("transferAfterServicesMkc");
                    voucher.setActName("转售后机器");
                    voucher.setAccountSetId(ztid.toString());
                    voucher.setSubId(logId.toString());
                    voucher.setVoucherTime(DateTimeUtils.formatDate(LocalDateTime.now(), DateTimeUtils.NORMAL_DATE_FORMAT));
                    voucher.setAreaId(currUser.getAreaId());

                    NewVoucherBo.VoucherArgs args = new NewVoucherBo.VoucherArgs();
                    args.setId(logId);
                    voucher.setArgsO(args);
                    voucherService.addNewVoucher(voucher);
                }
                // 更新库存
                int updateMkc = productMkcMapper.updateShouhouMkc(currUser.getAreaId(), basketId);
                if (updateMkc <= 0) {
                    throw new CustomizeException("审核失败，存在的原因：1.不是现货！2.地区不一样;");
                }
            }
        }
    }

    @Override
    public AreaInfo getShouhouAreaInfo(Shouhou shouhou) {
        return getAreaInfo(getShouhouAreaId(shouhou))
                .orElseThrow(() -> new CustomizeException("售后单门店信息获取异常"));
    }

    private Integer getShouhouAreaId(Shouhou shouhou) {
        if(ObjectUtil.defaultIfNull(shouhou.getToareaid(), 0) > 0){
            return shouhou.getToareaid();
        }
        return ObjectUtil.defaultIfNull(shouhou.getAreaid(), 0);
    }

    private Optional<AreaInfo> getAreaInfo(Integer areaId){
        return Optional.ofNullable(SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(areaId))
                .filter(R::isSuccess).map(R::getData);
    }

    /**
     * /获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice
     * @param refundDetail
     */
    private void setBuyPriceAndSubInfo(RefundMachineDetailVo refundDetail) {
        //
        Shouhou shouhou = refundDetail.getShouhou();
        if(ObjectUtil.defaultIfNull(shouhou.getSubId(),0) <= 0){
            throw new CustomizeException("外修单不能进行退换机操作");
        }
        // xq
        BuyPriceSubInfoBo buyPriceAndSubInfo = refundMachineService.getBuyPriceAndSubInfo(shouhou.getSubId(), shouhou, null);
        if(Objects.isNull(buyPriceAndSubInfo)){
            throw new CustomizeException(StrUtil.format("获取不到[{}]订单信息,请检测订单状态或者库存状态",shouhou.getSubId()));
        }

        //还没有退款类型,通过退款来进行获取不可退金额
        NotRefundBo notRefundBo = RefundMoneyUtil.getNotRefundMoney(TuihuanKindEnum.TK,
                DecideUtil.iif(Objects.equals(0, Optional.ofNullable(shouhou.getIshuishou()).orElse(IshuishouEnum.NEW_MACHINE.getCode())), BusinessTypeEnum.SALE_ORDER.getCode(), BusinessTypeEnum.LP_ORDER.getCode()),
                buyPriceAndSubInfo.getOrderId());
        BigDecimal notRefundMoney = NumberUtil.null2Zero(notRefundBo.getNotRefundMoney());
        //获取所有已付金额(未退款的)
        BigDecimal allYifuM = RefundMoneyUtil.getAllYifuM(buyPriceAndSubInfo.getOrderId(), buyPriceAndSubInfo.getSubCheck(), buyPriceAndSubInfo.getYifuMoney(),
                DecideUtil.iif(Objects.equals(0, Optional.ofNullable(shouhou.getIshuishou()).orElse(IshuishouEnum.NEW_MACHINE.getCode())), TuihuanKindEnum.TK, TuihuanKindEnum.TK_LP));
        BigDecimal maxRefundPrice = NumberUtil.max(NumberUtil.min(ObjectUtil.defaultIfNull(buyPriceAndSubInfo.getYifuMoney(), BigDecimal.ZERO), allYifuM.subtract(notRefundMoney)), BigDecimal.ZERO);
        BigDecimal totalPrice = NumberUtil.max(ObjectUtil.defaultIfNull(buyPriceAndSubInfo.getTotalPrice(), BigDecimal.ZERO), BigDecimal.ZERO);
        refundDetail.setTotalRefundPrice(NumberUtil.min(totalPrice,maxRefundPrice));
        refundDetail.setGiftPrice(buyPriceAndSubInfo.getGiftPrice());
        refundDetail.setBaitiaoPrice(BigDecimal.ZERO);
        refundDetail.setKuBaitiaoPrice(BigDecimal.ZERO);
        refundDetail.setYifuM(buyPriceAndSubInfo.getYifuMoney());
        refundDetail.setCoinM(buyPriceAndSubInfo.getCoinMoney());
        refundDetail.setInprice(buyPriceAndSubInfo.getCostPrice());
        refundDetail.setUserId(buyPriceAndSubInfo.getUserId());
        refundDetail.setTradeDay(buyPriceAndSubInfo.getTradeDay());
        refundDetail.setTradeType(buyPriceAndSubInfo.getTradeType());
        refundDetail.setTradeTypeDesc(EnumUtil.getMessageByCode(TradeTypeEnum.class,buyPriceAndSubInfo.getTradeType()));
        refundDetail.setTradeDate(buyPriceAndSubInfo.getTradeDate());
        refundDetail.setPeizhi(buyPriceAndSubInfo.getPeizhi());
        refundDetail.setPiaoType(buyPriceAndSubInfo.getPiaoType());
        refundDetail.setBasketId(buyPriceAndSubInfo.getBasketId());
        // 售后接件id  售后接件单号
        refundDetail.setIsNeedValid(Boolean.TRUE);
        refundDetail.setSubId(buyPriceAndSubInfo.getOrderId());
    }

    /**
     * /获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice
     * @param orderId 订单号
     * @param shouhou 售后订单信息
     *
     * @param tuihuanKindEnum
     * @return BuyPriceSubInfoBo
     */
    @Override
    public BuyPriceSubInfoBo getBuyPriceAndSubInfo(Integer orderId, Shouhou shouhou, TuihuanKindEnum tuihuanKindEnum) {
        BigDecimal giftPrice = BigDecimal.ZERO;
        BigDecimal yifuMoney = BigDecimal.ZERO;
        BigDecimal coinMoney = BigDecimal.ZERO;
        int tradeDay = IntConstant.ZERO;
        int tradeType = IntConstant.ZERO;
        LocalDateTime tradeDate = null;
        String peizhi = StringUtils.EMPTY;
        Integer piaoType = IntConstant.ZERO;
        Integer subId = shouhou.getSubId();
        if (Objects.isNull(subId)) {
            subId = 0;
        }
        Integer isHuishou = ObjectUtil.defaultIfNull(shouhou.getIshuishou(),0);
        if (isHuishou == 2) {
            return null;
        }
        MTableInfoEnum tableInfoEnum = isHuishou == 0 ? MTableInfoEnum.SUB : MTableInfoEnum.RECOVER_MARKET_INFO;
        List<RefundDataBo> refundDataBos = CommenUtil.autoQueryHist(() -> this.refundMachineMapper.listRefundDataBySubId(shouhou.getSubId(),
                shouhou.getBasketId(), shouhou.getMkcId(), shouhou.getImei(), isHuishou), tableInfoEnum, subId);
        if (CollectionUtils.isEmpty(refundDataBos)) {
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"获取不到购买的订单信息,库存状态或订单状态不对");
            return null;
        }
        BuyPriceSubInfoBo buyPriceSubInfoBo = new BuyPriceSubInfoBo();
        RefundDataBo refundDataBo = refundDataBos.get(IntConstant.ZERO);
        BigDecimal price = refundDataBo.getPrice();
        LocalDateTime modifyDate = shouhou.getModidate();
        Integer userId = refundDataBo.getUserId();
        Integer basketId = refundDataBo.getBasketId();
        if (isHuishou == 0) {
            // 手机对应赠品成本金额
            // 判断赠品金额 排除贴膜成本
            giftPrice = basketService.getSubGiftPriceV2(Collections.singletonList(refundDataBo.getBasketId()) , true);
            // 计算大件退款金额 要加上贴膜成本
            BigDecimal filmPrice = basketService.getSubGiftPriceV2(Collections.singletonList(refundDataBo.getBasketId()), false);
            price = price.add(filmPrice);
            // #endregion
            tradeDate = refundDataBo.getTradeDate1();
            tradeDay = (int) DateTimeUtils.daysBetween(tradeDate,modifyDate);
            tradeType = 1;
            //优品
            if (Objects.equals(refundDataBo.getType(),22)) {
                tradeType = 2;
            }
            peizhi = refundDataBo.getProductPeizhi();
            yifuMoney = refundDataBo.getYifuMoney();
            BigDecimal shouhouCoinMoney = refundDataBo.getCoinMoney();
            if (shouhouCoinMoney.compareTo(BigDecimal.ZERO) > 0) {
                if (price.compareTo(yifuMoney) > 0) {
                    coinMoney = price.subtract(yifuMoney);
                }
            }
            // 获取发票信息
            Integer fapiaoKind = shouhouTuihuanService.getFapiaoKindBySubId(subId, isHuishou);
            if (fapiaoKind != null) {
                piaoType = fapiaoKind;
            }
            buyPriceSubInfoBo.setBusinessType(BusinessTypeEnum.SALE_ORDER.getCode());
        } else if (isHuishou == 1) {
            tradeType = 3;
            tradeDate = refundDataBo.getTradeDate1();
            if (modifyDate != null && tradeDate != null) {
                tradeDay = (int) Duration.between(tradeDate,modifyDate).toDays();
            }
            BigDecimal shouhouCoinMoney = refundDataBo.getCoinMoney();
            yifuMoney = refundDataBo.getYifuMoney();
            if (shouhouCoinMoney.compareTo(BigDecimal.ZERO) > 0) {
                if (price.compareTo(yifuMoney) > 0) {
                    coinMoney = price.subtract(yifuMoney);
                }
            }
            // 获取发票信息
            Integer fapiaoKind = shouhouTuihuanService.getFapiaoKindBySubId(subId, isHuishou);

            if (fapiaoKind != null) {
                piaoType = fapiaoKind;
            }
            buyPriceSubInfoBo.setBusinessType(BusinessTypeEnum.LP_ORDER.getCode());
        } else {
            basketId = IntConstant.ZERO;
        }
        // 扣减保价金额
        if (isHuishou == 0) {
            // 扣减保价金额
            BigDecimal koujianBaojia = shouhouTuihuanService.getKoujianBaojia(basketId);
            if (Objects.nonNull(koujianBaojia) && koujianBaojia.compareTo(BigDecimal.ZERO) > 0) {
                price = price.subtract(koujianBaojia);
            }
        }
        buyPriceSubInfoBo.setTradeDate(tradeDate);
        // 设置交易完成时间, 三方获取交易完成之后的退订
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.GROUP_REFUND_ORDER_TRADEDATE, buyPriceSubInfoBo.getTradeDate()));
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.GROUP_REFUND_ORDER_BASKET_IDS,Collections.singletonList(shouhou.getBasketId())));
        //扣减商品对应的不可退金额记录
        NotRefundBo notRefundBo = RefundMoneyUtil.getNotRefundMoney(Optional.ofNullable(tuihuanKindEnum).orElse(TuihuanKindEnum.TK),
                DecideUtil.iif(Objects.equals(0, isHuishou), BusinessTypeEnum.SALE_ORDER.getCode(), BusinessTypeEnum.LP_ORDER.getCode()),
                orderId);
        Map<Integer, Tuple> splitBasketMap = RefundMoneyUtil.splitNotRefundPrice(Collections.singleton(basketId), notRefundBo, orderId);
        int splitNotRefundIndex = 1;
        BigDecimal splitNotRefund = splitBasketMap.get(basketId).get(splitNotRefundIndex);
        price = NumberUtil.max(ObjectUtil.defaultIfNull(price.subtract(splitNotRefund), BigDecimal.ZERO), BigDecimal.ZERO);
        // 详情
        buyPriceSubInfoBo.setOrderId(orderId);
        buyPriceSubInfoBo.setCostPrice(refundDataBo.getCostPrice());
        buyPriceSubInfoBo.setAreaId(refundDataBo.getAreaId());
        buyPriceSubInfoBo.setShouhouAreaId(getShouhouAreaId(shouhou));
        buyPriceSubInfoBo.setSubCheck(refundDataBo.getSubCheck());
        buyPriceSubInfoBo.setTotalPrice(price.setScale(2,RoundingMode.HALF_UP));
        buyPriceSubInfoBo.setGiftPrice(giftPrice);
        buyPriceSubInfoBo.setYifuMoney(yifuMoney);
        buyPriceSubInfoBo.setCoinMoney(coinMoney);
        buyPriceSubInfoBo.setUserId(userId);
        buyPriceSubInfoBo.setTradeDay(tradeDay);
        buyPriceSubInfoBo.setTradeType(tradeType);
        buyPriceSubInfoBo.setPeizhi(peizhi);
        buyPriceSubInfoBo.setPiaoType(piaoType);
        buyPriceSubInfoBo.setBasketId(basketId);

        return buyPriceSubInfoBo;
    }

    /**
     * 通过售后ID获取售后信息
     * @param shouhouId
     * @return
     */
    @Override
    public Shouhou getShouhouById(Integer shouhouId){
        return SpringContextUtil.reqCache(()-> {
            LambdaQueryWrapper<Shouhou> shouhouLambdaQueryWrapper = new LambdaQueryWrapper<>();
            shouhouLambdaQueryWrapper.eq(Shouhou::getId, shouhouId);
            shouhouLambdaQueryWrapper.ne(Shouhou::getUserid, 76783);
            return shouhouService.list(shouhouLambdaQueryWrapper).stream().findFirst().orElse(null);
        }, RequestCacheKeys.REFUND_MACHINE_SERVICE_GET_SHOUHOU_BY_ID, shouhouId);
    }

    /**
     * 通过售后ID获取售后退还信息
     * @param shouhouId
     * @return
     */
    @Override
    public ShouhouTuiHuanPo getShouhouTuihuanByShouhouId(Integer shouhouId){
        return SpringContextUtil.reqCache(()-> SpringUtil.getBean(TuiHuanService.class).lambdaQuery()
                .in(ShouhouTuiHuanPo::getTuihuanKind,TuihuanKindEnum.HJT.getCode(), TuihuanKindEnum.HZB.getCode(),
                        TuihuanKindEnum.TK.getCode(), TuihuanKindEnum.HQTXH.getCode())
                .eq(ShouhouTuiHuanPo::getShouhouId,shouhouId).list().stream().findFirst().orElse(null),
                RequestCacheKeys.SHOUHOU_TUIHUAN_BY_SHOUHOU_ID, shouhouId);
    }


    @Override
    public ShouhouTuiHuanPo getShouhouTuihuanBySubId(Integer subId){
        return SpringUtil.getBean(TuiHuanService.class).lambdaQuery()
                .in(ShouhouTuiHuanPo::getTuihuanKind,TuihuanKindEnum.HJT.getCode(), TuihuanKindEnum.HZB.getCode(),
                        TuihuanKindEnum.TK.getCode(), TuihuanKindEnum.HQTXH.getCode(),TuihuanKindEnum.BATCH_TK.getCode())
                .eq(ShouhouTuiHuanPo::getSubId,subId).isNull(ShouhouTuiHuanPo::getCheck3)
                .list().stream().findFirst().orElse(null);
    }

    /**
     * basketID获取赠品信息
     * @param basketId
     * @return
     */
    public List<TuiGiftBasket> getGiftBasketList(Integer basketId) {
        return refundMachineMapper.getGiftBasketListByBasketId(basketId);
    }

    /**
     * 是否为组合退款
     * @param tuihuanKind
     * @return
     */
    private boolean isGroupRefund(Integer tuihuanKind){
        return TuihuanKindEnum.TK.getCode().equals(tuihuanKind) || TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind);
    }

    private boolean isShowGroupDetail(Integer tuihuanKind,BigDecimal refundPrice){
        return TuihuanKindEnum.TK.getCode().equals(tuihuanKind)
                || TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind) && NumberUtil.null2Zero(refundPrice).compareTo(BigDecimal.ZERO)>0;
    }
}
