package com.jiuji.oa.afterservice.refund.vo.req.smallpro;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/1/4 16:48
 */
@Data
@Accessors(chain = true)
@ApiModel("小件退换提交vo")
public class RefundSmallproFormVo {
    @ApiModelProperty("售后id")
    @NotNull(message = "售后单号不能为空")
    private Integer shouhouId;
    @ApiModelProperty("退换类型")
    @NotNull(message = "退换类型不能为空")
    private Integer tuihuanKind;
    @ApiModelProperty("购机时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tradeDate;
    @ApiModelProperty("购机天数")
    private Integer tradeDay;
    /**
     * 扣减了不可退金额
     */
    @ApiModelProperty("总金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal totalPrice;
    @ApiModelProperty("总折价金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal zhejiaM;
    @ApiModelProperty("商品的成本")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal inprice;
    @ApiModelProperty("所换订单充值的金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal subIdM;
    /**
     * 单号(交易单号)
     */
    @ApiModelProperty(value = "单号")
    private Integer subId;
    @ApiModelProperty("所换机型单号")
    private Integer huanSubId;

    /**
     * 组合退款的信息对象
     */
    @ApiModelProperty("组合退款的信息对象")
    private GroupTuihuanFormVo tuihuanFormVo;

    /**内部流转字段 start*/
    @ApiModelProperty(value = "退款id",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private Integer tuihuanId;
    @ApiModelProperty(value = "退款分类",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private TuihuanKindEnum tuihuanKindEnum;

    @ApiModelProperty(value = "验证类型枚举",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private ValidMemberType validtWayEnum;

    @ApiModelProperty("商品订单的购买金额")
    @JSONField(format = "0.00@DOWN",serialize = false,deserialize = false)
    private BigDecimal buypriceM;
    /**
     * 售后信息
     */
    @ApiModelProperty(value = "售后信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private Smallpro smallpro;
    /**
     * 售后门店信息
     */
    @ApiModelProperty(value = "售后门店信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private AreaInfo smallproAreaInfo;
    /**
     * 购买门店信息
     */
    @ApiModelProperty(value = "购买门店信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private AreaInfo buyAreaInfo;
    @ApiModelProperty(value = "当前门店信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private AreaInfo currAreaInfo;
    @ApiModelProperty(value = "当前用户",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private OaUserBO currUser;
    /**内部流转字段 end*/
}
