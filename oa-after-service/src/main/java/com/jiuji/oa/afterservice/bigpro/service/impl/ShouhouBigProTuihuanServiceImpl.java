package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.afterservice.batchreturn.dao.BatchReturnMkcDao;
import com.jiuji.oa.afterservice.bigpro.bo.HuanMkcBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouHuanBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouTuihuanReturnDataBo;
import com.jiuji.oa.afterservice.bigpro.bo.Transact;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouBigProTuihuanMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.service.tuihuan.TuiHuanService;
import com.jiuji.oa.afterservice.bigpro.vo.req.tuihuan.AlipayInfoReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.HuanInfo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.ConfigConsts;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.SecretEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.source.MUrlSource;
import com.jiuji.oa.afterservice.common.source.MoaUrlSource;
import com.jiuji.oa.afterservice.common.source.WwwUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.HttpClientUtil;
import com.jiuji.oa.afterservice.common.util.OaVerifyUtil;
import com.jiuji.oa.afterservice.other.enums.SaveMoneyEkindEnum;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.NetPayRefundInfoService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.other.service.ShouyinOtherService;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.enums.JiujiTenantEnum;
import com.jiuji.oa.afterservice.smallpro.service.SmallproForwardExService;
import com.jiuji.oa.afterservice.smallpro.vo.req.oaApiReq.OaApiReq;
import com.jiuji.oa.afterservice.stock.po.MkcDellogs;
import com.jiuji.oa.afterservice.stock.po.ProductMkc;
import com.jiuji.oa.afterservice.stock.service.MkcDellogsService;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @<NAME_EMAIL>
 * @date 2020/6/17 16:44
 **/
@Service
@Slf4j
public class ShouhouBigProTuihuanServiceImpl implements ShouhouBigProTuihuanService {

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Autowired
    private ShouhouReturncbService shouhouReturncbService;
    @Autowired
    @Lazy
    private ShouhouService shouhouService;
    @Autowired
    private ProductinfoService productinfoService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private SubService subService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private SaveMoneyService saveMoneyService;
    @Autowired
    @Lazy
    private ShouhouExService shouhouExService;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Resource
    private UserInfoClient userInfoClient;
    @Autowired
    private SmsService smsService;
    @Autowired
    private NetPayRefundInfoService netPayRefundInfoService;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    private ShouhouBigProTuihuanMapper shouhouBigProTuihuanMapper;
    @Resource
    private WwwUrlSource urlSource;
    @Autowired
    private ShouyinOtherService shouyinOtherService;
    @Autowired
    private WeixinUserService weixinUserService;
    @Resource
    private MUrlSource mUrlSource;
    @Autowired
    private SmallproForwardExService smallproForwardExService;
    @Resource
    private InwcfUrlSource inwcfUrlSource;
    @Resource
    private BatchReturnMkcDao batchReturnMkcDao;
    @Autowired
    private ShouhouMapper shouhouMapper;
    @Autowired
    private SmallproMapper smallproMapper;
    @Resource
    @Lazy
    private ShouhouBigProTuihuanService shouhouBigProTuihuanService;

    private final Long two = 2L;

    @Override
    public R<Boolean> tuihuanCheck1(Integer tuihuanId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (!oaUserBO.getRank().contains("63") && !oaUserBO.getRank().contains("6c1") && !oaUserBO.getRank().contains("6e5") && !oaUserBO.getRank().contains("777")) {
            return R.error("无操作权限！");
        } else {
            //判断如果是微信秒退，是否保存了OPENID
            Boolean flag = shouhouTuihuanService.checkHasOpenID(tuihuanId);
            if (flag) {

                Boolean result = this.check1(tuihuanId, oaUserBO.getAreaId(), oaUserBO.getUserName());
                return result ? R.success("操作成功") : R.error("操作失败！");

            } else {
                return R.error("微信秒退必须客户扫码验证后才能继续，如未操作，可撤销重来！");
            }
        }
    }

    @Override
    public Boolean check1(Integer tuihuanId, Integer areaId, String user) {

        ShouhouTuihuan tuihuan = shouhouTuihuanService.getOne(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getAreaid, areaId).eq(ShouhouTuihuan::getId, tuihuanId).isNull(ShouhouTuihuan::getCheck1));
        if (tuihuan == null) {
            return false;
        }
        Integer tuihuanKind = tuihuan.getTuihuanKind();
        Integer tuihuanGzInfo = tuihuan.getGzinfo() == null ? -1 : tuihuan.getGzinfo();
        String checkType = tuihuan.getCheckType();
        Boolean flag = true;

        if (StringUtils.isNotEmpty(checkType)) {
            String tuiWay = tuihuan.getTuiWay();
            if ("正常退换".equals(checkType)) {
                flag = false;
                LambdaUpdateWrapper<ShouhouTuihuan> updateWrapper = new LambdaUpdateWrapper();
                updateWrapper.set(ShouhouTuihuan::getCheck1, 1);
                updateWrapper.set(ShouhouTuihuan::getCheck1user, user);
                updateWrapper.set(ShouhouTuihuan::getCheck1dtime, LocalDateTime.now());
                updateWrapper.set(ShouhouTuihuan::getCheck2, 1);
                updateWrapper.set(ShouhouTuihuan::getCheck2dtime, LocalDateTime.now());
                updateWrapper.set(ShouhouTuihuan::getCheck2user, user);
                updateWrapper.eq(ShouhouTuihuan::getAreaid, areaId);
                updateWrapper.isNull(ShouhouTuihuan::getCheck1);
                updateWrapper.eq(ShouhouTuihuan::getId, tuihuanId);
                Boolean updateFlag = shouhouTuihuanService.update(updateWrapper);
                if (!updateFlag) {
                    return false;
                }
                ShouhouReturncb returncb = new ShouhouReturncb();

                Integer ppid = this.getPpidByShouhouId(tuihuan.getShouhouId());
                BigDecimal price = this.getMemberpriceByPpid(ppid);

                returncb.setOldshouhouId(tuihuan.getShouhouId());
                returncb.setShzrPrice(price);
                returncb.setPpid(ppid);
                returncb.setTuihuanId(tuihuanId);
                returncb.setTuihuanKind(tuihuanKind);
                returncb.setDtime(LocalDateTime.now());
                returncb.setInuser(user);
                returncb.setGzinfo(null);
                if (-1 != tuihuanGzInfo) {
                    returncb.setGzinfo(tuihuanGzInfo);
                }
                shouhouReturncbService.save(returncb);

                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
                AreaInfo areaInfo = null;
                if (areaInfoR.getCode() != ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    areaInfo = areaInfoR.getData();
                }
                Boolean ccbFlag = true;
                if (areaInfo != null && "建行分期返回".equals(tuiWay) && (areaInfo.getXtenant() != JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode().intValue() && areaInfo.getXtenant() != JiujiTenantEnum.JIUJI_TENANT_HUAWEI.getCode().intValue())) {
                    ccbFlag = false;
                }
                Boolean isValidt = tuihuan.getIsValidt() == null ? false : tuihuan.getIsValidt();
                if ("银行转账,微信秒退,支付宝秒退".indexOf(tuiWay) == -1 && isValidt && ccbFlag) {
                    //直接办理(退款和换其他，不涉及MKC_ID)
                    Integer areaKind = areaInfo == null ? null : areaInfo.getKind1();
                    List<String> myRank = Arrays.asList("64", "92", "27", "777");
                    //Transact 等C#提供借口
                    transact(tuihuanId, 0, myRank, areaId, "系统", areaKind);

                }
                return true;
            }
        }
        if (flag) {
            LambdaUpdateWrapper<ShouhouTuihuan> updateWrapper = new LambdaUpdateWrapper();
            updateWrapper.set(ShouhouTuihuan::getCheck1, 1);
            updateWrapper.set(ShouhouTuihuan::getCheck1dtime, LocalDateTime.now());
            updateWrapper.set(ShouhouTuihuan::getCheck1user, user);
            if (1 == tuihuanKind || 2 == tuihuanKind) {
                updateWrapper.set(ShouhouTuihuan::getCheck2, 1);
                updateWrapper.set(ShouhouTuihuan::getCheck2dtime, LocalDateTime.now());
            }
            updateWrapper.eq(ShouhouTuihuan::getAreaid, areaId).isNull(ShouhouTuihuan::getCheck1).eq(ShouhouTuihuan::getId, tuihuanId);
            Boolean updateFlag = shouhouTuihuanService.update(updateWrapper);
            if (updateFlag) {
                ShouhouReturncb returncb = new ShouhouReturncb();

                Integer ppid = this.getPpidByShouhouId(tuihuan.getShouhouId());
                BigDecimal price = this.getMemberpriceByPpid(ppid);

                returncb.setOldshouhouId(tuihuan.getShouhouId());
                returncb.setShzrPrice(price);
                returncb.setPpid(ppid);
                returncb.setTuihuanId(tuihuanId);
                returncb.setTuihuanKind(tuihuanKind);
                returncb.setDtime(LocalDateTime.now());
                returncb.setInuser(user);
                returncb.setGzinfo(null);
                if (-1 != tuihuanGzInfo) {
                    returncb.setGzinfo(tuihuanGzInfo);
                }
                shouhouReturncbService.save(returncb);
                return true;
            } else {
                return false;
            }
        }

        return true;
    }

    @Override
    public R<Boolean> tuihuanCheck2(Integer tuihuanId) {

        ShouhouTuihuan tuihuan = shouhouTuihuanService.getOne(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getId, tuihuanId).isNull(ShouhouTuihuan::getCheck2));
        if (tuihuan == null) {
            return R.error("未找到记录！");
        }
        Integer tuihuanKind = tuihuan.getTuihuanKind();
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        List<String> myRank = oaUserBO.getRank();
        if (CollectionUtils.isNotEmpty(myRank)) {
            if ((1 == tuihuanKind || 2 == tuihuanKind) && !myRank.contains("64")) {
                return R.error("需要权限 64！");
            }
            if ((3 == tuihuanKind || 4 == tuihuanKind) && !myRank.contains("65") && !myRank.contains("777")) {
                return R.error("需要权限 65！");
            }
        }

        LambdaUpdateWrapper<ShouhouTuihuan> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(ShouhouTuihuan::getCheck2, 1);
        updateWrapper.set(ShouhouTuihuan::getCheck2dtime, LocalDateTime.now());
        updateWrapper.set(ShouhouTuihuan::getCheck2user, oaUserBO.getUserName());
        updateWrapper.isNull(ShouhouTuihuan::getCheck2);
        updateWrapper.eq(ShouhouTuihuan::getId, tuihuanId);
        Boolean updateFlag = shouhouTuihuanService.update(updateWrapper);
        if (!updateFlag) {
            return R.error("审核失败！");
        }

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(oaUserBO.getAreaId());
        AreaInfo areaInfo = null;
        if (areaInfoR.getCode() != ResultCode.SUCCESS && areaInfoR.getData() != null) {
            areaInfo = areaInfoR.getData();
        }
        String checkType = tuihuan.getCheckType();
        Boolean ccbFlag = true;
        String tuiWay = tuihuan.getTuiWay();
        if (areaInfo != null && "建行分期返回".equals(tuiWay) && (areaInfo.getXtenant() != JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode().intValue() && areaInfo.getXtenant() != JiujiTenantEnum.JIUJI_TENANT_HUAWEI.getCode().intValue())) {
            ccbFlag = false;
        }
        Boolean isValidt = tuihuan.getIsValidt() == null ? false : tuihuan.getIsValidt();
        if ("银行转账,微信秒退,支付宝秒退".indexOf(tuiWay) == -1 && !"特殊退换".equals(checkType) && isValidt && ccbFlag) {
            //直接办理(退款和换其他，不涉及MKC_ID)
            Integer areaKind = areaInfo == null ? null : areaInfo.getKind1();
            //Transact 等C#提供借口
            transact(tuihuanId, 0, myRank, oaUserBO.getAreaId(), "系统", areaKind);

        }
        return R.success("操作成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> tuihuanDel(Integer tuihuanId, String remark) {

        List<ShouhouTuihuan> tuihuanList = shouhouTuihuanService.list(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getIsdel, false).isNull(ShouhouTuihuan::getCheck3).eq(ShouhouTuihuan::getId, tuihuanId));
        if (CollectionUtils.isEmpty(tuihuanList)) {
            return R.error("错误，退换记录不存在！");
        }

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String userName = oaUserBO.getUserName();
        ShouhouTuihuan tuihuan = tuihuanList.get(0);

        //查找是否已经扣除ovg返现金额 如果已扣除，需要加回来
        BigDecimal returnSaveMoney = BigDecimal.ZERO;
        Integer userId = null;
        Integer basketId = null;
        Integer areaId = null;
        if (3 == tuihuan.getTuihuanKind() || 4 == tuihuan.getTuihuanKind()) {
            ShouhouHuanBo info = shouhouService.getShouhouHuan(tuihuan.getShouhouId());
            if (info != null) {
                areaId = info.getAreaId();
                Integer ishuishou = info.getIshuishou() == null ? 0 : info.getIshuishou();
                if (ishuishou == 0) {
                    userId = this.getUserIdBySubId(info.getSubId() == null ? 0 : info.getSubId());
                    HuanMkcBo mkc = basketService.huanMKC(userId, info.getMkcId(), info.getImei(), ishuishou);
                    if (mkc != null) {
                        basketId = mkc.getBasketId();
                    }
                }
            }

            if (userId != null && basketId != null) {
                List<SaveMoney> saveMoneyList = saveMoneyService.list(new LambdaQueryWrapper<SaveMoney>().select(SaveMoney::getMoney)
                        .eq(SaveMoney::getUserid, userId).eq(SaveMoney::getSubId, basketId).eq(SaveMoney::getKind, 20)
                        .eq(SaveMoney::getComment, "OVG退款余额退回").orderByDesc(SaveMoney::getDtime));
                if (CollectionUtils.isNotEmpty(saveMoneyList)) {
                    returnSaveMoney = saveMoneyList.get(0).getMoney();
                }
            }
        }

        String thInuser = null;
        if (tuihuan.getCheck3() != null && tuihuan.getCheck3()) {
            try {

                Integer tuihuanKind = tuihuan.getTuihuanKind() == null ? 0 : tuihuan.getTuihuanKind();
                if (TuihuanKindEnum.TWXF.getCode().equals(tuihuanKind)) {
                    shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getIstui, null).eq(Shouhou::getId, tuihuanId));
                }
                ShouhouTuihuan shouhouTuihuan = shouhouTuihuanService.getOne(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getIsdel, false)
                        .isNull(ShouhouTuihuan::getCheck3).eq(ShouhouTuihuan::getId, tuihuanId));

                if (shouhouTuihuan != null) {
                    thInuser = shouhouTuihuan.getInuser();
                    shouhouTuihuan.setIsdel(true);
                    shouhouTuihuanService.updateById(shouhouTuihuan);
                }

                //更新退款金额信息
                netPayRefundInfoService.updateRefundPriceByTuihuanId(tuihuanId);

                //退款 或者 换其他型号 原始订单有相应赠品 如果程序自动提交了赠品退款 则 赠品退款也需要删除
                if (TuihuanKindEnum.TK.getCode().equals(tuihuanKind) || TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind)) {

                    if (userId != null && basketId != null && returnSaveMoney.compareTo(BigDecimal.ZERO) != 0) {
                        R<String> saveMoneyR = shouhouExService.saveMoney(userId, basketId, BigDecimal.valueOf(Math.abs(returnSaveMoney.doubleValue())), "系统", "OVG退款撤销余额返回", SaveMoneyEkindEnum.SAVE_MONEY_EKIND_20.getCode(), areaId, null, true, 0, false);
                        if (saveMoneyR.getCode() != ResultCode.SUCCESS) {

                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return R.error("OVG退款撤销余额返回失败！");
                        }
                    }
                    R<Boolean> delTuiR = shouhouExService.delTuiEx(tuihuan.getShouhouId(), tuihuanKind);
                    if (delTuiR.getCode() != ResultCode.SUCCESS) {
                        return R.error("撤销失败");
                    }

                }
                //同时删除退换亏损数据
                shouhouReturncbService.remove(new LambdaQueryWrapper<ShouhouReturncb>().eq(ShouhouReturncb::getOldshouhouId, tuihuan.getShouhouId() == null ? 0 : tuihuan.getShouhouId()));

            } catch (Exception e) {
//                e.printStackTrace();
                log.error("撤销异常：{}", e.getMessage());
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("撤销失败！");
            }

            if (StringUtils.isNotEmpty(remark)) {
                Integer wxId = tuihuan.getShouhouId() == null ? 0 : tuihuan.getShouhouId();
                String msgInfo = "";
                String link = moaUrlSource.getWxDetail(wxId.toString());
                R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(thInuser);
                if (userVoR.getCode() == ResultCode.SUCCESS && userVoR.getData() != null) {
                    msgInfo = "（已通知提交人：" + thInuser + "）";
                    String message = "你提交的维修单号<a href=" + link + ">" + wxId + "</a>被撤销，撤销原因：" + "mark" + "，请重新按照规范进行提交（撤销人：" + userName + "）";

                    weixinUserService.senWeixinAndOaMsg(message, message, link, userVoR.getData().getCh999Id().toString(), OaMesTypeEnum.SHTZ.getCode().toString());
                }
                shouhouService.saveShouhouLog(tuihuan.getShouhouId(), "撤销退换申请操作，原因：" + remark + msgInfo, userName, null, false);

            }

        }

        return R.success("撤销成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> groupTuihuanDel(Integer tuihuanId, String remark) {
        TuiHuanService tuiHuanService = SpringUtil.getBean(TuiHuanService.class);
        ShouhouTuiHuanPo tuihuan = tuiHuanService.getById(tuihuanId);
        if (ObjectUtil.isNull(tuihuan) || Boolean.TRUE.equals(tuihuan.getIsdel())) {
            throw new CustomizeException("错误，退换记录不存在！");
        }
        if (Boolean.TRUE.equals(tuihuan.getCheck3())) {
            throw new CustomizeException("已退款办理,不允许删除！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String userName = oaUserBO.getUserName();

        //查找是否已经扣除ovg返现金额 如果已扣除，需要加回来
        BigDecimal returnSaveMoney = BigDecimal.ZERO;
        Integer userId = null;
        Integer basketId = null;
        Integer areaId = null;
        if (TuihuanKindEnum.TK.getCode().equals(tuihuan.getTuihuanKind()) || TuihuanKindEnum.HQTXH.getCode().equals(tuihuan.getTuihuanKind())) {
            ShouhouHuanBo info = shouhouService.getShouhouHuan(tuihuan.getShouhouId());
            if (info != null) {
                areaId = info.getAreaId();
                Integer ishuishou = info.getIshuishou() == null ? 0 : info.getIshuishou();
                if (ishuishou == 0) {
                    userId = this.getUserIdBySubId(info.getSubId() == null ? 0 : info.getSubId());
                    HuanMkcBo mkc = basketService.huanMKC(userId, info.getMkcId(), info.getImei(), ishuishou);
                    if (mkc != null) {
                        basketId = mkc.getBasketId();
                    }
                }
            }

            if (userId != null && basketId != null) {
                List<SaveMoney> saveMoneyList = saveMoneyService.list(new LambdaQueryWrapper<SaveMoney>().select(SaveMoney::getMoney)
                        .eq(SaveMoney::getUserid, userId).eq(SaveMoney::getSubId, basketId).eq(SaveMoney::getKind, 20)
                        .eq(SaveMoney::getComment, "OVG退款余额退回").orderByDesc(SaveMoney::getDtime));
                if (CollectionUtils.isNotEmpty(saveMoneyList)) {
                    returnSaveMoney = saveMoneyList.get(0).getMoney();
                }
            }
        }

        String thInuser = tuihuan.getInuser();

        Integer tuihuanKind = tuihuan.getTuihuanKind() == null ? 0 : tuihuan.getTuihuanKind();
        if (TuihuanKindEnum.TWXF.getCode().equals(tuihuanKind)) {
            shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getIstui, null).eq(Shouhou::getId, tuihuan.getShouhouId()));
        }
        tuiHuanService.lambdaUpdate().eq(ShouhouTuiHuanPo::getIsdel,false).isNull(ShouhouTuiHuanPo::getCheck3)
                .eq(ShouhouTuiHuanPo::getId,tuihuanId).set(ShouhouTuiHuanPo::getIsdel,true).update();

        //退款 或者 换其他型号 原始订单有相应赠品 如果程序自动提交了赠品退款 则 赠品退款也需要删除
        if (TuihuanKindEnum.TK.getCode().equals(tuihuanKind) || TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind)) {

            if (userId != null && basketId != null && returnSaveMoney.compareTo(BigDecimal.ZERO) != 0) {
                R<String> saveMoneyR = shouhouExService.saveMoney(userId, basketId, BigDecimal.valueOf(Math.abs(returnSaveMoney.doubleValue())), "系统", "OVG退款撤销余额返回", SaveMoneyEkindEnum.SAVE_MONEY_EKIND_20.getCode(), areaId, null, true, 0, false);
                if (saveMoneyR.getCode() != ResultCode.SUCCESS) {

                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error("OVG退款撤销余额返回失败！");
                }
            }
            R<Boolean> delTuiR = shouhouExService.delTuiEx(tuihuan.getShouhouId(), tuihuanKind);
            if (delTuiR.getCode() != ResultCode.SUCCESS) {
                return R.error("撤销失败");
            }

        }
        //同时删除退换亏损数据
        shouhouReturncbService.remove(new LambdaQueryWrapper<ShouhouReturncb>().eq(ShouhouReturncb::getOldshouhouId, tuihuan.getShouhouId() == null ? 0 : tuihuan.getShouhouId()));

        if(TuihuanKindEnum.HJT.getCode().equals(tuihuanKind)){
            shouhouBigProTuihuanService.mkcDellogsh2(oaUserBO.getAreaId(), tuihuan.getBasketId(), userName);
        }
        //记录操作日志

        if (StringUtils.isNotEmpty(remark)) {
            Integer wxId = tuihuan.getShouhouId() == null ? 0 : tuihuan.getShouhouId();
            String msgInfo = "";
            String link = moaUrlSource.getWxDetail(wxId.toString());
            R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(thInuser);
            if (userVoR.getCode() == ResultCode.SUCCESS && userVoR.getData() != null) {
                msgInfo = "（已通知提交人：" + thInuser + "）";
                String message = "你提交的维修单号<a href=" + link + ">" + wxId + "</a>被撤销，撤销原因：" + "mark" + "，请重新按照规范进行提交（撤销人：" + userName + "）";

                weixinUserService.senWeixinAndOaMsg(message, message, link, userVoR.getData().getCh999Id().toString(), OaMesTypeEnum.SHTZ.getCode().toString());
            }
            shouhouService.saveShouhouLog(tuihuan.getShouhouId(), "撤销退换申请操作，原因：" + remark + msgInfo, userName, null, false);

        }

        return R.success("撤销成功");
    }

    /**
     * 售后换机头转入撤销
     * @param areaId
     * @param mkcId
     * @param userName
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mkcDellogsh2(Integer areaId, Integer mkcId, String userName) {
        ProductMkcService productMkcService = SpringUtil.getBean(ProductMkcService.class);
        ProductMkc productMkc = productMkcService.getById(mkcId);
        if(Objects.isNull(productMkc)){
            throw new CustomizeException("mkc错误或地区不符或仅现货才可作废");
        }
        if(NumberConstant.THREE.equals(productMkc.getKcCheck())){
            return;
        }
        MkcDellogsService mkcDellogsService = SpringUtil.getBean(MkcDellogsService.class);
        MkcDellogs mkcDellogs = new MkcDellogs().setMkcId(mkcId).setInuser(userName).setAreaid(areaId)
                .setComment("售后换机头撤销，转出售后操作").setKinds("h2").setPrice1(productMkc.getInbeihuoprice())
                .setPrice2(null).setPpriceid(productMkc.getPpriceid()).setFrareaid(Convert.toStr(productMkc.getFrareaid()))
                .setYouhuiPrice(null).setLpToAreaId(null).setCheck1user(userName).setCheck1(Boolean.TRUE)
                .setCheck2user(userName).setCheck2(Boolean.TRUE);
        mkcDellogsService.insertMkcDellogs(mkcDellogs,userName);
        if (mkcDellogs.getId() != null){
            //更新库存状态
            //更新库存
            //string sql_mkc = "update product_mkc set kc_check=3 where kc_check=6 and areaid=@area and basket_id is null and id=@mkc_id";

            boolean result = productMkcService.lambdaUpdate().eq(ProductMkc::getAreaid, areaId).eq(ProductMkc::getKcCheck, NumberConstant.SIX)
                    .isNull(ProductMkc::getBasketId).eq(ProductMkc::getId, mkcId).set(ProductMkc::getKcCheck, NumberConstant.THREE)
                    .update();
            if (!result)
            {
                throw new CustomizeException("审核失败，存在的原因：1.不是现货！2.地区不一样;");
            }
        }
    }

    @Override
    public R<String> getReturnMobile(Integer subId, String type) {

        ShouhouTuihuanReturnDataBo dt = subService.getReturnDataTable(subId, type);
        if (dt != null) {
            return R.success("操作成功", dt.getMobile());
        }
        return R.error("获取失败");
    }

    private Integer getUserIdBySubId(Integer subId) {
        Integer userId = subService.getUserIdBySubId(subId);
        return userId == null ? subService.getHisUserIdBySubId(subId) : userId;
    }

    private Integer getPpidByShouhouId(Integer shouhouId) {
        Shouhou shouhou = shouhouService.getById(shouhouId);
        Integer ppid = shouhou.getPpriceid();
        return shouhouId;
    }

    private BigDecimal getMemberpriceByPpid(Integer ppid) {
        BigDecimal price = BigDecimal.ZERO;
        List<Productinfo> productinfoList = productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, ppid));
        if (CollectionUtils.isNotEmpty(productinfoList)) {
            price = productinfoList.get(0).getMemberprice() == null ? BigDecimal.ZERO : productinfoList.get(0).getMemberprice();
        }
        return price;
    }

    @Override
    public R<String> sendCode(Integer tuihuanid, Integer shouhouid, Integer areaid, Integer islp) {
        String mobile = shouhouBigProTuihuanMapper.getSubMobileFromSub(shouhouid);
        if (islp.equals(1)) {
            mobile = shouhouBigProTuihuanMapper.getSubMobileFromRecover(shouhouid);
        }
        String code = RandomUtil.randomNumbers(6);
        String message = String.format("您正在进行[售后退换机服务]操作，验证码%s。如非本人操作请忽略。", code);
        if (StrUtil.isNotBlank(mobile)) {
            R<Boolean> result = smsService.sendSms(mobile, message, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(two)), "系统", smsService.getSmsChannelByTenant(areaid, ESmsChannelTypeEnum.VERIFICATION_CODE));
            if (Boolean.TRUE.equals(result.getData())) {
                redisTemplate.opsForValue().set(RedisKeys.SHOUHOU_TUIHUAN_MIAOTUI_CODE_PREFIX + tuihuanid, code, Duration.ofMinutes(1000));
                return R.success("验证码发送成功");
            } else {
                return R.error("验证码发送过程中出现异常!");
            }

        } else {
            return R.error("无法获取会员手机号码！");
        }

    }

    /**
     * 保存退换机授权短信验证码
     *
     * @param tuihuanid 退换货id
     * @param code      验证码
     * @return R
     */
    @Override
    @Transactional
    public R<String> saveCode(Integer tuihuanid, String code) {
        if (StrUtil.isBlank(code) || !code.equals(redisTemplate.opsForValue().get(RedisKeys.SHOUHOU_TUIHUAN_MIAOTUI_CODE_PREFIX + tuihuanid))) {
            return R.error("验证码错误");
        }
        ShouhouTuihuan entity = shouhouTuihuanService.getByIdSqlServer(tuihuanid);
        if (entity != null && StrUtil.isBlank(entity.getPaymobilecode())) {
            entity.setPaymobilecode(code);
            if (shouhouTuihuanService.updateById(entity)) {
                String qrCodeUrl = urlSource.getWeixinSceneQrcode(tuihuanid.toString());
                String url = HttpUtil.get(qrCodeUrl);
                if (StrUtil.isNotBlank(url)) {
                    return R.success("保存成功!", url);
                }
            }
        }
        return R.error("保存失败");
    }

    @Override
    public R<HuanInfo> huanInfo(Integer id, Integer kind, Integer mkcid, Long subid) {
        OaUserBO currentUser = abstractCurrentRequestComponent.getCurrentStaffId();
        if (ObjectUtil.equal(subid, 0)) {
            String ppriceid = shouhouBigProTuihuanMapper.getHuanInfo1(id, currentUser.getAreaId());
            HuanInfo k = shouhouBigProTuihuanMapper.getHuanInfo2(mkcid, currentUser.getAreaId());
            if (StrUtil.isBlank(ppriceid)) {
                return R.error("售后机商品id为空，不可通过此系统更换！");
            }
            if (k == null) {
                return R.error("id错误");
            }
            if (ppriceid.equals(k.getPprikceid())) {
                return R.success(k);
            }
            switch (kind) {
                case 1:
                    return R.error("机型或颜色不相同");
                case 2:
                    return R.error("颜色不相同");
                default:
                    return R.error("商品不匹配");
            }
        } else {
            Shouhou r = shouhouBigProTuihuanMapper.GetHuanBySub1(id, currentUser.getAreaId());
            if (r == null) {
                return R.error("地区错误，请确认机器是否已经转出");
            }
            Sub k = shouhouBigProTuihuanMapper.GetHuanBySub2(subid, r.getUserid(), currentUser.getAreaId());
            if (k == null) {
                return R.error("id错误或地区错误!");
            }
            String msg = "";
            if (shouyinOtherService.getOtherShouYinBo(r.getSubId(), 7) != null && !shouyinOtherService.isJiexinPPid(subid)) {
                msg = "【原单为捷信0息分期，新订单非捷信0期分期，请注意退款金额】";
            }
            HuanInfo result = new HuanInfo();
            DecimalFormat df = new DecimalFormat("#.00");
            result.setMoney(k.getYingfuM().equals(k.getYifuM()) ? df.format(k.getYingfuM()) : df.format(k.getYingfuM().subtract(k.getYifuM())));
            return R.success(msg, result);
        }
    }

    @Override
    public R<Boolean> inviteWeixinMiaotui(Integer tuihuanId) {
        List<Shouhou> resultList = shouhouBigProTuihuanMapper.checkUserIsBindWeixin(tuihuanId);
        if (CollectionUtils.isEmpty(resultList)) {
            return R.error("用户没有关注微信，无法发送信息");
        }
        Integer areaId = resultList.get(0).getAreaid();
        if (areaId == null) {
            return R.error("门店信息不存在");
        }
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
        if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() == null) {
            return R.error("获取门店信息错误");
        }
        AreaInfo areaInfo = areaInfoR.getData();
        if (areaInfo.getIsSend()) {
            WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(resultList.get(0).getUserid().intValue());
            if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid())) {

                String url = mUrlSource.getQuickRefund(resultList.get(0).getSubId().toString());
                smallproForwardExService.pushInfoToWechatTemplateMessage(weixinUser.getOpenid(), "身份验证", "", DateUtil.localDateTimeToString(LocalDateTime.now()), "欢迎使用微信秒退，现在邀请您进行身份验证。", url, weixinUser.getWxid());
            }

        }
        shouhouTuihuanService.update(new LambdaUpdateWrapper<ShouhouTuihuan>().set(ShouhouTuihuan::getIsSendWeixinInviteCode, true).eq(ShouhouTuihuan::getId, tuihuanId));

        return R.success("操作成功");
    }

    @Override
    public R<String> sendReturnCode(Integer subId, String type) {

        ShouhouTuihuanReturnDataBo tuihuanReturnDataBo = subService.getReturnDataTable(subId, type);
        if (tuihuanReturnDataBo == null) {
            return R.error("无效数据！");
        }
        String mobile = tuihuanReturnDataBo.getMobile();
        Integer userId = tuihuanReturnDataBo.getUserid();
        String uCodeKey = subId + "_returnMoney_" + type;
        String uKey = "";
        if (!redisTemplate.hasKey(uCodeKey)) {
            uKey = RandomUtil.randomNumbers(6);
            redisTemplate.opsForValue().set(uCodeKey, uKey, 5, TimeUnit.MINUTES);
        } else {
            uKey = redisTemplate.opsForValue().get(uCodeKey).toString();
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Integer areaId = oaUserBO.getAreaId();
        String msg = String.format("您正在进行[订单%s退款]操作，验证码%s。如非本人操作请忽略。",subId, uKey);
        smsService.sendSms(mobile, msg, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(two)), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.VERIFICATION_CODE));

//        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
//        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
//            if (areaInfoR.getData().getIsSend()) {
//                String webName = areaInfoR.getData().getPrintName().replace("九机网", "九机");
//                DateTimeFormatter df = DateTimeFormatter.ofPattern("HH:mm");
//                //String msg = "尊敬的" + webName + "客户，您于" + df.format(LocalDateTime.now()) + "分在" + areaInfoR.getData().getPrintName() + areaInfoR.getData().getAreaName() + "店对订单" + subId + "进行退款操作，验证码" + uKey;
//                String msg = String.format("您正在进行[订单%s退款]操作，验证码%s。如非本人操作请忽略。",subId, uKey);
//               // msg = areaInfoR.getData().getXtenant().equals(Integer.valueOf(JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode().intValue())) ? msg + "如有疑问可致电服务监督电话：************。" : msg;
//                smsService.sendSms(mobile, msg, DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(two)), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
//            }
//        }
        return R.success("操作成功");
    }

    @Override
    public R<String> transact(Integer id, Integer mkcId, List<String> myRank, Integer areaId, String user,
                              Integer areaKind1) {

        String url = inwcfUrlSource.getShouhouTransact();

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        Transact transact = new Transact();
        transact.setShouhouId(id).setArea_kind1(areaKind1).setAreaid(areaId).setRank(myRank).setBasket_id(mkcId).setUser(user);
        OaApiReq oaApiReq = getOaApiReq(transact);

        oaApiReq.setData(null);
        Map<String, Object> stringObjectMap = CommenUtil.transBean2Map(oaApiReq);
        stringObjectMap.put("timestamp", oaApiReq.getTimeStamp());
        stringObjectMap.put("Data", transact);
        HashMap<String, Object> map = new HashMap<>();
        map.put("item", stringObjectMap);
        String params = JSONObject.toJSONString(map);
        try {
            String kcResult = HttpClientUtil.post(url, params, headers);
            R r = JSON.parseObject(JSON.parse(kcResult).toString(), R.class);
            if (r.getCode() == 0) {
                return R.success(r.getUserMsg());
            } else {
                log.error("售后退换办理操作失败" + kcResult);
                return R.error("售后退换办理操作失败，返回结果为：" + kcResult);
            }
        } catch (Exception e) {
            log.error("售后退换办理操作失败", e);
            return R.error("售后退换办理操作失败，返回结果为：" + e.getMessage());
        }
    }

    @Override
    public R<List<SmallproPayInfoBO>> getAlipayPayInfo(AlipayInfoReq req) {

        Integer type = 1;
        List<String> ids = new LinkedList<>();
        List<String> tuiWayList = new LinkedList<>();
        List<SmallproPayInfoBO> payInfoList;
        ids.add(String.valueOf(req.getSubId()));
        if (Arrays.asList(TuihuanKindEnum.TDJ.getCode(), TuihuanKindEnum.TPJ.getCode()).contains(req.getTuiHuanKind())) {
            Integer subPid = shouhouMapper.getSubPidBySubId(Long.valueOf(req.getSubId()));
            if (CommenUtil.isNotNullZero(subPid)) {
                ids.add(subPid.toString());
            }
        } else if (Objects.equals(req.getTuiHuanKind(), TuihuanKindEnum.TDJ_LP.getCode())) {
            type = 3;
        } else if (Arrays.asList(TuihuanKindEnum.TWXF.getCode(), TuihuanKindEnum.TDJ_WXF.getCode()).contains(req.getTuiHuanKind())) {
            type = 2;
        } else if (Objects.equals(10, req.getTuiHuanKind())) {
            type = 10;
        }

        String tuiWay = req.getTuiWay();
        if (StringUtils.isNotEmpty(tuiWay)) {
            tuiWay = tuiWay.replaceAll("返回", "");
            tuiWayList.add(tuiWay);
        }
        Integer subId = req.getSubId();
        if (CommenUtil.isNotNullZero(subId)) {
            if (Objects.equals(tuiWay, "微信")) {
                tuiWayList.add("微信");
                tuiWayList.add("微信APP");
            }
            if (Objects.equals(tuiWay, "兴业银行扫码")) {
                tuiWayList.add("扫码枪");
                tuiWayList.add("兴业银行扫码");
            }
            payInfoList = smallproMapper.getPayInfoBySubId(type, ids, tuiWayList);
        } else {
            payInfoList = smallproMapper.getPayInfoByAfterSaleREId(req.getTuiHuanId());
        }
        return R.success(payInfoList);
    }

    private OaApiReq getOaApiReq(Transact aftersaleRefund) {
        OaApiReq oaApiReq = new OaApiReq();
        oaApiReq.setTimeStamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        oaApiReq.setData(aftersaleRefund);
        String secret = batchReturnMkcDao.getSecretByCode(SecretEnum.SH_THJ.getCode());
        oaApiReq.setSign(OaVerifyUtil.createSign(
                secret, oaApiReq));

        return oaApiReq;
    }


}
