package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.NoticePushBeforeCustomerArriveStoreBo;
import com.jiuji.oa.afterservice.bigpro.dao.AfterSaleAppointmentMapper;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.service.AfterSaleAppointmentService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> quan
 * @description: 售后预约服务实现类
 * @date 2021/9/1 19:58
 */
@Service
@Slf4j
public class AfterSaleAppointmentServiceImpl implements AfterSaleAppointmentService {

    @Resource
    private AfterSaleAppointmentMapper afterSaleAppointmentMapper;
    @Resource
    private SmsService smsService;
    @Resource
    private ShouhouYuyueService yuYueService;
    @Resource
    private SysConfigClient sysConfigClient;

    private static String APPOINTMENT_MOA_URL = "{0}/new/#/afterService/detail/{1,number,#}";
    private static String CONTENT = "【预约到店提醒】预约单 {0,number,#}，预计到店时间为{1}，请提前跟进。";

    @Override
    public R<String> sendNoticeMsgToStaffBeforeCustomerArriveStore() {
        List<NoticePushBeforeCustomerArriveStoreBo> noticeAppointmentList = afterSaleAppointmentMapper.getAppointmentInfoBeforeCustomerArriveStore();
        if (CollectionUtils.isEmpty(noticeAppointmentList)) {
            return R.success("操作成功");
        }
        List<Integer> roleIds = Arrays.asList(NumberConstant.NINE, NumberConstant.TWENTY, NumberConstant.TWENTY - NumberConstant.TWO, NumberConstant.ONE_HUNDRED_EIGHTY);
        List<Integer> areaIds = noticeAppointmentList.stream().map(NoticePushBeforeCustomerArriveStoreBo::getAreaId).collect(Collectors.toList());
        List<NoticePushBeforeCustomerArriveStoreBo> workingUsers = afterSaleAppointmentMapper.getWorkingCh999IdByAreaIdsAndRoleIds(roleIds, areaIds);
        if (CollectionUtils.isEmpty(workingUsers)) {
            return R.success("操作成功");
        }
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        noticeAppointmentList.stream().forEach(p -> {
            List<String> pushIds = workingUsers.stream()
                    .filter(a -> Objects.equals(p.getAreaId(), a.getAreaId())).map(NoticePushBeforeCustomerArriveStoreBo::getCh999Id).map(String::valueOf).collect(Collectors.toList());
            Optional.ofNullable(pushIds).filter(CollectionUtils::isNotEmpty)
                    .ifPresent(k -> {
                        log.info("预约单号：" + p.getId() + ",门店ID：" + p.getAreaId() + " 推送用户ID：" + String.join(SignConstant.COMMA, pushIds));
                        String link = MessageFormat.format(APPOINTMENT_MOA_URL, host, p.getId());
                        try {
                            link = URLEncoder.encode(link, StandardCharsets.UTF_8.name());
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        smsService.sendOaMsg(MessageFormat.format(CONTENT, p.getId(), p.getArriveTime()), link, String.join(SignConstant.COMMA, pushIds), OaMesTypeEnum.SYSTEM);
//                        smsService.sendOaMsg(MessageFormat.format(CONTENT, p.getId(), p.getArriveTime()), link, "10722,13494", OaMesTypeEnum.SYSTEM);

                    });
        });

        List<Integer> appointmentIds = noticeAppointmentList.stream().map(NoticePushBeforeCustomerArriveStoreBo::getId).collect(Collectors.toList());
        //更新推送状态
        yuYueService.update(new LambdaUpdateWrapper<ShouhouYuyue>()
                .set(ShouhouYuyue::getIsPush, NumberConstant.ONE)
                .in(ShouhouYuyue::getId, appointmentIds));
        return R.success("操作成功");
    }
}
