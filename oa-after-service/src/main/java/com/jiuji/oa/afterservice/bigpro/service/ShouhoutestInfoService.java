package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouTestResultBo;
import com.jiuji.oa.afterservice.bigpro.bo.WeixiuTestOptionBo;
import com.jiuji.oa.afterservice.bigpro.po.ShouhoutestInfo;
import com.jiuji.oa.afterservice.bigpro.vo.req.WeixiuTestOptionReq;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
public interface ShouhoutestInfoService extends IService<ShouhoutestInfo> {
    /**
     * 检查是否有测试结果
     * @param shouhouIds
     * @return
     */
    List<ShouhouTestResultBo> checkShouhouTestResult(List<Integer> shouhouIds);

    /**
     * 售后维修机 测试提交保存接口 对应C#实现（SubmitWeixiuTestOptions）
     * @param req
     * @return
     */
    R<Boolean> saveTestOptions(WeixiuTestOptionReq req);

    List<WeixiuTestOptionBo> getWeixiuTestOptions();

}
