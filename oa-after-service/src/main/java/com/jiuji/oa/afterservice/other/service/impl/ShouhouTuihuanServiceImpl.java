package com.jiuji.oa.afterservice.other.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.TuihuanBuyPriceOutBo;
import com.jiuji.oa.afterservice.bigpro.enums.FaultTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.IshuishouEnum;
import com.jiuji.oa.afterservice.bigpro.po.PurchaseOrderResult;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.WeixinUser;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.ModuleVersionKeys;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.source.ConstantsSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.*;
import com.jiuji.oa.afterservice.other.dao.ShouhouTuihuanMapper;
import com.jiuji.oa.afterservice.other.dao.ShouyinOtherMapper;
import com.jiuji.oa.afterservice.other.document.RecoverBasketSaleLogNew;
import com.jiuji.oa.afterservice.other.document.RecoverSaleLogNewDocument;
import com.jiuji.oa.afterservice.other.enums.OtherShouYinTypeEnum;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.po.Shouying;
import com.jiuji.oa.afterservice.other.repository.RecoverBasketSaleLogRepository;
import com.jiuji.oa.afterservice.other.repository.RecoverSaleLogRepository;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.other.service.ShouyinOtherService;
import com.jiuji.oa.afterservice.other.service.ShouyingService;
import com.jiuji.oa.afterservice.other.vo.req.ShouhouTuihuanReq;
import com.jiuji.oa.afterservice.other.vo.res.RecoverSubLogRes;
import com.jiuji.oa.afterservice.other.vo.res.ZhonyiRes;
import com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper;
import com.jiuji.oa.afterservice.refund.service.RefundMachineService;
import com.jiuji.oa.afterservice.smallpro.po.NetpayRecord;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.service.NetpayRecordService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.enums.AfterExamineEnum;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.sys.vo.res.AfterExamineConfigVO;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-15
 */
@Slf4j
@Service
public class ShouhouTuihuanServiceImpl extends ServiceImpl<ShouhouTuihuanMapper, ShouhouTuihuan> implements ShouhouTuihuanService {

    @Resource
    private ShouyinOtherMapper shouyinOtherMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ConstantsSource constantsSource;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private SubService subService;
    @Autowired
    private ShouyingService shouyingService;
    @Lazy
    @Autowired
    private SmallproService smallproService;
    @Autowired
    private RecoverSaleLogRepository recoverSaleLogRepository;
    @Autowired
    private RecoverBasketSaleLogRepository recoverBasketSaleLogRepository;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private WeixinUserService weixinUserService;
    @Autowired
    private NetpayRecordService netpayRecordService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private ShouyinOtherService shouyinOtherService;
    @CreateCache(name = "afterservice:ShouhouTuihuanService.countAfterServicesDiscount", expire = 5,timeUnit = TimeUnit.HOURS, cacheType = CacheType.BOTH)
    @CachePenetrationProtect
    private Cache<String, Integer> userCountCache;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ShouhouRefundMoneyMapper refundMoneyMapper;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    @Override
    public List<ShouhouTuihuan> listSqlServer(Wrapper wrapper) {
        return baseMapper.listSqlServer(wrapper);
    }

    @Override
    public ShouhouTuihuan getByIdSqlServer(Integer id) {
        return baseMapper.getByIdSqlServer(id);
    }


    @Override
    public TuihuanBuyPriceOutBo getTuihuanBuyPrice(TuihuanBuyPriceOutBo tuihuanBuyPrice) {
        BigDecimal giftPrice = BigDecimal.ZERO;
        BigDecimal baitiaoPrice = BigDecimal.ZERO;
        BigDecimal kuBaitiaoPrice = BigDecimal.ZERO;
        BigDecimal yifuM = BigDecimal.ZERO;
        Integer userId = 0;
        Integer tradeDay = 0;
        Integer tradeType = 0;
        Integer basketId ;
        LocalDateTime tradeDate = null;
        String peizhi = "";
        String piaoType = "";
        BigDecimal coinM = null;

        tuihuanBuyPrice.setGiftPrice(giftPrice);
        tuihuanBuyPrice.setBaitiaoPrice(baitiaoPrice);
        tuihuanBuyPrice.setKuBaitiaoPrice(kuBaitiaoPrice);
        tuihuanBuyPrice.setYifuM(yifuM);
        tuihuanBuyPrice.setUserId(userId);
        tuihuanBuyPrice.setTradeDay(tradeDay);
        tuihuanBuyPrice.setTradeType(tradeType);
        tuihuanBuyPrice.setTradeDate(tradeDate);
        tuihuanBuyPrice.setPeizhi(peizhi);
        tuihuanBuyPrice.setPiaoType(piaoType);

        if (Objects.equals(tuihuanBuyPrice.getIsHuiShou(),2)) {
            tuihuanBuyPrice.setPrice(BigDecimal.ZERO);
            tuihuanBuyPrice.setBasketId(0);
            tuihuanBuyPrice.setCoinM(BigDecimal.ZERO);
            return tuihuanBuyPrice;
        }
        TuihuanBuyPriceBo tuihuanBuyPriceBo = baseMapper.getTuihuanInfo(tuihuanBuyPrice.getImei(), tuihuanBuyPrice.getMkcId(), tuihuanBuyPrice.getIsHuiShou(),tuihuanBuyPrice.getBuySubId());
        if (tuihuanBuyPriceBo == null) {
            tuihuanBuyPrice.setPrice(BigDecimal.ZERO);
            return tuihuanBuyPrice;
        }
        BigDecimal price = tuihuanBuyPriceBo.getPrice() == null ? BigDecimal.ZERO : tuihuanBuyPriceBo.getPrice();
        userId = tuihuanBuyPriceBo.getUserid();
        tuihuanBuyPrice.setUserId(userId);

        if (CommenUtil.isNullOrZero(tuihuanBuyPrice.getIsHuiShou())) {

            //判断赠品金额 排除贴膜成本
            giftPrice = basketService.getSubGiftPrice(tuihuanBuyPriceBo.getSubId(), tuihuanBuyPriceBo.getBasketId(), true);
            //计算大件退款金额 要加上贴膜成本
            BigDecimal filmPrice = basketService.getSubGiftPrice(tuihuanBuyPriceBo.getSubId(), tuihuanBuyPriceBo.getBasketId(), false);
            filmPrice = filmPrice == null ? BigDecimal.ZERO : filmPrice;
            price = price.add(filmPrice);
            basketId = tuihuanBuyPriceBo.getBasketId();
            //排除代金券金额
            price = price.subtract(subService.getSubDJQPrice(tuihuanBuyPriceBo.getSubId()));

            OtherShouYinBo baitiaoObj = shouyinOtherService.getOtherShouYinBo(tuihuanBuyPriceBo.getSubId(), 10);
            if (baitiaoObj != null) {
                baitiaoPrice = baitiaoObj.getPrice();
            }

            tradeDate = tuihuanBuyPriceBo.getTradeDate1();
            if (tuihuanBuyPriceBo.getModidate() != null && tuihuanBuyPriceBo.getTradeDate1() != null) {
                tradeDay = (int) Duration.between(tuihuanBuyPriceBo.getModidate(), tuihuanBuyPriceBo.getTradeDate1()).toDays();
            }
            tradeType = 1;
            //优品
            if (tuihuanBuyPriceBo.getType() != null && tuihuanBuyPriceBo.getType() == 22) {
                tradeType = 2;
            }
            peizhi = tuihuanBuyPriceBo.getProductPeizhi();
            yifuM = tuihuanBuyPriceBo.getYifuM();
            if (tuihuanBuyPriceBo.getCoinM() != null && tuihuanBuyPriceBo.getCoinM().intValue() > 0) {
                if (tuihuanBuyPriceBo.getPrice().compareTo(tuihuanBuyPriceBo.getYifuM()) > 0) {
                    coinM = tuihuanBuyPriceBo.getPrice().subtract(tuihuanBuyPriceBo.getYifuM());
                }
            }

            //获取库分期金额
            kuBaitiaoPrice = baseMapper.getKcFqje(tuihuanBuyPriceBo.getSubId());
            //获取发票信息
            Integer kind = baseMapper.getFapiaoKindInfo(tuihuanBuyPriceBo.getSubId(), tuihuanBuyPrice.getIsHuiShou());
            if (kind != null) {
                piaoType = String.valueOf(kind);
            }
        } else if (Objects.equals(tuihuanBuyPrice.getIsHuiShou(),1)) {
            tradeDate = tuihuanBuyPriceBo.getTradeDate1();
            if (tuihuanBuyPrice.getModiDate() != null && tuihuanBuyPriceBo.getTradeDate1() != null) {
                tradeDay = (int) Duration.between(tuihuanBuyPrice.getModiDate() , tuihuanBuyPriceBo.getTradeDate1()).toDays();
            }
            tradeType = 3;
            coinM = tuihuanBuyPriceBo.getCoinM();
            yifuM = tuihuanBuyPriceBo.getYifuM();
            if (coinM != null && coinM.compareTo(BigDecimal.ZERO) > 0) {
                if (tuihuanBuyPriceBo.getPrice() != null && tuihuanBuyPriceBo.getPrice().compareTo(yifuM) > 0) {
                    coinM = tuihuanBuyPriceBo.getPrice().subtract(yifuM);
                } else {
                    coinM = BigDecimal.ZERO;
                }
            }
            basketId = tuihuanBuyPriceBo.getBasketId();
            //获取良品白条金额
            OtherShouYinBo otherShouYinBo = shouyinOtherMapper.getOtherShouYinBo(tuihuanBuyPriceBo.getSubId(), OtherShouYinTypeEnum.ZYJF.getCode());
            if (otherShouYinBo != null) {
                baitiaoPrice = otherShouYinBo.getPrice();
            }
            //获取库分期金额
            kuBaitiaoPrice = baseMapper.getKcFqje(tuihuanBuyPriceBo.getSubId());
            //获取发票信息
            Integer kind = baseMapper.getFapiaoKindInfo(tuihuanBuyPriceBo.getSubId(), tuihuanBuyPrice.getIsHuiShou());
            if (kind != null) {
                piaoType = String.valueOf(kind);
            }
        } else {
            coinM = BigDecimal.ZERO;
            basketId = 0;
        }
        //扣减保价金额
        if (CommenUtil.isNullOrZero(tuihuanBuyPrice.getIsHuiShou()) && basketId != null && basketId != 0) {
            BigDecimal bjPrice = baseMapper.getKoujianBaojia(basketId);
            if (bjPrice != null && bjPrice.compareTo(BigDecimal.ZERO) > 0) {
                price = price.subtract(bjPrice);
            }
        }

        tuihuanBuyPrice.setCoinM(coinM);
        tuihuanBuyPrice.setKuBaitiaoPrice(kuBaitiaoPrice);
        tuihuanBuyPrice.setPrice(price);
        tuihuanBuyPrice.setPiaoType(piaoType);
        tuihuanBuyPrice.setBaitiaoPrice(baitiaoPrice);
        tuihuanBuyPrice.setTradeType(tradeType);
        tuihuanBuyPrice.setTradeDay(tradeDay);
        tuihuanBuyPrice.setTradeDate(tradeDate);
        tuihuanBuyPrice.setGiftPrice(giftPrice);
        tuihuanBuyPrice.setPeizhi(peizhi);
        tuihuanBuyPrice.setUserId(userId);
        tuihuanBuyPrice.setYifuM(yifuM);

        return tuihuanBuyPrice;
    }

    @Override
    public TuihuanBuyPriceBo getTuihuanInfo(String imei, Integer mkcId, Integer ishuishou) {

        return baseMapper.getTuihuanInfo(imei, mkcId, ishuishou,null);
    }

    @Override
    public BigDecimal getOvgReturnPrice(Integer basket_id, Integer userid) {
        return baseMapper.getSaveMoney(basket_id, userid);
    }

    @Override
    public ZhonyiRes getZhonyiMoney(Integer shouhouId) {
        if (shouhouId == null || shouhouId == 0) {
            return null;
        }
        Integer basketId = baseMapper.getBasketIdByShouhouId(shouhouId);
        if (basketId == null || basketId == 0) {
            return null;
        }
        Long subId = baseMapper.getSubIdByBasketId(basketId);
        if (subId == null || subId == 0) {
            return null;
        }
        ShouyinOtherSimpleBo shouyinOther = baseMapper.getShouyinOtherBySubId(subId);
        if (shouyinOther != null) {
            ZhonyiRes zhonyiRes = new ZhonyiRes();
            zhonyiRes.setSubId(subId);
            zhonyiRes.setPrice(shouyinOther.getSubPay04());
            shouyinOther.setId(shouyinOther.getId());
            return zhonyiRes;
        }
        return null;
    }

    @Override
    public R tuihuan(ShouhouTuihuanReq th) {
        BigDecimal inprice = BigDecimal.ZERO;
        Integer area = 0;
        Integer userid = 0;
        //验证码验证
        if (CommenUtil.isCheckTrue(th.getIsValidt()) && StringUtils.isNotEmpty(th.getKeyCode())) {
            Integer sub_id = th.getSubId();
            //售后退订是11 退款是5
            if (th.getTuihuanKind() != null && th.getTuihuanKind() == 11) {
                sub_id = th.getShouhouId();
            }
            R<Boolean> ret = this.validtReturnCode(sub_id, String.valueOf(th.getTuihuanKind()), th.getCodeType(),
                    th.getKeyCode());
            if (ResultCode.SUCCESS != ret.getCode()) {
                return ret;
            }
        }

        if (1 != th.getArea_kind1() && "余额".equals(th.getTuiWay())) {
            return R.error("加盟店不支持余额退款!");
        }
        // 类别11是退订，不做istui的标记
        if (th.getTuihuanKind() == 5) {
            baseMapper.updateShouhouTui(th.getShouhouId());
            th.setSubId(null);
        } else if (th.getTuihuanKind() != 7 && th.getTuihuanKind() != 11) {
            th.setSubId(null);
        }
        if ((th.getTuihuanKind() == 6 || th.getTuihuanKind() == 7 || th.getTuihuanKind() == 8) &&
                "自提点余额".equals(th.getTuiWay())) {
            Integer count = baseMapper.checkZitidianAccount(th.getSubId());
            if (count == 0) {
                return R.error("自提点账户查找失败!");
            }
        }
        if (th.getTuihuanKind() == 5) {
            Integer count =
                    baseMapper.selectCount(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getIsdel,
                            false).eq(ShouhouTuihuan::getShouhouId, th.getShouhouId()).eq(ShouhouTuihuan::getTuihuanKind, 5));
            if (count > 0) {
                return R.error("已经存在退款记录");
            }
        }
        if (Arrays.asList(3, 4, 5, 6, 7, 8, 10, 11).contains(th.getTuihuanKind())) {
            Integer count =
                    baseMapper.selectCount(new LambdaQueryWrapper<ShouhouTuihuan>().eq(ShouhouTuihuan::getIsdel,
                            false).eq(ShouhouTuihuan::getShouhouId, th.getShouhouId()).eq(ShouhouTuihuan::getTuihuanKind, th.getTuihuanKind()).isNull(ShouhouTuihuan::getCheck3));
            if (count > 0) {
                return R.error("已经存在退款记录");
            }
        }
        if (th.getTuihuanKind() == 5) {
            Shouhou shouhou = shouhouService.getById(th.getShouhouId());
            if (shouhou != null) {
                inprice = shouhou.getCostprice();
                area = shouhou.getAreaid();
            }
        } else if (Arrays.asList(6, 7, 8, 11).contains(th.getTuihuanKind())) {
            switch (th.getTuihuanKind()) {
                // 退订
                case 6:
                    Sub sub = subService.list(new LambdaQueryWrapper<Sub>().eq(Sub::getSubCheck, 1).eq(Sub::getSubId, th.getSubId())).stream().findFirst().orElse(null);
                    if (sub != null) {
                        if (th.getTuikuanM().compareTo(sub.getYifuM()) == 0) {
                            Integer count = baseMapper.bhjlCountBySubId(th.getSubId());
                            if (count > 0) {
                                return R.error("存在备货记录，不能退订，请核对！");
                            }
                        }
                        Integer bhsdCount = baseMapper.bhsdCountBySubId(th.getSubId());
                        if (CommenUtil.isNotNullZero(bhsdCount)) {
                            return R.error("存在备货锁定项，不能退订，请核对！");
                        }
                        Integer ckspCount = baseMapper.ckspCountBySubId(th.getSubId());
                        if (ckspCount > 0) {
                            return R.error("存在已出库商品，不能退订，请核对！");
                        }
                        if (th.getTuikuanM().compareTo(sub.getYifuM()) > 0) {
                            return R.error("退款金额不能大于全款！");
                        }
                        inprice = th.getTuikuanM();
                        th.setZhejiaM(BigDecimal.ZERO);
                        area = sub.getAreaId();
                        userid = sub.getUserId().intValue();
                    }
                    //支付宝优惠码的必须原路径退款
                    Integer alyhmCount = baseMapper.checkIsZfbyhm(th.getSubId(), this.aliPayYouhuiPpriceids());
                    if (CommenUtil.isNotNullZero(alyhmCount) && "支付宝(pay1)返回".equals(th.getTuiWay())) {
                        return R.error("此订单只能以【支付宝(pay1)返回】退订！");
                    }
                    if ("现金".equals(th.getTuiWay())) {
                        QueryWrapper<Shouying> qw = new QueryWrapper<>();
                        qw.lambda().eq(Shouying::getSubId, th.getSubId()).select(Shouying::getSubPay03);
                        List<BigDecimal> subcheck3List = shouyingService.listSqlServer(qw).stream().map(e -> e.getSubPay03()).collect(Collectors.toList());
                        Boolean flag = subcheck3List.stream().allMatch(e -> e.compareTo(BigDecimal.ZERO) > 0);
                        if (flag) {
                            return R.error("网上支付订单请原路径退订！");
                        }
                    }
                    break;
                case 7:
                    //退货
                    List<Long> basketIds = null;
                    if (CollectionUtils.isNotEmpty(th.getDetails())) {
                        basketIds = th.getDetails().stream().map(e -> e.getBasketId()).collect(Collectors.toList());
                        Integer tjqxNum = baseMapper.tjqxCount(basketIds);
                        if (tjqxNum > 0 && CollectionUtils.isNotEmpty(th.getRankList()) && th.getRankList().contains("7a7")) {
                            return R.error("您没有提交权限，需要7a7的权值！");
                        }
                    }
                    Smallpro smallpro = smallproService.getByIdSqlServer(th.getShouhouId());
                    if (smallpro == null || StringUtils.isEmpty(smallpro.getCodeMsg())) {
                        return R.error("请先填写保存退货验证码！");
                    }
                    if (CommenUtil.isNotNullZero(th.getSubId()) && CollectionUtils.isNotEmpty(basketIds)) {
                        List<BasketSimpleBo> basketList = baseMapper.getBaskets(th.getSubId(), basketIds);
                        if (CollectionUtils.isNotEmpty(basketList)) {
                            inprice = basketList.stream().map(e -> e.getInPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                            area = basketList.get(0).getAreaId();
                            userid = basketList.get(0).getUserId();
                        }
                    }

                    break;
                case 8:
                    // 退订【二手良品】
                    RecoverMarketInfoBo recoverMarketInfoBo = baseMapper.getRecoverMarkInfoBySubId(th.getSubId());
                    if (recoverMarketInfoBo != null) {
                        inprice = th.getTuikuanM();
                        th.setZhejiaM(BigDecimal.ZERO);
                        area = recoverMarketInfoBo.getAreaId();
                        userid = recoverMarketInfoBo.getUserId();
                    }
                    break;
                case 11:
                    Shouhou shouhou = shouhouService.getById(th.getShouhouId());
                    if (shouhou != null) {
                        inprice = th.getTuikuanM();
                        th.setZhejiaM(BigDecimal.ZERO);
                        area = shouhou.getAreaid();
                        userid = shouhou.getUserid().intValue();
                    }
                    break;
            }

            //微信秒退提交验证start
            if ("微信秒退,支付宝秒退".contains(th.getTuiWay())) {
                String type = "";
                if (th.getTuihuanKind() != null && th.getTuihuanKind() == 8) {
                    type = "2";
                }
                List<RecoverSubLogRes> subLogs = getRecoverSubLogs(th.getSubId(), "1");
                if (CollectionUtils.isNotEmpty(subLogs) && subLogs.stream().anyMatch(e -> e.getComment().contains("手机由"))) {
                    return R.error("该订单不能使用此退款方式，原因：存在手机修改记录！");
                }
                //加盟校验，加盟店非网上支付的不能提交
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(area);
                if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                    if (areaInfoR.getData().getKind1() != 1) {
                        if (Arrays.asList(6, 8, 11).contains(th.getTuihuanKind())) {
                            return R.error("暂不支持" + th.getTuiWay() + "！");
                        }
                        BigDecimal sub_pay03 = baseMapper.getSubPaySum(th.getSubId(), Arrays.asList("订金" + type, "交易" + type));
                        if (sub_pay03.compareTo(BigDecimal.ZERO) == 0) {
                            return R.error("该订单不能使用此退款方式，原因：非网上支付订单！");
                        }
                        //微信秒退,已退金额
                        BigDecimal tuiMoney = baseMapper.getWechatTuikuanM(th.getSubId());
                        if (sub_pay03.compareTo(tuiMoney.add(th.getTuikuanM())) < 0) {
                            return R.error("退款金额大于网上支付金额！");
                        }
                    }
                }
                //退定金,退配件要通知客户验证的，范老板不要删除掉
                if (Arrays.asList(6, 7).contains(th.getTuihuanKind()) && "微信秒退".equals(th.getTuiWay())) {
                    WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(userid);
                    if (weixinUser == null || StringUtils.isEmpty(weixinUser.getOpenid())) {
                        return R.error("该客户还未绑定微信");
                    }
                }
            }
            //微信秒退提交验证end
        }

        if (CommenUtil.isCheckTrue(th.getIsYuyue())) {
            th.setCurArea(area);
        }
        if (!th.getCurArea().equals(area) && th.getTuihuanKind() != 7) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(area);
            String areaName = (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) ? areaInfoR.getData().getArea() : "";
            return R.error("地区不符，请切换至" + areaName + "再操作！");
        }

        List<NetPayBo> netList = new ArrayList<>();
        if (Arrays.asList(5, 6, 7, 8, 11).contains(th.getTuihuanKind())) {
            Integer type = 1;
            if (th.getTuihuanKind() == 5 || th.getTuihuanKind() == 11) {
                type = 2;
            } else if (th.getTuihuanKind() == 8) {
                type = 3;
            }
            boolean isPayOnline = ("支付宝返回,微信返回,微信(电子)返回,微信(电子)APP返回,兴业银行扫码返回,ApplePay返回,支付宝（兴业）返回,浦发扫码返回,支付宝(pay1)返回,支付宝(dzpay)返回,浦发扫码" +
                    "(92653)返回,浦发扫码(92427)返回,兴业扫码(叁玖)返回,中信扫码(06306)返回,中信扫码(06305)返回,浦发扫码(93155)返回,兴业扫码(24833)返回,微信" +
                    "(yy)返回,微信APP(yy)返回,支付宝(yy)返回,浦发扫码(93057)返回,中信扫码(74026)返回,中信扫码(25762)返回,建行分期返回,平安扫码(39878)返回," +
                    "首信易扫码支付返回").contains(th.getTuiWay());
            if (isPayOnline && StringUtils.isEmpty(th.getNetPay())) {
                return R.error("原路径退款请先确定交易号！");
            }
            if (StringUtils.isNotEmpty(th.getTuiWay()) && isPayOnline && StringUtils.isNotEmpty(th.getNetPay())) {
                netList = JSONArray.parseArray(th.getNetPay(), NetPayBo.class);
                for (NetPayBo netPayBo : netList) {
                    NetPayRecordBo netPayRecordBo = baseMapper.getNetPayRecordInfo(type, netPayBo.getId());
                    if (netPayRecordBo != null) {
                        if (netPayBo.getPrice() != null && netPayBo.getPrice().compareTo((netPayRecordBo.getMoney().subtract(netPayRecordBo.getRefundPrice()))) > 0) {
                            String msg = "交易号[" + netPayRecordBo.getTradeNo() + "]退款金额不能大于" + netPayRecordBo.getMoney().subtract(netPayRecordBo.getRefundPrice()) + "！";
                            return R.error(msg);
                        }
                    } else {
                        return R.error("交易号查找失败！");
                    }

                }
                BigDecimal sumPrice = netList.stream().map(e -> e.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                th.setTuikuanM(sumPrice);
                th.setTuikuanM1(sumPrice);
            }

            //库分期金额自动提交退订
            if (th.getKuBaiTiaoM() != null && th.getKuBaiTiaoM().compareTo(BigDecimal.ZERO) > 0) {
                LambdaQueryWrapper<NetpayRecord> wrapper = new LambdaQueryWrapper();
                wrapper.eq(NetpayRecord::getType, type).eq(NetpayRecord::getSubNumber, th.getSubId() == null ? th.getShouhouId() : th.getSubId())
                        .eq(NetpayRecord::getPayWay, "库分期");
                List<NetpayRecord> netPayList = netpayRecordService.listSqlServer(wrapper);
                if (CollectionUtils.isNotEmpty(netPayList)) {
                    netPayList = netPayList.stream().filter(e -> e.getMoney() != null && (e.getMoney().subtract(e.getRefundPrice() == null ? BigDecimal.ZERO : e.getRefundPrice())).compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(netPayList)) {
                        BigDecimal realKuBaitiaoPrice = BigDecimal.ZERO;
                        BigDecimal tmpPrice = th.getKuBaiTiaoM();
                        for (NetpayRecord netpayRecord : netPayList) {
                            if (tmpPrice != null && tmpPrice.compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal leftPrice = netpayRecord.getMoney().subtract(netpayRecord.getRefundPrice() == null ? BigDecimal.ZERO : netpayRecord.getRefundPrice());
                                if (tmpPrice.compareTo(leftPrice) > 0) {
                                    realKuBaitiaoPrice = realKuBaitiaoPrice.add(leftPrice);
                                    netList.add(new NetPayBo(netpayRecord.getId(), leftPrice));
                                    tmpPrice = tmpPrice.subtract(leftPrice);
                                } else {
                                    realKuBaitiaoPrice = realKuBaitiaoPrice.add(tmpPrice);
                                    netList.add(new NetPayBo(netpayRecord.getId(), tmpPrice));
                                    tmpPrice = BigDecimal.ZERO;
                                }

                            }
                        }
                        th.setKuBaiTiaoM(realKuBaitiaoPrice);
                    } else {
                        R.error("未找到库分期支付记录！");
                    }
                }
            }
        }
        Integer areaId = th.getCurArea();
        if (th.getTuihuanKind() == 7) {
            Smallpro smallpro = smallproService.getByIdSqlServer(th.getSmallproid());
            if (smallpro != null) {
                areaId = smallpro.getAreaId() == null ? 0 : smallpro.getAreaId();
            }
        }
        //更新事物处理
        //手动开启事务
        TransactionStatus transactionStatus = null;
        try {
            ShouhouTuihuan shth = new ShouhouTuihuan();
            shth.setShouhouId(th.getShouhouId());
            shth.setTuihuanKind(th.getTuihuanKind());
            shth.setInuser(th.getCurUser());
            shth.setAreaid(areaId);
            shth.setComment(th.getComment() + th.getOtherDsc());
            shth.setTuiWay(th.getTuiWay());
            shth.setBankname(th.getBankname());
            shth.setBankfuming(th.getBankfuming());
            shth.setBanknumber(th.getBanknumber());
            shth.setTuikuanM(th.getTuikuanM());
            shth.setTuikuanM1(th.getTuikuanM1());
            shth.setZhejiaM(th.getZhejiaM());
            shth.setSubId(th.getSubId());
            shth.setInprice(inprice);
            shth.setBasketIds(th.getBasketIds());
            shth.setCoinM(th.getCoinM());
            shth.setSmallproid(th.getSmallproid());
            shth.setBaitiaoM(th.getBaitiaoM());
            shth.setKuBaiTiaoM(th.getKuBaiTiaoM());
            shth.setIsValidt(th.getIsValidt());
            baseMapper.insert(shth);
        } catch (Exception e) {
            log.error(e.getMessage());
//           e.printStackTrace();
        }

        //todo
        return null;
    }

    @Override
    public R<Boolean> validtReturnCode(Integer sub_id, String returnType, String validtWay, String code) {
        try {
            String uCodeKey = sub_id + "_returnMoney_" + returnType;
            //余额
            if ("1".equals(validtWay)) {
                String code1 = stringRedisTemplate.opsForValue().get(uCodeKey);
                if (StringUtils.isEmpty(code1)) {
                    return R.error("请先发送验证码");
                }
                if (code1.equalsIgnoreCase(code)) {
                    return R.success(true);
                } else {
                    return R.error("验证码错误！");
                }
            } else if ("2".equals(validtWay)) {
                // 会报sonar的bug  暂时注释 逻辑写完再放开
                // DataTable dt = getReturnDataTable(sub_id, returnType); todo
//                ReturnDataRes returnDataRes = null;
//                if (returnDataRes != null) {
//                    return this.validtPayPwd(returnDataRes.getUserId(),code);
//                } else {
                return R.error("查无相关用户信息！");
//                }
            }

        } catch (Exception ex) {
            log.error("获取退款数据异常:{}", ex.getMessage());
        }
        return R.error("获取退款数据异常！");
    }

    @Override
    public R<Boolean> validatePayPwd(Integer userid, String payPwd) {
        try {
            BBSXPUsersSimpleBo bbsxpUsersSimpleBo = baseMapper.getMemberInfoByUserId(userid);
            if (bbsxpUsersSimpleBo == null) {
                return R.error("查无相关用户");
            }
            if (StringUtils.isEmpty(bbsxpUsersSimpleBo.getSaltPay())) {
                return R.error("用户未设置支付密码");
            }
            String pwdNew = bbsxpUsersSimpleBo.getSaltPay() + payPwd + constantsSource.getSaltValue();
            String md5PwdNew = DigestUtils.md5DigestAsHex(pwdNew.getBytes(StandardCharsets.UTF_8));
            if (!md5PwdNew.equals(bbsxpUsersSimpleBo.getPayPwd())) {
                return R.error("支付密码验证失败");
            }
            return R.success(true);
        } catch (Exception e) {
            log.error("验证用户支付密码异常:{}", e.getMessage());
        }
        return R.error("验证用户支付密码异常");
    }

    @Override
    public List<Integer> aliPayYouhuiPpriceids() {
        String json = stringRedisTemplate.opsForValue().get(RedisKeys.ALI_YOUHUIMA);
        return StrUtil.splitTrim(json, StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList());
    }

    @Override
    public List<RecoverSubLogRes> getRecoverSubLogs(Integer subId, String type) {
        if ("2".equals(type)) {
            Optional<RecoverBasketSaleLogNew> rbslnOpt = recoverBasketSaleLogRepository.findById(subId);
            if (rbslnOpt.isPresent() && CollectionUtils.isNotEmpty(rbslnOpt.get().getConts())) {
                return rbslnOpt.get().getConts().stream().filter(e -> Arrays.stream(type.split(",")).collect(Collectors.toList()).contains(String.valueOf(e.getType()))).map(e -> {
                    RecoverSubLogRes recoverSubLogRes = new RecoverSubLogRes();
                    BeanUtils.copyProperties(e, recoverSubLogRes);
                    return recoverSubLogRes;
                }).collect(Collectors.toList());
            }
        }
        Optional<RecoverSaleLogNewDocument> rsndOpt = recoverSaleLogRepository.findById(subId);
        if (rsndOpt.isPresent()) {
            RecoverSaleLogNewDocument rslnd = rsndOpt.get();
            if (CollectionUtils.isNotEmpty(rslnd.getConts())) {
                return rslnd.getConts().stream().filter(e -> Arrays.stream(type.split(",")).collect(Collectors.toList()).contains(String.valueOf(e.getType()))).map(e -> {
                    RecoverSubLogRes recoverSubLogRes = new RecoverSubLogRes();
                    BeanUtils.copyProperties(e, recoverSubLogRes);
                    return recoverSubLogRes;
                }).collect(Collectors.toList());
            }
        }
        return null;
    }

    @Override
    public Boolean checkHasOpenID(Integer tuihuanId) {
        ShouhouTuihuan tuihuan = super.getById(tuihuanId);
        if (tuihuan == null || tuihuan.getCheck1() == null) {
            return false;
        }
        return !"微信秒退".equals(tuihuan.getTuiWay()) || !StringUtils.isNotEmpty(tuihuan.getPayOpenId());
    }

    @Override
    public boolean deleteById(ShouhouTuihuan shouhouTuihuan) {
        return baseMapper.deleteUserById(shouhouTuihuan);
    }

    @Override
    public BigDecimal getSaveMoneyByUserId(Integer userId) {
        return baseMapper.getSaveMoneyByUserId(userId);
    }

    @Override
    public List<ShouhouTuiHuanInfo>  getAfterServicesDiscount(Integer userId) {
        return baseMapper.getAfterServicesDiscount(userId);
    }

    @Override
    public Integer countAfterServicesDiscount(Integer userId) {
        //360天缓存5小时
        Integer count360 = userCountCache.computeIfAbsent(StrUtil.format("afterCount:{}",userId), cacheKey -> baseMapper.countAfterServicesDiscount(userId, 5, 365));
        //5天内实时查
        Integer count5 = baseMapper.countAfterServicesDiscount(userId, null, 5);

        return ObjectUtil.defaultIfNull(count360,0) + ObjectUtil.defaultIfNull(count5,0);
    }

    @Override
    public List<Integer> getDiscountSubIds(Integer userId, int year) {
        List<Integer> subIds = baseMapper.getDiscountSubIds(userId, year);

        // 是否查询历史数据
        boolean isQueryHistory = LocalDate.now().getYear() - year > 1;
        if (isQueryHistory) {
            List<Integer> historySubIds = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,()-> baseMapper.getDiscountSubIds(userId, year));
            subIds.addAll(historySubIds);
        }

        // 去重
        return subIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 良品自动退换
     * @param shouhouTuiHuanPo
     * @return
     */
    @Override
    public Boolean lpAutomaticRefund(ShouhouTuiHuanPo shouhouTuiHuanPo){
        if(ObjectUtil.isNull(shouhouTuiHuanPo)){
            return Boolean.FALSE;
        }
        Integer shouHouId = shouhouTuiHuanPo.getShouhouId();
        Integer tuihuanKind = shouhouTuiHuanPo.getTuihuanKind();
        if(XtenantEnum.isSaasXtenant() || ObjectUtil.isNull(shouHouId) || !TuihuanKindEnum.TK.getCode().equals(tuihuanKind)){
            return Boolean.FALSE;
        }
        String checkType = shouhouTuiHuanPo.getCheckType();
        if(StrUtil.isEmpty(checkType)){
            checkType =  Convert.toStr(SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.CHECK_TYPE)).get());
        }
        String faultType = shouhouTuiHuanPo.getFaultType();
        if(StrUtil.isEmpty(faultType)){
            faultType =  Convert.toStr(SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.FAULT_TYPE)).get());
        }
        Shouhou shouhou = Optional.ofNullable(shouhouService.getById(shouHouId)).orElse(new Shouhou());
        String wuLiYouText = StrUtil.format("{}{}", ShouhouExService.getWuLiYouDays(), ShouhouExService.WU_LI_YOU_TEXT);
        //良品退还剩余次数
        Integer goodLeftCount = Optional.ofNullable(refundMoneyMapper.getGoodLeftCount(shouhou.getUserid())).orElse(NumberConstant.ONE);
        String wuliyou = shouhou.getWuliyou();
        //判断是否为 7天无理由退换 并且是良品 ， 剩余次数大于0 且 故障类型“无故障”  且 审核类别“正常退货  且 处理方式“退款”
        if(wuLiYouText.equals(wuliyou) && IshuishouEnum.GOOD_PRODUCT.getCode().equals(shouhou.getIshuishou())
                && goodLeftCount>NumberConstant.ZERO && FaultTypeEnum.FAULTY.getMessage().equals(faultType)
                && RefundMachineService.ZHENG_CHANG_TUI_HUAN.equals(checkType)
                && TuihuanKindEnum.TK.getCode().equals(shouhouTuiHuanPo.getTuihuanKind())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> autoTuihuanCheck(Integer tuihuanId) {
        if (CommenUtil.isNullOrZero(tuihuanId)) {
            return R.error("退换id错误");
        }
        ShouhouTuiHuanPo shouhouTuiHuanPo = baseMapper.getPoByIdSqlServer(tuihuanId);
        if (Objects.isNull(shouhouTuiHuanPo)) {
            return R.error(StrUtil.format("退货申请[{}]不存在", tuihuanId));
        }

        int tuihuanKind = shouhouTuiHuanPo.getTuihuanKind();
        String tuihuanWay = shouhouTuiHuanPo.getTuiWay();

        //获取审批流当前流程节点
        ShouhouRefundDetailVo.ProcessStatus currStatus = ShouhouRefundService.addProcessInfo(shouhouTuiHuanPo, ShouhouRefundDetailVo.ProcessStatus.SUBMIT, new LinkedList<>());
        ShouhouRefundDetailVo.ProcessStatus nextStatus = ShouhouRefundDetailVo.ProcessStatus.nextProcess(currStatus);
        Boolean lpAutomaticRefund = lpAutomaticRefund(shouhouTuiHuanPo);
        //增加上线限制,只有上线的租户才会生效
        if (!lpAutomaticRefund && ModuleVersionKeys.isNotEnabled(ModuleVersionKeys.SHOUHOU_SHENGEHE_V1, true)) {
            return R.success(currStatus.getCode());
        }

        //大件退款、换其它型号（支付宝秒退、微信秒退）跳过该逻辑
        if ((TuihuanKindEnum.TK.getCode().equals(tuihuanKind) || TuihuanKindEnum.HQTXH.getCode().equals(tuihuanKind)) && (ShouhouRefundService.ALIPAY_REFUND_WAY.equals(tuihuanWay) || ShouhouRefundService.WECHAT_REFUND_WAY.equals(tuihuanWay))){
            return R.success(currStatus.getCode());
        }
        //通过退换kind获取审核配置枚举
        AfterExamineEnum afterExamineEnum = AfterExamineEnum.getEnumByTuihuanKind(tuihuanKind);
        if (afterExamineEnum == null) {
            return R.success(currStatus.getCode());
        }

        //获取审核配置
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        AfterExamineConfigVO afterExamineConfigVO = sysConfigService.getAfterExamineByCode(afterExamineEnum.getCode());
        if (afterExamineConfigVO == null) {
            return R.success(currStatus.getCode());
        }
        //开始自动审核
        LambdaUpdateChainWrapper<ShouhouTuihuan> lambdaUpdate = lambdaUpdate();
        lambdaUpdate.eq(ShouhouTuihuan::getId, shouhouTuiHuanPo.getId());
        // 一审没审且自动审核
        if (nextStatus == ShouhouRefundDetailVo.ProcessStatus.CHECK1 && (Boolean.FALSE.equals(afterExamineConfigVO.getFirExamineSwitch())|| lpAutomaticRefund)) {
            lambdaUpdate.isNull(ShouhouTuihuan::getCheck1).set(ShouhouTuihuan::getCheck1, true).set(ShouhouTuihuan::getCheck1user, "系统").set(ShouhouTuihuan::getCheck1dtime, LocalDateTime.now());
            currStatus = nextStatus;
            nextStatus = ShouhouRefundDetailVo.ProcessStatus.nextProcess(currStatus);
        }
        //二审没审且自动审核
        if (nextStatus == ShouhouRefundDetailVo.ProcessStatus.CHECK2 && (Boolean.FALSE.equals(afterExamineConfigVO.getSecExamineSwitch()) || lpAutomaticRefund)){
            lambdaUpdate.isNull(ShouhouTuihuan::getCheck2).set(ShouhouTuihuan::getCheck2, true).set(ShouhouTuihuan::getCheck2user, "系统").set(ShouhouTuihuan::getCheck2dtime, LocalDateTime.now());
            currStatus = nextStatus;
            //nextStatus = ShouhouRefundDetailVo.ProcessStatus.nextProcess(currStatus);
        }
        if (StrUtil.isNotBlank(lambdaUpdate.getWrapper().getSqlSet())) {
            boolean isUpdate = lambdaUpdate.update();
        }
        return R.success(currStatus.getCode());
    }

    @Override
    public Integer getShouhouIdbyTuihuanId(Integer tuiHuanId) {
        return baseMapper.getShouhouIdbyTuihuanId(tuiHuanId);
    }

    @Override
    public Integer getFapiaoKindBySubId(Integer subId, Integer isHuiShou) {
        return baseMapper.getFapiaoKindInfo(subId, isHuiShou);
    }

    @Override
    public BigDecimal getKoujianBaojia(Integer basketId) {
        return baseMapper.getKoujianBaojia(basketId);
    }

    @Override
    public Shouhou getIsHuishouByShouhouId(Integer shouhouId) {
        return baseMapper.getIsHuishouByShouhouId(shouhouId);
    }

    @Override
    public Integer getShThBySubId(Integer subId) {
        return baseMapper.getShThBySubId(subId);
    }

}
