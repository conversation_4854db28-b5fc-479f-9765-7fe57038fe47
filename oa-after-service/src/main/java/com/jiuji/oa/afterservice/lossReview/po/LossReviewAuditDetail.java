package com.jiuji.oa.afterservice.lossReview.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.afterservice.lossReview.enums.ErrorTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> yao yao
 * @since 2023-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LossReviewAuditDetail extends Model<LossReviewAuditDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer lossReviewAuditId;

    /**
     * @see com.jiuji.oa.afterservice.lossReview.enums.ErrorKindEnum
     */
    private Integer kind;
    /**
     * @see ErrorTypeEnum
     */
    private Integer errorType;

    private String errorReason;

    private BigDecimal lossAmount;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @TableLogic
    private Integer isDeleted;


    public LossReviewAuditDetail copy() {
        LossReviewAuditDetail target = new LossReviewAuditDetail();
        target.setId(this.id);
        target.setLossReviewAuditId(this.lossReviewAuditId);
        target.setKind(this.kind);
        target.setErrorType(this.errorType);
        target.setErrorReason(this.errorReason);
        target.setLossAmount(this.lossAmount);
        target.setCreateTime(this.createTime);
        target.setUpdateTime(this.updateTime);
        target.setIsDeleted(this.isDeleted);
        return target;
    }

}
