package com.jiuji.oa.afterservice.common.validate.constraints;
import com.jiuji.oa.afterservice.common.validate.ContactPhoneValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 联系电话校验.
 */
@Target({FIELD, METHOD, PARAMETER, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = ContactPhoneValidator.class)
@Documented
public @interface ContactPhone {

    String message() default "移动手机或固定电话号码不合法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
