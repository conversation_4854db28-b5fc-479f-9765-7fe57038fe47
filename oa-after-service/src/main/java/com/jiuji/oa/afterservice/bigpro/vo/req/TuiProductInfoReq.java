package com.jiuji.oa.afterservice.bigpro.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取可接件商品信息请求参数
 */
@Data
public class TuiProductInfoReq {


    @NotNull(message = "订单号不能为空")
    private Integer subId;

    @NotNull(message = "大小件区别不能为空")
    private Integer isMobile;

    private Integer yuYueId;
} 