package com.jiuji.oa.afterservice.bigpro.m.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.cloud.after.enums.ServiceEnum;
import com.jiuji.oa.afterservice.api.po.TaxPiao;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouExMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.ServiceTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.WxKindEnum;
import com.jiuji.oa.afterservice.bigpro.m.bo.ProductKcStatusInfo;
import com.jiuji.oa.afterservice.bigpro.m.bo.RecoverReferencePriceDataInfo;
import com.jiuji.oa.afterservice.bigpro.m.bo.RepairRecords;
import com.jiuji.oa.afterservice.bigpro.m.constant.UrlConstants;
import com.jiuji.oa.afterservice.bigpro.m.enums.ImeiQueryResultEnum;
import com.jiuji.oa.afterservice.bigpro.m.service.ShouhouMService;
import com.jiuji.oa.afterservice.bigpro.m.vo.MoaImeiQueryRes;
import com.jiuji.oa.afterservice.bigpro.m.vo.PcImeiQueryInfoBo;
import com.jiuji.oa.afterservice.bigpro.m.vo.PcImeiQueryRes;
import com.jiuji.oa.afterservice.bigpro.m.vo.ServiceInfoAndSubInfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.ImeiQueryRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.other.bo.ShouhouTuiHuanInfo;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceVO;
import com.jiuji.oa.afterservice.smallpro.dao.TiemoCardMapper;
import com.jiuji.oa.afterservice.sub.po.Category;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.imei.client.ImeiClient;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class ShouhouMServiceImpl extends ServiceImpl<ShouhouExMapper, Shouhou> implements ShouhouMService {

    @Autowired
    @Qualifier("pushMessageExecutor")
    private ThreadPoolTaskExecutor executor;
    @Resource
    private ImeiClient imeiClient;
    @Autowired
    private ImeisearchlogsService imeisearchlogsService;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private ShouhouExMapper shouhouExMapper;
    @Autowired
    private WeixinUserService weixinUserService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private FaPiaoService faPiaoService;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Resource
    private MemberClient memberClient;
    @Autowired
    private TiemoCardMapper tiemoCardMapper;
    @Lazy
    @Autowired
    private ShouhouExService shouhouExService;
    @Resource
    private ShouhouMapper shouhouMapper;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private CategoryService categoryService;


    @Override
    public R<MoaImeiQueryRes> imeiQueryBeforeReceive(String imei, Integer userId) {
        final OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        //串号搜索历史
        CompletableFuture<List<ImeiSearchLogBo>> logFuture = CompletableFuture.supplyAsync(() -> imeisearchlogsService.getImeiSearchLogsByImei(imei), this.executor);
        //回收参考价
        CompletableFuture<RecoverReferencePriceDataInfo> recoverFuture;
        //查询是不是库存机
        CompletableFuture<ProductKcStatusInfo> kcInfoFuture = CompletableFuture.supplyAsync(() -> shouhouExMapper.getProductKcStatusInfoByImei(imei), this.executor);
        List<ImeiQueryRes> imeiQueryList = shouhouService.imeiQuery(imei, 10, Boolean.TRUE);
        //自修信息查询
        ServiceInfoAndSubInfo serviceInfoAndSubInfo = this.getServiceInfoAndSubInfoV2(imei,userId, Boolean.TRUE);
        ProductKcStatusInfo kcInfo = kcInfoFuture.join();
        //机子是否处于库存状态
        Boolean isKc = kcInfoFuture != null && kcInfo != null;

        MoaImeiQueryRes result = new MoaImeiQueryRes();

        //根据IMEI查询该设备的分类id是否为8或者子级
        //获取商品id
        Optional<ProductImeiBo> productInfoByImei = Optional.ofNullable(shouhouMapper.getProductInfoByImei(imei));
        if (productInfoByImei.isPresent()){
            //根据商品id获取cid
            List<Integer> cidByPpid = productinfoService.getCidsByPpid(Collections.singletonList(productInfoByImei.get().getPpid()));
            //获取分类为8或以下的cid
            List<Integer> productChildCidList = categoryService.getProductChildCidList(Collections.singletonList(NumberConstant.EIGHT));
            //如果IMEI的分类是8或8的下级，则显示软件接件
            if (!CollUtil.intersection(cidByPpid, productChildCidList).isEmpty()){
                result.setIsSoftwareOrder(Boolean.TRUE);
            }
        }

        //如果是库存机，ui只显示库存的基本信息以及串号查询记录
        result.setImeiSearchLogs(logFuture.join());
        if (isKc) {
            result.setProductKcStatusInfo(kcInfo);
            result.setResultType(ImeiQueryResultEnum.STATUS_KC.getCode());
            result.setTips(ImeiQueryResultEnum.STATUS_KC.getTips());
            if (serviceInfoAndSubInfo != null
                    && serviceInfoAndSubInfo.getOrderInfo() != null
                    && CommenUtil.isNotNullZero(serviceInfoAndSubInfo.getOrderInfo().getSubId())
                    && !(serviceInfoAndSubInfo.getOrderInfo().getTradeDate() != null && kcInfo.getDTime() != null
                            && serviceInfoAndSubInfo.getOrderInfo().getTradeDate().isBefore(kcInfo.getDTime()))
            ) {
                result.setMemberProductInfo(serviceInfoAndSubInfo);
                result.setAllowSoftSave(Boolean.TRUE);
            }
            return R.success(result);
        }

        // 外修查询失败
        if (CollectionUtils.isEmpty(imeiQueryList)) {
            result.setResultType(ImeiQueryResultEnum.STATUS_WX_FAIL.getCode());
            result.setTips(ImeiQueryResultEnum.STATUS_WX_FAIL.getTips());
            //从服务中获取机型信息
            setProductInfoByService(serviceInfoAndSubInfo, result);
            return R.success(result);
        }

        ImeiQueryRes item = imeiQueryList.get(0);
        Integer productId = item.getProductId();
        String printName = sysConfigService.getWebNameByXtenant(oaUserBO.getXTenant());
        recoverFuture = CompletableFuture.supplyAsync(() -> this.getReferencePriceData(productId), this.executor);
        if (CommenUtil.isNullOrZero(item.getUserClass()) && StringUtils.isEmpty(item.getRealName())) {
            //外修结果集处理
            result.setResultType(ImeiQueryResultEnum.STATUS_WX_SUCCESS.getCode());
            result.setTips(MessageFormat.format(ImeiQueryResultEnum.STATUS_WX_SUCCESS.getTips(), printName));
            MoaImeiQueryRes.WxProductColorInfo productColorInfo = new MoaImeiQueryRes.WxProductColorInfo();
            productColorInfo.setProductName(item.getProductName());
            productColorInfo.setProductColors(imeiQueryList.stream().map(p -> {
                MoaImeiQueryRes.WxProductColorInfo.ProductColorItem colorItem = new MoaImeiQueryRes.WxProductColorInfo.ProductColorItem();
                colorItem.setPpid(p.getPpriceid());
                colorItem.setProductColor(p.getProductColor());
                return colorItem;
            }).collect(Collectors.toList()));
            result.setProductColorInfo(productColorInfo);
            return R.success(result);
        }
        //回收单商品信息处理
        if(serviceInfoAndSubInfo != null && serviceInfoAndSubInfo.getOrderInfo() != null
                && Boolean.TRUE.equals(serviceInfoAndSubInfo.getOrderInfo().getLastRecoverFlag())){
            //从服务中获取机型信息
            setProductInfoByService(serviceInfoAndSubInfo, result);
        }

        //自修结果组装
        result.setResultType(ImeiQueryResultEnum.STATUS_ZX.getCode());
        result.setTips(ImeiQueryResultEnum.STATUS_ZX.getTips());
        RecoverReferencePriceDataInfo referencePriceData = recoverFuture.join();
        List<MoaImeiQueryRes.OtherBusiness> otherBusinessList = new LinkedList<>();
        MoaImeiQueryRes.OtherBusiness recoverItem = new MoaImeiQueryRes.OtherBusiness().setName("回收参考价");
        if (referencePriceData != null && serviceInfoAndSubInfo != null && serviceInfoAndSubInfo.getMemberBasicInfo() != null) {
            String moaHost = sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, oaUserBO.getXTenant());
            String link = String.format(UrlConstants.OA_RECOVER_URL, moaHost, serviceInfoAndSubInfo.getMemberBasicInfo().getUserId(), imei);
            recoverItem.setReferencePrice(referencePriceData.getPrice()).setLink(link);
            otherBusinessList.add(recoverItem);
        }
        MoaImeiQueryRes.OtherBusiness filmItem = new MoaImeiQueryRes.OtherBusiness().setName("贴膜服务");
        Integer count = tiemoCardMapper.checkValidFilm(imei);
        filmItem.setFilmValid(CommenUtil.isNotNullZero(count) ? "是" : "否");
        otherBusinessList.add(filmItem);
        result.setOtherBusiness(otherBusinessList);
        result.setMemberProductInfo(serviceInfoAndSubInfo);
        //查询客户维修记录
        ImeiQueryRes imeiQueryRes = imeiQueryList.stream().filter(t -> CommenUtil.isNotNullZero(t.getUserId())).findAny().orElse(null);
        if (imeiQueryRes != null) {
            List<RepairRecords> repairRecords = shouhouExService.getRepairRecordsByImeiOrMobile(imei, null);
            if (CollectionUtils.isNotEmpty(repairRecords)) {
                repairRecords = repairRecords.stream().map(r -> {
                    r.setGuranteeText(CommenUtil.isNullOrZero(r.getGurantee()) ? "不在" : "在");
                    WxKindEnum wxKindEnum = EnumUtil.getEnumByCode(WxKindEnum.class, r.getWxKind());
                    r.setWxKindText(CommenUtil.isNullOrZero(r.getWxKind()) ? "自修" :
                            wxKindEnum == null ? "自修" : wxKindEnum.getMessage());
                    r.setImei("本机");
                    return r;
                }).collect(Collectors.toList());

                result.setRepairRecords(repairRecords);
            }
        }
        return R.success(result);
    }

    private void setProductInfoByService(ServiceInfoAndSubInfo serviceInfoAndSubInfo, MoaImeiQueryRes result) {
        Optional.ofNullable(serviceInfoAndSubInfo).map(service -> service.getOrderInfo())
                .filter(orderInfo -> StrUtil.isNotBlank(orderInfo.getProductName()) || StrUtil.isNotBlank(orderInfo.getProductColor()))
                .map(orderInfo -> {
                    MoaImeiQueryRes.WxProductColorInfo r = new MoaImeiQueryRes.WxProductColorInfo();
                    r.setProductName(orderInfo.getProductName());
                    MoaImeiQueryRes.WxProductColorInfo.ProductColorItem productColorItem = new MoaImeiQueryRes.WxProductColorInfo.ProductColorItem();
                    productColorItem.setProductColor(orderInfo.getProductColor());
                    productColorItem.setPpid(orderInfo.getPpid());
                    r.setProductColors(Collections.singletonList(productColorItem));
                    result.setResultType(ImeiQueryResultEnum.STATUS_WX_SUCCESS.getCode());
                    return r;
                }).ifPresent(result::setProductColorInfo);
    }

    @Override
    public RecoverReferencePriceDataInfo getReferencePriceData(Integer productId) {
        if (CommenUtil.isNullOrZero(productId)) {
            return null;
        }
        String host = sysConfigService.getValueByCode(SysConfigConstant.M_URL);
        String url = String.format(UrlConstants.RECOVER_URL, host, productId.toString());
        String json = HttpUtil.get(url);
        R<RecoverReferencePriceDataInfo> recoverDataR = JSONObject.parseObject(json, new TypeReference<R<RecoverReferencePriceDataInfo>>() {
        });
        if (recoverDataR.getCode() != ResultCode.SUCCESS || recoverDataR.getData() == null) {
            return null;
        }
        return recoverDataR.getData();
    }

    @Override
    public ServiceInfoAndSubInfo getServiceInfoAndSubInfo(String imei, Boolean recordOperate) {

        BaoxiuAndBuyBo info = shouhouService.getServiceInfo(imei, recordOperate);
        if (info == null) {
            return null;
        }

        ServiceInfoAndSubInfo subInfo = new ServiceInfoAndSubInfo();

        //会员信息
        ServiceInfoAndSubInfo.MemberBasicInfo memberBasicInfo = new ServiceInfoAndSubInfo.MemberBasicInfo();
        if (CommenUtil.isNotNullZero(info.getUserid())) {
            buildMemberBasicInfo(info, memberBasicInfo);
        }
        if (CollectionUtils.isNotEmpty(info.getHuishouinfo())) {
            List<HuishouInfoBo> recoverList = info.getHuishouinfo();
            recoverList = recoverList.stream().sorted(Comparator.comparing(HuishouInfoBo::getSub_id, Comparator.reverseOrder())).collect(Collectors.toList());
            HuishouInfoBo recoverInfo = recoverList.get(0);
            buildMemberBasicInfo(recoverInfo, memberBasicInfo);
        }

        //订单信息
        ServiceInfoAndSubInfo.OrderInfo orderInfo = new ServiceInfoAndSubInfo.OrderInfo();
        buildOrderInfo(info, orderInfo, memberBasicInfo);
        //获取发票状态 invoiceTicket
        if (CommenUtil.isNotNullZero(info.getUserid()) && CommenUtil.isNotNullZero(info.getSub_id()) && info.getTradedate() != null) {
            orderInfo.setInvoiceTicket(Boolean.FALSE);
            R<TaxPiao> piaoInfo = faPiaoService.getPiaoInfo(Long.valueOf(info.getSub_id()), null, Long.valueOf(info.getUserid()), DateUtil.localDateTimeToString(info.getTradedate()));
            if (piaoInfo.getCode() == ResultCode.SUCCESS && piaoInfo.getData() != null) {
                orderInfo.setInvoiceTicket(Boolean.TRUE);
            }
        }

        //组装数据
        List<ServiceInfoAndSubInfo.AfterServiceInfo> serviceInfoList = buildAfterServiceInfo(info);
        serviceInfoList = serviceInfoList.stream().filter(ServiceInfoAndSubInfo.AfterServiceInfo::getExists).collect(Collectors.toList());
        subInfo.setMemberBasicInfo(memberBasicInfo)
                .setOrderInfo(orderInfo)
                .setServiceInfoList(serviceInfoList);

        return subInfo;
    }

    @Override
    public ServiceInfoAndSubInfo getServiceInfoAndSubInfoV2(String imei, Integer userId, Boolean recordOperate) {

        ServiceInfoVO info = SpringUtil.getBean(ServiceRecordService.class).getRecord(imei, userId, recordOperate);;
        ServiceInfoAndSubInfo subInfo = new ServiceInfoAndSubInfo();

        //会员信息
        ServiceInfoAndSubInfo.MemberBasicInfo memberBasicInfo = new ServiceInfoAndSubInfo.MemberBasicInfo();
        if (CommenUtil.isNotNullZero(info.getUserId())) {
            buildMemberBasicInfoV2(info, memberBasicInfo);
        }

        //订单信息
        ServiceInfoAndSubInfo.OrderInfo orderInfo = new ServiceInfoAndSubInfo.OrderInfo();
        buildOrderInfoV2(info, orderInfo, memberBasicInfo);
        //获取发票状态 invoiceTicket
        if (CommenUtil.isNotNullZero(info.getUserId()) && CommenUtil.isNotNullZero(info.getSubId()) && info.getTradeDate() != null) {
            orderInfo.setInvoiceTicket(Boolean.FALSE);
            R<TaxPiao> piaoInfo = faPiaoService.getPiaoInfo(Long.valueOf(info.getSubId()), null, Long.valueOf(info.getUserId()), DateUtil.localDateTimeToString(info.getTradeDate()));
            if (piaoInfo.getCode() == ResultCode.SUCCESS && piaoInfo.getData() != null) {
                orderInfo.setInvoiceTicket(Boolean.TRUE);
            }
        }

        //组装数据
        List<ServiceInfoAndSubInfo.AfterServiceInfo> serviceInfoList = buildAfterServiceInfoV2(info);
        serviceInfoList = serviceInfoList.stream().filter(ServiceInfoAndSubInfo.AfterServiceInfo::getExists).collect(Collectors.toList());
        subInfo.setMemberBasicInfo(memberBasicInfo)
                .setOrderInfo(orderInfo)
                .setServiceInfoList(serviceInfoList);

        return subInfo;
    }

    @Override
    public R<PcImeiQueryRes> pcImeiQuery(String imei, Integer userId) {
        if(StrUtil.isBlank(imei)){
            return R.error("串号不能为空");
        }
        ServiceInfoVO serviceInfo = SpringUtil.getBean(ServiceRecordService.class).getRecord(imei, userId, false);
        PcImeiQueryRes result = new PcImeiQueryRes();
        result.setServiceInfo(serviceInfo);
        //状态 1 有订单 2 有三方数据 3 没有三方数据
        result.setStatus(DecideUtil.iif(CommenUtil.isNullOrZero(serviceInfo.getSubId()),
                PcImeiQueryRes.ImeiQueryStatusEnum.NOT_HAS_INFO.getCode(),
                PcImeiQueryRes.ImeiQueryStatusEnum.HAS_SUB.getCode()));
        if(StrUtil.length(imei) == 15 && CommenUtil.isNullOrZero(serviceInfo.getSubId())){
            result.setThirdInfo(PcImeiQueryInfoBo.wrapper(shouhouService.imeiQueryInfo(imei)));
            if(Optional.ofNullable(result.getThirdInfo()).filter(ti->StrUtil.isNotBlank(ti.getModel()))
                    .filter(ti->CollUtil.isNotEmpty(ti.getProductInfo())).isPresent()){
                result.setStatus(PcImeiQueryRes.ImeiQueryStatusEnum.HAS_PRODUCT_INFO.getCode());
            }
        }
        if(Objects.equals(result.getStatus(),PcImeiQueryRes.ImeiQueryStatusEnum.NOT_HAS_INFO.getCode())
                || Boolean.TRUE.equals(serviceInfo.getLastRecoverFlag())){
            //从服务中获取机型信息
            Optional.of(serviceInfo)
                    .filter(si -> StrUtil.isNotBlank(si.getProductName()) || StrUtil.isNotBlank(si.getProductColor()))
                    .map(si -> {
                        PcImeiQueryInfoBo thirdInfo = Optional.ofNullable(result.getThirdInfo()).orElseGet(() -> new PcImeiQueryInfoBo());
                        thirdInfo.setBrand(Optional.ofNullable(thirdInfo.getBrand()).filter(brank->StrUtil.isNotBlank(brank)).orElseGet(()->
                                Optional.ofNullable(si.getBrandId()).map(categoryService::getById).map(Category::getName).orElse(null)));
                        thirdInfo.setBrandId(Optional.ofNullable(thirdInfo.getBrandId()).filter(brankId->Objects.nonNull(brankId))
                                .orElseGet(()->si.getBrandId()));
                        thirdInfo.setProductId(Optional.ofNullable(thirdInfo.getProductId()).filter(productId->Objects.nonNull(productId))
                                .orElseGet(()->si.getProductId()));
                        thirdInfo.setThirdParty(Optional.ofNullable(thirdInfo.getThirdParty()).filter(thirdParty->Objects.nonNull(thirdParty))
                                .orElse(Boolean.FALSE));
                        thirdInfo.setModel(Optional.ofNullable(thirdInfo.getModel()).filter(model->StrUtil.isNotBlank(model))
                                .orElseGet(()->si.getProductName()));
                        thirdInfo.setProductInfo(Optional.ofNullable(thirdInfo.getProductInfo()).filter(productInfo->CollUtil.isNotEmpty(productInfo))
                                .orElseGet(()->Collections.singletonList(new ProductInfoBo().setProductColor(si.getProductColor()).setPpid(si.getPpriceId()))));
                        if(!Objects.equals(result.getStatus(),PcImeiQueryRes.ImeiQueryStatusEnum.NOT_HAS_INFO.getCode())){
                            result.setStatus(PcImeiQueryRes.ImeiQueryStatusEnum.HAS_PRODUCT_INFO.getCode());
                        }
                        return thirdInfo;
                    }).ifPresent(result::setThirdInfo);
        }
        return R.success(result);
    }

    private void buildMemberBasicInfoV2(ServiceInfoVO info, ServiceInfoAndSubInfo.MemberBasicInfo memberBasicInfo) {
        //校验微信绑定情况
        String wxBindUrl = weixinUserService.getWxBindUrl(info.getUserId(), info.getMkcAreaId(), Optional.ofNullable(info.getSubId()).map(String::valueOf).orElse("0"), 1);
        List<ShouhouTuiHuanInfo> tuihuanList = shouhouTuihuanService.getAfterServicesDiscount(info.getUserId());
        R<MemberBasicRes> memberInfoR = memberClient.getMemberBasicInfo(info.getUserId());
        memberBasicInfo.setUserId(info.getUserId())
                .setUserClass(info.getUserClass())
                .setUserClassName(info.getUserClassName())
                .setBlackList(info.getBlacklist())
                .setUserName(info.getUsername())
                .setIsBindWechat(StringUtils.isNotEmpty(wxBindUrl) ? Boolean.FALSE : Boolean.TRUE)
                .setWechatBindUrl(wxBindUrl)
                .setAfterServicesDiscount(tuihuanList.size())
                .setTuiHuanList(tuihuanList)
                .setMobile(info.getSubMobile());
        if (memberInfoR.getCode() == ResultCode.SUCCESS && memberInfoR.getData() != null) {
            memberBasicInfo.setRealName(memberInfoR.getData().getRealName());
        }
    }

    private void buildOrderInfoV2(ServiceInfoVO info, ServiceInfoAndSubInfo.OrderInfo orderInfo, ServiceInfoAndSubInfo.MemberBasicInfo memberBasicInfo) {
        //保修
        Optional<ServiceVO.DetailInfo> baoxiuOpt = BaoxiuAndBuyBoWrapper.getServiceDetailInfo(info, ServiceEnum.BAO_XIU);
        //意外保
        Optional<ServiceVO.DetailInfo> yiWaiOpt = BaoxiuAndBuyBoWrapper.getServiceDetailInfo(info,ServiceEnum.YI_WAI_BAO);
        orderInfo.setProductId(info.getProductId())
                .setPpid(info.getPpriceId())
                .setMkcId(info.getMkcId())
                .setProductName(info.getProductName())
                .setProductColor(info.getProductColor())
                .setTradeDate(info.getTradeDate())
                .setImei(info.getImei())
                .setArea(info.getMkcArea())
                .setAreaId(info.getMkcAreaId())
                .setSubMobile(info.getSubMobile())
                .setBrandId(info.getBrandId())
                .setCid(info.getCid())
                .setPrice(info.getPrice())
                .setSubId(info.getSubId())
                .setBasketId(info.getBasketId())
                .setType(SimpleServiceSubInfoBo.OrderTypeEnum.LP_ORDER.getCode().equals(info.getOrderType()) ? info.getSaleType() : info.getSubType())
                .setSubTo(info.getSubTo())
                .setBoughtCarePlusFlag(info.getBoughtPlus() != null)
                .setIsGuarantee(info.getBaoXiu())
                .setGuaranteeEndDate(BaoxiuAndBuyBoWrapper.getServiceEndDate(baoxiuOpt))
                .setIsXcMkc(info.getXcMkc())
                .setXcMkcInfo(info.getXcMkcInfo())
                .setIsRecover(info.getHuishou())
                .setImeiList(info.getImeilist().stream().map(log-> JSON.toJSONString(log)).collect(Collectors.toList()))
                .setLastRecoverFlag(info.getLastRecoverFlag())
                .setRecoverInfo(buildRecoverInfo(DecideUtil.iif(Boolean.TRUE.equals(info.getHuishou())
                        ,()-> Collections.singletonList(HuishouInfoBo.wrapper(info,BaoxiuAndBuyBoWrapper.getServiceEndDate(baoxiuOpt))),()->null)))
                .setAccidentProtectionVersion(yiWaiOpt.map(ServiceVO.DetailInfo::getVersionDes).orElse(null))
                .setIsAccident(yiWaiOpt.isPresent())
                .setUserId(memberBasicInfo.getUserId())
                .setUserClass(memberBasicInfo.getUserClass())
                .setUserClassName(memberBasicInfo.getUserClassName())
                .setBlackList(memberBasicInfo.getBlackList())
                .setUserName(memberBasicInfo.getUserName())
                .setIsBindWx(memberBasicInfo.getIsBindWechat())
                .setWxBindUrl(memberBasicInfo.getWechatBindUrl())
                .setAfterServicesDiscount(memberBasicInfo.getAfterServicesDiscount())
                .setTuiHuanList(memberBasicInfo.getTuiHuanList())
                .setMobile(info.getSubMobile())
                .setBaoXiu(info.getBaoXiu())
                .setSubCheck(info.getSubCheck())
                .setRealName(memberBasicInfo.getRealName());
    }

    private void buildMemberBasicInfo(BaoxiuAndBuyBo info, ServiceInfoAndSubInfo.MemberBasicInfo memberBasicInfo) {
        //校验微信绑定情况
        String wxBindUrl = weixinUserService.getWxBindUrl(info.getUserid(), info.getAreaid(), Optional.ofNullable(info.getSub_id()).map(String::valueOf).orElse("0"), 1);
        List<ShouhouTuiHuanInfo> tuihuanList = shouhouTuihuanService.getAfterServicesDiscount(info.getUserid());
        R<MemberBasicRes> memberInfoR = memberClient.getMemberBasicInfo(info.getUserid());
        memberBasicInfo.setUserId(info.getUserid())
                .setUserClass(info.getUserclass())
                .setUserClassName(info.getUserclassname())
                .setBlackList(info.getBlacklist())
                .setUserName(info.getUsername())
                .setIsBindWechat(StringUtils.isNotEmpty(wxBindUrl) ? Boolean.FALSE : Boolean.TRUE)
                .setWechatBindUrl(wxBindUrl)
                .setAfterServicesDiscount(tuihuanList.size())
                .setTuiHuanList(tuihuanList)
                .setMobile(info.getSub_mobile());
        if (memberInfoR.getCode() == ResultCode.SUCCESS && memberInfoR.getData() != null) {
            memberBasicInfo.setRealName(memberInfoR.getData().getRealName());
        }
    }

    private void buildMemberBasicInfo(HuishouInfoBo info, ServiceInfoAndSubInfo.MemberBasicInfo memberBasicInfo) {
        //校验微信绑定情况
        String wxBindUrl = weixinUserService.getWxBindUrl(info.getUserid(), info.getAreaid(), info.getSub_id().toString(), 1);
        List<ShouhouTuiHuanInfo> tuihuanList = shouhouTuihuanService.getAfterServicesDiscount(info.getUserid());
        R<MemberBasicRes> memberInfoR = memberClient.getMemberBasicInfo(info.getUserid());
        memberBasicInfo.setUserId(info.getUserid())
                .setUserClass(info.getUserclass())
                .setUserClassName(info.getUserclassname())
                .setBlackList(info.getBlacklist())
                .setUserName(info.getUsername())
                .setIsBindWechat(StringUtils.isNotEmpty(wxBindUrl) ? Boolean.FALSE : Boolean.TRUE)
                .setWechatBindUrl(wxBindUrl)
                .setAfterServicesDiscount(tuihuanList.size())
                .setTuiHuanList(tuihuanList)
                .setMobile(info.getSub_mobile());
        if (memberInfoR.getCode() == ResultCode.SUCCESS && memberInfoR.getData() != null) {
            memberBasicInfo.setRealName(memberInfoR.getData().getRealName());
        }
    }

    private void buildOrderInfo(BaoxiuAndBuyBo info, ServiceInfoAndSubInfo.OrderInfo orderInfo, ServiceInfoAndSubInfo.MemberBasicInfo memberBasicInfo) {
        orderInfo.setProductId(info.getProductid())
                .setPpid(info.getPpriceid())
                .setMkcId(info.getMkc_id())
                .setProductName(info.getProduct_name())
                .setProductColor(info.getProduct_color())
                .setTradeDate(info.getTradedate())
                .setImei(info.getImei())
                .setArea(info.getArea())
                .setAreaId(info.getAreaid())
                .setSubMobile(info.getSub_mobile())
                .setBrandId(info.getBrandid())
                .setCid(info.getCid())
                .setPrice(info.getPrice())
                .setSubId(info.getSub_id())
                .setBasketId(info.getBasket_id())
                .setType(info.getType())
                .setSubTo(info.getSub_to())
                .setBoughtCarePlusFlag(info.getBoughtCarePlusFlag())
                .setIsGuarantee(CommenUtil.isNotNullZero(info.getIsbaoxiu()))
                .setGuaranteeEndDate(info.getBaoxiuEndDate())
                .setIsXcMkc(CommenUtil.isNotNullZero(info.getIsXcMkc()))
                .setXcMkcInfo(info.getIsXcMkcInfo())
                .setIsRecover(CommenUtil.isNotNullZero(info.getIshuishou()))
                .setImeiList(info.getImeilist())
                .setLastRecoverFlag(info.getLastRecoverFlag())
                .setRecoverInfo(buildRecoverInfo(info.getHuishouinfo()))
                .setAccidentProtectionVersion(info.getYiwai_version())
                .setIsAccident(CommenUtil.isNotNullZero(info.getIsyiwai()))
                .setUserId(memberBasicInfo.getUserId())
                .setUserClass(memberBasicInfo.getUserClass())
                .setUserClassName(memberBasicInfo.getUserClassName())
                .setBlackList(memberBasicInfo.getBlackList())
                .setUserName(memberBasicInfo.getUserName())
                .setIsBindWx(memberBasicInfo.getIsBindWechat())
                .setWxBindUrl(memberBasicInfo.getWechatBindUrl())
                .setAfterServicesDiscount(memberBasicInfo.getAfterServicesDiscount())
                .setTuiHuanList(memberBasicInfo.getTuiHuanList())
                .setMobile(info.getSub_mobile())
                .setRealName(memberBasicInfo.getRealName());
    }

    private List<ServiceInfoAndSubInfo.RecoverInfo> buildRecoverInfo(List<HuishouInfoBo> huishouInfoList) {
        if (CollectionUtils.isEmpty(huishouInfoList)) {
            return new ArrayList<>();
        }
        List<ServiceInfoAndSubInfo.RecoverInfo> result = huishouInfoList.stream().map(info -> ServiceInfoAndSubInfo.RecoverInfo.from(info)).collect(Collectors.toList());

        Comparator<ServiceInfoAndSubInfo.RecoverInfo> bySubIdDesc = Comparator.comparing(ServiceInfoAndSubInfo.RecoverInfo::getSubId).reversed();

        result.sort(bySubIdDesc);
        return result;
    }

    private List<ServiceInfoAndSubInfo.AfterServiceInfo> buildAfterServiceInfoV2(ServiceInfoVO info){
        return info.getServiceVos().stream().map(service->
        {
            Optional<ServiceVO.DetailInfo> detailInfoOpt = Optional.ofNullable(service.getDetailInfo());
            return new ServiceInfoAndSubInfo.AfterServiceInfo().setServiceName(service.getName())
                    .setExpired(Objects.equals(ServiceVO.DetailInfo.EffectiveEnum.INVALID.getCode(), detailInfoOpt.map(ServiceVO.DetailInfo::getEffective).orElse(null)))
                    .setPrice(detailInfoOpt.map(ServiceVO.DetailInfo::getPrice).orElse(null))
                    .setFeiYong(detailInfoOpt.map(ServiceVO.DetailInfo::getFeiyong).orElse(null))
                    .setInsureYear(detailInfoOpt.map(ServiceVO.DetailInfo::getYears).map(BigDecimal::doubleValue).orElse(null))
                    .setServiceStartDate(detailInfoOpt.map(ServiceVO.DetailInfo::getStartTime).orElse(null))
                    .setServiceEndDate(detailInfoOpt.map(ServiceVO.DetailInfo::getEndTime).orElse(null))
                    .setServiceTradeDate(detailInfoOpt.map(ServiceVO.DetailInfo::getTradeDate).orElse(null))
                    .setIsInUse(detailInfoOpt.map(ServiceVO.DetailInfo::getUse).orElse(null))
                    .setAfterServiceShouHouId(detailInfoOpt.map(ServiceVO.DetailInfo::getStopShouhouId).orElse(null))
                    .setStopSubType(detailInfoOpt.map(ServiceVO.DetailInfo::getStopSubType).orElse(null))
                    .setExists(detailInfoOpt.isPresent());
        })
                .collect(Collectors.toList());
    }

    private List<ServiceInfoAndSubInfo.AfterServiceInfo> buildAfterServiceInfo(BaoxiuAndBuyBo info) {

        List<ServiceInfoAndSubInfo.AfterServiceInfo> serviceInfoList = new LinkedList<>();
        //售前碎屏保
        if (info.getSuipinbao_tradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo screenService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            screenService.setServiceName("碎屏保")
                    .setExpired(CommenUtil.isNotNullZero(info.getSuipinIsGuoqi()))
                    .setPrice(info.getSuipinbao_price())
                    .setFeiYong(info.getSuipin_feiyong())
                    .setInsureYear(info.getSuipinbao_year())
                    .setServiceStartDate(info.getSuipinbaoStartDate())
                    .setServiceEndDate(info.getSuipinbaoEndDate())
                    .setServiceTradeDate(info.getSuipinbao_tradedate())
                    .setIsInUse(CommenUtil.isNotNullZero(info.getSuipinIsUse()))
                    .setAfterServiceShouHouId(shouhouExMapper.getShouHouIdByServiceType(BaoXiuTypeEnum.SPB.getCode(), info.getImei()))
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(screenService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo screenService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            screenService.setServiceName("碎屏保").setExists(Boolean.FALSE);
            serviceInfoList.add(screenService);
        }
        //售后碎屏保
        if (info.getAfterServicesScreenTradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo afterScreenService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            afterScreenService.setServiceName("售后碎屏保")
                    .setExpired(CommenUtil.isNotNullZero(info.getAfterServicesScreenIsGuoqi()))
                    .setPrice(info.getAfterServicesScreen_price())
                    .setFeiYong(info.getAfterServicesScreen_feiyong())
                    .setInsureYear(Optional.ofNullable(info.getAfterServicesScreen_year()).map(Integer::doubleValue).orElse(null))
                    .setServiceStartDate(info.getAfterServicesScreenStartDate())
                    .setServiceEndDate(info.getAfterServicesScreenEndDate())
                    .setServiceTradeDate(info.getAfterServicesScreenTradedate())
                    .setIsInUse(CommenUtil.isNotNullZero(info.getAfterServicesScreenIsUse()))
                    .setAfterServiceShouHouId(info.getAfterServicesScreenShouhouId())
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(afterScreenService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo afterScreenService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            afterScreenService.setServiceName("售后碎屏保").setExists(Boolean.FALSE);
            serviceInfoList.add(afterScreenService);
        }

        //售前电池保
        if (info.getDianchibao_tradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo batteryService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            batteryService.setServiceName("电池保")
                    .setExpired(CommenUtil.isNotNullZero(info.getDianchiIsGuoqi()))
                    .setPrice(info.getDianchibao_price())
                    .setFeiYong(info.getDianchi_feiyong())
                    .setInsureYear(Optional.ofNullable(info.getDianchibao_year()).map(Integer::doubleValue).orElse(null))
                    .setServiceStartDate(info.getDainChibaoStartDate())
                    .setServiceEndDate(info.getDainChibaoEndDate())
                    .setServiceTradeDate(info.getDianchibao_tradedate())
                    .setIsInUse(CommenUtil.isNotNullZero(info.getDianchiIsUse()))
                    .setAfterServiceShouHouId(shouhouExMapper.getShouHouIdByServiceType(BaoXiuTypeEnum.DCB.getCode(), info.getImei()))
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(batteryService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo batteryService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            batteryService.setServiceName("电池保").setExists(Boolean.FALSE);
            serviceInfoList.add(batteryService);
        }
        //售后电池保
        if (info.getAfterServicesBatteryTradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo afterBatteryService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            afterBatteryService.setServiceName("售后电池保")
                    .setExpired(CommenUtil.isNotNullZero(info.getAfterServicesBatteryIsGuoqi()))
                    .setPrice(info.getAfterServicesBattery_price())
                    .setFeiYong(info.getAfterServicesBattery_feiyong())
                    .setInsureYear(Optional.ofNullable(info.getAfterServicesBattery_year()).map(Integer::doubleValue).orElse(null))
                    .setServiceStartDate(info.getAfterServicesBatteryStartDate())
                    .setServiceEndDate(info.getAfterServicesBatteryEndDate())
                    .setServiceTradeDate(info.getAfterServicesBatteryTradedate())
                    .setIsInUse(CommenUtil.isNotNullZero(info.getAfterServicesBatteryIsUse()))
                    .setAfterServiceShouHouId(info.getAfterServicesBatteryShouhouId())
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(afterBatteryService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo afterBatteryService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            afterBatteryService.setServiceName("售后电池保").setExists(Boolean.FALSE);
            serviceInfoList.add(afterBatteryService);
        }

        //售前后盖  屏背保
        if (info.getPinbeibao_tradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo backCoverService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            backCoverService.setServiceName("屏背保")
                    .setExpired(CommenUtil.isNotNullZero(info.getPinbeiIsGuoqi()))
                    .setPrice(info.getPinbeibao_price())
                    .setFeiYong(info.getPinbeibao_feiyong())
                    .setInsureYear(Optional.ofNullable(info.getPinbeibao_year()).map(Integer::doubleValue).orElse(null))
                    .setServiceStartDate(info.getPinbeibaoStartDate())
                    .setServiceEndDate(info.getPinbeibaoEndDate())
                    .setServiceTradeDate(info.getPinbeibao_tradedate())
                    .setIsInUse(CommenUtil.isNotNullZero(info.getPinbeiIsUse()))
                    .setAfterServiceShouHouId(shouhouExMapper.getShouHouIdByServiceType(BaoXiuTypeEnum.PBB.getCode(), info.getImei()))
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(backCoverService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo backCoverService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            backCoverService.setServiceName("屏背保").setExists(Boolean.FALSE);
            serviceInfoList.add(backCoverService);
        }

        //售后后盖保
        if (info.getAfterServicesBackCoverTradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo afterBackCoverService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            afterBackCoverService.setServiceName(ServiceTypeEnum.ServiceTypeEnum_11.getMessage()).setServicePpid(ServiceTypeEnum.ServiceTypeEnum_11.getCode())
                    .setExpired(CommenUtil.isNotNullZero(info.getAfterServicesBackCoverIsGuoqi()))
                    .setPrice(info.getAfterServicesBackCover_price())
                    .setFeiYong(info.getAfterServicesBackCover_feiyong())
                    .setInsureYear(Optional.ofNullable(info.getAfterServicesBackCover_year()).map(Integer::doubleValue).orElse(null))
                    .setServiceStartDate(info.getAfterServicesBackCoverStartDate())
                    .setServiceEndDate(info.getAfterServicesBackCoverEndDate())
                    .setServiceTradeDate(info.getAfterServicesBackCoverTradedate())
                    .setIsInUse(CommenUtil.isNotNullZero(info.getAfterServicesBackCoverIsUse()))
                    .setAfterServiceShouHouId(info.getAfterServicesBackCoverShouhouId())
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(afterBackCoverService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo afterBackCoverService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            afterBackCoverService.setServiceName(ServiceTypeEnum.ServiceTypeEnum_11.getMessage()).setExists(Boolean.FALSE);
            serviceInfoList.add(afterBackCoverService);
        }
        //延保
        if (info.getYanbao_tradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo extendedInsuranceService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            extendedInsuranceService.setServiceName("延保")
                    .setExpired(LocalDateTime.now().isAfter(info.getYanbao_tradedate()))
                    .setPrice(info.getYanbao_price())
                    .setFeiYong(info.getYanbao_feiyong())
                    .setInsureYear(Optional.ofNullable(info.getYanbao_year()).map(Integer::doubleValue).orElse(null))
                    .setServiceStartDate(info.getYanbaoEndDate().minusYears(info.getYanbao_year()))
                    .setServiceEndDate(info.getYanbaoEndDate())
                    .setServiceTradeDate(info.getYanbao_tradedate())
                    .setIsInUse(LocalDateTime.now().isBefore(info.getYanbaoEndDate()) && LocalDateTime.now().isAfter(info.getYanbaoEndDate().minusYears(info.getYanbao_year())))
                    .setAfterServiceShouHouId(shouhouExMapper.getShouHouIdByServiceType(BaoXiuTypeEnum.YB.getCode(), info.getImei()))
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(extendedInsuranceService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo extendedInsuranceService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            extendedInsuranceService.setServiceName("延保").setExists(Boolean.FALSE);
            serviceInfoList.add(extendedInsuranceService);
        }

        //意外保
        if (info.getYiwai_tradedate() != null) {
            ServiceInfoAndSubInfo.AfterServiceInfo accidentService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            accidentService.setServiceName("意外保")
                    .setExpired(CommenUtil.isNotNullZero(info.getYiwaiIsGuoqi()))
                    .setPrice(info.getYiwai_price())
                    .setFeiYong(info.getYiwai_feiyong())
                    .setInsureYear(Optional.ofNullable(info.getYiwai_year()).map(Integer::doubleValue).orElse(null))
                    .setServiceStartDate(info.getYiwaiStartDate())
                    .setServiceEndDate(info.getYiwaiEndDate())
                    .setServiceTradeDate(info.getYiwai_tradedate())
                    .setIsInUse(CommenUtil.isNotNullZero(info.getYiwaiIsUse()))
                    .setAfterServiceShouHouId(shouhouExMapper.getShouHouIdByServiceType(BaoXiuTypeEnum.YWB.getCode(), info.getImei()))
                    .setExists(Boolean.TRUE);
            serviceInfoList.add(accidentService);
        } else {
            ServiceInfoAndSubInfo.AfterServiceInfo accidentService = new ServiceInfoAndSubInfo.AfterServiceInfo();
            accidentService.setServiceName("意外保").setExists(Boolean.FALSE);
            serviceInfoList.add(accidentService);
        }

        return serviceInfoList;
    }

}
