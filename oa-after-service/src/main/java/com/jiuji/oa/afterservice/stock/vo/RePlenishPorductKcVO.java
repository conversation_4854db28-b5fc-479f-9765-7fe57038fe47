package com.jiuji.oa.afterservice.stock.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 补货返回数据展示
 * @Author: xiaojianbing
 * @Date 2020/9/27
 */
@Data
@Accessors(chain = true)
public class RePlenishPorductKcVO implements Serializable {
    private static final long serialVersionUID = -7755586865055958335L;
    @ApiModelProperty(value = "ppid")
    private Integer ppid;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品颜色")
    private String productColor;
    @ApiModelProperty(value = "商品条码")
    private String barCode;
    @ApiModelProperty(value = "库存数量")
    private Integer stockCount;
    @ApiModelProperty(value = "库位应取货数量")
    private Integer pjCount;
    @ApiModelProperty(value = "库位实际取货数量")
    private Integer actualCount;
    @ApiModelProperty(value = "商品实际取货数量")
    private Integer totalCount;
    @ApiModelProperty(value = "配件对应的取货门店信息")
    private List<PickPjProductAreaVO> productAreas;
}
