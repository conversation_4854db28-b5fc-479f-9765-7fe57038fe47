package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.entity.ProductSn;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnQueryReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnUpdateBindReq;
import com.jiuji.tc.common.vo.R;


public interface ProductSnService extends IService<ProductSn> {

    /**
     * 根据篮式ID和商品ID查询ProductSn列表
     *
     * @param queryReq 包含basketId和ppid的查询请求对象
     * @return ProductSn列表
     */
    ProductSn getProductSnByBasketIdAndPpid(ProductSnQueryReq queryReq);
    
    /**
     * 根据商品ID和SN设置篮式ID并解绑
     *
     * @param updateReq 更新请求对象
     * @return 是否成功
     */
    R<Boolean> updateBasketIdAndUnbind(ProductSnUpdateBindReq updateReq);
}

