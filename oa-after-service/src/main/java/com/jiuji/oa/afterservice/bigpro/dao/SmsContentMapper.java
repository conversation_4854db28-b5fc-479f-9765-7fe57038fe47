package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.po.SmsContent;
import com.jiuji.oa.afterservice.shouhou.vo.sms.AppInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * ${comments}
 * 
 * <AUTHOR> quan
 * @email ${email}
 * @date 2020-05-07 11:10:11
 */
@Mapper
public interface SmsContentMapper extends BaseMapper<SmsContent> {

    /**
     * 更新短信用户信息
     * 暂时用不到,由c#提供同步用户信息接口
     * 也就没建对应的表
     * @param appInfoVo
     * @return
     */
    int updateAppInfo(@Param("appInfoVo") AppInfoVo appInfoVo, @Param("xtenant") long xtenant);

    /**
     * 控制并发保证不会插入多条
     * 暂时用不到,由c#提供同步用户信息接口
     * 也就没建对应的表
     * @param appInfoVo
     * @return
     */
    int insertAppInfo(@Param("appInfoVo") AppInfoVo appInfoVo, @Param("xtenant") long xtenant);

    /**
     * 获取短信用户名密码
     * @param xtenant
     * @return
     */
    AppInfoVo getAppInfo(@Param("xtenant") long xtenant);
}
