package com.jiuji.oa.afterservice.bigpro.entity;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * (Productbarcode)表实体类
 *
 * <AUTHOR>
 * @since 2024-05-24 17:04:12
 */
@Data
@Accessors(chain = true)
@TableName("productBarcode")
public class Productbarcode implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer ppriceid;
    @TableField(value = "barCode")
    private String barCode;
    @TableField(value = "isDefault")
    private Boolean isDefault;
    @TableField(value = "operator")
    private String operator;

    @TableField(value = "opXtenant")
    private Integer opXtenant;
    @TableField(value = "addTime")
    private LocalDateTime addTime;
    @TableField(value = "updateDate")
    private LocalDateTime updateDate;



}
