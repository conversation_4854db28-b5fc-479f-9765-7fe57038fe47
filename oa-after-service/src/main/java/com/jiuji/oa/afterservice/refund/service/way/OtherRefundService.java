package com.jiuji.oa.afterservice.refund.service.way;

import com.alicp.jetcache.anno.Cached;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.refund.service.BaseRefundService;
import com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo;

import java.util.List;

/**
 * 其他退款服务类
 * <AUTHOR>
 * @since 2022/7/19 15:59
 */
public interface OtherRefundService extends BaseRefundService<OtherRefundVo> {
    String YU_E = "余额";
    String XIAN_JIN = "现金";
    String ZI_TI_DIAN_YU_E = "自提点余额";

}
