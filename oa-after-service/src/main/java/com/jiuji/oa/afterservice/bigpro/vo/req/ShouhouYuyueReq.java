package com.jiuji.oa.afterservice.bigpro.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jiuji.oa.afterservice.bigpro.bo.AddinfopsBo;
import com.jiuji.oa.afterservice.bigpro.bo.LockWxpjBo;
import com.jiuji.oa.afterservice.bigpro.bo.shouhou.ScanCodeAddSub;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/3/20
 */
@ApiModel
@Data
@Accessors(chain = true)
public class ShouhouYuyueReq implements ScanCodeAddSub {
    @ApiModelProperty(value = "预约单编号")
    private Integer id;

    @ApiModelProperty(value = "订单号")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private Integer subId;

    @ApiModelProperty(value = "客户手机")
    @NotNull
    private String mobile;

    @ApiModelProperty(value = "客户名称")
    @NotNull
    private String userName;

    @ApiModelProperty(value = "是否大件")
    private Boolean isMobile;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "处理方式")
    private Integer dealWay;

    @ApiModelProperty(value = "服务方式")
    private Integer servicesWay;

    @NotNull
    @ApiModelProperty(value = "处理地区")
    private String area;

    @ApiModelProperty(value = "basketId")
    private Integer basketId;

    @ApiModelProperty(value = "商品skuId")
    private Integer ppid;

    @ApiModelProperty(value = "客户id")
    private Integer userId;

    @ApiModelProperty(value = "商品名称")
    @NotNull
    private String productName;

    @ApiModelProperty(value = "商品规格")
    private String productColor;

    @ApiModelProperty(value = "门店id")
    private Integer areaId;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "快递时间")
    private LocalDateTime kdtime;

    private Integer delivery;

    @ApiModelProperty(value = "串号")
    @NotNull
    private String imei;

    @ApiModelProperty(value = "串号来源 1、扫码获取串号，2、无法获取串号")
    private Integer imeiFromtype;

    @ApiModelProperty(value = "评论内容")
    private String comment;

    @ApiModelProperty(value = "问题描述")
    @NotNull
    private String problem;

    private LocalDateTime tuiDate;

    @ApiModelProperty(value = "是否备份数据",notes = "1是，0否")
    private Integer isBakData;

    @ApiModelProperty(value = "无理由")
    private String wuliyou;

    private String yuyuePPids;

    @ApiModelProperty(value = "快递单")
    private String kuaididan;

    @ApiModelProperty(value = "快递公司")
    private String kuaidigongsi;

    @ApiModelProperty(value = "优惠码")
    private String coupon;

    @ApiModelProperty(value = "问题分类id")
    private String troubleIds;

    @ApiModelProperty(value = "地址信息")
    private AddinfopsBo addressInfo;

    @ApiModelProperty(value = "小件退换商品")
    private TuiData tuidata;

    @ApiModelProperty(value = "维修配件")
    private List<LockWxpjBo> wxpjList;
    @ApiModelProperty(value = "附件信息")
    private List<FileReq> attachment;
    @ApiModelProperty("加单类型 识别码= 0 我的客户= 1 验证码加单= 2")
    private String addType;
    @ApiModelProperty(value = "扫码加单的会员电话")
    private String scanCodeUserMobile;
    /**
     * 订单来源
     * @see com.jiuji.cloud.after.enums.ShouhouyuyueFromSourceEnum
     */
    @ApiModelProperty("订单来源")
    private Integer fromSource;


    private Boolean isQuJianddress;



    @Data
    public static class TuiData{
        @ApiModelProperty(value = "订单id")
        private Integer sub;
        @ApiModelProperty(value = "退款金额")
        private BigDecimal tuim;
        @ApiModelProperty(value = "折价")
        private BigDecimal zhem;
        @ApiModelProperty(value = "退款方式")
        private String way;
        @ApiModelProperty(value = "开户行")
        private String bname;
        @ApiModelProperty(value = "账户名")
        private String bfm;
        @ApiModelProperty(value = "卡号")
        private String bid;
        @ApiModelProperty(value = "订单basket信息")
        private List<Basket> basket;

        @Data
        public static class Basket{
            @ApiModelProperty(value = "basketId")
            private Integer id;
            @ApiModelProperty(value = "退货数量")
            private Integer count;
        }
    }
}
