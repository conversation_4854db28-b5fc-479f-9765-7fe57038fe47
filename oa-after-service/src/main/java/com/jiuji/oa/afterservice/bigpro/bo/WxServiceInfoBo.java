package com.jiuji.oa.afterservice.bigpro.bo;

import com.jiuji.oa.afterservice.bigpro.po.Imeisearchlogs;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/3/16
 */
@Data
public class WxServiceInfoBo {
    private Integer stats;
    private Integer productid;
    private Integer ppriceid;
    private Integer mkc_id;
    private String product_name;
    private String product_color;
    private LocalDateTime tradedate;
    private String imei;
    private String area;
    private String sub_mobile;
    private Integer userid;
    private Integer userclass;
    private String userclassname;
    private String xtenanttype;
    private Boolean blacklist;
    private Integer brandid;
    private Integer cid;
    private BigDecimal price;
    private Integer sub_id;
    private Integer basket_id;
    private Integer type;
    private String sub_to;

    private Boolean bougthCarePlusFlag;
    /**
     * 是否保修
     */
    private Boolean isbaoxiu;
    /**
     * 保修结束时间
     */
    private LocalDateTime BaoxiuEndDate;
    private String ixXcMkcInfo;
    private String yiwai_version;
    /**
     * 回收标志
     */
    private Boolean lastRecoverFlag;
    /**
     * 串号查询日志
     */
    private List<Imeisearchlogs> imeilist;
    /**
     * 是否回收商品
     */
    private Integer ishuishou;


}
