package com.jiuji.oa.afterservice.sys.service;

import com.jiuji.oa.afterservice.sys.vo.req.SpecialOperationVO;
import com.jiuji.tc.common.vo.R;

/**
 * 创建服务的接口类
 * <AUTHOR>
 * @since 2024/7/4 11:05
 */
public interface SpecialOperationService {
    /**
     * 根据类型获取特殊操作的内容
     * @param operationType
     * @return
     */
    R getSpecialOperation(String operationType);

    /**
     * 提交特殊操作请求
     * @param specialOperation
     * @return
     */
    R submitSpecialOperation(SpecialOperationVO specialOperation);

    R<SpecialOperationVO> getSmallproRefundAllowed(String operationType);


    R submitSmallproRefundAllowed(SpecialOperationVO specialOperationVO);
}
