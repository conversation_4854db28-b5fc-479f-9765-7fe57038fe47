package com.jiuji.oa.afterservice.bigpro.service;

import com.jiuji.oa.afterservice.bigpro.bo.RepairPriceBo;
import com.jiuji.tc.common.vo.R;

/**
 * 售后维修接口
 * @author: gengjiaping
 * @date: 2020/3/10
 */
public interface RepairService {

    /**
     * 更新维修费用
     * @param wxid
     * @return
     */
    R<Boolean> updateShouhouFeiyong(Integer wxid);

    /**
     * 获取维修费用
     * @param wxid
     * @return
     */
    RepairPriceBo getShouhouFeiyong(Integer wxid);

}
