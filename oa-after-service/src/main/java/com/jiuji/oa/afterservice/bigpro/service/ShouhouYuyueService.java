package com.jiuji.oa.afterservice.bigpro.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.after.vo.req.ShouhouyuyueFromSource;
import com.jiuji.oa.afterservice.bigpro.bo.HandleYuYueData;
import com.jiuji.oa.afterservice.bigpro.bo.LockWxpjBo;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.YjsxAddress;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.vo.OrderPartsVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouYuYueAlterImeiReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouYuyueListReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
public interface ShouhouYuyueService extends IService<ShouhouYuyue> {

    /**
     * 获取预约单URL
     * @param yuYueId
     * @return
     */
    String getYuYueIdUrl(Integer yuYueId);
    /**
     * 创建订购单
     * @param orderPartsVo
     */
    void createOrderParts(OrderPartsVo orderPartsVo);
    /**
     * 是否使用新版预约单
     * @return
     */
    Boolean isUseNewYuYue();

    /**
     * 通过业务进行确认
     */
    String CONFIRM_BY_YW = "confirm_by_yw";

    /**
     * 售后预约列表分页查询
     * @param param
     * @return
     */
    PageVo getShouhouYuyuePage(ShouhouYuyueListReq param);

    /**
     * 获取售后预约单
     * @param id
     * @return
     */
    ShouhouYuyueInfoRes getShouhouYuyueInfo(Integer id);

    /**
     * 通过商品ppids获取锁定的维修配件
     * @param yuyueId
     * @return
     */
    List<LockWxpjBo> getLockWxpj(Integer yuyueId,List<Integer> ppids);

    /**
     * 获取维修故障类别
     * @return
     */
    @Cached(name = "getRepairTypes",expire = 1,timeUnit = TimeUnit.HOURS)
    List<ShouhouYuyueInfoRes.RepairClassifyInfo> getRepairTypes();

    /**
     * 保修信息查询
     * @param imei
     * @param isSaveImeiSearchLog 是否保存串号查询日志
     */
    R<WxServiceInfoRes> getServiceInfo(String imei, Boolean isSaveImeiSearchLog);

    /**
     * 获取客服确认默认用户
     * @param stype
     * @param yyid
     * @return
     */
    List<UserSimpleInfoRes> getYuyueConfirmUsers(Integer stype, Integer yyid);

    /**
     *客服确认
     * @param yyid
     * @param smUserId
     * @return
     */
    R<Boolean> yuyueConfirm(Integer yyid,Integer smUserId);

    /**
     * 取消预约单
     *
     * @param yyid
     * @param remark
     * @param userId
     * @return
     */
    R<Boolean> delYuyue(Integer yyid, String cancleKind, String remark, String operator, Integer userId);

    /**
     * 保存或更新预约单
     * @param param
     * @return
     */
    R<Integer> saveOrUpdateYuyue(ShouhouYuyueReq param);

    /**
     * 设置为 已上门 或 已到店 （生成售后单）
     * @param id
     * @param smddType
     * @param wuliuFlag
     * @return
     */
    R<Boolean> yuyueconfirmSmDdEnter(Integer id,Integer smddType,Boolean wuliuFlag);

    R<List<OrderPartsVo>> orderingAccessories(Integer id, Integer smddType, Boolean wuliuFlag);

    /**
     * 通过串号和手机号码查询是否存在其他预约单
     * @param mobile
     * @param imei
     * @return
     */
    R<Integer> hasYuyueSubByMobileAndImei(String mobile,String imei);

    /**
     * 获取预约商品信息
     * @param subId
     * @return
     */
   List<YuyueProductInfoRes> getYuyueProduct(Integer subId);

    /**
     * 根据订单号查询可退换商品信息
     * @param subId
     * @return
     */
    List<YuyueProductInfoRes> getTuiProductInfoBySubId(Integer subId,Integer isMobile);

    /**
     * 业务确认
     * @param yuyueId
     */
   R<Boolean> yuyueconfirmYwEnter(Integer yuyueId);


    /**
     * 恢复处理的数据
     * @param data
     * @return
     */

    List<HandleYuYueData> recoveryHandleData(String data);
    /**
     * 处理已到店、已上门 成为已完成
     * 处理 未取件为已完成
     * @return
     */
    List<HandleYuYueData> handleData();

    /**
     * 把state==5的预约单 添加到 sub_delcollect表里面
     * @return
     */
    String handleDataDel();

    /**
     * 钻石会员校验
     * @param yuyueId
     * @return
     */
    R<String> checkDiamondsMember(Integer yuyueId);

    /**
     *发送预约确认短信，发送已接件短信
     * @param type
     * @param mobile
     * @param id
     * @param userId
     * @return
     */
   R<Boolean> sendYuyueSms(Integer type,String mobile,Integer id,Integer userId);

    /**
     * 获取接件短信内容
     * @param shouhouId
     * @param userId
     * @return
     */
   R<String> getYuyueSmsContent(Integer shouhouId,Integer userId);

    /**
     * 发送电子凭证
     * @param subId
     * @param userId
     * @param type
     * @param webUrl
     * @return
     */
   R<Boolean> certificateShouhouYuyueAndShouhou(Integer subId,Integer userId,Integer type,String webUrl);

    /**
     * 中邮已收件
     * @param yuyueId
     * @return
     */
    R<Boolean> zyYishoujian(Integer yuyueId);

    /**
     * 获取排队叫号打印
     * @param area
     * @return
     */
    R<List<YuyuePaiduiPrintRes>> getPaiduiPring(String area);

    /**
     * 修改串号信息
     * @param req
     * @return
     */
    R<Boolean> updateImeiById(ShouhouYuYueAlterImeiReq req);

    R<List<YjsxAddress>> getYjsxAddress();

    /**
     * 获取微信接收人id
     *
     * @param classify
     * @return
     */
    List<Integer> getWxSmsReceiverUserIds(String classify);

    /**
     * 获取在职人员
     *
     * @param ch999Ids
     * @return
     */
    List<UserSimpleInfoRes> getWorkingUsers(List<Integer> ch999Ids);

    R<Boolean> setShouhouyuyueFromSource(ShouhouyuyueFromSource shouhouyuyueFromSource);

    String updateYuyueLog();

    Integer getFromsourceById(Integer id);

    String getFromSourceDescById(Integer yuyueId);


    /**
     * 初始化AreaSubject
     * @param areaId
     * @return
     */
    AreaInfo initializationAreaSubject(Integer areaId);
}
