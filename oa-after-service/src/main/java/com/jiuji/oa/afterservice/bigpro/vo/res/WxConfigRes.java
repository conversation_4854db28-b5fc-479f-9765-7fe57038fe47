package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.jiuji.oa.afterservice.bigpro.po.Wxconfigoption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 机型资料配置
 * @author: <PERSON>
 * @date: 2020/5/19 16:02
 */
@Data
@ApiModel
public class WxConfigRes {

    @ApiModelProperty(value = "编号")
    private Integer id;

    @ApiModelProperty(value = "机型名称")
    private String name;

    @ApiModelProperty(value = "负责人")
    private String user;

    @ApiModelProperty(value = "负责人手机")
    private String mobile;

    @ApiModelProperty(value = "依据关键词")
    private String keys;

    @ApiModelProperty(value = "检测周期 时间1")
    private String minTime1;

    @ApiModelProperty(value = "检测周期 时间2")
    private String maxTime1;

    @ApiModelProperty(value = "检测周期 所需要资料配置")
    private String config1;

    @ApiModelProperty(value = "换机周期  时间")
    private String minTime2;

    @ApiModelProperty(value = "换机周期  时间")
    private String maxTime2;

    @ApiModelProperty(value = "换机周期 所需要资料配置")
    private String config2;

    @ApiModelProperty(value = "维修周期 时间")
    private String minTime3;

    @ApiModelProperty(value = "维修周期 时间")
    private String maxTime3;

    @ApiModelProperty(value = "维修周期 所需要资料配置")
    private String config3;

    private Integer rank;

    @ApiModelProperty(value = "有配件最快")
    private String time41;

    @ApiModelProperty(value = "有配件最慢")
    private String time42;

    @ApiModelProperty(value = "无配件最快")
    private String time43;

    @ApiModelProperty(value = "无配件最慢")
    private String time44;

    @ApiModelProperty(value = "主板芯片级维修最快")
    private String time45;

    @ApiModelProperty(value = "主板芯片级维修最慢")
    private String time46;

    @ApiModelProperty(value = "外修/非保/意外保/延保直接在公司售后中心报价维修处理")
    private String config4;

    @ApiModelProperty(value = "DC维修负责人")
    private String weiXiuUser;

    @ApiModelProperty(value = "DC维修负责人手机")
    private String weiXiuMobile;

    private String comment;

    @ApiModelProperty(value = "维修配置选项")
    private List<Wxconfigoption> config1Options;

    @ApiModelProperty(value = "维修配置选项")
    private List<Wxconfigoption> config2Options;

    @ApiModelProperty(value = "维修配置选项")
    private List<Wxconfigoption> config3Options;

    @ApiModelProperty(value = "维修配置选项")
    private List<Wxconfigoption> config4Options;

}
