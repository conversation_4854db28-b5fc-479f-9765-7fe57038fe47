package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理状态枚举
 * @author: <PERSON>
 * @date: 2020/5/20
 */
@Getter
@AllArgsConstructor
public enum DealStatsEnum implements CodeMessageEnumInterface {
    CLZ(0,"处理中"),
    YXH(1,"已修好"),
    FSZ(2,"返深圳"),
    XBH(3,"修不好"),
    YSC(5,"已删除");;
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
