package com.jiuji.oa.afterservice.bigpro.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ProductColorInfo {
    private Integer ppriceid;
    private Integer productid;
    private String name;
    private String value;
    private BigDecimal costprice;
    private Integer ppriceid1;
    private Integer cid;
    private Integer id;
    private Integer detailid;
    private Integer orderid;
    private Integer standId;
}
