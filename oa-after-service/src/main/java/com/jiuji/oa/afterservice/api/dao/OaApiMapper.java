package com.jiuji.oa.afterservice.api.dao;

import com.jiuji.oa.afterservice.api.bo.Ch999SimpleUserRoleBo;
import com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO;
import com.jiuji.oa.afterservice.api.bo.ShouhouPriceBo;
import com.jiuji.oa.afterservice.api.bo.YouhuiMaInfo;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.express.WuliuInfo;
import com.jiuji.oa.afterservice.shouhou.vo.AfterSaleSearchVO;
import com.jiuji.oa.afterservice.shouhou.vo.ProductKcInfo;
import com.jiuji.oa.afterservice.shouhou.vo.ShouHouDetailVo;
import com.jiuji.oa.afterservice.shouhou.vo.ShouhouDaiyongjiVo;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @author: Li quan
 * @date: 2020/6/28 14:21
 */

@Mapper
public interface OaApiMapper {

    /**
     * 查询售后基本信息、会员信息
     *
     * @param id
     * @param userId
     * @param imei
     * @param isMobile 是否通过手机号查询
     * @return
     */
    ShouHouDetailVo getShouhouInfoByIdAndUserIdAndImei(@Param("id") Integer id,
                                                       @Param("userId") Integer userId,
                                                       @Param("imei") String imei,
                                                       @Param("isMobile") Boolean isMobile,
                                                       @Param("officeName") String officeName);

    /**
     * 查询物流信息
     *
     * @param shouhouId
     * @return
     */
    WuliuInfo getWuliuInfoByShouhouId(@Param("shouhouId") Integer shouhouId);

    /**
     * 获取备用机信息
     *
     * @param shouhouId
     * @return
     */
    ShouhouDaiyongjiVo getShouhouDaiyongjiInfoByShouhouId(@Param("shouhouId") Integer shouhouId);

    /**
     * 查询售后维修成本信息
     *
     * @param shouhouId
     * @return
     */
    List<ShouhouPriceBo> getShouhouPriceByShouhouId(@Param("shouhouId") Integer shouhouId);

    /**
     * 查询优惠码
     *
     * @param yuyueId
     * @return
     */
    List<YouhuiMaInfo> getYouhuiMaList(@Param("yuyueId") Integer yuyueId);

    /**
     * 校验预约上门取件名额
     *
     * @return
     */
    Integer checkYuyueSmqjCount();

    /**
     * 校验导购员工是否有效
     *
     * @param ch999Id
     * @return
     */
    Integer checkStaffInfo(@Param("ch999Id") Integer ch999Id);

    /**
     * 获取最新维修信息 维修专区调用
     *
     * @param rows 记录行数
     * @return R
     */
    List<AfterSaleSearchVO> getAfterSale(@Param("rows") Integer rows);

    List<Ch999UserBasicBO> listAllUserBasicInfo(@Param("officeName")String officeName);

    /**
     * @param type
     * @return
     */
    List<ProductKcInfo> queryProductStockByPpid(@Param("ppids") List<Integer> ppids, @Param("type") Integer type);

    /**
     * @param type
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<ProductKcInfo> queryProductStockByPpidV3(@Param("ppids") List<Integer> ppids, @Param("type") Integer type);

    /**
     * 获取用户壳膜订单
     * @param teamId 会员id
     * @return 订单数量
     */
    Integer getOrderClassByUserId(@Param("teamId") Integer teamId,@Param("cidList")  List<Integer> cidList);

    /**
     * 获取团队票的id
     * @param userId
     * @return
     */
    Integer listTeamIds(@Param("userId") Integer userId);

    /**
     * 通过电话号码获取员工角色名称
     */
    Ch999SimpleUserRoleBo  getRoleNameByMobile(@Param("mobile") String mobile);
}
