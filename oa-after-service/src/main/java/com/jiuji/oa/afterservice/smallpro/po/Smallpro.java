package com.jiuji.oa.afterservice.smallpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.afterservice.common.constant.SmallproGroupEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProKindEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProStatsEnum;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("Smallpro")
public class Smallpro extends Model<Smallpro> implements Serializable {

    private static final long serialVersionUID = 2640070274336541135L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 1-- 销售单贴膜损耗
     * 2-- 小件单贴膜损耗
     * @see SourceTypeEnum
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 接件方式
     */
    private String connectionMethod;

    /**
     * 接件Fid
     */
    @TableField(exist = false)
    private String connectionFid;
    /**
     * 名称
     */
    @TableField("Name")
    private String name;
    /**
     * 地区
     */
    @TableField("Area")
    private String area;

    /**
     * 购买者id
     */
    @TableField("userid")
    private Integer userId;
    /**
     * 订单号
     */
    @TableField("sub_id")
    private Integer subId;
    /**
     * 购买时间
     */
    @TableField("Buydate")
    private LocalDateTime buyDate;
    /**
     * 外观
     */
    @TableField("Outward")
    private String outward;

    @TableField("IsBaoxiu")
    @ApiModelProperty(value = "是否在保 true在保，0非保")
    private Boolean isBaoxiu;
    /**
     * 分组ID
     * @see SmallproGroupEnum
     */
    @TableField("Groupid")
    private Integer groupId;
    /**
     * 接件人
     */
    @TableField("Inuser")
    private String inUser;
    /**
     * 接件时间
     */
    @TableField("Indate")
    private LocalDateTime inDate;
    /**
     * 处理方式[1修 2换 3退 4现货]
     * @see SmallProKindEnum
     */
    @TableField("Kind")
    private Integer kind;
    /**
     * 状态  0处理中，1处理完成，2已删除，3送修中
     * @see SmallProStatsEnum
     */
    @TableField("Stats")
    private Integer stats;
    /**
     * 客户名称
     */
    @TableField("Username")
    private String userName;

    /**
     * 客户手机
     */
    @TableField("Mobile")
    private String mobile;

    /**
     * 故障描述
     */
    @TableField(value = "Problem", strategy = FieldStrategy.NOT_NULL)
    private String problem;
    /**
     * 备注
     */
    @TableField("Comment")
    private String comment;
    /**
     * 转到门店
     */
    @TableField("toarea")
    private String toArea;
    /**
     * 是否转地区中
     */
    @TableField("istoarea")
    private Boolean isToArea;
    /**
     * 接件确认
     */
    @TableField("yuyueCheck")
    private Boolean yuyueCheck;

    /**
     * 预约ID
     */
    @TableField("yuyueid")
    private Integer yuyueId;
    /**
     * 门店id
     */
    @TableField("areaid")
    private Integer areaId;

    /**
     * 转门店areaid
     */
    @TableField("toareaid")
    private Integer toAreaId;
    /**
     * 现货中关联的订单id
     */
    @TableField("oldid")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private Integer oldId;

    /**
     * @see OldIdTypeEnum
     * 关联订单类型
     */
    @TableField("old_id_type")
    private Integer oldIdType;
    /**
     * oldId 对应的userId
     */
    @TableField("old_user_id")
    private Integer oldUserId;
    /**
     * 验证码
     */
    @TableField("codeMsg")
    private String codeMsg;

    @TableField("owenStats")
    private Integer owenStats;
    /**
     * 是否收银标志
     */
    @TableField("isshouyinglock")
    private Boolean isShouyingLock;
    /**
     * 维修费
     */
    @TableField("feiyong")
    private BigDecimal feiyong;
    /**
     * 维修成本
     */
    @TableField("costprice")
    private BigDecimal costPrice;
    /**
     * 收银用户
     */
    @TableField("shouyinuser")
    private String shouyinUser;
    /**
     * 收银时间
     */
    @TableField("shouyindate")
    private LocalDateTime shouyinDate;
    @TableField("istui")
    private Boolean isTui;
    /**
     * 取件时间
     */
    @TableField("qujiandate")
    private LocalDateTime qujianDate;
    /**
     * 维修渠道
     */
    @TableField("wxqudao")
    private String wxQudao;
    /**
     * 维修状态
     */
    @TableField("wxState")
    private Integer wxState;
    /**
     * 维修人
     */
    @TableField("wxuser")
    private String wxUser;

    @TableField("isCheckmsg")
    private Integer isCheckMsg;

    @TableField("isdel")
    private Boolean isDel;
    @TableField("isth")
    private Boolean isTh;
    @TableField("tongzhitime")
    private LocalDateTime tongzhiTime;
    /**
     * 生成物流单Id
     */
    @TableField("wuliuid")
    private Integer wuliuId;
    /**
     *
     */
    @TableField("fcWuliuId")
    private Integer fcWuliuId;
    @TableField("intertime")
    private LocalDateTime interTime;
    @TableField("toareatime")
    private LocalDateTime toAreaTime;
    /**
     * 配置
     */
    @TableField("config")
    private String config;

    /**
     * 数据解绑状态
     */
    @TableField("dataRelease")
    private Integer dataRelease;
    /**
     * 串号/SN
     */
    @TableField("imei")
    private String imei;

    /**
     * 验证码图片fId
     */
    @TableField("fid")
    private String fid;
    /**
     * 备货订单Id
     */
    @TableField("hhSubId")
    private Integer hhSubId;
    /**
     * 置换PPid
     */
    @TableField("changePpriceid")
    private Integer changePpriceid;

    /**
     * 九机服务类型
     * @see SmallProServiceTypeEnum
     */
    @TableField("ServiceType")
    private Integer serviceType;

    /**
     * 修好/未修好时间
     */
    @TableField("wx_time")
    private LocalDateTime wxTime;

    /**
     * 异常状态 0或null:无异常  1:发货异常
     */
    @TableField("abnormal_type")
    private Integer abnormalType;

    /**
     * 处理方式是否进行了特殊处理 特殊处理时，权值审核逻辑更改
     */
    @ApiModelProperty(value = "处理方式是否进行了特殊处理")
    @TableField("is_special_treatment")
    private Boolean isSpecialTreatment;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
