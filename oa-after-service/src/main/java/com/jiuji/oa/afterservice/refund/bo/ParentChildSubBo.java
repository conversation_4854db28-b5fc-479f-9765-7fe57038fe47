package com.jiuji.oa.afterservice.refund.bo;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "父子订单实体")
public class ParentChildSubBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "subId")
    @ApiModelProperty(value = "子订单ID")
    private Integer subId;

    @TableField("idType")
    @ApiModelProperty(value = "ID类型")
    private Integer idType;

    @TableField("yifuM")
    @ApiModelProperty(value = "父子订单已付金额")
    private BigDecimal yifuM;

    @TableField("subCheck")
    @ApiModelProperty(value = "父子订单状态")
    private Integer subCheck;

    /**
     * 订单ID类型枚举
     * Enum for Order ID Types
     *
     * <AUTHOR>
     * @date 2019/11/13
     * @since 1.0.0
     */
    @AllArgsConstructor
    @Getter
    @ApiModel(value = "订单ID类型枚举")
    public enum IdTypeEnum implements CodeMessageEnumInterface {

        @EnumValue
        @ApiModelProperty(value = "当前订单", example = "0")
        CURR_ORDER(0, "当前订单"),

        @EnumValue
        @ApiModelProperty(value = "父订单", example = "1")
        PARENT_ORDER(1, "父订单"),

        @EnumValue
        @ApiModelProperty(value = "子订单", example = "2")
        CHILD_ORDER(2, "子订单");

        private final Integer code;
        private final String message;
    }

}
