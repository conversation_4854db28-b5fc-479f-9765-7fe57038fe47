package com.jiuji.oa.afterservice.csharp.returns.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 微信重新支付请求接口
 * <AUTHOR>
 * @since 2022/10/17 10:40
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class WechatRetryPayReq {

    /**
     * 退换id
     */
    @ApiModelProperty("退换id")
    private Integer orderid;
    /**
     * 售后退换明细id
     */
    @ApiModelProperty("售后退换明细id")
    private Integer detailId;
    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String customerName;
    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String inuser;
    /**
     * 对应的表
     */
    @ApiModelProperty("对应的表")
    private String subsystem;
}
