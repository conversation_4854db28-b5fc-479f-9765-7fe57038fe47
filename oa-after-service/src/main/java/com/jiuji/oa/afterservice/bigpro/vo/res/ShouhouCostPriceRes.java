package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/4/16
 */
@Data
@Accessors(chain = true)
@ApiModel("添加维修费res")
public class ShouhouCostPriceRes {
    private Integer shouhoufuwu;
    private String fuwuConfig;

    @ApiModelProperty(value = "绑定配件库存信息")
    private WxFeeBo wxFeeBo;

    @ApiModelProperty(value = "服务介绍url")
    private String url;

    @ApiModelProperty(value = "服务描述")
    private String description;

    @ApiModelProperty(value = "规格")
    private String productColor;

}
