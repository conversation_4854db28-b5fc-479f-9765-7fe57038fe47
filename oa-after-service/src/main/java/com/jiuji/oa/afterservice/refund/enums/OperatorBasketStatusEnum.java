package com.jiuji.oa.afterservice.refund.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 发票类型
 */
@Getter
@AllArgsConstructor
public enum OperatorBasketStatusEnum implements CodeMessageEnumInterface {
    HANDLED(1,"已办理"),
    CANCELLED(2,"返销") ;

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**
     * @param code
     * @return
     */
    public static OperatorBasketStatusEnum getEnumByCode(Integer code) {
        OperatorBasketStatusEnum[] value = values();
        for (OperatorBasketStatusEnum e : value) {
            if (Objects.equals(e.getCode(),code)) {
                return e;
            }
        }
        return null;
    }
}
