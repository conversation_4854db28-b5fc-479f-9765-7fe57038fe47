package com.jiuji.oa.afterservice.electronicTicket.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/2/24
 * @description 门店类型
 */
@Getter
@AllArgsConstructor
public enum ConfirmTypeEnum implements CodeMessageEnumInterface {

    /**
     * 门店类型
     */
    CONFIRM_TYPE_ONE(1, "系统确认"),
    CONFIRM_TYPE_TWO(2, "人工确认");

    /**
     * key
     */
    private final Integer code;
    /**
     * value
     */
    private final String message;

}
