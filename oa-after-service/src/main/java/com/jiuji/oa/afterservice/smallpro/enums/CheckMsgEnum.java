package com.jiuji.oa.afterservice.smallpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * description: <检查消息状态枚举类>
 *
 * <AUTHOR>
 * @date 2024-07-30
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum CheckMsgEnum implements CodeMessageEnumInterface {

    /**
     * 检查消息状态 - 编码-编码信息
     */
    NOT_CHECKED(0, "未检查"),
    CHECKED(1, "已检查"),
    CHECKING(2, "检查中"),
    CHECK_FAILED(3, "检查失败");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举对象
     */
    public static CheckMsgEnum valueOfByCode(Integer code){
        for (CheckMsgEnum temp : CheckMsgEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述信息
     * @param code 编码
     * @return 描述信息
     */
    public static String getMessageByCode(Integer code){
        for (CheckMsgEnum temp : CheckMsgEnum.class.getEnumConstants()) {
            if (Objects.equals(temp.getCode(),code)) {
                return temp.getMessage();
            }
        }
        return "";
    }
}
