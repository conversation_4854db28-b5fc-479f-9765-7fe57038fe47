package com.jiuji.oa.afterservice.smallpro.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.repository.ShouhouLogNewRepository;
import com.jiuji.oa.afterservice.bigpro.repository.document.ShouhouLogNew;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.smallpro.bo.user.UserSmallproQuery;
import com.jiuji.oa.afterservice.smallpro.bo.user.UserSmallproResVo;
import com.jiuji.oa.afterservice.smallpro.service.UserSmallproService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.alibaba.druid.sql.ast.SQLPartitionValue.Operator.List;

/**
 * 用戶小件管理接口
 * <AUTHOR>
 * @since 2022/2/21 14:59
 */
@RestController
@RequestMapping("/api/updateImeiError")
@Api(tags = "用戶小件管理")
@Slf4j
public class UpdateImeiErrorController {

    @Autowired
    private UserSmallproService userSmallproService;
    @Resource
    private ShouhouLogNewRepository shouhouLogNewRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private ProductinfoService productinfoService;

    @GetMapping("/updateErrorData")
    public R<String> updateErrorData( ){
        // 构建查询条件
        Query query = new Query();
        query.addCriteria(Criteria.where("conts.comment").regex("同步修改信息").regex("机型由【"));
        query.addCriteria(Criteria.where("_id").gt(3231439).lte(3900039));
        List<ShouhouLogNew> shouhouLogNews = mongoTemplate.find(query, ShouhouLogNew.class);
        log.info("售后异常单据："+shouhouLogNews.size());
        List<Integer> shouhouIdList = shouhouLogNews.stream().map(ShouhouLogNew::getId).collect(Collectors.toList());
        //查询出异常单子
        List<Shouhou> shouhous = CommonUtils.bigDataInQuery(shouhouIdList, ids -> shouhouService.lambdaQuery()
                .in(Shouhou::getId, shouhouIdList)
                .select(Shouhou::getId, Shouhou::getName, Shouhou::getPpriceid).list());
        List<Integer> ppidList = shouhous.stream().map(Shouhou::getPpriceid).collect(Collectors.toList());
        Map<Integer, Productinfo> productMap  = productinfoService.getProductMapByPpids(ppidList);
        new MultipleTransaction().execute(DataSourceConstants.DEFAULT, () -> {
            for (Shouhou shouhou : shouhous) {
                Productinfo productinfo = productMap.getOrDefault(shouhou.getPpriceid(),new Productinfo());
                String name = shouhou.getName();
                if(!productinfo.getProductName().equals(name)){
                    String comment = "异常单号：" + shouhou.getId() + "，商品名称由：" + name + "，修改为：" + productinfo.getProductName();
                    boolean update = shouhouService.lambdaUpdate().set(Shouhou::getName, productinfo.getProductName()).eq(Shouhou::getId, shouhou.getId()).update();
                    if(!update){
                        throw new CustomizeException(comment);
                    }
                    log.info(comment);
                }
            }
        }).commit();
        return R.success("");
    }
}
