package com.jiuji.oa.afterservice.smallpro.bo.smallproExchange;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description: <Class introduction>
 * translation: <Method translation introduction>
 *
 * <AUTHOR>
 * @date 2020/4/14
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproBankInstallmentInfoBO {

    private String otherDsc;

    private Integer fenQiNum;

    private Integer midplatform;
}
