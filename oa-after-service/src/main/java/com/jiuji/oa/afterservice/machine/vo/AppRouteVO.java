package com.jiuji.oa.afterservice.machine.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * app路由信息
 * <AUTHOR>
 * @since 2021/8/18 11:56
 */
@NoArgsConstructor
@Data
@ApiModel("app路由信息VO")
public class AppRouteVO {
    @ApiModelProperty("商品id")
    private Integer basketId;
    @ApiModelProperty("商品ppriceid")
    private Integer ppriceid;
    @ApiModelProperty("订单状态")
    private Integer subCheck;
    @ApiModelProperty("是否有验机配置")
    private Boolean hasCheckConfig;
    @ApiModelProperty("服务记录id")
    private Integer serverRecordId;
    @ApiModelProperty("路由地址")
    private String url;
    @ApiModelProperty("地址类型 1 app路由 2 http地址")
    private Integer urlType;
}
