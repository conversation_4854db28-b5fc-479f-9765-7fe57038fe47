package com.jiuji.oa.afterservice.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> qiweiqing
 * @Date: 2019/11/04/18:16
 * @Description:
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PageReq {

    @NotNull(message = "当前页数不能为空")
    @Min(value = 1)
    @ApiModelProperty(value = "当前第几页", required = true, example = "1")
    private Integer current;

    @NotNull(message = "每页数量不能为空")
    @Min(value = 1)
    @Max(value = 1000)
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer size;

    public Integer getStartRows() {
        if (current == null || size == null) {
            return 0;
        }
        return (current - 1) * size;
    }

}
