package com.jiuji.oa.afterservice.smallpro.vo.res;

import com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardLastChangePPidBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * description: <小件最后一次置换商品Res>
 * translation: <The last replacement of the small item Res>
 *
 * <AUTHOR>
 * @date 2020/4/17
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproLastChangePpidRes implements Serializable {

    private static final long serialVersionUID = -8015195732416407022L;

    /**
     * 上一次置换商品校验信息
     */
    private FilmCardLastChangePPidBO changePpidInfo;
    /**
     * 置换商品对应信息
     */
    private SmallproProductInfoBO changeProductInfo;
    /**
     * 是否30天无理由换新
     */
    private Boolean isFreeExchange;
    /**
     * 是否获取到了上一次的ppid
     */
    private Boolean isGetLastPpid;
    /**
     * 默认填写Ppid
     */
    private Integer ppriceId;

    private Integer code;

    private String message;
}
