package com.jiuji.oa.afterservice.other.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO;
import com.jiuji.oa.afterservice.other.dao.ReceivePersonConfigMapper;
import com.jiuji.oa.afterservice.other.po.ReceivePersonConfig;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.service.ReceivePersonConfigService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@Service
public class ReceivePersonConfigServiceImpl extends ServiceImpl<ReceivePersonConfigMapper, ReceivePersonConfig> implements ReceivePersonConfigService {

    @Autowired
    private AreainfoService areainfoService;

    @Override
    public List<ReceivePersonConfig> listSqlServer(Wrapper wrapper) {
        return baseMapper.listSqlServer(wrapper);
    }

    @Override
    public LogisticsRecipientBO getReceiverUserInfo(Integer logisticsType, Integer fromAreaId, Integer toAreaId) {
        LogisticsRecipientBO result = new LogisticsRecipientBO();
        List<ReceivePersonConfig> allConfig = ((ReceivePersonConfigServiceImpl) AopContext.currentProxy()).getAllConfig();
        List<String> areaList = new ArrayList<>();
        List<String> wTypeList = new ArrayList<>();
        List<ReceivePersonConfig> collect =
                allConfig.stream().sorted(Comparator.comparing(ReceivePersonConfig::getAddTime).reversed()).collect(Collectors.toList());
        for (ReceivePersonConfig personConfig : collect) {
            String toArea = personConfig.getToArea();
            String wType = personConfig.getWType();
            if (StringUtils.isNotEmpty(toArea)) {
                areaList = Arrays.asList(toArea.split(","));
            }
            if (StringUtils.isNotEmpty(wType)) {
                wTypeList = Arrays.asList(wType.split(","));
            }
            if (areaList.contains(toAreaId.toString()) && wTypeList.contains(logisticsType.toString())) {
                result.setReceiveUserId(personConfig.getReceiveUserid());
                result.setReceiveUserName(personConfig.getReceiveUserName());
                return result;
            }
        }
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(fromAreaId);
        List<Integer> backAreaList = Stream.of(backEndInfo.getDcAreaId(), backEndInfo.getH1AreaId(), backEndInfo.getD1AreaId())
                .collect(Collectors.toList());
        backAreaList.addAll(backEndInfo.getHqAreaIds());
        // 后端地区查不到直接返回
        if (backAreaList.contains(toAreaId)) {
            return null;
        }
        // 通用查找 按照级别查找
        List<LogisticsRecipientBO> resultList = baseMapper.getNormalReceiver(toAreaId);
        if (resultList != null && resultList.size() > 0) {
            result = resultList.get(0);
            return result;
        }
        return null;
    }

    @Override
    public List<ReceivePersonConfig> getAllConfig() {
        List<ReceivePersonConfig> list = list(new LambdaQueryWrapper<ReceivePersonConfig>().
                isNull(ReceivePersonConfig::getIsdel).or().eq(ReceivePersonConfig::getIsdel, false));
        return list;
    }
}
