package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: gengjiaping
 * @date: 2020/4/2
 */
@AllArgsConstructor
@Getter
public enum YuyueSmsTypeEnum implements CodeMessageEnumInterface {
    YWQRJD(1,"业务确认节点"),
    YQJJD(2,"已取件节点");

    /**
     * 类型
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
}
