package com.jiuji.oa.afterservice.bigpro.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouQuDaoList;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouQudaoService;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouJieJianQueryReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouQuDaoBatchWaiSongReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouJieJianInfo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.ExcelUtils;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "大件：维修单批量外送相关")
@RestController
@RequestMapping("/api/bigpro/qudao")
@AllArgsConstructor
@Slf4j
public class ShouhouQuDaoController {

    private final ShouhouQudaoService shouhouQudaoService;

    @ApiOperation(value = "维修单批量外送", httpMethod = "POST")
    @PostMapping("/shouHouBatchWaiSong")
    public R<Boolean> shouHouBatchWaiSong(@RequestBody @Validated ShouhouQuDaoBatchWaiSongReq req) {
        return R.success(shouhouQudaoService.saveOrUpdateWaiSongBatch(req));
    }

    @ApiOperation(value = "根据售后单号获取接件信息", httpMethod = "POST")
    @PostMapping("/getJieJianInfoListByIds")
    public R<List<ShouhouJieJianInfo>> getJieJianInfoListByIds(@RequestBody ShouhouJieJianQueryReq req) {
        return R.success(shouhouQudaoService.getJieJianInfoListByIds(req.getShouhouIdList()));
    }

    @ApiOperation(value = "维修单外送信息导出功能", httpMethod = "POST")
    @PostMapping("/exportWaiSongQuDaoList")
    public void shouHouBatchWaiSong(@RequestBody ShouhouJieJianQueryReq req,HttpServletResponse response) throws IOException {
        OaUserBO oaUserBO = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        log.warn("维修单外送信息导出条件: {}", JSON.toJSONString(req));
        List<List<Object>> data = new ArrayList<>();
        List<String> header = CollUtil.newLinkedList(
                "维修单号",
                "客户姓名",
                "客户电话",
                "设备机型",
                "设备串号",
                "故障描述",
                "发票（未开/已开）",
                "出险服务",
                "配置",
                "保修状态",
                "接件时间",
                "外送渠道"
        );
        boolean isQueryInUserInfo = XtenantEnum.isJiujiXtenant() && CollUtil.isNotEmpty(req.getShouhouIdList());
        if (isQueryInUserInfo){
            //只有九机优化为通过售后id查询
            header.add("接件人");
            header.add("接件人电话");
        }else{
            req.setShouhouIdList(null);
            req.setUserName(oaUserBO.getUserName());
        }
        List<ShouhouQuDaoList> shouHouQuDaoList = shouhouQudaoService.getShouHouQuDaoList(req);
        Map<String, Ch999UserVo> userInfoMap = Collections.emptyMap();
        if (isQueryInUserInfo){
            List<String> inUsers = shouHouQuDaoList.stream().map(ShouhouQuDaoList::getInUser).distinct().collect(Collectors.toList());
            userInfoMap = Optional.ofNullable(SpringUtil.getBean(UserInfoClient.class).getCh999UserByUserNames(inUsers)).filter(R::isSuccess)
                    .map(R::getData).map(uList -> uList.stream().collect(Collectors.toMap(Ch999UserVo::getCh999Name, ch -> ch, (ch1, ch2) -> ch1)))
                    .orElse(Collections.emptyMap());
        }
        for (ShouhouQuDaoList record : shouHouQuDaoList) {
            List<Object> row = new ArrayList<>();
            row.add(record.getId());
            row.add(record.getMemberName());
            row.add(record.getMobile());
            row.add(record.getProductColor());
            row.add(record.getImei());
            row.add(record.getProblem());
            row.add(record.getPiaoStatus());
            row.add(record.getServiceType());
            row.add(record.getPeizhi());
            row.add(record.getBaoXiuStatus());
            row.add(record.getReceiveDate());
            row.add(record.getQdName());
            if(isQueryInUserInfo){
                row.add(record.getInUser());
                row.add(Optional.ofNullable(userInfoMap.get(record.getInUser())).map(Ch999UserVo::getMobile).orElse(null));
            }
            data.add(row);
        }
        ExcelUtils.export(response, data, header, null, "渠道列表", 1);
    }

}
