package com.jiuji.oa.afterservice.webaddreservation.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.mapstruct.CommonStructMapper;
import com.jiuji.oa.afterservice.webaddreservation.entity.InquiryInformation;
import com.jiuji.oa.afterservice.webaddreservation.entity.InquiryInformationFaultAccessory;
import com.jiuji.oa.afterservice.webaddreservation.mapper.InquiryInformationMapper;
import com.jiuji.oa.afterservice.webaddreservation.service.InquiryInformationFaultAccessoryService;
import com.jiuji.oa.afterservice.webaddreservation.service.InquiryInformationService;
import com.jiuji.oa.afterservice.webaddreservation.vo.AddInquiryInformationVO;
import com.jiuji.oa.afterservice.webaddreservation.vo.InquiryInformationFaultAccessoryVO;
import com.jiuji.oa.afterservice.webaddreservation.vo.InquiryInformationVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@DS(DataSourceConstants.OA_NC)
@Slf4j
public class InquiryInformationServiceImpl extends ServiceImpl<InquiryInformationMapper, InquiryInformation> implements InquiryInformationService {


    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    @Resource
    private InquiryInformationFaultAccessoryService informationFaultAccessoryService;

    @Resource
    private CommonStructMapper commonStructMapper;


    @Override
    @Transactional
    public R<String> saveInquiryInformation(AddInquiryInformationVO addInquiryInformationVO) {
        InquiryInformationVO inquiryInformationVO = addInquiryInformationVO.getInquiryInformationVO();
        log.warn("前端传入参数：{}", JSONUtil.toJsonStr(addInquiryInformationVO));
        List<InquiryInformationFaultAccessoryVO> accessoryVOList = addInquiryInformationVO.getFaultAccessoryVOList();
        if(CollectionUtils.isEmpty(accessoryVOList)){
            throw new CustomizeException("维修配件信息不能为空");
        }
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录信息不能为空"));
        InquiryInformation inquiryInformation = commonStructMapper.toInquiryInformation(inquiryInformationVO);
        inquiryInformation.setSubmitUser(userBO.getUserName()).setSubmitTime(LocalDateTime.now());
        //询价主表数据保存
        boolean informationFlag = this.save(inquiryInformation);
        if (informationFlag) {
            Long informationId = inquiryInformation.getId();
            List<InquiryInformationFaultAccessoryVO> faultAccessoryVOList = addInquiryInformationVO.getFaultAccessoryVOList();
            if (CollectionUtils.isNotEmpty(faultAccessoryVOList)) {
                List<InquiryInformationFaultAccessory> collect = faultAccessoryVOList.stream().map(obj -> {
                    InquiryInformationFaultAccessory inquiryInformationFaultAccessory = commonStructMapper.toInquiryInformationFaultAccessory(obj);
                    inquiryInformationFaultAccessory.setInformationFaultId(informationId);
                    return inquiryInformationFaultAccessory;
                }).collect(Collectors.toList());
                //询价故障配件表保存
                boolean flag = informationFaultAccessoryService.saveBatch(collect);
                if (!flag) {
                    throw new CustomizeException("配件表保存失败");
                }

            }
        }else{
            throw new CustomizeException("询价主表保存失败");
        }
        return R.success("保存成功");
    }
}
