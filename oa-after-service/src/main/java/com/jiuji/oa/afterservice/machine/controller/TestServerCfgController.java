package com.jiuji.oa.afterservice.machine.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jiuji.oa.afterservice.common.aspect.ParamPreProcess;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.machine.bo.TestServerCfgBO;
import com.jiuji.oa.afterservice.machine.po.MachineTestServerCfgPO;
import com.jiuji.oa.afterservice.machine.service.TestServerCfgService;
import com.jiuji.oa.afterservice.machine.vo.ServerEditLeftVO;
import com.jiuji.oa.oacore.common.req.PageReq;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 服务配置控制器
 * <AUTHOR>
 * @since 2021/8/11 13:57
 */
@RestController
@RequestMapping("/api/testServer/cfg")
@Api(tags = "服务配置")
@Slf4j
public class TestServerCfgController {

    @Autowired
    private TestServerCfgService testServerCfgService;
    @Autowired
    private AbstractCurrentRequestComponent currentRequestComponent;

    @PostMapping("/list")
    @ApiOperation(value = "服务配置列表")
    public R<IPage<MachineTestServerCfgPO>> list(@Valid @RequestBody PageReq pageReq){
        return testServerCfgService.listServer(pageReq);
    }

    @PostMapping("/onOrOff")
    @ApiOperation(value = "启用禁用")
    public R<Boolean> onOrOff(@ApiParam("服务id") @RequestParam("id") Integer id, @ApiParam("当前状态") @RequestParam("isEnable") Boolean isEnable){
        return testServerCfgService.onOrOff(id,isEnable);
    }

    @PostMapping("/getLeftDataForEdit")
    @ApiOperation(value = "获取左侧tab菜单数据")
    public R<List<ServerEditLeftVO>> getLeftDataForEdit(@RequestParam(value = "id",required = false) Integer id){
        return testServerCfgService.getLeftDataForEdit(id);
    }

    @PostMapping("/getDataForEdit")
    @ApiOperation(value = "获取保存基础数据")
    public R<TestServerCfgBO> getDataForEdit(@RequestParam(value = "id",required = false) Integer id){
        return testServerCfgService.getDataForEdit(id);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存服务")
    @ParamPreProcess
    public R<TestServerCfgBO> save(@Valid @RequestBody TestServerCfgBO testServerCfgBO){
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        return testServerCfgService.saveServer(testServerCfgBO,oaUser.getUserName());
    }
}
