package com.jiuji.oa.afterservice.bigpro.controller;

import com.jiuji.oa.afterservice.bigpro.service.ShouhouServiceReportService;
import com.jiuji.oa.afterservice.bigpro.vo.req.PrintServiceReportReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouServiceReportReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.PrintShouhouServiceReportRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouServiceReportRes;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 10:07
 * @Description
 */
@Api(tags = "售后质检报告功能")
@RestController
@RequestMapping("/api/bigpro/shouhou/report")
public class ShouhouServiceReportController {

    @Resource
    private ShouhouServiceReportService shouhouServiceReportService;
    /**
     * 新增接口
     */
    @PostMapping("/saveReport")
    @ApiOperation("新增售后质检报告")
    public R<Boolean> saveReport(@RequestBody ShouhouServiceReportReq req){
        return shouhouServiceReportService.saveReport(req);
    }

    /**
     * 查询接口
     */
    @GetMapping("/getList")
    @ApiOperation("查询售后质检报告")
    public R<ShouhouServiceReportRes> getShouhouServiceReportList(@RequestParam(value = "imei" ,required = false) String imei,
                                                                        @RequestParam(value = "shouhouId" ,required = false) Integer shouhouId,
                                                                        @RequestParam(value = "subId" ,required = false) Integer subId){
        return shouhouServiceReportService.getShouhouServiceReportList(imei,shouhouId,subId);
    }

    /**
     * 根据id查询当前售后质检报告
     * @param id 质检编号
     * @return
     */
    @GetMapping("/getServiceReportById")
    @ApiOperation("获取售后质检报告详情")
    public R<PrintShouhouServiceReportRes> getShouhouServiceReportById(@RequestParam(value = "id" ,required = false) Integer id){
        return shouhouServiceReportService.getShouhouServiceReportById(id);
    }


    /**
     * 打印售后质检报告
     * @param req 小文件服务器地址
     * @return
     */
    @PostMapping("/print")
    @ApiOperation(value = "打印售后质检报告", httpMethod = "POST")
    public R<Boolean> printShouhouServiceReport(@RequestBody PrintServiceReportReq req){
        return shouhouServiceReportService.printShouhouServiceReport(req);
    }

}
