package com.jiuji.oa.afterservice.bigpro.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.YuyueLockProductInfoBo;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyuelockppids;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */
public interface ShouhouYuyuelockppidsService extends IService<ShouhouYuyuelockppids> {
    /**
     * 是否锁定维修配件
     * @return
     */
    Boolean isLockWxPeijian(Integer yyid,Integer ppid);

    /**
     * 保存售后预约锁定的ppid
     * @param yyid
     * @param ppid
     * @param areaId
     * @return
     */
    Boolean saveYuyueLockppids(Integer yyid,Integer ppid,Integer areaId);

    /**
     * 删除预约锁定商品ppid
     * @param yyid
     * @param ppid
     * @return
     */
    Boolean delYuyueLockppids(Integer yyid,Integer ppid);

    /**
     * 获取预约锁定的配件商品信息
     * @param yyId
     * @return
     */
    List<YuyueLockProductInfoBo>  getYuyueLockProductList(Integer yyId,Integer areaId);

}
