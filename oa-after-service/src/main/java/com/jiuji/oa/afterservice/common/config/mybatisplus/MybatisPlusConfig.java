package com.jiuji.oa.afterservice.common.config.mybatisplus;

import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.core.parser.ISqlParser;
import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Lucare.Feng on 2017/2/24.
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * 需要进行租户过滤的表
     */
//    public static List<String> tenantTbs = new ArrayList<>();

    @Bean
    @Primary
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        List<ISqlParser> sqlParserList = new ArrayList<>();
        ISqlParser nolockSqlParser = new JiujiSqlServerWithNolockSqlParser();
        sqlParserList.add(nolockSqlParser);
        paginationInterceptor.setSqlParserList(sqlParserList);
        return paginationInterceptor;
    }


    /**
     * 注入主键生成器
     */
//    @Bean
//    public IKeyGenerator keyGenerator() {
//        return new DB2KeyGenerator();
//    }

    /**
     * 注入sql注入器
     */
    @Bean
    public ISqlInjector sqlInjector() {
        return new LogicSqlInjector();
    }

}
