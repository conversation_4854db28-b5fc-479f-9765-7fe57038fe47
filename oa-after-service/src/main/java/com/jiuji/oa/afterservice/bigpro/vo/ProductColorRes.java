package com.jiuji.oa.afterservice.bigpro.vo;

import com.jiuji.oa.afterservice.bigpro.po.ProductColorInfo;
import com.jiuji.oa.afterservice.bigpro.po.StandGroup;
import com.jiuji.oa.afterservice.bigpro.po.StandGroupPpid;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ProductColorRes {

    private List<StandGroup> standGroupList;

    private List<StandGroupPpid> standGroupPpidList;

}
