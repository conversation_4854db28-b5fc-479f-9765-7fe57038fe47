package com.jiuji.oa.afterservice.common;

import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import lombok.AllArgsConstructor;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;


/**
 * description: <  >
 * translation: <  >
 * date : 2020-10-14 10:22
 *
 * <AUTHOR> leee41
 **/
@AllArgsConstructor
public class OaUserResolver implements HandlerMethodArgumentResolver {
    private final AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        Class<?> parameterType = parameter.getParameterType();
        return OaUserBO.class.isAssignableFrom(parameterType);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        return abstractCurrentRequestComponent.getCurrentStaffId();
    }
}
