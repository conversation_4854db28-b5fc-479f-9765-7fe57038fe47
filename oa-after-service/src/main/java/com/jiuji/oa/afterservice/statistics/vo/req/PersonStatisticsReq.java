package com.jiuji.oa.afterservice.statistics.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuji.oa.afterservice.bigpro.statistics.service.DaQu;
import com.jiuji.oa.afterservice.statistics.enums.PersonStatisticsTypeEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * description: <个人业绩统计查询>
 * <p>
 * date : 2020-10-10 13:58
 *
 * <AUTHOR> leee41
 **/
@Data
@ApiModel(value = "个人业绩统计查询REQ")
public class PersonStatisticsReq implements Serializable, DaQu {

    private static final long serialVersionUID = 1321758490748627326L;
    @ApiModelProperty("表头分类")
    private Set<PersonStatisticsTypeEnum> types;
    @ApiModelProperty("表头分类代码")
    private Set<Integer> typeCodes;
    @ApiModelProperty("开始时间")
    private LocalDateTime start;
    @ApiModelProperty("结束时间")
    private LocalDateTime end;
    @ApiModelProperty("地区code")
    private List<String> areaCodes = new ArrayList<>();
    @ApiModelProperty("地区分类(自营,加盟,小店)")
    @Min(value = 0)
    private Integer areaKind;

    @JsonIgnore
    private List<Long> areaIds = new ArrayList<>();

    @ApiModelProperty(hidden = true)
    private Integer authorizeId;

    @ApiModelProperty(hidden = true)
    private Boolean authPart;

    @ApiModelProperty(hidden = true)
    private Integer yaPingAreaId;
    /**
     * 统计的版本号 2 v2版本
     */
    private Integer version;
    /**
     * 通过工号查询
     * 目前支持的查询:  修不好量 总修好量
     */
    private String weixiuren;


    @Getter
    @AllArgsConstructor
    public enum PersonStatisticsVersionEnum implements CodeMessageEnumInterface {
        VERSION_1(1,"统计第1版")
        ,VERSION_2(2,"统计第2版")
        ;

        /**
         * 编码
         */
        private Integer code;
        /**
         * 编码对应信息
         */
        private String message;
    }
}
