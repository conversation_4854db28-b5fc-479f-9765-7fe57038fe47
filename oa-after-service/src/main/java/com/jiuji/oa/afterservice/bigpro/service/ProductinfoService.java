package com.jiuji.oa.afterservice.bigpro.service;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.PriceConfigPpriceidBo;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.ProductAreaPrices;
import com.jiuji.oa.afterservice.bigpro.po.PictureInfo;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.SelectKeyType;
import com.jiuji.oa.afterservice.bigpro.vo.res.ProductSimpleKcRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ProductSimpleRes;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 1 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface ProductinfoService extends IService<Productinfo> {

    Map<Integer, PictureInfo> selectPictureByEntityMap(List<Productinfo> productInfoEntityList);

    /**
     *    @see RepairAccessoriesKeyEnum
     *  判断key是什么类型
     * @param key
     * @return
     */
    SelectKeyType getKeyType(String key);
    /**
     * 通过ppid获取商品信息
     * @param ppids
     * @return
     */
  Map<Integer,Productinfo> getProductMapByPpids(List<Integer> ppids);

    /**
     * 通过ppid获取商品信息
     * @param ppid
     * @return
     */
    @Cached(name = "afterservice:ProductinfoService:getProductinfoByPpid",expire = 15,timeUnit = TimeUnit.MINUTES,cacheType = CacheType.BOTH)
    Productinfo getProductinfoByPpid(Integer ppid);

    /**
     * 机型搜索
     * @param world
     * @param pid
     * @param limit
     * @return
     */
    List<ProductSimpleRes> searchProduct(String world, Integer pid, Integer limit,Boolean isSmallProduct);

  /**
   * 维修商品搜索 库存
   * @param word
   * @param ppid
   * @return
   */
   List<ProductSimpleKcRes> searchProductKC(String word,Integer ppid);

    /**
     * 商品搜索 库存-- 赠品
     * @param word
     * @param ppid
     * @return
     */
    List<ProductSimpleKcRes> searchGiveProductKC(String word,Integer ppid);

    /**
     * 读取价格配置
     * @return
     */
    @Cached(name = "com.jiuji.oa.afterservice.bigpro.service.ProductinfoService.getConfigs",expire = 10,timeUnit = TimeUnit.MINUTES)
   List<PriceConfigPpriceidBo> getConfigs();

    /**
     * 获取区域价格
     * @param cityId
     * @param ppid
     * @param memberPrice
     * @param xtenant
     * @return
     */

   BigDecimal getProductPrices(Integer cityId,Integer ppid,BigDecimal memberPrice,Integer xtenant);

    @Cached(name = "com.jiuji.oa.afterservice.bigpro.service.ProductinfoService.getConfigProductPriceMap",expire = 10,timeUnit = TimeUnit.MINUTES,cacheType = CacheType.BOTH)
    Map<Integer,List<PriceConfigPpriceidBo>> getConfigProductPriceMap(Integer cityId);

    /**
     * 根据串号读取新机配置
     *
     * @param imei imei
     * @return R
     * @see .net:oa999DAL.ShouHou.GetConfigByImei
     */
    String getConfigByImei(String imei);

    /**
     * 根据ppid查询商品分类cid
     * @param ppidList
     * @return
     */
    List<Integer> getCidsByPpid(List<Integer> ppidList);

    /**
     * 根据barcode查询商品
     */
    Productinfo getProductByBarCode(String barCode);

    /**
     * 获取区域价格
     * @param cityCode
     * @param ppid
     * @param xTenant
     * @return
     */
    List<ProductAreaPrices> getProductAreaPrices(Integer cityCode,Integer ppid,Integer xTenant);

    /**
     * 是否为特价商品
     * @param ppriceid
     * @return
     */
    boolean isBargainProduct(Integer ppriceid);

    Long getProductIdByProductName(String name);
}
