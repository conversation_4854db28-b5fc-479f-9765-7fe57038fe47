<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.api.dao.OaApiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.shouhou.vo.ShouHouDetailVo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="peizhi" property="peizhi" />
        <result column="problem" property="problem" />
        <result column="comment" property="comment" />
        <result column="username" property="username" />
        <result column="mobile" property="mobile" />
        <result column="tel" property="tel" />
        <result column="stats" property="stats" />
        <result column="baoxiu" property="baoxiu" />
        <result column="inuser" property="inuser" />
        <result column="imei" property="imei" />
        <result column="xianshi" property="xianshi" />
        <result column="contentcsdate" property="contentcsdate" />
        <result column="tradedate" property="tradedate" />
        <result column="modidate" property="modidate" />
        <result column="feiyong" property="feiyong" />
        <result column="costprice" property="costprice" />
        <result column="weixiuren" property="weixiuren" />
        <result column="dyjid" property="dyjid" />
        <result column="offtime" property="offtime" />
        <result column="area" property="area" />
        <result column="shouyinglock" property="shouyinglock" />
        <result column="shouyingdate" property="shouyingdate" />
        <result column="shouyinguser" property="shouyinguser" />
        <result column="userid" property="userid" />
        <result column="kinds" property="kinds" />
        <result column="isticheng" property="isticheng" />
        <result column="waiguan" property="waiguan" />
        <result column="result_dtime" property="result_dtime" />
        <result column="issoft" property="issoft" />
        <result column="modidtime" property="modidtime" />
        <result column="product_id" property="product_id" />
        <result column="product_color" property="product_color" />
        <result column="buyarea" property="buyarea" />
        <result column="pandian" property="pandian" />
        <result column="pandiandate" property="pandiandate" />
        <result column="toarea" property="toarea" />
        <result column="istui" property="istui" />
        <result column="pandianinuser" property="pandianinuser" />
        <result column="ppriceid" property="ppriceid" />
        <result column="mkc_id" property="mkc_id" />
        <result column="isquick" property="isquick" />
        <result column="wcount" property="wcount" />
        <result column="weixiuzuid" property="weixiuzuid" />
        <result column="weixiuzuid_jl" property="weixiuzuid_jl" />
        <result column="isweixiu" property="isweixiu" />
        <result column="weixiudtime" property="weixiudtime" />
        <result column="weixiu_startdtime" property="weixiu_startdtime" />
        <result column="orderid" property="orderid" />
        <result column="isquji" property="isquji" />
        <result column="isfan" property="isfan" />
        <result column="pingjia" property="pingjia" />
        <result column="pingjia1" property="pingjia1" />
        <result column="sub_id" property="sub_id" />
        <result column="webtype1" property="webtype1" />
        <result column="webtype2" property="webtype2" />
        <result column="webstats" property="webstats" />
        <result column="basket_id" property="basket_id" />
        <result column="ishuishou" property="ishuishou" />
        <result column="yuyueid" property="yuyueid" />
        <result column="sxmobile" property="sxmobile" />
        <result column="wxkind" property="wxkind" />
        <result column="areaid" property="areaid" />
        <result column="toareaid" property="toareaid" />
        <result column="buyareaid" property="buyareaid" />
        <result column="feiyong" property="price" />
        <result column="yifum" property="yifuM" />

        <result column="tuihuan_kind" property="tuihuan_kind" />
        <result column="nowarea" property="nowarea" />
        <result column="userclass" property="userclass" />
        <result column="area_zh" property="area_zh" />
        <result column="toarea_zh" property="toarea_zh" />
        <result column="dyj_name" property="dyj_name" />
        <result column="dyj_imei" property="dyj_imei" />
        <result column="dyj_price" property="dyj_price" />
        <result column="shouyinglock" property="shouyinglock" />
        <result column="isPj" property="isPj" />
    </resultMap>

    <select id="getShouhouInfoByIdAndUserIdAndImei" resultMap="BaseResultMap">
        select top 1 h.* ,isnull(h.webstats,0) as webstats_,isnull(h.webtype1,0) as webtype1_,isnull(h.webtype2,0) as webtype2_
                        ,isnull(h.issoft,0) as issoft_,t.tuihuan_kind,isnull(h.pandian,0) as pandian_,isnull(h.toareaid,h.areaid) as nowarea
                        ,u.userclass,zh.areaid as area_zh,zh.toareaid as toarea_zh,d.name as dyj_name,d.imei as dyj_imei,d.price as dyj_price
                        ,ISNULL(h.shouyinglock,0) shouyinglock,q.id wsqdid,CASE WHEN e.Id IS NOT NULL THEN 1 ELSE 0 END isPj
                    FROM shouhou h WITH(NOLOCK)
                    LEFT join
                    (
                        SELECT tuihuan_kind,shouhou_id
                        FROM shouhou_tuihuan WITH(NOLOCK)
                        WHERE isnull(isdel,0)=0
                    ) t on h.id=t.shouhou_id
                    LEFT join bbsxp_users u WITH(NOLOCK) on ISNULL(h.sxuserid,h.userid) = u.id
                    LEFT join
                    (
                        SELECT areaid,toareaid,shouhou_id
                        FROM shouhou_toarea WITH(NOLOCK)
                        WHERE isnull([check],0)=0
                    ) zh on zh.shouhou_id = h.id
                    LEFT join daiyongji d WITH(NOLOCK) on h.dyjid=d.id
                    LEFT JOIN dbo.shouhou_qudao q WITH(NOLOCK) ON q.shouhouid = h.id
                    LEFT JOIN ${officeName}.dbo.Evaluate e WITH(NOLOCK) ON e.SubId = h.id AND e.EvaluateType = 3
                    WHERE 1=1
                    <!--h.xianshi = 1-->
                    <choose>
                        <when test="imei == null or imei == '' ">
                            <choose>
                                <when test="id != null and userId != null and userId > 0">
                                    and h.id = #{id} AND (h.userid = #{userId} or h.sxuserid = #{userId})
                                </when>
                                <when test="id != null">
                                    and h.id = #{id}
                                </when>
                            </choose>
                        </when>
                        <otherwise>
                            <choose>
                                <when test="isMobile != null and isMobile == true">
                                    and isnull(h.isquji,0)=0 and (h.mobile=#{imei} or h.tel=#{imei})
                                </when>
                                <otherwise>
                                    and isnull(h.isquji,0)=0 and h.imei=#{imei}
                                </otherwise>
                            </choose>
                        </otherwise>
                    </choose>

        order by h.id desc
    </select>

    <select id="getWuliuInfoByShouhouId" resultType="com.jiuji.oa.afterservice.express.WuliuInfo">
        select top 1 w.id,com,nu FROM wuliu w WITH(NOLOCK)
        WHERE wutype = 5 and (w.danhaobind = #{shouhouId}
            OR EXISTS(SELECT shouhou_id FROM dbo.shouhou_yuyue yy WITH(NOLOCK) WHERE yy.shouhou_id = #{shouhouId} AND yy.id = w.danhaobind))
        order by w.id desc
    </select>

    <select id="getShouhouDaiyongjiInfoByShouhouId" resultType="com.jiuji.oa.afterservice.shouhou.vo.ShouhouDaiyongjiVo">
        SELECT d.name,d.imei,y.signimg,y.signtime ,CASE WHEN y.paystate=2 AND ISNULL(y.total,0)>0 THEN y.total ELSE 0 END yajin
        FROM dbo.shouhou s WITH(NOLOCK)
        INNER JOIN dbo.daiyongji d WITH(NOLOCK) ON s.dyjid = d.id
        INNER JOIN dbo.daiyongji_yajin y WITH(NOLOCK) ON y.wxid=s.id
        WHERE s.id =#{shouhouId} and y.cancel = 0
    </select>

    <select id="getShouhouPriceByShouhouId" resultType="com.jiuji.oa.afterservice.api.bo.ShouhouPriceBo">
        SELECT ISNULL(price,0) as price,1 kinds FROM shouhou_huishou h WITH(NOLOCK)
        WHERE h.shouhou_id = #{shouhouId} AND ISNULL(h.isdel,0) = 0 AND ISNULL(h.ishuan,0) = 0 AND ISNULL(h.isfan,0) = 0
        UNION ALL
        SELECT ISNULL(youhuifeiyong,0) as price,2 FROM dbo.shouhou s WITH(NOLOCK) WHERE id = #{shouhouId}
        UNION ALL
        SELECT ISNULL(price1,0) as price,3 FROM dbo.wxkcoutput k WITH(NOLOCK)
        WHERE k.stats != 3 AND k.wxid = #{shouhouId} AND k.price1 > 0 AND k.price = 0
        AND EXISTS
        (
        SELECT 1 FROM dbo.shouhou s WITH(NOLOCK) WHERE s.id = k.wxid AND ISNULL(s.ServiceType,0) > 0
        )
        UNION ALL
        SELECT ISNULL(k.price1 - k.price,0) as price,4 FROM dbo.wxkcoutput k WITH(NOLOCK)
        WHERE k.stats != 3 AND k.wxid = #{shouhouId} AND k.price1 != k.price
    </select>

    <select id="getYouhuiMaList" resultType="com.jiuji.oa.afterservice.api.bo.YouhuiMaInfo">
        SELECT n.CardID as cardId,n.ch999_id as ch999Id,c.prices FROM dbo.cardLogs c WITH(NOLOCK)
        INNER JOIN dbo.NumberCard n WITH(NOLOCK) ON c.cardid = n.ID
        WHERE c.sub_id = #{yuyueId} AND useType = 1 AND c.liangpin = 4
        ORDER BY c.id
    </select>

    <select id="checkYuyueSmqjCount" resultType="java.lang.Integer">
        SELECT count(1) FROM shouhou_yuyue AS yuyue WITH(NOLOCK)
        JOIN shouhou_rom_upgrade WITH(NOLOCK) AS rom ON rom.YuyueId = yuyue.id
        WHERE datediff(day, getdate(), stime) = 0 and rom.type = -4 and isnull(yuyue.isdel,0)=0
    </select>

    <select id="checkStaffInfo" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.ch999_user u WITH(NOLOCK) WHERE u.iszaizhi = 1 AND u.ch999_id = #{ch999Id}
    </select>

    <select id="getAfterSale" resultType="com.jiuji.oa.afterservice.shouhou.vo.AfterSaleSearchVO">
       SELECT  id,
        name,
        product_color,
        DATEDIFF( MINUTE, modidate, modidtime ) AS weixiuminutes,
        problem,
        areaid as area,
        isquick,
        baoxiu,
        stats,
        ServiceType,
        modidate,
        isnull( isquji, 0 ) AS isquji_,
        modidtime
        FROM shouhou WITH ( NOLOCK )
        WHERE
        userid <![CDATA[ <> ]]> 76783
        AND modidtime IS NOT NULL
        AND stats = 1
        AND areaid not in(SELECT DISTINCT (a.HQAreaId) FROM(
        select cast(f.split_value as int) AS HQAreaId  from dbo.authorize a with(nolock) cross apply dbo.F_SPLIT(a.HQAreaId,',') f where isnumeric(f.split_value)=1
        ) a where a.HQAreaId is not null)
        ORDER BY
        modidtime DESC
        OFFSET 0 ROWS FETCH NEXT #{rows} ROWS ONLY
    </select>

    <select id="listAllUserBasicInfo" resultType="com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO">
        select
            a.ch999_id as ch999Id,
            a.ch999_name as ch999Name,
            a.isshixi,
            a.mobile as mobile,
            a.area as area,
            a.areaid as areaId,
            a.departCode as departCode,
            a.area1 as area1,
            a.area1id as area1id,
            a.zhiwu as zhiwu,
            a.zhiwuid as zhiwuId,
            a.mainRole as mainRole,
            a.roles as roles,
            b.url as avatar,
            a.zhiji,
            zw.leve level,
            a.islogin
        from ch999_user a with(nolock)
            left join zhiwu zw with(nolock) on zw.id = a.zhiwuid
            left join  ${officeName}.dbo.appHeadimg b with(nolock) on a.ch999_id = b.ch999_id
            where a.iszaizhi=1 and a.ch999_id>1
    </select>

    <select id="queryProductStockByPpid" resultType="com.jiuji.oa.afterservice.shouhou.vo.ProductKcInfo">
        <if test="type == 1">
            select k.ppriceid as ppid,count(1) kcCount from dbo.product_mkc k with(nolock) where
            kc_check in (2,3)
            and basket_id is null and isnull(mouldFlag,0)=0
            and k.areaid != 203
            and k.ppriceid in
            <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
            and not exists(select 1 from dbo.xc_mkc x with(nolock) where k.id = x.mkc_id and isnull(x.isLock,0)=1 ) group by k.ppriceid
        </if>
        <if test="type == 2">
            select ppriceid as ppid,sum(leftCount) as kcCount from dbo.product_kc with(nolock)
            where ppriceid in
            <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
            group by ppriceid
        </if>

        <if test="type == 3">
            SELECT t.ppid, SUM ( t.leftCount ) kcCount
            FROM (
            SELECT ppriceid ppid, leftCount
            FROM dbo.product_kc kc WITH(NOLOCK)
            LEFT JOIN areainfo a with(NOLOCK) on kc.areaid = a.id
            where kc.areaid not IN (13, 22, 14, 203) and a.kind1  =1 ) t
            WHERE t.ppid in
            <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
            group by t.ppid
        </if>
    </select>

    <select id="queryProductStockByPpidV3" resultType="com.jiuji.oa.afterservice.shouhou.vo.ProductKcInfo">
        <if test="type == 1">
            select k.ppriceid as ppid,count(1) kcCount from dbo.product_mkc k with(nolock) where
            kc_check in (2,3)
            and basket_id is null and isnull(mouldFlag,0)=0
            and k.areaid != 203
            and k.ppriceid in
            <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
            and not exists(select 1 from dbo.xc_mkc x with(nolock) where k.id = x.mkc_id and isnull(x.isLock,0)=1 ) group by k.ppriceid
        </if>
        <if test="type == 2">
            select ppriceid as ppid,sum(leftCount) as kcCount from dbo.product_kc with(nolock)
            where ppriceid in
            <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
            group by ppriceid
        </if>

        <if test="type == 3">
            SELECT t.ppid, SUM ( t.leftCount ) kcCount
            FROM (
            SELECT ppriceid ppid, leftCount
            FROM dbo.product_kc kc WITH(NOLOCK)
            LEFT JOIN areainfo a with(NOLOCK) on kc.areaid = a.id
            where a.attribute=1101 and a.kind1  =1 and leftCount>0) t
            WHERE t.ppid in
            <foreach collection="ppids" index="index" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
            group by t.ppid
        </if>
    </select>

    <select id="getOrderClassByUserId" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT s.sub_id) FROM basket_extend be with(nolock)
        inner join basket b with(nolock) on b.basket_id = be.basket_id
        INNER JOIN dbo.productinfo pf with(nolock) ON pf.ppriceid = b.ppriceid
        inner join sub s with(nolock) on s.sub_id = b.sub_id
        inner join BBSXP_Users u with(nolock) on u.ID = s.userid
        where be.price_kind = 2 and u.team_id = #{teamId} and s.sub_check = 3 AND pf.cid in
        <foreach collection="cidList" index="index" item="cids" separator="," open="(" close=")">
            #{cids}
        </foreach>
        AND ISNULL(b.isdel,0) = 0
    </select>

    <select id="listTeamIds" resultType="java.lang.Integer">
        select distinct team_id from dbo.BBSXP_Users with(nolock) where ID = #{userId}
        and team_id>0 and team_id is not null
    </select>
    <select id="getRoleNameByMobile" resultType="com.jiuji.oa.afterservice.api.bo.Ch999SimpleUserRoleBo">
        SELECT top 1 r.RoleName FROM ch999_user u with(nolock)
                left join RoleInfo r with(nolock) on u.mainRole=r.id
        where u.mainRole is not null and iszaizhi=1
          and u.mobile = #{mobile}
        order by u.ch999_id desc
    </select>


</mapper>
