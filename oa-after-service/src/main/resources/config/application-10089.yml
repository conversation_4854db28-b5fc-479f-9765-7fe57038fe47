consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.litian.co/
  upload:
    url: http://**************:9333
instance-zone: 10089
jiuji:
  sys:
    moa: https://moa.litian.co
    pc: https://oa.litian.co
    xtenant: 10089
  xtenant: 89000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10089:ytOKipU444fi@***********:27017,***********:27017,***********:27017/ch999oa__10089
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10089
    password: JV#APDFmbEPf
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10089
  oa_nc:
    dbname: oa_nc__10089
    password: hUNwd6dFVPrU
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10089
office:
  sys:
    xtenant: 10089
rabbitmq:
  master:
    password: GFYCN
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10089
    vhost: oaAsync__10089
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: kiwmc
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10089
    vhost: oa__10089
  oaAsync:
    password: GFYCN
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10089
    vhost: oaAsync__10089
  printer:
    password: kKbHL
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10089
    vhost: printer__10089
redis:
  oa:
    host: ***********
    password: google99
    port: 6390
    url: google99@***********:6390
sms:
  send:
    email:
      url: http://sms.litian.co/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.litian.co/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10089
sqlserver:
  after_write:
    dbname: ch999oanew__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "z2Rod^eaRNh^"
    port: 1433
    username: ch999oanew__10089
  ch999oanew:
    dbname: ch999oanew__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "z2Rod^eaRNh^"
    port: 1433
    username: ch999oanew__10089
  ch999oanewReport:
    dbname: ch999oanew__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "z2Rod^eaRNh^"
    port: 1433
    username: ch999oanew__10089
  ch999oanewHis:
    dbname: ch999oanew__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "z2Rod^eaRNh^"
    port: 1433
    username: ch999oanew__10089
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: fC4Mwlb5McQv
    port: 1433
    username: office__10089
  oanewWrite:
    dbname: ch999oanew__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "z2Rod^eaRNh^"
    port: 1433
    username: ch999oanew__10089
  office:
    dbname: office__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: fC4Mwlb5McQv
    port: 1433
    username: office__10089
  officeWrite:
    dbname: office__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: fC4Mwlb5McQv
    port: 1433
    username: office__10089
  smallpro_write:
    dbname: ch999oanew__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "z2Rod^eaRNh^"
    port: 1433
    username: ch999oanew__10089
  web999:
    dbname: web999__10089
    host: sqlserver.serv.hb.saas.ch999.cn
    password: A1LTfDFFqosw
    port: 1433
    username: web999__10089
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.litian.co/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.litian.co/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'