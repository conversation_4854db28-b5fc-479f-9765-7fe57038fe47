
consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.jsjinzhen.com/
  upload:
    url: http://**************:9333
instance-zone: 10060
jiuji:
  sys:
    moa: https://moa.jsjinzhen.com
    pc: https://oa.jsjinzhen.com
    xtenant: 10060
  xtenant: 60000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10060:KZN1ZHvE@**************:27017,**************:27017,**************:27017/ch999oa__10060
  url1: ***************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: manage_training__10060
    password: manage_trainingdAe
    url: **************:3306
    username: manage_training__10060
  oa_core:
    dbname: oa_core__10060
    password: oa_corenxm
    url: **************:3306
    username: oa_core__10060
  oa_nc:
    dbname: oa_nc__10060
    password: oa_ncoMD
    url: **************:3306
    username: oa_nc__10060
office:
  sys:
    xtenant: 10060
rabbitmq:
  master:
    password: rMkWl
    port: 5672
    url: **************
    username: oaAsync__10060
    vhost: oaAsync__10060
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: MeLxU
    port: 5672
    url: **************
    username: oa__10060
    vhost: oa__10060
  oaAsync:
    password: rMkWl
    port: 5672
    url: **************
    username: oaAsync__10060
    vhost: oaAsync__10060
  printer:
    password: caSeu
    port: 5672
    url: **************
    username: printer__10060
    vhost: printer__10060
redis:
  oa:
    host: **************
    password: google99
    port: 6396
    url: google99@**************:6396
sms:
  send:
    email:
      url: http://sms.jsjinzhen.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.jsjinzhen.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10060
sqlserver:
  after_write:
    dbname: ch999oanew__10060
    host: **************
    password: ch999oanewBVOyJ
    port: 1433
    username: ch999oanew__10060
  ch999oanew:
    dbname: ch999oanew__10060
    host: **************
    password: ch999oanewBVOyJ
    port: 1433
    username: ch999oanew__10060
  ch999oanewReport:
    dbname: ch999oanew__10060
    host: **************
    password: ch999oanewBVOyJ
    port: 1433
    username: ch999oanew__10060
  ch999oanewHis:
    dbname: ch999oanew__10060
    host: **************
    password: ch999oanewBVOyJ
    port: 1433
    username: ch999oanew__10060
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10060
    host: **************
    password: officeGmuGA
    port: 1433
    username: office__10060
  oanewWrite:
    dbname: ch999oanew__10060
    host: **************
    password: ch999oanewBVOyJ
    port: 1433
    username: ch999oanew__10060
  office:
    dbname: office__10060
    host: **************
    password: officeGmuGA
    port: 1433
    username: office__10060
  officeWrite:
    dbname: office__10060
    host: **************
    password: officeGmuGA
    port: 1433
    username: office__10060
  smallpro_write:
    dbname: ch999oanew__10060
    host: **************
    password: ch999oanewBVOyJ
    port: 1433
    username: ch999oanew__10060
  web999:
    dbname: web999__10060
    host: **************
    password: web999hpHQK
    port: 1433
    username: web999__10060
url:
  delImgUrl: http://data3:5083
  oa-push-info: http://inwcf.jsjinzhen.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.jsjinzhen.com/
  source:
      path: i18n/url
  uploadImgUrl: http://data3:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'