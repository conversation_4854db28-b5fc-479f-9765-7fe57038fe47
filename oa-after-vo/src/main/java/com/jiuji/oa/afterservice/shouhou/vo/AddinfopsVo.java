package com.jiuji.oa.afterservice.shouhou.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AddinfopsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String Reciver;

    private String Address;

    private String mobile;

    private String Tel;

    private String Email;

    private Integer cityid;

    /**
     * 3,4预约地址；1，2售后地址，当预约单变成售后单后预约地址将变成售后地址，以售后地址为准
     * 1,3为上门取件地址
     * 2,4为修好后送货地址
     */
    private Integer type;

    private Integer BindId;

    private String psuser;

    private String Consignee;

}
