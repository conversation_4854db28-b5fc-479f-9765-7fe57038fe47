package com.jiuji.oa.afterservice.shouhou.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后查询所用实体类
 * 对应C#实体 oaData.shouhouSearch
 *
 * <AUTHOR> <EMAIL>
 * @date 2020/6/30 11:36
 * @see .net:oaData.shouhouSearch
 **/
@Data
@Accessors(chain = true)
public class AfterSaleSearchVO {
    private Integer id;
    private Boolean ismobile;
    private String name;
    private String product_color;
    private LocalDateTime modidate;
    private String baoxiu;
    private String stats;
    private Integer tuihuan_kind;
    private Boolean isquji_;
    private String problem;
    private Integer weixiuminutes;
    private String area;
    private LocalDateTime modidtime;
    private Integer yuyueid;
    private BigDecimal feiyong;
    private Integer ppriceid;
    private LocalDateTime offtime;
    private Byte serviceType;
    private Integer productId;
}
