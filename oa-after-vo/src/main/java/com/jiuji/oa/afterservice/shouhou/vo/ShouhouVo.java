package com.jiuji.oa.afterservice.shouhou.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description:
 * @author: Li quan
 * @date: 2020/7/8 14:25
 */
@Data
@ApiModel
public class ShouhouVo {
    /**
     * 自动编号
     */
    @ApiModelProperty(value = "编号")
    private Integer id;

    /**
     * 机型
     */
    @ApiModelProperty(value = "机型")
    private String name;

    /**
     * 配置
     */
    @ApiModelProperty(value = "配置")
    private String peizhi;

    /**
     * 故障
     */
    @ApiModelProperty(value = "故障")
    private String problem;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comment;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;

    /**
     * 状态
     */
    @ApiModelProperty(value = "处理结果")
    private String result;

    @ApiModelProperty(value = "维修状态：0处理中，1已修好，3修不好")
    private Integer stats;

    @ApiModelProperty(value = "保修状态：0不在保，1在保")
    private Integer baoxiu;

    @ApiModelProperty(value = "登记人")
    private String inuser;

    @ApiModelProperty(value = "串号")
    private String imei;

    @ApiModelProperty(value = "是否显示")
    private Boolean xianshi;

    @ApiModelProperty(value = "测试时间")
    private LocalDateTime contentcsdate;

    @ApiModelProperty(value = "购买时间")
    private LocalDateTime tradedate;

    @ApiModelProperty(value = "接件时间")
    private LocalDateTime modidate;

    @ApiModelProperty(value = "维修费用")
    private BigDecimal feiyong;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costprice;

    @ApiModelProperty(value = "维修人")
    private String weixiuren;

    @ApiModelProperty(value = "代用机ID")
    private Integer dyjid;

    @ApiModelProperty(value = "取机时间")
    private LocalDateTime offtime;

    @ApiModelProperty(value = "接件地区")
    private String area;


    @ApiModelProperty(value = "售后锁标识")
    private Integer shouyinglock;

    @ApiModelProperty(value = "收银时间")
    private LocalDateTime shouyingdate;

    @ApiModelProperty(value = "收银人")
    private String shouyinguser;

    @ApiModelProperty(value = "客户id")
    private Long userid;

    @ApiModelProperty(value = "地区分类：bd,dz")
    private String kinds;

    @ApiModelProperty(value = "是否提成")
    private Boolean isticheng;

    @ApiModelProperty(value = "外观描述")
    private String waiguan;

    @ApiModelProperty(value = "跟进时间")
    private LocalDateTime result_dtime;

    @ApiModelProperty(value = "是否软件接件")
    private Boolean issoft;

    @ApiModelProperty(value = "修好时间")
    private LocalDateTime modidtime;

    @ApiModelProperty(value = "商品id")
    private Long product_id;

    @ApiModelProperty(value = "商品规格")
    private String product_color;

    @ApiModelProperty(value = "购买门店")
    private String buyarea;

    @ApiModelProperty(value = "是否盘点")
    private Boolean pandian;

    @ApiModelProperty(value = "盘点时间")
    private LocalDateTime pandiandate;

    @ApiModelProperty(value = "转到地区id")
    private String toarea;

    @ApiModelProperty(value = "是否退换中")
    private Boolean istui;

    @ApiModelProperty(value = "盘点人")
    private String pandianinuser;

    @ApiModelProperty(value = "ppid")
    private Integer ppriceid;

    @ApiModelProperty(value = "库存id")
    @JsonProperty("mkc_id")
    private Integer mkcId;

    @ApiModelProperty(value = "是否快速")
    private Boolean isquick;

    @ApiModelProperty(value = "维修次数")
    private Integer wcount;

    @ApiModelProperty(value = "维修组ID")
    private Integer weixiuzuid;

    @ApiModelProperty(value = "维修组记录")
    private Integer weixiuzuid_jl;

    @ApiModelProperty(value = "是否维修")
    private Boolean isweixiu;

    @ApiModelProperty(value = "维修组完成时间")
    private LocalDateTime weixiudtime;

    @ApiModelProperty(value = "分配时间")
    private LocalDateTime weixiu_startdtime;

    @ApiModelProperty(value = "短单号")
    private String orderid;

    @ApiModelProperty(value = "是否取件")
    private Boolean isquji;

    @ApiModelProperty(value = "是否返修")
    private Boolean isfan;

    @ApiModelProperty(value = "评价类型")
    private Integer pingjia;

    @ApiModelProperty(value = "评价类型1")
    private Integer pingjia1;

    @ApiModelProperty(value = "对应订单id")
    private Integer sub_id;

    @ApiModelProperty(value = "对应basket_id")
    private Integer basket_id;

    @ApiModelProperty(value = "退换类别 1换机头 2换主板 3退款 4换其它型号")
    private Integer tuihuan_kind;

    @ApiModelProperty(value = "当前地区")
    private String nowarea;

    @ApiModelProperty(value = "用户等级")
    private Integer userclass;

    @ApiModelProperty(value = "最后未发出的转地区发出门店代码")
    private String area_zh;

    @ApiModelProperty(value = "最后未发出的转地区接收门店代码")
    private String toarea_zh;

    @ApiModelProperty(value = "代用机商品名称")
    private String dyj_name;

    @ApiModelProperty(value = "代用机串号")
    private String dyj_imei;

    @ApiModelProperty(value = "代用机价格")
    private BigDecimal dyj_price;

    @ApiModelProperty(value = "网站状态 1 未审核 2 已审核 3 已收到 4 已发出 5已完成")
    private Integer webstats;

    @ApiModelProperty(value = "1维修  2退货  3换货")
    private Integer webtype1;

    @ApiModelProperty(value = "1预约到店  2上门取机  3快递至三九")
    private Integer webtype2;

    @ApiModelProperty(value = "售后状态")
    private String stateName;

    @ApiModelProperty(value = "售后预约id")
    private Integer yuyueid;

    @ApiModelProperty(value = "维修倒计时")
    private String wxDjs;

    @ApiModelProperty(value = "是否通过测试")
    private Boolean isPassTest;

    @ApiModelProperty(value = "处理方式 1修 2换 3多 4送 5显示总成置换 6快")
    private Integer wxkind;

    @ApiModelProperty(value = "代用机")
    private ShouhouDaiyongjiVo daiyongji;

    @ApiModelProperty(value = "0：默认值，什么也不是，1：新机订单 2：二手良品订单")
    private Integer subType;

    @ApiModelProperty(value = "已优惠金额(排除九机服务抵扣，剩下的配件的优惠金额，以及优惠码使用金额以及配件回收抵扣价格)")
    private BigDecimal youhuiPrice;

    @ApiModelProperty(value = "九机服务抵扣 (维修单出过险并且维修配件价格为0 ，计算维修配件的优惠金额)")
    private BigDecimal servicePrice;

    @ApiModelProperty(value = "需付款 现在维修单计算出的维修费用")
    private BigDecimal price;

    @ApiModelProperty(value = "已付金额")
    private BigDecimal yifuM;

    @ApiModelProperty(value = "维修总价 九机服务抵扣+已优惠+需付款")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "是否外送")
    private Boolean isOutwardDelivery;

    @ApiModelProperty(value = "是否评价")
    private Boolean ispj;

    @JsonIgnore
    private String sxmobile;

    @JsonIgnore
    private Integer areaid;

    @JsonIgnore
    private Integer buyareaid;

    @JsonIgnore
    private Integer toareaid;

}
