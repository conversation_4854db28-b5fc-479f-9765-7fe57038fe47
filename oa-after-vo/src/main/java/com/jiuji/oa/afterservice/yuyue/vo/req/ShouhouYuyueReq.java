package com.jiuji.oa.afterservice.yuyue.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 售后预约请求参数
 * @author: <PERSON> quan
 * @date: 2020/6/30 14:04
 */
@Data
public class ShouhouYuyueReq {

    @ApiModelProperty("预约单基本信息")
    @NotNull(message = "预约单基本信息不能为空")
    @Valid
    private ShouhouYuyueReqVo yuyue;

    @ApiModelProperty("退换信息")
    private YuyueTuiDataReqVo tui;

    @ApiModelProperty("故障信息")
    private List<String> troubles;
}
