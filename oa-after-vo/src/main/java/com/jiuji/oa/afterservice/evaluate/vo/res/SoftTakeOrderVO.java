package com.jiuji.oa.afterservice.evaluate.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @since 2019/12/17
 */
@Data
public class SoftTakeOrderVO implements Serializable {
    private static final long serialVersionUID = -815674777654622964L;

    /**
     * id: 接件单号
     */
    private Integer id;

    /**
     * subId:订单id
     */
    private Integer subId;
    /**
     * isHuishou:是否是回收单
     */
    private Boolean isHuishou;
    /**
     * modiDate:接件时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modiDate;
    /**
     * imei:对应串号
     */
    private String imei;
}
