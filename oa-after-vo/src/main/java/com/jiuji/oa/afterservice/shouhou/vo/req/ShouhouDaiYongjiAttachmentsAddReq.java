package com.jiuji.oa.afterservice.shouhou.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 售后备用机附件上传请求接口
 */
@Data
@Accessors(chain = true)
public class ShouhouDaiYongjiAttachmentsAddReq {

    @ApiModelProperty(value = "售后ID")
    private Integer shouhouId;

    @ApiModelProperty(value = "会员id")
    private Integer userId;

    @ApiModelProperty(value = "代用机编号")
    private Integer dyjId;

    @ApiModelProperty(value = "代用机名称")
    private String productName;

    @ApiModelProperty(value = "附件信息")
    private List<FileItem> attachmentsList;

    @Data
    @Accessors(chain = true)
    public static class FileItem{
        @ApiModelProperty(value = "fid")
        private String fid;

        @ApiModelProperty(value = "文件名")
        private String fileName;

        @ApiModelProperty(value = "文件路径")
        private String filePath;
        private String suffix;
        private Double fileSize;
    }
}
