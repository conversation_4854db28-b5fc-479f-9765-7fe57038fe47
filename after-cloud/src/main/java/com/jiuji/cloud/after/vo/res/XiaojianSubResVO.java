package com.jiuji.cloud.after.vo.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class XiaojianSubResVO {
    private Integer subId;
    private Integer kind;
    private String kindName;
    private String problem;
    private String outward;
    private Integer stats;
    private String statsName;
    private Boolean isBaoxiu;
    private String baoxiuState;
    private BigDecimal feiyong;
    private BigDecimal costprice;
    private Integer yuyueId;
    private String mobile;
    private String userName;
    private String inDate;
    private String buyDate;
    private Integer buySubId;
    private Integer wcount;
    private List<XiaojianProductVO> products;
    private String inuser;
    private Integer inuserCh999Id;
    private String inuserHeaderImage;
    private Integer areaid;
    private String areaname;
    private String troubleDesc;
    private List<SmallproLogModel> logs;
    private Boolean ispj;
    private Integer totalPoIntegers;
    private String pjKinds;
    private String areaCode;
    private String areaName;
    private String areaTel;
    private String areaAddress;
    private Boolean isWeb;

    private Boolean showBaoxiuStatus;
    private String commentCode;

    /**
     * 订单号
     */
    private Integer oldId;
    /**
     * 订单号类型
     * @see com.jiuji.oa.afterservice.smallpro.enums.OldIdTypeEnum
     */
    private Integer oldIdType;
    /**
     * 接件备注
     */
    private String comment;
}
