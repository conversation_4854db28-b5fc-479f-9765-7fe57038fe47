package com.jiuji.cloud.after.vo.refund;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.cloud.after.vo.refund.enums.RefundBusinessEnum;
import com.jiuji.cloud.after.vo.refund.enums.ThirdRefundTypeEnum;
import com.jiuji.cloud.after.vo.refund.enums.TuiGroupEnum;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 其他退款实体类Vo
 * <AUTHOR>
 * @since 2022/7/27 8:43
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
@ApiModel("其他退款实体类Vo")
public class OtherRefundVo implements Refund<OtherRefundVo> {
    /**
     * 退款明细的主键
     */
    @ApiModelProperty("退款明细的主键")
    private Integer id;
    /**
     * 分组代码
     * @see TuiGroupEnum
     */
    @ApiModelProperty("分组代码")
    @NotNull(message = "分组代码不能为空")
    private Integer groupCode;
    /**
     * 退款方式名称
     */
    @ApiModelProperty("退款方式名称")
    @NotBlank(message = "退款方式不能为空")
    private String returnWayName;
    /**
     * 本次退款金额
     */
    @ApiModelProperty("本次退款金额")
    @NotNull(message = "本次退款金额不能为空")
    @Min(value = 0,message = "本次退款金额必须大于等于0")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal refundPrice;
    /**
     * 实付金额
     */
    @ApiModelProperty("实付金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal actualPayPrice;
    /**
     * 退款的业务类型 0和null是普通退款 1 运营商返现
     * @see RefundBusinessEnum
     */
    @ApiModelProperty("退款的业务类型")
    private Integer refundBusinessType;

    /**
     * 收银记录id
     */
    @ApiModelProperty("收银记录id")
    private Integer shouyingId;
    /**
     * 收银时间
     */
    @JSONField(format = TimeFormatConstant.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime dtime;

    /**
     * 退款方式
     * @see ThirdRefundTypeEnum
     */
    @ApiModelProperty("退款方式")
    private Integer thirdRefundType;

}
