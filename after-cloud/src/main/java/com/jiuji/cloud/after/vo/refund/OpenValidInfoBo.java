package com.jiuji.cloud.after.vo.refund;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/8/9 16:59
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "微信支付宝信息验证对象")
public class OpenValidInfoBo {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;
    /**
     * @see IdTypeEnum
     */
    @ApiModelProperty(value = "id类型枚举",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private IdTypeEnum idTypeEnum;

    /**
     * 主要用途: 提示绑定微信号
     * @see com.jiuji.oa.afterservice.bigpro.po.BbsxpUsers
     */
    @ApiModelProperty("九机会员编号")
    private Integer userId;
    /**
     * 退款方式
     */
    @ApiModelProperty("退款方式")
    private String tuiWay;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo.OpenType
     */
    @ApiModelProperty(value = "平台类型枚举",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private OpenIdInfoBo.OpenType openType;
    /**
     * 支付openid
     */
    private String payOpenId;
    /**
     * 订单类型
     */
    private OrderBusinessTypeEnum orderBusinessTypeEnum;
    /**
     * 业务单号
     */
    private Integer subId;

    /**
     * id类型枚举
     * 1 暂时没用 2 回收表id 3 退换表id 4 维修外送报账id  5 退换详情id
     */
    @Getter
    @AllArgsConstructor
    public enum IdTypeEnum implements CodeMessageEnumInterface {
        UNKNOWN(1,"暂时没用")
        ,RECOVER_ID(2,"回收表id")
        ,TUIHUAN_ID(3,"退换表id")
        ,WAISONG_ID(4,"维修外送报账配件id")
        ,TUIHUAN_DETAIL_ID(5,"退换详情id")
        ;
        /**
         * 类别
         */
        private Integer code;
        /**
         * 名称
         */
        private String message;
    }

    /**
     * 订单类型
     * 1: 销售单支付
     * 2: 维修单支付
     * 3: 良品单支付
     * 4: 乐捐单支付
     * 5: 客户投诉奖励
     * 6: 备用机押金单支付
     * 10:售后小件单支付
     * 20: 回收单退款
     * 21: 发票回收退款
     * 22: 物流快捷报销
     * 23: 员工打赏
     * 24: 维修报销
     * 25: 回收价保
     */
    @Getter
    @AllArgsConstructor
    public enum OrderBusinessTypeEnum implements CodeMessageEnumInterface {
        ORDER(1,"销售单支付")
        ,WEIXIU(2,"维修单支付")
        ,LIANGPIN(3,"良品单支付")
        ,LEJUAN(4,"乐捐单支付")
        ,TOUSU(5,"客户投诉奖励")
        ,BEIYOUNJI_YAJIN(6,"备用机押金单支付")
        ,SHOUHOU_XIAOJIAN(10,"售后小件单支付")
        ,HUISHOU_TUIKUAN(20,"回收单退款")
        ,FAPIAO(21,"发票回收退款")
        ,WULIU_BAOXIAO(22,"物流快捷报销")
        ,DASHANG(23,"员工打赏")
        ,WEIXIU_BAOXIAO(24,"维修报销")
        ,HUISHOU_BAOJA(25,"回收价保")
        ;
        /**
         * 类别
         */
        private Integer code;
        /**
         * 名称
         */
        private String message;
    }

}
