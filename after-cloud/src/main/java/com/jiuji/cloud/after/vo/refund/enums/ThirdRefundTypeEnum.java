package com.jiuji.cloud.after.vo.refund.enums;

import cn.hutool.core.util.ObjectUtil;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/8/22 17:36
 */
@Getter
@AllArgsConstructor
public enum ThirdRefundTypeEnum implements CodeMessageEnumInterface {
    NOT_SUPPORT_REFUND(0,"不支持退款"),
    ORIGIN_WAY_REFUND(1,"原路径退款"),
    OTHER_WAY_REFUND(2,"其他方式退款"),
    ORIGIN_WAY_OR_OTHER_WAY_REFUND(3,"原路径或其它方式退款"),
    ORIGIN_WAY_REFUND_DISCOUNT(4, "原路径退款参与折价"),
    ORIGIN_WAY_OR_OTHER_WAY_FULL_REFUND(5, "原路径或其它方式退款(原路径整笔退)"),
    ORIGIN_WAY_FULL_REFUND(6, "原路径退款(整笔退)"),
    RECORD_ONLY_NO_REFUND(7, "仅做账不进行实际退款"),
    // 原样充值到新订单的退款
    COPY_PAY_TO_NEW_ORDER(8, "原样充值到新订单的退款"),
    ;

    /**
     * 编码
     */
    private Integer code;

    /**
     * 编码对应信息
     */
    private String message;

    /**
     * 能够退款
     * @param refundType
     * @return
     */
    public static boolean canRefund(Integer refundType){
        return Stream.of(ORIGIN_WAY_REFUND, ORIGIN_WAY_FULL_REFUND,OTHER_WAY_REFUND, ORIGIN_WAY_OR_OTHER_WAY_REFUND, ORIGIN_WAY_OR_OTHER_WAY_FULL_REFUND
                        , ORIGIN_WAY_REFUND_DISCOUNT)
                .map(ThirdRefundTypeEnum::getCode).anyMatch(type -> ObjectUtil.equal(refundType,type));
    }

    /**
     * 三方只能原路径退款展示"否"
     * @param refundType
     * @return
     */
    public static boolean canNotTransportNewSub(Integer refundType){
        return !otherRefund(refundType);
    }

    /**
     * 三方可以其他方式退款的展示"是"
     * @param refundType
     * @return
     */
    public static boolean canTransportNewSub(Integer refundType){
        return otherRefund(refundType);
    }




    public static boolean originRefund(Integer refundType){
        return Stream.of(ORIGIN_WAY_REFUND, ORIGIN_WAY_FULL_REFUND, ORIGIN_WAY_OR_OTHER_WAY_REFUND, ORIGIN_WAY_OR_OTHER_WAY_FULL_REFUND,
                        ORIGIN_WAY_REFUND_DISCOUNT)
                .map(ThirdRefundTypeEnum::getCode).anyMatch(type -> ObjectUtil.equal(refundType,type));
    }

    public static boolean otherRefund(Integer refundType){
        return Stream.of(OTHER_WAY_REFUND, ORIGIN_WAY_OR_OTHER_WAY_REFUND, ORIGIN_WAY_OR_OTHER_WAY_FULL_REFUND)
                .map(ThirdRefundTypeEnum::getCode).anyMatch(type -> ObjectUtil.equal(refundType,type));
    }

    public static boolean fullRefund(Integer refundType){
        return Stream.of(ORIGIN_WAY_OR_OTHER_WAY_FULL_REFUND, ORIGIN_WAY_FULL_REFUND)
                .map(ThirdRefundTypeEnum::getCode).anyMatch(type -> ObjectUtil.equal(refundType,type));
    }


    /**
     *
     * @param refundType
     * @param isDiscount null 不限制 true 仅查询参与折价的 false 不查询参与折价
     * @return
     */
    public static boolean onlyOriginRefund(Integer refundType, Boolean isDiscount){
        Stream.Builder<ThirdRefundTypeEnum> streamBuilder = Stream.builder();
        if(isDiscount == null || Boolean.TRUE.equals(isDiscount)){
            //需要查询折价
            streamBuilder.add(ORIGIN_WAY_REFUND_DISCOUNT);
        }
        if(!Boolean.TRUE.equals(isDiscount)){
            //非仅折价限制
            streamBuilder.add(ORIGIN_WAY_REFUND).add(ORIGIN_WAY_FULL_REFUND);
        }
        return streamBuilder.build().map(ThirdRefundTypeEnum::getCode).anyMatch(type -> ObjectUtil.equal(refundType,type));
    }
}
