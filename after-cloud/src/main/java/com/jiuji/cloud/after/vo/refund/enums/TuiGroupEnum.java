package com.jiuji.cloud.after.vo.refund.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1 网上支付原路径 2 刷卡支付原路径 3 银行转账 4 微信支付宝秒退 5 三方支付原路退款 6 其他退款
 * <AUTHOR>
 * @since 2022/7/18 20:39
 */
@Getter
@AllArgsConstructor
public enum TuiGroupEnum implements CodeMessageEnumInterface {
    DEDUCTION_NOT_SAVE(-1,"扣款分组不存记录",99),
    NET_PAY_ORIGIN_WAY(1,"网上支付原路退款",1),
    CARD_PAY_ORIGIN_WAY(2,"刷卡支付原路退款",2),
    BANK_PAY_TRANSFER_ACCOUNTS(3,"银行转账退款",4),
    WECHAT_ALIPAY_SECONDS_REFUND(4,"微信支付宝秒退",5),
    THIRD_PAY_ORIGIN_WAY(5, "三方支付原路退款",3),
    OTHER_REFUND(6,"其他退款",6)
    ;

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**
     * 排序
     */
    private Integer sort;
}
