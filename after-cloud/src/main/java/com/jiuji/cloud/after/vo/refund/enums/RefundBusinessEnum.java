package com.jiuji.cloud.after.vo.refund.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 返现
 */
@Getter
@AllArgsConstructor
public enum RefundBusinessEnum implements CodeMessageEnumInterface {
    ZERO(0,"普通退款"),
    SELECT_RECURRENCE(1,"选中运营商返现"),
    NO_SELECT_RECURRENCE(2,"未选中运营商返现");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    /**
     * @param code
     * @return
     */
    public static RefundBusinessEnum getEnumByCode(Integer code) {
        RefundBusinessEnum[] value = values();
        for (RefundBusinessEnum e : value) {
            if (Objects.equals(e.getCode(),code)) {
                return e;
            }
        }
        return null;
    }
}
