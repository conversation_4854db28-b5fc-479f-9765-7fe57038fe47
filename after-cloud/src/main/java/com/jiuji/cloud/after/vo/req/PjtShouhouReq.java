package com.jiuji.cloud.after.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.math.BigDecimal;

@Data
public class PjtShouhouReq {

    @ApiModelProperty(value = "转售单Id",required = true)
    @NotNull(groups = Default.class,message = "转售单号不能为空")
    private Long recoverSubId;

    @ApiModelProperty(value = "转售单明细Id",required = true)
    @NotNull(groups = Default.class,message = "转售单明细Id不能为空")
    private Integer recoverBasketId;

    @ApiModelProperty(value = "mkcId",required = true)
    @NotNull(groups = Default.class,message = "mkcId不能为空")
    private Integer mkcId;

    @ApiModelProperty(value = "problem",required = true)
    @NotNull(groups = Default.class,message = "故障描述不能为空")
    private String problem;

    private BigDecimal returnPrice;

}
