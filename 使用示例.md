# 其他退款信息接口使用示例

## 接口信息

### OaApiController 中的接口
- **路径**: `/groupRefund/listAllOtherRefund`
- **方法**: GET
- **参数**: 
  - `orderId` (Integer): 订单ID
  - `tuihuanKind` (Integer): 退款类型
- **返回**: `R<List<OtherRefundVo>>`

### OaAfterClient 中的 Feign 接口
- **方法**: `listAllOtherRefund`
- **参数**: 
  - `orderId` (Integer): 订单ID  
  - `tuihuanKind` (Integer): 退款类型
- **返回**: `R<List<OtherRefundVo>>`

## 使用方式

### 1. 直接调用 OaApiController
```java
// GET /afterservice/api/wcf/groupRefund/listAllOtherRefund?orderId=123&tuihuanKind=3
```

### 2. 通过 Feign 客户端调用
```java
@Autowired
private OaAfterClient oaAfterClient;

public void example() {
    Integer orderId = 123;
    Integer tuihuanKind = 3; // 退款类型，参考 TuihuanKindEnum
    
    R<List<OtherRefundVo>> result = oaAfterClient.listAllOtherRefund(orderId, tuihuanKind);
    
    if (result.getCode() == 200) {
        List<OtherRefundVo> otherRefunds = result.getData();
        // 处理其他退款信息
        for (OtherRefundVo refund : otherRefunds) {
            System.out.println("退款方式: " + refund.getReturnWayName());
            System.out.println("退款金额: " + refund.getRefundPrice());
            System.out.println("实付金额: " + refund.getActualPayPrice());
        }
    }
}
```

## 退款类型说明 (TuihuanKindEnum)
- 1: 换机头 (HJT)
- 2: 换主板 (HZB)  
- 3: 退款 (TK)
- 4: 换其它型号 (HQTXH)
- 5: 退维修费 (TWXF)
- 6: 退订金 (TDJ)
- 7: 退配件 (TPJ)
- 8: 退订金(良品) (TDJ_LP)
- 9: 小件换货 (SMALL_PRO_REFUND)
- 10: 小件退维修费 (SMALL_PRO_REFUND_REPAIR_FEE)
- 11: 退订金(维修费) (TDJ_WXF)
- 12: 大件批量退款 (BATCH_TK)
- 13: 小件换货(换其他型号) (SMALL_PRO_HQTXH)
- 99: 良品退款 (TK_LP)

## 返回数据结构 (OtherRefundVo)
```java
{
    "id": 123,                          // 退款明细的主键
    "groupCode": 6,                     // 分组代码 (参考 TuiGroupEnum)
    "returnWayName": "现金",             // 退款方式名称
    "refundPrice": 100.00,              // 本次退款金额
    "actualPayPrice": 100.00,           // 实付金额
    "refundBusinessType": 0,            // 退款的业务类型 (0=普通退款, 1=运营商返现)
    "shouyingId": 456,                  // 收银记录id
    "dtime": "2024-01-01 12:00:00",     // 收银时间
    "thirdRefundType": 1                // 退款方式类型
}
```

## 错误处理
当服务调用失败时，会通过 `OaAfterClientFallBack` 返回错误信息：
```java
// 服务调用失败时的返回示例
{
    "code": 500,
    "message": "远程调用/afterservice/api/wcf/groupRefund/listAllOtherRefund 失败,参数[123,3]",
    "data": null
}
```

## 实现原理
该接口调用了 `OtherRefundServiceImpl#listAll` 方法，该方法会：
1. 获取父子订单号
2. 获取收银类型
3. 查询所有其他收银记录（现金、余额等）
4. 设置分组代码并返回结果

## 完整的实现文件
1. **OaApiController** - 提供 REST 接口
2. **OaAfterClient** - Feign 客户端接口
3. **OaAfterClientFallBack** - 服务降级处理
4. **OtherRefundVo** - 返回数据模型
5. **相关枚举类** - RefundBusinessEnum, ThirdRefundTypeEnum, TuiGroupEnum
